package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Product;
import com.maersk.sd1.sds.dto.ProductListInput;
import com.maersk.sd1.sds.dto.ProductListOutput;
import com.maersk.sd1.sds.repository.ProductListRepository;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductListServiceTest {
    @InjectMocks
    private ProductListService productListService;

    @Mock
    private Root<Product> root;

    @Mock
    private CriteriaQuery<?> query;

    @Mock
    private CriteriaBuilder cb;

    @Mock
    private ProductListRepository productListRepository;

    private Product product;
    private ProductListInput.Input input;

    @BeforeEach
    void setUp() {
        product = new Product();
        product.setId(1);
        product.setProductCode("P001");
        product.setProductName("Test Product");
        product.setRegistrationDate(LocalDateTime.now());
        product.setModificationDate(LocalDateTime.now());
        product.setActive(true);

        input = new ProductListInput.Input();
        input.setPage(1);
        input.setSize(10);
        input.setProductCode("P001");
        input.setProductName("Test Product");
        input.setRegistrationDateMin(LocalDate.now().minusDays(10));
        input.setRegistrationDateMax(LocalDate.now());
        input.setModificationDateMin(LocalDate.now().minusDays(5));
        input.setModificationDateMax(LocalDate.now());
    }

    @Test
    void Given_ValidInput_When_ListProducts_Then_ReturnFilteredResults() {
        Page<Product> page = new PageImpl<>(List.of(product));
        when(productListRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        ProductListOutput output = productListService.listProducts(input);

        assertNotNull(output);
        assertEquals(1, output.getTotalRecords().getFirst().getFirst());
        assertFalse(output.getProducts().isEmpty());
    }

    @Test
    void Given_EmptyDatabase_When_ListProducts_Then_ReturnEmptyList() {
        Page<Product> emptyPage = new PageImpl<>(Collections.emptyList());
        when(productListRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        ProductListOutput output = productListService.listProducts(input);

        assertNotNull(output);
        assertEquals(0, output.getTotalRecords().getFirst().getFirst());
        assertTrue(output.getProducts().isEmpty());
    }

    @Test
    void Given_DatabaseError_When_ListProducts_Then_HandleExceptionGracefully() {
        when(productListRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenThrow(new RuntimeException("Database error"));

        ProductListOutput output = productListService.listProducts(input);

        assertNotNull(output);
    }

    @Test
    void Given_ValidInput_When_ListProductsCalled_Then_PredicatesAreApplied() {
        Page<Product> page = new PageImpl<>(List.of(product));
        when(productListRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        ProductListOutput output = productListService.listProducts(input);

        assertNotNull(output);
        verify(productListRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void Given_NullInput_When_ListProducts_Then_HandleGracefully() {
        ProductListOutput output = productListService.listProducts(null);

        assertNotNull(output);
    }

    @Test
    void Given_MissingOptionalFields_When_ListProducts_Then_DefaultValuesApplied() {
        input.setProductCode(null);
        input.setProductName(null);
        input.setRegistrationDateMin(null);
        input.setRegistrationDateMax(null);

        Page<Product> page = new PageImpl<>(List.of(product));
        when(productListRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        ProductListOutput output = productListService.listProducts(input);

        assertNotNull(output);
        assertEquals(1, output.getTotalRecords().getFirst().getFirst());
    }

    @Test
    void Given_InvalidDateRange_When_ListProducts_Then_NoResults() {
        input.setRegistrationDateMin(LocalDate.now().plusDays(1));
        input.setRegistrationDateMax(LocalDate.now().plusDays(2));
        Page<Product> emptyPage = new PageImpl<>(Collections.emptyList());
        when(productListRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        ProductListOutput output = productListService.listProducts(input);

        assertNotNull(output);
        assertEquals(0, output.getTotalRecords().getFirst().getFirst());
        assertTrue(output.getProducts().isEmpty());
    }
    @Test
    void Given_ValidInput_When_ListProductsIsCalled_Then_ReturnsSuccessfulResponse() {
        // Given
        ProductListInput.Input testInput = new ProductListInput.Input();
        testInput.setPage(1);
        testInput.setSize(10);

        Product mockProduct = new Product();
        mockProduct.setId(1);
        mockProduct.setProductCode("P123");
        mockProduct.setProductName("Test Product");
        mockProduct.setRegistrationDate(LocalDateTime.now());
        mockProduct.setModificationDate(LocalDateTime.now());

        Page<Product> page = new PageImpl<>(Collections.singletonList(mockProduct), PageRequest.of(0, 10), 1);
        when(productListRepository.findAll((Example<Product>) any(), any(Pageable.class))).thenReturn(page);

        ProductListOutput output = productListService.listProducts(testInput);

        // Then
        assertNotNull(output);
    }

    @Test
    void testBuildSpecification_WithNoFilters() {
        Specification<Product> specification = productListService.buildSpecification(input);
        assertNotNull(specification);

        Predicate result = specification.toPredicate(root, query, cb);
        assertNull(result);
    }
    @Test
    void Given_ExceptionOccurs_When_ListProductsIsCalled_Then_ReturnsErrorResponse() {
        // Given
        ProductListInput.Input testInput = new ProductListInput.Input();
        when(productListRepository.findAll((Example<Product>) any(), any(Pageable.class))).thenThrow(new RuntimeException("Database error"));

        // When
        ProductListOutput output = productListService.listProducts(testInput);

        // Then
        assertNotNull(output);
    }
}