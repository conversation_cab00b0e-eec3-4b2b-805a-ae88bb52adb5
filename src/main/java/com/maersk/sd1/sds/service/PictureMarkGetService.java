package com.maersk.sd1.sds.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.SystemRule;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.sds.dto.PictureMarkGetOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@RequiredArgsConstructor
@Service
public class PictureMarkGetService {

    private static final Logger logger = LogManager.getLogger(PictureMarkGetService.class);

    private final SystemRuleRepository systemRuleRepository;

    @Transactional(readOnly = true)
    public PictureMarkGetOutput getPictureMarkUrl() {
        PictureMarkGetOutput output = new PictureMarkGetOutput();
        try {
            Optional<SystemRule> ruleOpt = systemRuleRepository.findByAliasAndActive("sd1_config_env", true);
            if (ruleOpt.isPresent()) {
                String ruleJson = ruleOpt.get().getRule();

                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(ruleJson);
                JsonNode urlPictureMarkNode = jsonNode.get("url_picture_mark");
                String urlPictureMark = (urlPictureMarkNode != null) ? urlPictureMarkNode.asText() : null;

                output.setUrlPictureMark(urlPictureMark);
            } else {
                output.setUrlPictureMark(null);
            }
        } catch (Exception e) {
            logger.error("Error in getPictureMarkUrl", e);
            output.setUrlPictureMark(null);
        }
        return output;
    }

}
