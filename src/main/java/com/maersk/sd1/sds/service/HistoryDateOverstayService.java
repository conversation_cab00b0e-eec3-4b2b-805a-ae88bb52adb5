package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.CargoDocumentRepository;
import com.maersk.sd1.sds.dto.IHistoryDateOverStayOutput;
import com.maersk.sd1.sds.controller.dto.HistoryDateOverStayOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class HistoryDateOverstayService {

    private static final Logger logger = LogManager.getLogger(HistoryDateOverstayService.class);

    private final CargoDocumentRepository cargoDocumentRepository;

    public HistoryDateOverstayService(CargoDocumentRepository cargoDocumentRepository) {
        this.cargoDocumentRepository = cargoDocumentRepository;
    }

    @Transactional(readOnly = true)
    public List<HistoryDateOverStayOutput> listHistoryDateOverStay(Integer documentoCargaId, Integer idiomaId) {
        if (documentoCargaId == null || idiomaId == null) {
            throw new IllegalArgumentException("documentoCargaId and idiomaId cannot be null");
        }
        try {
            Integer realProgramacionNaveDetalleId = cargoDocumentRepository.findVesselProgrammingDetailId(documentoCargaId);
            logger.info("Real programacion_nave_detalle_id found: {}", realProgramacionNaveDetalleId);

            Long businessUnitId = cargoDocumentRepository.findBusinessUnitId(documentoCargaId);
            logger.info("BusinessUnit ID found: {}", businessUnitId);

            List<IHistoryDateOverStayOutput> rawResults = cargoDocumentRepository.rawNativeOverstayHistory(documentoCargaId, idiomaId);

            List<HistoryDateOverStayOutput> finalList = new ArrayList<>();
            for (IHistoryDateOverStayOutput row : rawResults) {
                HistoryDateOverStayOutput dto = new HistoryDateOverStayOutput();
                dto.setBlMaster(row.getBlMaster());
                dto.setShippingLine(row.getShippingLine());
                dto.setContainer(row.getContainer());
                dto.setOverstayDate(row.getOverstayDate());
                dto.setRegistrationDate(row.getRegistrationDate());
                dto.setLinkedBl(row.getLinkedBl());
                dto.setOtherShipProgram(row.getOtherShipProgram());
                dto.setRegistrationUserId(row.getRegistrationUserId());
                dto.setRegistrationUserNames(row.getRegistrationUserNames());
                dto.setRegistrationUserLastNames(row.getRegistrationUserLastNames());
                dto.setReason(row.getReason());
                dto.setUserComment(row.getUserComment());
                dto.setFileName(row.getFileName());
                dto.setOrigin(row.getOrigin());
                finalList.add(dto);
            }

            return finalList;
        } catch (Exception e) {
            logger.error("Error listing damage records:", e);
            throw new DamageRecordException("Error listing damage records", e);
        }

    }
    public static class DamageRecordException extends RuntimeException {
        public DamageRecordException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}