package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.ActivityLog;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;

import com.maersk.sd1.common.repository.ActivityLogRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sds.controller.dto.ActivityLogUpdateInput;
import com.maersk.sd1.sds.controller.dto.ActivityLogUpdateOutput;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Log4j2
@Service
public class ActivityLogUpdateService {


    private final ActivityLogRepository activityLogRepository;


    private final CatalogRepository catalogRepository;

    @Autowired
    public ActivityLogUpdateService(ActivityLogRepository activityLogRepository, CatalogRepository catalogRepository)
    {
        this.activityLogRepository = activityLogRepository;
        this.catalogRepository = catalogRepository;
    }


    @Transactional
    public ActivityLogUpdateOutput updateActivityLog(ActivityLogUpdateInput.Input input) {
        ActivityLogUpdateOutput output = new ActivityLogUpdateOutput();
        try {
            // 1) Check if main ActivityLog exists
            Optional<ActivityLog> mainActivityLogOpt = activityLogRepository.findById(input.getActivityLogId());
            if (mainActivityLogOpt.isEmpty()) {
                output.setRespEstado(0);
                output.setRespMensaje("Activity log not found");
                output.setRespIntegration(null);
                return output;
            }

            ActivityLog mainActivityLog = mainActivityLogOpt.get();

            // 2) Check if main ActivityLog is retryable
            if (mainActivityLog.getRetryable() == null || !mainActivityLog.getRetryable()) {
                output.setRespEstado(0);
                output.setRespMensaje("Selected log is not retryable");
                output.setRespIntegration(null);
                return output;
            }

            // 3) Find all logs that should be updated
            Integer eirNumberParam = input.getEirNumber() == null ? null : input.getEirNumber().intValue();
            List<ActivityLog> logsToUpdate = activityLogRepository.findActivityLogsForUpdate(
                    input.getActivityLogId(),
                    input.getContainerNumber(),
                    eirNumberParam,
                    input.getActivityAlias(),
                    input.getSubBusinessUnitId(),
                    input.getModuleAlias()
            );

            if (logsToUpdate.isEmpty()) {
                // If there's no matching rows at all, we can choose to return a message or proceed.
                output.setRespEstado(0);
                output.setRespMensaje("No activity logs found to update.");
                output.setRespIntegration(null);
                return output;
            }

            // 4) Fetch the Catalog status using alias
            Catalog newCatalogStatus = catalogRepository.findByAlias(input.getStatusAlias());
            if (newCatalogStatus == null) {
                output.setRespEstado(0);
                output.setRespMensaje("Status catalog not found");
                output.setRespIntegration(null);
                return output;
            }

            // 5) Optionally fetch user from DB if needed. For now, create a new user placeholder.
            //    In real scenario, we have to get from UserRepository if we want to ensure it exists.
            User modificationUser = new User(input.getUserModificationId());

            // 6) Update all logs
            for (ActivityLog log : logsToUpdate) {
                log.setModificationUser(modificationUser);
                log.setModificationDate(LocalDateTime.now());
                log.setCatStatus(newCatalogStatus);
                // set data_input if you want to replicate or store the new input (procedure doesn't do it).
                // log.setInputData(input.getDataInput()); // optional if needed.
                log.setRetryable(false);
            }
            activityLogRepository.saveAll(logsToUpdate);

            // 7) Set success output
            output.setRespEstado(1);
            output.setRespMensaje("Updated correctly");
            output.setRespIntegration(null);
        } catch (Exception e) {
            log.error("Error updating activity log", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespIntegration(null);
        }

        return output;
    }
}
