package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.StockEmptyRepository;
import com.maersk.sd1.common.repository.StockFullRepository;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sds.dto.EIRFindStock;
import com.maersk.sd1.sds.repository.SdsEirRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class StockService {

    private static final String CNTX_PLACEHOLDER = "{CNTX}";
    private static final String PRC_EIR_MANUAL = "PRC_EIR_MANUAL";


    private final StockEmptyRepository stockEmptyRepository;


    private final StockFullRepository stockFullRepository;


    private final SdsEirRepository sdsEirRepository;


    private final BusinessUnitRepository businessUnitRepository;


    private final GESCatalogService gesCatalogService;


    private final MessageLanguageService messageLanguageService;

    public String execute(Integer businessUnitId,Integer subBusinessUnitId, Integer catMovementId, Integer catEmptyFullId,Integer containerId, String containerNumber, String messageTrace, Integer languageId) {

        String message = null;

        HashMap<String, Integer> catalogs = gesCatalogService.findIdsByAliasesIn(List.of(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS, Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS, Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS, Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS));

        if(Objects.equals(catMovementId, catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS))){

            boolean notOkGI = false;

            List<Integer> catMovements = sdsEirRepository.findFirstMovementTypeForStock(containerId, businessUnitId);

            Integer eirCatMovementId = catMovements.get(0);

            if(Objects.equals(catEmptyFullId, catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))){

                Integer stockEmptyId = stockEmptyRepository.validateStockGateInContainer(containerId, businessUnitId);

                if(stockEmptyId != null){

                    if(Objects.equals(eirCatMovementId, catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))){
                        stockEmptyRepository.updateStockEmptyInStockFalse(Parameter.DEFAULT_USER_ID_MASTER , stockEmptyId, messageTrace);
                    }

                    notOkGI = true;

                }

            }

            if(Objects.equals(catEmptyFullId, catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))){

                Integer stockFullId = stockFullRepository.validateStockGateInContainer(containerId, businessUnitId);

                if(stockFullId != null){

                    if(Objects.equals(eirCatMovementId, catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))){

                        stockFullRepository.updateStockFullInStockFalse(Parameter.DEFAULT_USER_ID_MASTER , stockFullId, messageTrace);

                    }

                    notOkGI = true;

                }

            }

            if(notOkGI){

                EIRFindStock eirFindStock = sdsEirRepository.findEIRStockGateIn(containerId, businessUnitId);

                message = messageLanguageService.getMessage(PRC_EIR_MANUAL, 2, languageId, Map.of(CNTX_PLACEHOLDER, containerNumber, "{IDX}", eirFindStock.getEirId()+"|"+eirFindStock.getBusinessUnitName()+"|"+eirFindStock.getCatMovementTypeVar2()+" "+eirFindStock.getCatEmptyFullDesc()));

            }

        }

        if(Objects.equals(catMovementId, catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))){

            boolean notOkGI = false;

            if(Objects.equals(catEmptyFullId, catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))){

                Integer stockEmptyId = stockEmptyRepository.validateStockGateOutContainer(containerId, subBusinessUnitId);

                if(stockEmptyId == null){

                    notOkGI = true;

                }

            }

            if(Objects.equals(catEmptyFullId, catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))){

                Integer stockFullId = stockFullRepository.validateStockGateOutContainer(containerId, subBusinessUnitId);

                if(stockFullId == null){

                    notOkGI = true;

                }

            }

            if(notOkGI){

                EIRFindStock eirFindStock = sdsEirRepository.findEIRStockGateIn(containerId, businessUnitId);

                if(eirFindStock == null){

                    EIRFindStock eirFindStockOnlyContainer = sdsEirRepository.findEIRStockGateIn(containerId);

                    String businessUnitName = businessUnitRepository.findNameByBusinessUnit(subBusinessUnitId);

                    if(eirFindStockOnlyContainer == null){

                        message = messageLanguageService.getMessage(PRC_EIR_MANUAL, 12, languageId, Map.of(CNTX_PLACEHOLDER, containerNumber, "{SUNX}", businessUnitName));

                    } else {

                        message = messageLanguageService.getMessage(PRC_EIR_MANUAL, 3, languageId, Map.of(CNTX_PLACEHOLDER, containerNumber, "{SUNX}", businessUnitName, "{IDX}", eirFindStockOnlyContainer.getEirId()+"|"+eirFindStockOnlyContainer.getBusinessUnitName()+"|"+eirFindStockOnlyContainer.getCatMovementTypeVar2()+" "+eirFindStockOnlyContainer.getCatEmptyFullDesc()));

                    }

                }

            }

        }

        return message;

    }

}
