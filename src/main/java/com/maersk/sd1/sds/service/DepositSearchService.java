package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.DepotRepository;
import com.maersk.sd1.sds.dto.DepositSearchInput;
import com.maersk.sd1.sds.dto.DepositSearchOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DepositSearchService {

    private static final Logger logger = LogManager.getLogger(DepositSearchService.class);

    private final DepotRepository depotRepository;

    @Transactional(readOnly = true)
    public List<DepositSearchOutput> searchDeposits(DepositSearchInput.Input input) {
        List<DepositSearchOutput> depositList;
        try {
            // Prepare a pageable for top 10 results
            PageRequest topTen = PageRequest.of(0, 10);

            Page<DepositSearchOutput> pageResult = depotRepository.findDeposits(
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getDepositId(),
                    input.getDepositName(),
                    topTen);

            depositList = pageResult.getContent();
        } catch (Exception e) {
            logger.error("Error occurred while searching deposits", e);
            return Collections.emptyList();
        }
        return depositList;
    }
}

