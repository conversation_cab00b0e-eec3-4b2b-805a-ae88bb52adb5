package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.CargoDocumentObtainOutput;
import com.maersk.sd1.sds.dto.CargoDocumentObtainDetailOutput;
import com.maersk.sd1.sds.dto.CargoDocumentObtainOutputItem;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CargoDocumentObtainService {

    private static final Logger logger = LogManager.getLogger(CargoDocumentObtainService.class.getName());

    private final CargoDocumentRepository cargoDocumentRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final BusinessUnitConfigRepository businessUnitConfigRepository;
    private final CatalogRepository catalogRepository;

    @Transactional(readOnly = true)
    public CargoDocumentObtainOutput cargoDocumentObtain(Integer subBusinessUnitId, Integer documentoCargaId, Integer languageId) {
        logger.info("Starting cargoDocumentObtain with subBusinessUnitId={}, documentoCargaId={}, languageId={}.",
                subBusinessUnitId, documentoCargaId, languageId);

        Optional<BusinessUnit> subBUOpt = businessUnitRepository.findById(subBusinessUnitId);
        if (subBUOpt.isEmpty()) {
            throw new EntityNotFoundException("Sub Business Unit not found: " + subBusinessUnitId);
        }
        BusinessUnit subBU = subBUOpt.get();
        BusinessUnit parentBU = subBU.getParentBusinessUnit();
        if (parentBU == null) {
            parentBU = subBU;
        }

        final int TIPO_CONFIGURACION_ZONA = 20251;
        int zonaHorariaGmt = 0; // default
        List<BusinessUnitConfig> configs = businessUnitConfigRepository.findByBusinessUnitIdAndCatConfigurationTypeId(parentBU.getId(), TIPO_CONFIGURACION_ZONA);
        if (!configs.isEmpty()) {
            try {
                zonaHorariaGmt = Integer.parseInt(configs.getFirst().getValue());
            } catch (NumberFormatException ex) {
                logger.warn("Unable to parse time zone value: {}", configs.getFirst().getValue());
            }
        }

        CargoDocumentObtainOutputItem output = new CargoDocumentObtainOutputItem();
        CargoDocument cargoDocument = cargoDocumentRepository.findCargoDocumentFullData(documentoCargaId);
        if (cargoDocument == null) {
            return new CargoDocumentObtainOutput();
        }

        output.setDocumentoCargaId(cargoDocument.getId());

        if (cargoDocument.getVesselProgrammingDetail() != null) {
            if (cargoDocument.getVesselProgrammingDetail().getCatOperation() != null) {
                Catalog cop = cargoDocument.getVesselProgrammingDetail().getCatOperation();
                output.setCatOperacionId(cop.getId());
                String catOpDesc = cop.getDescription() != null ? cop.getDescription() : "";
                String catOpLong = cop.getLongDescription() != null ? cop.getLongDescription() : "";
                output.setCatOperacionDescripcion(catOpDesc + "-" + catOpLong);
            }
            output.setManifiestoAno(cargoDocument.getVesselProgrammingDetail().getManifestYear());
            output.setManifiestoNumero(cargoDocument.getVesselProgrammingDetail().getManifestNumber());
            output.setInicioOperacion(cargoDocument.getVesselProgrammingDetail().getBeginningOperation());
            output.setFinOperacion(cargoDocument.getVesselProgrammingDetail().getEndingOperation());

            if (cargoDocument.getVesselProgrammingDetail().getCatCreationOrigin() != null) {
                output.setCatOrigenCreacionId(
                        cargoDocument.getVesselProgrammingDetail().getCatCreationOrigin().getId()
                );
                output.setCatOrigenCreacionDescripcion(
                        cargoDocument.getVesselProgrammingDetail().getCatCreationOrigin().getDescription()
                );
            }

            var catOriginCreation = cargoDocument.getCatOriginCreation();
            if (catOriginCreation != null) {
                output.setCatOrigenCreacionId1(catOriginCreation.getId());
                output.setCatOrigenCreacionDescripcion1(catOriginCreation.getDescription());
            }

            if (cargoDocument.getVesselProgrammingDetail().getVesselProgramming() != null) {
                if (cargoDocument.getVesselProgrammingDetail().getVesselProgramming().getVessel() != null) {
                    output.setNaveId(
                            cargoDocument.getVesselProgrammingDetail().getVesselProgramming().getVessel().getId()
                    );
                    output.setNave(
                            cargoDocument.getVesselProgrammingDetail().getVesselProgramming().getVessel().getName()
                    );
                }
                output.setViaje(
                        cargoDocument.getVesselProgrammingDetail().getVesselProgramming().getVoyage()
                );
                output.setFechaEta(
                        cargoDocument.getVesselProgrammingDetail().getVesselProgramming().getEtaDate()
                );
                output.setFechaEtd(
                        cargoDocument.getVesselProgrammingDetail().getVesselProgramming().getEtdDate()
                );
            }
        }

        if (cargoDocument.getCatCargoOrigin() != null) {
            output.setCatOrigenCargaId(cargoDocument.getCatCargoOrigin().getId());
            output.setCatOrigenCargaDescripcion(cargoDocument.getCatCargoOrigin().getDescription());
        }
        if (cargoDocument.getCatTransportRoute() != null) {
            output.setCatViaTransporteId(cargoDocument.getCatTransportRoute().getId());
            output.setCatViaTransporteDescripcion(cargoDocument.getCatTransportRoute().getDescription());
        }
        output.setDocumentoCarga(cargoDocument.getCargoDocument());
        output.setEsDesglosado(cargoDocument.getIsBroken());
        if (cargoDocument.getShippingLine() != null) {
            output.setLineaNavieraId(cargoDocument.getShippingLine().getId());
            output.setLineaNavieraNombre(cargoDocument.getShippingLine().getName());
        }
        if (cargoDocument.getMasterCargoDocument() != null) {
            output.setDocumentoCargaMadreId(cargoDocument.getMasterCargoDocument().getId());
        }
        if (cargoDocument.getOriginPort() != null) {
            output.setPuertoOrigenId(cargoDocument.getOriginPort().getId());
            output.setPuertoNombre(cargoDocument.getOriginPort().getPort() + "-" + cargoDocument.getOriginPort().getName());
        }
        if (cargoDocument.getLoadingPort() != null) {
            output.setPuertoEmbarqueId(cargoDocument.getLoadingPort().getId());
            output.setPuertoEmbarqueNombre(cargoDocument.getLoadingPort().getPort() + "-" + cargoDocument.getLoadingPort().getName());
        }
        if (cargoDocument.getDischargePort() != null) {
            output.setPuertoDescargaId(cargoDocument.getDischargePort().getId());
            output.setPuertoDescargaNombre(cargoDocument.getDischargePort().getPort() + "-" + cargoDocument.getDischargePort().getName());
        }
        if (cargoDocument.getDestinationPort() != null) {
            output.setPuertoDestinoId(cargoDocument.getDestinationPort().getId());
            output.setPuertoDestinoNombre(cargoDocument.getDestinationPort().getPort() + "-" + cargoDocument.getDestinationPort().getName());
        }
        if (cargoDocument.getTransferPort() != null) {
            output.setPuertoTransbordoId(cargoDocument.getTransferPort().getId());
            output.setPuertoTransbordoNombre(cargoDocument.getTransferPort().getPort() + "-" + cargoDocument.getTransferPort().getName());
        }
        if (cargoDocument.getDepot() != null) {
            output.setDepositoId(cargoDocument.getDepot().getId());
            output.setNombreDeposito(cargoDocument.getDepot().getNameDeposit());
        }
        if (cargoDocument.getShipperCompany() != null) {
            output.setEmpresaEmbarcadorId(cargoDocument.getShipperCompany().getId());
            output.setEmpresaEmbarcadorRazonSocial(
                    cargoDocument.getShipperCompany().getDocument() + "-" + cargoDocument.getShipperCompany().getLegalName()
            );
        }
        if (cargoDocument.getConsigneeCompany() != null) {
            output.setEmpresaConsignatarioId(cargoDocument.getConsigneeCompany().getId());
            output.setEmpresaConsignatarioRazonSocial(
                    cargoDocument.getConsigneeCompany().getDocument() + "-" + cargoDocument.getConsigneeCompany().getLegalName()
            );
        }
        if (cargoDocument.getNotifierCompany() != null) {
            output.setEmpresaNotificanteId(cargoDocument.getNotifierCompany().getId());
            output.setEmpresaNotificanteRazonSocial(
                    cargoDocument.getNotifierCompany().getDocument() + "-" + cargoDocument.getNotifierCompany().getLegalName()
            );
        }
        if (cargoDocument.getCustomsAgencyCompany() != null) {
            output.setEmpresaAgenciaAduanaId(cargoDocument.getCustomsAgencyCompany().getId());
            output.setEmpresaAgenciaAduanaRazonSocial(
                    cargoDocument.getCustomsAgencyCompany().getDocument() + "-" + cargoDocument.getCustomsAgencyCompany().getLegalName()
            );
        }
        output.setEmbarcadorDetalle(cargoDocument.getShipperDetail());
        output.setConsignatarioDetalle(cargoDocument.getConsigneeDetail());
        output.setNotificanteDetalle(cargoDocument.getNotifierDetail());
        output.setNumeroDetalleAduana(cargoDocument.getCustomsDetailsNumber());
        if (cargoDocument.getCatDocumentCargoStatus() != null) {
            output.setCatEstadoDocumentoCargaId(
                    cargoDocument.getCatDocumentCargoStatus().getId()
            );
            output.setCatEstadoDocumentoCargaDescripcion(
                    cargoDocument.getCatDocumentCargoStatus().getDescription()
            );
        }
        if (cargoDocument.getEmptyDepot() != null) {
            output.setDepositoVacioId(cargoDocument.getEmptyDepot().getId());
            output.setNombreDepositoVacio(
                    cargoDocument.getEmptyDepot().getCodeDeposit() + "-" + cargoDocument.getEmptyDepot().getNameDeposit()
            );
        }
        if (cargoDocument.getCatMoveType() != null) {
            Catalog cmt = cargoDocument.getCatMoveType();
            // Suppose we have a repository function: messageLanguageRepository.fnTranslatedCatalogLongDescription()
            String catMoveTypeTranslation = catalogRepository.fnCatalogoTraducidoDesLarga(cmt.getId(), languageId);
            output.setCatMoveType(catMoveTypeTranslation);
            output.setCatMoveTypeAlias(cmt.getAlias());
        }

        output.setMaerskDepotWithSd1(cargoDocument.getMaerskDepotWithSd1());
        if (cargoDocument.getOriginDestinationDepot() != null) {
            output.setOriginDestinationDepotId(
                    cargoDocument.getOriginDestinationDepot().getId()
            );
            output.setOriginDestinationDepot(
                    cargoDocument.getOriginDestinationDepot().getNameDeposit()
            );
        }

        List<CargoDocumentDetail> detailEntities = cargoDocumentDetailRepository.findCargoDocumentDetailFullData(documentoCargaId);
        List<CargoDocumentObtainDetailOutput> detailOutputs = new ArrayList<>();
        for (CargoDocumentDetail d : detailEntities) {
            CargoDocumentObtainDetailOutput detailDto = new CargoDocumentObtainDetailOutput();
            detailDto.setDocumentoCargaDetalleId(d.getId());
            if (d.getProduct() != null) {
                detailDto.setProductoId(d.getProduct().getId());
                detailDto.setNombreProducto(d.getProduct().getProductName());
            }
            if (d.getContainer() != null) {
                detailDto.setNumeroContenedor(d.getContainer().getContainerNumber());
                if (d.getContainer().getCatContainerType() != null) {
                    detailDto.setCatTipoContenedorId(d.getContainer().getCatContainerType().getId());
                    detailDto.setTipoContenedorDescripcion(d.getContainer().getCatContainerType().getDescription());
                }
                if (d.getContainer().getCatSize() != null) {
                    detailDto.setCatTamanoId(d.getContainer().getCatSize().getId());
                    detailDto.setTamanoContenedorDescripcion(d.getContainer().getCatSize().getDescription());
                }
            }
            detailDto.setPrecintoManifestado(""); // Placeholder if we need.

            detailDto.setCantidadManifestada(d.getManifestedQuantity());
            detailDto.setPesoManifestado(d.getManifestedWeight());
            detailDto.setVolumenManifestado(d.getManifestedVolume());

            if (d.getCatWeightMeasureUnit() != null) {
                detailDto.setCatUnidadMedidaPesoId(d.getCatWeightMeasureUnit().getId());
                detailDto.setCatUnidadMedidaPesoDescripcion(d.getCatWeightMeasureUnit().getDescription());
            }

            detailDto.setEsCargaPeligrosa(d.getIsDangerousCargo());
            detailDto.setEsCargaRefrigerada(d.getIsRefrigeratedCargo());

            detailDto.setMarcas(d.getBrands());
            detailDto.setMercaderia(d.getCommodity());

            if (d.getCatCargoCondition() != null) {
                detailDto.setCatCondicionCargaId(d.getCatCargoCondition().getId());
                detailDto.setCatCondicionCargaDescripcion(d.getCatCargoCondition().getDescription());
            }
            if (d.getCatManifestedContainerType() != null) {
                detailDto.setCatTipoContenedorManifestadoId(d.getCatManifestedContainerType().getId());
                detailDto.setCatTipoContenedorManifestadoDescripcion(d.getCatManifestedContainerType().getDescription());
            }
            if (d.getCatCreationOrigin() != null) {
                detailDto.setCatOrigenCreacionId(d.getCatCreationOrigin().getId());
                detailDto.setCatOrigenCreacionDescripcion(d.getCatCreationOrigin().getDescription());
            }
            if (d.getCatManifestedSize() != null) {
                detailDto.setCatTamanoManifestadoId(d.getCatManifestedSize().getId());
                detailDto.setCatTamanoManifestadoDescripcion(d.getCatManifestedSize().getDescription());
            }
            if (d.getCatMeasureUnitQuantity() != null) {
                detailDto.setCatUnidadMedidaCantidadId(d.getCatMeasureUnitQuantity().getId());
                detailDto.setCatUnidadMedidaCantidadDescripcion(d.getCatMeasureUnitQuantity().getDescription());
            }
            if (d.getEmptyPickUpBusinessUnit() != null) {
                detailDto.setUnidadNegocioRecojoVacioId(d.getEmptyPickUpBusinessUnit().getId());
                detailDto.setUnidadNegocioRecojoVacio(d.getEmptyPickUpBusinessUnit().getName());
            }
            if (d.getRegistrationUser() != null) {
                detailDto.setUsuarioRegistroId(d.getRegistrationUser().getId());
                detailDto.setUsuarioRegistroNombres(d.getRegistrationUser().getNames());
                String apellidos = d.getRegistrationUser().getFirstLastName();
                if (d.getRegistrationUser().getSecondLastName() != null) {
                    apellidos += (" " + d.getRegistrationUser().getSecondLastName());
                }
                detailDto.setUsuarioRegistroApellidos(apellidos);
            }
            if (d.getRegistrationDate() != null) {
                detailDto.setFechaRegistro(d.getRegistrationDate().plusHours(zonaHorariaGmt));
            }
            if (d.getModificationUser() != null) {
                detailDto.setUsuarioModificacionId(d.getModificationUser().getId());
                detailDto.setUsuarioModificacionNombres(d.getModificationUser().getNames());
                String apellidos2 = d.getModificationUser().getFirstLastName();
                if (d.getModificationUser().getSecondLastName() != null) {
                    apellidos2 += (" " + d.getModificationUser().getSecondLastName());
                }
                detailDto.setUsuarioModificacionApellidos(apellidos2);
            }
            if (d.getModificationDate() != null) {
                detailDto.setFechaModificacion(d.getModificationDate().plusHours(zonaHorariaGmt));
            }

            detailOutputs.add(detailDto);
        }
        logger.info("cargoDocumentObtain finished successfully for documentoCargaId={}", documentoCargaId);
        return CargoDocumentObtainOutput.builder()
                .items(List.of(output))
                .detalles(detailOutputs)
                .build();
    }

    public class EntityNotFoundException extends RuntimeException {
        public EntityNotFoundException(String message) {
            super(message);
        }
    }
}