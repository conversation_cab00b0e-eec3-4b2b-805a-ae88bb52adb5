package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.VesselProgrammingDetailRepository;
import com.maersk.sd1.sds.dto.Detalle;
import com.maersk.sd1.sds.dto.VesselProgrammingDetailExportOutput;
import com.maersk.sd1.sds.exception.VesselProgrammingDetailExportException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class VesselProgrammingDetailExportService {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingDetailExportService.class);

    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Autowired
    public VesselProgrammingDetailExportService(VesselProgrammingDetailRepository vesselProgrammingDetailRepository) {
        this.vesselProgrammingDetailRepository = vesselProgrammingDetailRepository;
    }

    @Transactional(readOnly = true)
    public VesselProgrammingDetailExportOutput exportDetails(Integer unidadNegocioId) {
        VesselProgrammingDetailExportOutput output = new VesselProgrammingDetailExportOutput();
        try {
            Long count = vesselProgrammingDetailRepository.countExportDetailsByBusinessUnitId(unidadNegocioId);
            output.setTotalRegistros(count);

            List<Object[]> resultList = vesselProgrammingDetailRepository.findExportDetailsByBusinessUnitId(unidadNegocioId);
            List<Detalle> detalles = new ArrayList<>();
            for (Object[] row : resultList) {
                Detalle detalle = new Detalle();
                detalle.setProgramacionNaveDetalleId((Integer) row[0]);
                detalle.setNaveViaje((String) row[1]);
                detalle.setOperacion((String) row[2]);
                detalles.add(detalle);
            }
            output.setDetalles(detalles);
        } catch (Exception e) {
            logger.error("Error in exportDetails: ", e);
            throw new VesselProgrammingDetailExportException("Error exporting vessel programming details.", e);
        }
        return output;
    }
}

