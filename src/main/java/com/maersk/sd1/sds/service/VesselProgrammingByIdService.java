package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.VesselProgrammingByIdOutput;
import com.maersk.sd1.sds.repository.VesselProgrammingByIdRepository;
import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.Vessel;
import com.maersk.sd1.common.model.VesselProgramming;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class VesselProgrammingByIdService {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingByIdService.class);

    private final VesselProgrammingByIdRepository vesselProgrammingByIdRepository;

    public VesselProgrammingByIdService(VesselProgrammingByIdRepository vesselProgrammingByIdRepository) {
        this.vesselProgrammingByIdRepository = vesselProgrammingByIdRepository;
    }

    @Transactional(readOnly = true)
    public List<VesselProgrammingByIdOutput> getVesselProgrammingById(Integer vesselProgrammingId) {
        List<VesselProgrammingByIdOutput> outputList = new ArrayList<>();

        try {
            VesselProgramming vp = vesselProgrammingByIdRepository.findById(vesselProgrammingId)
                    .orElseThrow(() -> new IllegalArgumentException("No VesselProgramming found for the given ID."));
            Vessel vessel = vp.getVessel();
            Company shippingAgency = vp.getShippingAgencyCompany();
            Company opePortCompany = vp.getOpePortCompany();
            VesselProgrammingByIdOutput output = new VesselProgrammingByIdOutput();
            output.setVesselProgrammingId(vp.getId());
            output.setVesselId(vessel != null ? vessel.getId() : null);
            output.setVesselName(vessel != null ? vessel.getName() : null);
            output.setVoyage(vp.getVoyage());

            output.setEtaDate((vp.getEtaDate() != null) ? vp.getEtaDate().toLocalDate() : null);
            output.setEtdDate((vp.getEtdDate() != null) ? vp.getEtdDate().toLocalDate() : null);

            if (shippingAgency != null) {
                output.setShippingAgencyCompanyId(shippingAgency.getId());
                output.setShippingAgencyLegalName(shippingAgency.getLegalName());
            } else {
                output.setShippingAgencyCompanyId(null);
                output.setShippingAgencyLegalName(null);
            }

            if (opePortCompany != null) {
                output.setOpePortCompanyId(opePortCompany.getId());
                output.setOpePortLegalName(opePortCompany.getLegalName());
            } else {
                output.setOpePortCompanyId(null);
                output.setOpePortLegalName(null);
            }

            output.setActive(vp.getActive());

            outputList.add(output);

            return outputList;
        } catch (Exception e) {
            logger.error("Error in getVesselProgrammingById: ", e);
            throw e;
        }
    }
}