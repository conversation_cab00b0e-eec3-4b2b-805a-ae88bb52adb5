package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.BlManualSearchInputDTO;
import com.maersk.sd1.sds.controller.dto.BlManualSearchOutputDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class BlManualSearchService {

    private final CargoDocumentRepository cargoDocumentRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final PortRepository portRepository;
    private final VesselRepository vesselRepository;
    private final CatalogRepository catalogRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final VesselProgrammingRepository vesselProgrammingRepository;
    private final VesselProgrammingPortRepository vesselProgrammingPortRepository;
    private final DepotRepository depotRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final CompanyRepository companyRepository;

    @Transactional
    public List<BlManualSearchOutputDTO> searchManualBl(BlManualSearchInputDTO.Root input) {

        Integer businessUnitId = input.getInput().getBusinessUnitId();
        Integer subBusinessUnitId = input.getInput().getSubBusinessUnitId();
        String blNumber = input.getInput().getBlNumber();
        Integer languageId = input.getInput().getLanguageId();

        Integer cargoDocumentId = null;
        Integer shippingLineId = null;
        Integer vesselProgrammingDetailId = null;
        Integer vesselProgrammingId;
        String vesselName = null;
        String voyage = null;
        String operation = null;
        Integer loadingPortId = null;
        Integer dischargePortId = null;
        Integer shipperCompanyId = null;
        Integer consigneeCompanyId = null;
        String shipperDetail = null;
        String consigneeDetail = null;
        Integer emptyDepotId = null;
        Integer moveTypeId = null;
        PageRequest pageable = PageRequest.of(0, 1);
        List<CargoDocument> cargoDocuments = cargoDocumentRepository.findTopCargoDocumentForManualSearch(
                blNumber, subBusinessUnitId, pageable);

        if (CollectionUtils.isEmpty(cargoDocuments) && Objects.equals(businessUnitId, 4)) {
            String subBusinessUnitName = getSubBusinessUnitName(subBusinessUnitId);
            String dummyPort = "BOZZZ";
            String dummyPortName = "DUMMY BOLIVIA";

            List<Port> ports = portRepository.findPortByPortCode(dummyPort);
            Port portEntity;
            portEntity = createOrUpdatePort(ports, dummyPort, dummyPortName);
            shippingLineId = 4104;
            vesselName = subBusinessUnitName;
            List<Vessel> vessels = vesselRepository.findVesselsByShip(vesselName);
            Vessel vesselEntity;
            vesselEntity = createOrUpdateVessel(ports, vessels, vesselName);

            voyage = String.valueOf(LocalDate.now().getYear());
            int catOperationId = 42994;
            operation = getOperation(catOperationId, languageId);
            List<VesselProgrammingDetail> vesselProgrammingDetails = vesselProgrammingDetailRepository.findLatestByBusinessUnitVesselVoyageOperation(subBusinessUnitId,
                    vesselEntity.getId(), voyage, catOperationId, pageable);
            if (!CollectionUtils.isEmpty(vesselProgrammingDetails)) {
                VesselProgrammingDetail vesselProgrammingDetail = vesselProgrammingDetails.getFirst();
                vesselProgrammingDetailId = vesselProgrammingDetail.getId();
                vesselProgrammingId = vesselProgrammingDetail.getVesselProgramming().getId();
                vesselProgrammingDetailRepository.updateActiveStatus(vesselProgrammingDetailId, 1);
                vesselProgrammingRepository.updateActiveStatus(vesselProgrammingId, 1);
            } else {
                List<VesselProgramming> vesselProgrammings = vesselProgrammingRepository.findTopBySubBusinessUnitIdAndVesselId(subBusinessUnitId, vesselEntity.getId(),
                        voyage, pageable);
                vesselProgrammingId = createOrUpdateVesselProgramming(vesselProgrammings, businessUnitId, subBusinessUnitId, vesselEntity, voyage);
                VesselProgrammingDetail vesselProgrammingDetail = createVesselProgrammingDetail(vesselProgrammingId, catOperationId);
                vesselProgrammingDetailId = vesselProgrammingDetail.getId();
            }

            List<VesselProgrammingPort> vesselProgrammingPort = vesselProgrammingPortRepository.findVesselProgrammingPort(vesselProgrammingId, portEntity.getId());
            createOrUpdateVesselProgrammingPort(vesselProgrammingPort, vesselProgrammingId, portEntity);

            loadingPortId = portEntity.getId();
            dischargePortId = portEntity.getId();
            shipperDetail = "";
            consigneeDetail = "";
            List<Depot> depots = depotRepository.findTopBySubBusinessUnit(subBusinessUnitId, pageable);
            if (CollectionUtils.isEmpty(depots)) {
                List<Depot> depotByBusinessUnit = depotRepository.findTopByBusinessUnit(businessUnitId, pageable);
                if (!CollectionUtils.isEmpty(depotByBusinessUnit)) {
                    emptyDepotId = depotByBusinessUnit.getFirst().getId();
                }
            } else {
                emptyDepotId = depots.getFirst().getId();
            }

        } else {
            if (!CollectionUtils.isEmpty(cargoDocuments)) {
                CargoDocument cargoDocument = cargoDocuments.getFirst();
                cargoDocumentId = cargoDocument.getId();
                shippingLineId = Optional.ofNullable(cargoDocument.getShippingLine())
                        .map(ShippingLine::getId)
                        .orElse(null);
                vesselProgrammingDetailId = Optional.ofNullable(cargoDocument.getVesselProgrammingDetail())
                        .map(VesselProgrammingDetail::getId)
                        .orElse(null);
                vesselName = Optional.ofNullable(cargoDocument.getVesselProgrammingDetail())
                        .map(VesselProgrammingDetail::getVesselProgramming)
                        .map(VesselProgramming::getVessel)
                        .map(Vessel::getName)
                        .orElse(null);
                voyage = Optional.ofNullable(cargoDocument.getVesselProgrammingDetail())
                        .map(VesselProgrammingDetail::getVesselProgramming)
                        .map(VesselProgramming::getVoyage)
                        .orElse(null);
                String longDescription = Optional.ofNullable(cargoDocument.getVesselProgrammingDetail())
                        .map(VesselProgrammingDetail::getCatOperation)
                        .map(Catalog::getId)
                        .map(id -> catalogLanguageRepository.fnCatalogoTraducidoDesLarga(id, languageId))
                        .orElse(null);
                operation = Optional.ofNullable(cargoDocument.getVesselProgrammingDetail())
                        .map(VesselProgrammingDetail::getCatOperation)
                        .map(Catalog::getDescription)
                        .orElse(null) + " - " + longDescription;
                loadingPortId = Optional.ofNullable(cargoDocument.getLoadingPort())
                        .map(Port::getId)
                        .orElse(null);
                dischargePortId = Optional.ofNullable(cargoDocument.getDischargePort())
                        .map(Port::getId)
                        .orElse(null);
                shipperCompanyId = Optional.ofNullable(cargoDocument.getShipperCompany())
                        .map(Company::getId)
                        .orElse(null);
                consigneeCompanyId = Optional.ofNullable(cargoDocument.getConsigneeCompany())
                        .map(Company::getId)
                        .orElse(null);
                shipperDetail = cargoDocument.getShipperDetail();
                consigneeDetail = cargoDocument.getConsigneeDetail();
                emptyDepotId = Optional.ofNullable(cargoDocument.getEmptyDepot())
                        .map(Depot::getId)
                        .orElse(null);
                moveTypeId = Optional.ofNullable(cargoDocument.getCatMoveType())
                        .map(Catalog::getId)
                        .orElse(null);
            }

        }
        return List.of(BlManualSearchOutputDTO.builder()
                .cargoDocumentId(cargoDocumentId)
                .shippingLineId(shippingLineId)
                .vesselProgrammingDetailId(vesselProgrammingDetailId)
                .vesselName(vesselName)
                .voyage(voyage)
                .operation(operation)
                .loadingPortId(loadingPortId)
                .dischargePortId(dischargePortId)
                .shipperCompanyId(shipperCompanyId)
                .consigneeCompanyId(consigneeCompanyId)
                .shipperDetail(shipperDetail)
                .consigneeDetail(consigneeDetail)
                .emptyDepotId(emptyDepotId)
                .shippingLineName(getFormattedShippingLine(shippingLineId))
                .loadingPortName(getFormattedPort(loadingPortId))
                .dischargePortName(getFormattedPort(dischargePortId))
                .shipperCompanyName(getCompanyNameWithDocument(shipperCompanyId))
                .consigneeCompanyName(getCompanyNameWithDocument(consigneeCompanyId))
                .emptyDepotName(getDepotDetails(emptyDepotId))
                .moveTypeId(moveTypeId)
                .build());
    }

    private Integer createOrUpdateVesselProgramming(List<VesselProgramming> vesselProgrammings, Integer businessUnitId, Integer subBusinessUnitId, Vessel vesselEntity, String voyage) {
        Integer vesselProgrammingId;
        if (!CollectionUtils.isEmpty(vesselProgrammings)) {
            vesselProgrammingId = vesselProgrammings.getFirst().getId();
            vesselProgrammingRepository.updateActiveStatus(vesselProgrammingId, 1);
        } else {
            VesselProgramming vesselProgramming = createVesselProgramming(businessUnitId, subBusinessUnitId, vesselEntity.getId(), voyage);
            vesselProgrammingId = vesselProgramming.getId();
        }
        return vesselProgrammingId;
    }

    private void createOrUpdateVesselProgrammingPort(List<VesselProgrammingPort> vesselProgrammingPort, Integer vesselProgrammingId, Port portEntity) {
        if (CollectionUtils.isEmpty(vesselProgrammingPort)) {
            createVesselProgrammingPort(vesselProgrammingId, portEntity.getId());
        } else {
            vesselProgrammingPortRepository.activateVesselProgrammingPort(vesselProgrammingId, portEntity.getId());
        }
    }

    private String getOperation(int catOperationId, Integer languageId) {
        String operation;
        String description = catalogRepository.findShortDescriptionById(catOperationId);
        String longDescription = catalogLanguageRepository.fnCatalogoTraducidoDesLarga(catOperationId, languageId);
        operation = description + " - " + longDescription;
        return operation;
    }

    private Port createOrUpdatePort(List<Port> ports, String dummyPort, String dummyPortName) {
        Port portEntity;
        if (!CollectionUtils.isEmpty(ports)) {
            portEntity = ports.getFirst();
            portRepository.activatePortById(portEntity.getId());
        } else {
            portEntity = createPort(dummyPort, dummyPortName);
        }
        return portEntity;
    }

    private String getSubBusinessUnitName(Integer subBusinessUnitId) {
        return Optional.ofNullable(businessUnitRepository.findNameByBusinessUnit(subBusinessUnitId))
                .map(String::toUpperCase)
                .orElse(null);
    }

    private Vessel createOrUpdateVessel(List<Port> ports, List<Vessel> vessels, String vesselName) {
        Vessel vesselEntity;
        if (!CollectionUtils.isEmpty(ports)) {
            vesselEntity = vessels.getFirst();
            vesselRepository.activateVesselById(vesselEntity.getId());
        } else {
            vesselEntity = createVessel(vesselName);
        }
        return vesselEntity;
    }

    private Port createPort(String dummyPort, String dummyPortName) {
        Port port = Port.builder()
                .port(dummyPort)
                .name(dummyPortName)
                .country(Country.builder().id(50).build())
                .active(true)
                .registrationUser(User.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .build();
        return portRepository.save(port);
    }

    private Vessel createVessel(String vesselName) {
        String cleanedVesselName = vesselName.replace(" ", "");

        Vessel vessel = Vessel.builder()
                .ship(cleanedVesselName)
                .name(vesselName)
                .active(true)
                .registrationUser(User.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .build();

        return vesselRepository.save(vessel);
    }

    private VesselProgramming createVesselProgramming(
            Integer businessUnitId, Integer subBusinessUnitId, Integer vesselId, String voyage) {

        VesselProgramming vesselProgramming = VesselProgramming.builder()
                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                .vessel(Vessel.builder().id(vesselId).build())
                .voyage(voyage)
                .etaDate(LocalDateTime.now())
                .etdDate(LocalDateTime.now())
                .active(true)
                .registrationUser(User.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .catCreationOrigin(Catalog.builder().id(48243).build())
                .build();

        return vesselProgrammingRepository.save(vesselProgramming);
    }

    private VesselProgrammingDetail createVesselProgrammingDetail(
            Integer vesselProgrammingId, Integer operationId) {

        VesselProgrammingDetail detail = VesselProgrammingDetail.builder()
                .vesselProgramming(VesselProgramming.builder().id(vesselProgrammingId).build())
                .catOperation(Catalog.builder().id(operationId).build())
                .manifestYear(String.valueOf(LocalDateTime.now().getYear()))
                .manifestNumber("1")
                .beginningOperation(LocalDateTime.now())
                .endingOperation(LocalDateTime.now())
                .active(true)
                .registrationUser(User.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .catCreationOrigin(Catalog.builder().id(48243).build())
                .build();

        return vesselProgrammingDetailRepository.save(detail);
    }

    private void createVesselProgrammingPort(Integer vesselProgrammingId, Integer portId) {
        VesselProgrammingPort newPort = VesselProgrammingPort.builder()
                .vesselProgramming(VesselProgramming.builder().id(vesselProgrammingId).build())
                .port(Port.builder().id(portId).build())
                .active(true)
                .registrationUser(User.builder().id(1).build())
                .registrationDate(LocalDateTime.now())
                .build();

        vesselProgrammingPortRepository.save(newPort);
    }

    private String getFormattedShippingLine(Integer shippingLineId) {
        return shippingLineRepository.findFormattedShippingLine(shippingLineId);
    }

    private String getFormattedPort(Integer portId) {
        return portRepository.findFormattedPort(portId);
    }

    private String getCompanyNameWithDocument(Integer companyId) {
        return companyRepository.findCompanyNameWithDocument(companyId);
    }

    private String getDepotDetails(Integer depositId) {
        return depotRepository.findDepotDetails(depositId);
    }
}
