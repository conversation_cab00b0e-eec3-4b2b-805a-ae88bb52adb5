package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Product;
import com.maersk.sd1.sds.dto.ProductListInput;
import com.maersk.sd1.sds.dto.ProductListOutput;
import com.maersk.sd1.sds.dto.ProductListOutput.ProductDetailOutput;
import com.maersk.sd1.sds.repository.ProductListRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProductListService {

    private static final Logger logger = LogManager.getLogger(ProductListService.class);

    private final ProductListRepository productListRepository;

    @Autowired
    public ProductListService(ProductListRepository productListRepository) {
        this.productListRepository = productListRepository;
    }

    @Transactional(readOnly = true)
    public ProductListOutput listProducts(ProductListInput.Input input) {
        ProductListOutput output = new ProductListOutput();

        try {
            int pageNum = (input.getPage() == null) ? 1 : input.getPage();
            int sizeNum = (input.getSize() == null) ? Integer.MAX_VALUE : input.getSize();

            Pageable pageable = PageRequest.of(
                    Math.max(pageNum - 1, 0),
                    sizeNum,
                    Sort.by(Sort.Direction.DESC, "id")
            );

            Specification<Product> spec = buildSpecification(input);

            Page<Product> pageData = productListRepository.findAll(spec, pageable);

            long totalCount = pageData.getTotalElements();
            output.setTotalRecords(List.of(List.of(totalCount)));

            List<ProductDetailOutput> productDtoList = new ArrayList<>();
            for (Product product : pageData) {
                ProductDetailOutput detail = new ProductDetailOutput();
                detail.setProductId(product.getId());
                if (product.getBusinessUnit() != null) {
                    detail.setBusinessUnitId(Long.valueOf(product.getBusinessUnit().getId()));
                }
                detail.setProductCode(product.getProductCode());
                detail.setProductName(product.getProductName());
                if (product.getCatProductGroup() != null) {
                    detail.setProductGroupId(Long.valueOf(product.getCatProductGroup().getId()));
                }
                if (product.getCatWeightMeasureUnit() != null) {
                    detail.setWeightMeasureUnitId(Long.valueOf(product.getCatWeightMeasureUnit().getId()));
                }
                if (product.getCatPackaging() != null) {
                    detail.setPackagingId(Long.valueOf(product.getCatPackaging().getId()));
                }
                detail.setActive(product.getActive());

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                detail.setRegistrationDateLocal(formatDateTime(product.getRegistrationDate(), formatter));
                detail.setModificationDateLocal(formatDateTime(product.getModificationDate(), formatter));

                detail.setBookingEdiCommodity1(product.getBookingEdiCommodity1());
                detail.setBookingEdiCommodity2(product.getBookingEdiCommodity2());

                if (product.getCatRefrigerationType() != null) {
                    detail.setRefrigerationTypeId(Long.valueOf(product.getCatRefrigerationType().getId()));
                }

                if (product.getRegistrationUser() != null) {
                    detail.setUserRegistrationId(product.getRegistrationUser().getId());
                    detail.setUserRegistrationFirstName(product.getRegistrationUser().getNames());
                    detail.setUserRegistrationLastName(
                            (product.getRegistrationUser().getFirstLastName() == null ? "" : product.getRegistrationUser().getFirstLastName()) +
                                    " " +
                                    (product.getRegistrationUser().getSecondLastName() == null ? "" : product.getRegistrationUser().getSecondLastName())
                    );
                }

                if (product.getModificationUser() != null) {
                    detail.setUserModificationId(product.getModificationUser().getId());
                    detail.setUserModificationFirstName(product.getModificationUser().getNames());
                    detail.setUserModificationLastName(
                            (product.getModificationUser().getFirstLastName() == null ? "" : product.getModificationUser().getFirstLastName()) +
                                    " " +
                                    (product.getModificationUser().getSecondLastName() == null ? "" : product.getModificationUser().getSecondLastName())
                    );
                }

                productDtoList.add(detail);
            }

            output.setProducts(productDtoList);
        } catch (Exception e) {
            logger.error("Error listing products.", e);
        }

        return output;
    }

    public Specification<Product> buildSpecification(ProductListInput.Input input) {
        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

            if (input.getProductId() != null) {
                predicates.add(cb.equal(root.get("id"), input.getProductId()));
            }

            if (input.getBusinessUnitId() != null) {
                predicates.add(cb.equal(root.get("businessUnit").get("id"), input.getBusinessUnitId()));
            }

            if (input.getProductCode() != null && !input.getProductCode().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("productCode")), "%" + input.getProductCode().toLowerCase() + "%"));
            }

            if (input.getProductName() != null && !input.getProductName().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("productName")), "%" + input.getProductName().toLowerCase() + "%"));
            }

            if (input.getProductGroupId() != null) {
                predicates.add(cb.equal(root.get("productGroup").get("id"), input.getProductGroupId()));
            }

            if (input.getWeightMeasureUnitId() != null) {
                predicates.add(cb.equal(root.get("weightMeasureUnit").get("id"), input.getWeightMeasureUnitId()));
            }

            if (input.getPackagingId() != null) {
                predicates.add(cb.equal(root.get("packaging").get("id"), input.getPackagingId()));
            }

            if (input.getActive() != null) {
                predicates.add(cb.equal(root.get("active"), input.getActive()));
            }

            LocalDate regMin = input.getRegistrationDateMin();
            LocalDate regMax = input.getRegistrationDateMax();
            if (regMin != null && regMax != null) {
                LocalDateTime start = regMin.atStartOfDay();
                LocalDateTime end = regMax.plusDays(1).atStartOfDay();
                predicates.add(cb.between(root.get("registrationDate"), start, end));
            }

            LocalDate modMin = input.getModificationDateMin();
            LocalDate modMax = input.getModificationDateMax();
            if (modMin != null && modMax != null) {
                LocalDateTime start = modMin.atStartOfDay();
                LocalDateTime end = modMax.plusDays(1).atStartOfDay();
                predicates.add(cb.between(root.get("modificationDate"), start, end));
            }

            if (input.getBookingEdiCommodity1() != null && !input.getBookingEdiCommodity1().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("bookingEdiCommodity1")), "%" + input.getBookingEdiCommodity1().toLowerCase() + "%"));
            }

            if (input.getBookingEdiCommodity2() != null && !input.getBookingEdiCommodity2().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("bookingEdiCommodity2")), "%" + input.getBookingEdiCommodity2().toLowerCase() + "%"));
            }

            if (input.getRefrigerationTypeId() != null) {
                predicates.add(cb.equal(root.get("refrigerationType").get("id"), input.getRefrigerationTypeId()));
            }

            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
        };
    }

    private String formatDateTime(LocalDateTime dateTime, DateTimeFormatter formatter) {
        return (dateTime == null) ? null : dateTime.format(formatter);
    }
}
