package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.sds.dto.BookingGetInputDTO;
import com.maersk.sd1.sds.dto.BookingGetOutputDTO;
import com.maersk.sd1.sds.dto.BookingGetOutputDetailsDTO;
import com.maersk.sd1.sds.dto.BookingGetOutputRecordsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class BookingGetService {
    private final BookingRepository bookingRepository;
    private final BookingDetailRepository bookingDetailRepository;

    @Autowired
    public BookingGetService(BookingRepository bookingRepository, BookingDetailRepository bookingDetailRepository) {
        this.bookingRepository = bookingRepository;
        this.bookingDetailRepository = bookingDetailRepository;
    }

    public ResponseEntity<ResponseController<BookingGetOutputDTO>> bookingGetService(BookingGetInputDTO.Root request) {
        // Initialize output DTO and lists
        BookingGetOutputDTO output = new BookingGetOutputDTO();
        List<BookingGetOutputRecordsDTO> bookings = new ArrayList<>();
        List<BookingGetOutputDetailsDTO> bookingDetails = new ArrayList<>();

        try {
            // Extracting input values
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                throw new IllegalArgumentException("Invalid input request");
            }
            BookingGetInputDTO.Input input = request.getPrefix().getInput();
            Integer bookingId = input.getBookingId();
            Integer languageId = input.getLanguageId();

            // Ensure records is initialized as empty list to avoid NPE
            List<Object[]> records = bookingRepository.getBookingById(bookingId, languageId);
            if (records != null) {
                for (Object[] record : records) {
                    BookingGetOutputRecordsDTO bookingDTO = new BookingGetOutputRecordsDTO();

                    bookingDTO.setBookingId(record[0] != null ? (Integer) record[0] : null);
                    bookingDTO.setUnidadNegocioId(record[1] != null ? (BigDecimal) record[1] : BigDecimal.ZERO);
                    bookingDTO.setNumeroBooking(record[2] != null ? (String) record[2] : "");
                    bookingDTO.setFechaEmisionBooking(record[3] != null ? (Timestamp) record[3] : null);
                    bookingDTO.setPuertoEmbarqueId(record[4] != null ? (Integer) record[4] : null);
                    bookingDTO.setPuertoDescargaId(record[5] != null ? (Integer) record[5] : null);
                    bookingDTO.setPuertoDestinoId(record[6] != null ? (Integer) record[6] : null);
                    bookingDTO.setLineaNavieraId(record[7] != null ? (Integer) record[7] : null);
                    bookingDTO.setEmpresaClienteId(record[8] != null ? (BigDecimal) record[8] : BigDecimal.ZERO);
                    bookingDTO.setEmpresaEmbarcadorId(record[9] != null ? (BigDecimal) record[9] : BigDecimal.ZERO);
                    bookingDTO.setDepositoVacioId(record[10] != null ? (Integer) record[10] : null);
                    bookingDTO.setDepositoFullId(record[11] != null ? (Integer) record[11] : null);
                    bookingDTO.setProductoId(record[12] != null ? (Integer) record[12] : null);
                    bookingDTO.setTemperaturaC(record[13] != null ? (String) record[13] : "");
                    bookingDTO.setImoId(record[14] != null ? (Integer) record[14] : null);
                    bookingDTO.setBookingAprobado(Optional.ofNullable(record[15]).map(Boolean.class::cast).orElse(false));
                    bookingDTO.setBookingAprobadoFecha(record[16] != null ? (Timestamp) record[16] : null);
                    bookingDTO.setUsuarioAprobadoId(record[17] != null ? (BigDecimal) record[17] : BigDecimal.ZERO);
                    bookingDTO.setMercaderia(record[18] != null ? (String) record[18] : "");
                    bookingDTO.setCatEstadoBooking(record[19] != null ? (BigDecimal) record[19] : BigDecimal.ZERO);
                    bookingDTO.setEsColdtreatment(Optional.ofNullable(record[20]).map(Boolean.class::cast).orElse(false));
                    bookingDTO.setEsAtmosferaControlada(Optional.ofNullable(record[21]).map(Boolean.class::cast).orElse(false));
                    bookingDTO.setCatReferenciaCoparnId(record[22] != null ? (BigDecimal) record[22] : BigDecimal.ZERO);
                    bookingDTO.setFechaReferenciaCoparn(record[23] != null ? (Timestamp) record[23] : null);
                    bookingDTO.setCatOrigenCreacionBookingId(record[24] != null ? (BigDecimal) record[24] : BigDecimal.ZERO);
                    bookingDTO.setActivo(Optional.ofNullable(record[25]).map(Boolean.class::cast).orElse(false));
                    bookingDTO.setFechaRegistro(record[26] != null ? (Timestamp) record[26] : null);
                    bookingDTO.setFechaModificacion(record[27] != null ? (Timestamp) record[27] : null);
                    bookingDTO.setSubUnidadNegocioId(record[28] != null ? (BigDecimal) record[28] : BigDecimal.ZERO);
                    bookingDTO.setProgramacionNaveDetalleId(record[29] != null ? (Integer) record[29] : null);
                    bookingDTO.setNombreNave(record[30] != null ? (String) record[30] : "");
                    bookingDTO.setViaje(record[31] != null ? (String) record[31] : "");
                    bookingDTO.setOperacion(record[32] != null ? (String) record[32] : "");
                    bookingDTO.setManifiesto(record[33] != null ? (String) record[33] : "");
                    bookingDTO.setFechaEta(record[34] != null ? (String) record[34] : "");
                    bookingDTO.setFechaEtd(record[35] != null ? (String) record[35] : "");
                    bookingDTO.setShippingLineName(record[36] != null ? (String) record[36] : "");
                    bookingDTO.setPortLoadingName(record[37] != null ? (String) record[37] : "");
                    bookingDTO.setPortDischargeName(record[38] != null ? (String) record[38] : "");
                    bookingDTO.setPortDestinationName(record[39] != null ? (String) record[39] : "");
                    bookingDTO.setCompanyClientName(record[40] != null ? (String) record[40] : "");
                    bookingDTO.setCompanyShipperName(record[41] != null ? (String) record[41] : "");
                    bookingDTO.setProductName(record[42] != null ? (String) record[42] : "");
                    bookingDTO.setDepositEmptyName(record[43] != null ? (String) record[43] : "");
                    bookingDTO.setDepositFullName(record[44] != null ? (String) record[44] : "");
                    bookingDTO.setImoName(record[45] != null ? (String) record[45] : "");
                    bookingDTO.setCatMoveTypeId(record[46] != null ? (BigDecimal) record[46] : BigDecimal.ZERO);
                    bookingDTO.setMaerskDepotWithSd1(Optional.ofNullable(record[47]).map(Boolean.class::cast).orElse(false));
                    bookingDTO.setOriginDestinationDepotId(record[48] != null ? (Integer) record[48] : null);
                    bookingDTO.setOriginDestinationDepot(record[49] != null ? (String) record[49] : "");
                    bookingDTO.setCatMoveTypeAlias(record[50] != null ? (String) record[50] : "");

                    // Add the mapped DTO to the list
                    bookings.add(bookingDTO);
                }
            }

            // Ensure details is initialized as empty list
            List<Object[]> details = bookingDetailRepository.findBookingDetailByBookingId(bookingId);
            if (details != null) {
                for (Object[] detail : details) {
                    BookingGetOutputDetailsDTO bookingDetailDTO = new BookingGetOutputDetailsDTO();

                    bookingDetailDTO.setBookingDetailId(detail[0] != null ? (Integer) detail[0] : null);
                    bookingDetailDTO.setBookingId(detail[1] != null ? (Integer) detail[1] : null);
                    bookingDetailDTO.setCatTamanoId(detail[2] != null ? (BigDecimal) detail[2] : BigDecimal.ZERO);
                    bookingDetailDTO.setCatTamanoDesc(detail[3] != null ? (String) detail[3] : "");
                    bookingDetailDTO.setCatTipoContenedorId(detail[4] != null ? (BigDecimal) detail[4] : BigDecimal.ZERO);
                    bookingDetailDTO.setCatTipoContenedorDesc(detail[5] != null ? (String) detail[5] : "");
                    bookingDetailDTO.setCantidadReserva(detail[6] != null ? (BigDecimal) detail[6] : BigDecimal.ZERO);
                    bookingDetailDTO.setCantidadAtendida(detail[7] != null ? (BigDecimal) detail[7] : BigDecimal.ZERO);
                    bookingDetailDTO.setCargaMaximaRequerido(detail[8] != null ? (BigDecimal) detail[8] : BigDecimal.ZERO);
                    bookingDetailDTO.setCatOrigenCreacionBookingId(detail[9] != null ? (BigDecimal) detail[9] : BigDecimal.ZERO);
                    bookingDetailDTO.setActivo(Optional.ofNullable(detail[10]).map(Boolean.class::cast).orElse(false));
                    bookingDetailDTO.setUsuarioRegistroId(detail[11] != null ? (BigDecimal) detail[11] : BigDecimal.ZERO);
                    bookingDetailDTO.setUsuarioRegistroNombre(detail[12] != null ? (String) detail[12] : "");
                    bookingDetailDTO.setUsuarioRegistroApellidos(detail[13] != null ? (String) detail[13] : "");
                    bookingDetailDTO.setFechaRegistro(detail[14] != null ? (Timestamp) detail[14] : null);
                    bookingDetailDTO.setUsuarioModificacionId(detail[15] != null ? (BigDecimal) detail[15] : BigDecimal.ZERO);
                    bookingDetailDTO.setUsuarioModificacionNombre(detail[16] != null ? (String) detail[16] : "");
                    bookingDetailDTO.setUsuarioModificacionApellidos(detail[17] != null ? (String) detail[17] : "");
                    bookingDetailDTO.setFechaModificacion(detail[18] != null ? (Timestamp) detail[18] : null);

                    // Add the mapped DTO to the list
                    bookingDetails.add(bookingDetailDTO);
                }
            }

            // Set the bookings and details to the output DTO
            output.setBookings(bookings);
            output.setDetails(bookingDetails);

            // Return success response
            return ResponseEntity.ok(new ResponseController<>(output));

        } catch (Exception e) {
            log.error("e: ", e);
            return ResponseEntity.ok(new ResponseController<>(output)); // Return empty output on error
        }
    }
}