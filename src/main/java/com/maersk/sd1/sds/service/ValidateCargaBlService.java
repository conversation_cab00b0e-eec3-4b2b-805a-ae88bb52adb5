package com.maersk.sd1.sds.service;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.CompanyRepository;
import com.maersk.sd1.common.repository.PortRepository;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.sds.dto.ValidateCargaBlOutputDTO;
import com.maersk.sd1.sds.dto.ValidateCargaBlInput;
import com.maersk.sd1.sds.dto.ValidateCargaBlValues;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class ValidateCargaBlService {
    private final PortRepository portRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final CompanyRepository companyRepository;
    private final CatalogRepository catalogRepository;

    @Autowired
    public ValidateCargaBlService(PortRepository portRepository, ShippingLineRepository shippingLineRepository,
                                  CompanyRepository companyRepository, CatalogRepository catalogRepository) {
        this.portRepository = portRepository;
        this.shippingLineRepository = shippingLineRepository;
        this.companyRepository = companyRepository;
        this.catalogRepository = catalogRepository;
    }
    @Transactional
    public ResponseEntity<ResponseController<List<ValidateCargaBlOutputDTO>>> ruleGeneralGetService(ValidateCargaBlInput.Root request)
    {
        List<ValidateCargaBlOutputDTO> output = new ArrayList<>();
        try{
            ObjectMapper objectMapper = new ObjectMapper();
            ValidateCargaBlInput.Prefix prefix = request.getPrefix();
            ValidateCargaBlInput.Input input = prefix.getInput();

            List<ValidateCargaBlValues> documentos = objectMapper.readValue(input.getDocuments(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ValidateCargaBlValues.class));
            List<ValidateCargaBlOutputDTO> errorList = new ArrayList<>();
            for (ValidateCargaBlValues documento : documentos) {
                if (
                        isPoInvalid(documento) ||
                                isPdInvalid(documento) ||
                                isLineInvalid(documento) ||
                                isRucConsigneeInvalid(documento) ||
                                isRucIdentificationInvalid(documento) ||
                                isClientRoleInvalid(documento) ||
                                isDimensionInvalid(documento) ||
                                isTipoInvalid(documento) ||
                                isCondicionInvalid(documento) ||
                                isDuplicatedContenedor(documento, documentos) ||
                                isBlNull(documento) ||
                                isBlInvalid(documento) ||
                                isLineaNull(documento) ||
                                isContenedorNull(documento) ||
                                isCondicionNull(documento) ||
                                isTaraOrPesoNull(documento) ||
                                isTipoOrDimensionNull(documento) ||
                                isTaraOutOfRange(documento) ||
                                isPesoOutOfRange(documento)
                ){
                    ValidateCargaBlOutputDTO errorDTO = new ValidateCargaBlOutputDTO();
                    errorDTO.setIndex(documento.getIndex());
                    errorDTO.setBl(documento.getBl());
                    errorDTO.setContenedor(documento.getContenedor());
                    errorDTO.setMensajeError("Record with errors: ");

                    errorDTO.setError1(isPoInvalid(documento));
                    errorDTO.setError2(isPdInvalid(documento));
                    errorDTO.setError3(isLineInvalid(documento));
                    errorDTO.setError4(isRucConsigneeInvalid(documento));
                    errorDTO.setError5(isRucIdentificationInvalid(documento));
                    errorDTO.setError6(isClientRoleInvalid(documento));
                    errorDTO.setError7(isDimensionInvalid(documento));
                    errorDTO.setError8(isTipoInvalid(documento));
                    errorDTO.setError9(isCondicionInvalid(documento));
                    errorDTO.setError10(isDuplicatedContenedor(documento, documentos));
                    errorDTO.setError11(isBlNull(documento));
                    errorDTO.setError12(isBlInvalid(documento));
                    errorDTO.setError13(isLineaNull(documento));
                    errorDTO.setError14(isContenedorNull(documento));
                    errorDTO.setError15(isCondicionNull(documento));
                    errorDTO.setError16(isTaraOrPesoNull(documento));
                    errorDTO.setError17(isTipoOrDimensionNull(documento));
                    errorDTO.setError18(isTaraOutOfRange(documento));
                    errorDTO.setError19(isPesoOutOfRange(documento));

                    errorList.add(errorDTO);

                    log.info("Added error DTO for BL: {}", documento.getBl());

                }

            }
            return ResponseEntity.ok(new ResponseController<>(errorList));

        } catch (JsonMappingException e) {
            log.error("Json mapping error occurred: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("Unexpected error occurred: {}", e.getMessage(), e);
            return ResponseEntity.ok(new ResponseController<>(output));
        }
    }
    private Boolean isPoInvalid(ValidateCargaBlValues v) {
        return v.getPo() != null && !portRepository.existsByPort(v.getPo());
    }
    private Boolean isPdInvalid(ValidateCargaBlValues v) {
        return !portRepository.existsByPort(v.getPd());
    }
    private Boolean isLineInvalid(ValidateCargaBlValues v) {
        return v.getLinea()!=null && !shippingLineRepository.existsByShippingLineCompany(v.getLinea());
    }
    private Boolean isRucConsigneeInvalid(ValidateCargaBlValues v) {
        return v.getRucConsignatario()!=null && !companyRepository.existsByDocument(v.getRucConsignatario());
    }
    private Boolean isRucIdentificationInvalid(ValidateCargaBlValues v) {
        return v.getRucEmbarcador()!=null && !companyRepository.existsByDocument(v.getRucEmbarcador());
    }
    private Boolean isClientRoleInvalid(ValidateCargaBlValues v) {
        log.info("Validating client role: {}", v.getRolCliente());
        return v.getRolCliente()!=null && !companyRepository.existsByDocument(v.getRolCliente());
    }
    private Boolean isDimensionInvalid(ValidateCargaBlValues v) {
        return !catalogRepository.existsByDescriptionAndParentCatalogId(v.getDimension(), 165);
    }
    private Boolean isTipoInvalid(ValidateCargaBlValues v) {
        return !catalogRepository.existsByDescriptionAndParentCatalogId(v.getTipo(), 164);
    }
    private Boolean isCondicionInvalid(ValidateCargaBlValues v) {
        return v.getCondicion()!=null && !List.of("FCL", "LCL", "MTY").contains(v.getCondicion());
    }

    private Boolean isDuplicatedContenedor(ValidateCargaBlValues v, List<ValidateCargaBlValues> documentos) {
        return documentos.stream().filter(val -> val.getContenedor().equals(v.getContenedor())).count() > 1;
    }

    private Boolean isBlNull(ValidateCargaBlValues v) {
        return v.getBl() == null;
    }

    private Boolean isBlInvalid(ValidateCargaBlValues v) {
        return v.getBl().length() > 25;
    }

    private Boolean isLineaNull(ValidateCargaBlValues v) {
        return v.getLinea() == null;
    }

    private Boolean isContenedorNull(ValidateCargaBlValues v) {
        return v.getContenedor() == null;
    }

    private Boolean isCondicionNull(ValidateCargaBlValues v) {
        return v.getCondicion() == null;
    }

    private Boolean isTaraOrPesoNull(ValidateCargaBlValues v) {
        return v.getTara() == null || v.getPeso() == null;
    }

    private Boolean isTipoOrDimensionNull(ValidateCargaBlValues v) {
        return v.getTipo() == null || v.getDimension() == null;
    }

    private Boolean isTaraOutOfRange(ValidateCargaBlValues v) {
        return v.getTara() < 1 || v.getTara() > 9999;
    }

    private Boolean isPesoOutOfRange(ValidateCargaBlValues v) {
        return v.getPeso() < 1 || v.getPeso() > 99999999.999;
    }
}



