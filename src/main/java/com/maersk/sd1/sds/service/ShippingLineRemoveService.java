package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.ShippingLineRemoveInput;
import com.maersk.sd1.sds.controller.dto.ShippingLineRemoveOutput;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class ShippingLineRemoveService {

    private static final Logger logger = LogManager.getLogger(ShippingLineRemoveService.class);

    private final ShippingLineRepository shippingLineRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public ShippingLineRemoveService(ShippingLineRepository shippingLineRepository, MessageLanguageRepository messageLanguageRepository) {
        this.shippingLineRepository = shippingLineRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public ShippingLineRemoveOutput removeShippingLine(ShippingLineRemoveInput.Input input) {
        ShippingLineRemoveOutput output = new ShippingLineRemoveOutput();
        try {
            shippingLineRepository.deactivateShippingLine(
                    input.getShippingLineId(),
                    input.getUserModificationId(),
                    LocalDateTime.now()
            );

            output.setRespStatus(1);

            String message = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, input.getLanguageId());
            output.setRespMessage(message);
        } catch (Exception e) {
            logger.error("Error removing shipping line: ", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}