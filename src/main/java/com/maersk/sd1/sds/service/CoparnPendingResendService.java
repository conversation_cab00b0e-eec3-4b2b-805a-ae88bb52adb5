package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.BookingEdiSettingBURepository;
import com.maersk.sd1.sds.dto.CoparnPendingResendOutput;
import com.maersk.sd1.sds.dto.CoparnPendingResendOutput.PendingResend;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class CoparnPendingResendService {

    private static final Logger logger = LogManager.getLogger(CoparnPendingResendService.class);

    private final BookingEdiSettingBURepository bookingEdiSettingBURepository;

    @Autowired
    public CoparnPendingResendService(BookingEdiSettingBURepository bookingEdiSettingBURepository) {
        this.bookingEdiSettingBURepository = bookingEdiSettingBURepository;
    }

    @Transactional(readOnly = true)
    public CoparnPendingResendOutput getPendingResends(Integer seteoEdiCoparnId) {
        CoparnPendingResendOutput output = new CoparnPendingResendOutput();
        try {
            if (seteoEdiCoparnId == null) {
                logger.error("seteoEdiCoparnId is null.");
                output.setRespEstado(0);
                output.setRespMensaje("El ID de Seteo EDI Coparn no puede ser nulo.");
                output.setPendingResendRecords(new ArrayList<>());
                return output;
            }

            if (logger.isInfoEnabled()) {
                logger.info(String.format("Finding pending COPARN resends for seteoEdiCoparnId=%d", seteoEdiCoparnId));
            }
            List<PendingResend> pending = bookingEdiSettingBURepository.findPendingResends(seteoEdiCoparnId);

            output.setRespEstado(1);
            output.setRespMensaje("Consulta exitosa");
            output.setPendingResendRecords(pending);
            return output;
        } catch (Exception ex) {
            logger.error("Error retrieving pending COPARN resends.", ex);
            output.setRespEstado(0);
            output.setRespMensaje(String.format("Ocurrió un error al consultar los pendientes de reenvío: %s", ex.getMessage()));
            output.setPendingResendRecords(new ArrayList<>());
            return output;
        }
    }
}