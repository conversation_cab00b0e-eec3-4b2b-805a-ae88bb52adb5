package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.GetCompaniesProgrammingShipProcessDTO;
import com.maersk.sd1.common.repository.CompanyRoleRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GetCompaniesProgrammingShipService {

    private static final Logger logger = LogManager.getLogger(GetCompaniesProgrammingShipService.class);

    private final CompanyRoleRepository companyRoleRepository;

    public GetCompaniesProgrammingShipService(CompanyRoleRepository companyRoleRepository) {
        this.companyRoleRepository = companyRoleRepository;
    }

    public List<GetCompaniesProgrammingShipProcessDTO> getCompaniesProgramacionNave(Integer businessUnitId) {
        try {
            if (businessUnitId == null) {
                logger.error("Null businessUnitId received.");
                throw new IllegalArgumentException("businessUnitId cannot be null");
            }
            return companyRoleRepository.findCompaniesByBusinessUnitId(businessUnitId);
        } catch (Exception e) {
            logger.error("Error in getCompaniesProgramacionNave", e);
            throw e;
        }
    }
}