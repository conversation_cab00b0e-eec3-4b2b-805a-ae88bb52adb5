package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.PrinterFormat;
import com.maersk.sd1.sds.dto.PrinterFormatSearchOutputDTO;
import com.maersk.sd1.common.repository.PrinterFormatRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class PrinterFormatSearchService {

    private final PrinterFormatRepository printerFormatRepository;

    @Autowired
    public PrinterFormatSearchService(PrinterFormatRepository printerFormatRepository) {
        this.printerFormatRepository = printerFormatRepository;
    }

    public List<PrinterFormatSearchOutputDTO> searchPrinterFormat(String printername, int subBusinessUnitId) {
        List<PrinterFormat> printerFormats = printerFormatRepository.findPrinterFormatById(printername, subBusinessUnitId)
                .stream()
                .limit(10)
                .toList();

        if (printerFormats.isEmpty()) {
            return null;
        }

        return printerFormats.stream().map(printerFormat -> {
            PrinterFormatSearchOutputDTO output = new PrinterFormatSearchOutputDTO();

            output.setPrinterFormatId(printerFormat.getId());
            output.setName(printerFormat.getName());
            output.setAlias(printerFormat.getAlias());
            output.setConfiguration(printerFormat.getConfiguration());
            return output;
        }).toList();
    }
}