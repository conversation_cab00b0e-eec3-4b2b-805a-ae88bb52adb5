package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.ServiceCoparnProcessFile9Input;
import com.maersk.sd1.sds.dto.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.maersk.sd1.common.Parameter.*;

@Service
@RequiredArgsConstructor
public class BookingProcessArchiveService {

    private static final String BOOKING_UPDATED_CHANGES_EXECUTED = "Booking updated. Changes executed: ";
    private static final String YES = "[Yes]";
    private static final String TO = "] to [";
    private static final String UPD_DOC = "|Upd doc.";
    private static final String FLAG_TO_FLEX = "FLAG_TO_FLEX";
    private static final String FORMAT = "%020d";
    private static final String SIZE_TYPE_QTY_REBUILT = "Size-Type-Qty: rebuilt,";
    private static final String UPD_DOC1 = "|Upd DOC";

    private final CatalogRepository catalogRepository;
    private final  BookingRepository bookingRepository;
    private final  BookingEdiRepository bookingEdiRepository;
    private final  VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final  CargoDocumentRepository cargoDocumentRepository;
    private final  BookingDetailRepository bookingDetailRepository;
    private final  EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final  VesselProgrammingPortRepository vesselProgrammingPortRepository;
    private final  PortRepository portRepository;
    private final  CompanyRepository companyRepository;
    private final  CompanyRoleRepository companyRoleRepository;
    private final  CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final  ImoRepository imoRepository;
    private final  ServiceCoparnProcessFile9Service serviceCoparnProcessFile9Service;

    @Transactional
    public void execute(BookingProcessArchiveInputDTO input) {
        Integer bookingId;
        Integer ptoDestinationBkId = 0;
        Integer ptoDischargeBkId = 0;
        Integer clientBkId = 0;
        String groupProductDesBk = "";
        Integer codeImoBkId = 0;
        String temperatureBk = "";
        String messageUpdated = "";
        String messageUpdatedI = "";
        String edi5Processing = "";
        String actBkDetail = "";
        Boolean coldTreatmentBk = null;
        Boolean controlledAtmosphereBk = null;
        Integer catBookingStatus = 0;
        Integer vesselProgrammingDetailBkId = null;
        boolean updateEmissionDate = false;

        boolean bookingApproved = false;
        boolean proceedChange;
        Integer updateDetail = 0;
        Integer qUpdated = 0;

        Boolean refrigeratedCargo;
        Boolean hazardousCargo = false;
        Integer vesselProgrammingId = 0;
        Integer cargoDocumentId;
        String vesselDetail = "";
        String tempMessage1;
        String tempMessage2;
        List<DODetail> doDetails = new ArrayList<>();
        Integer originCreationBookingId = 47739;
        String ediRemarkRulesName;
        String bkRemarkRulesName = "";
        Map<String, Integer> catalogMap = catalogRepository.findIdsByAliases(getCatalogParameters()).stream()
                .collect(Collectors.toMap(
                        obj -> (String) obj[0],
                        obj -> (Integer) obj[1]
                ));
        Integer isBkEdiToProcess = catalogMap.get(IS_BKEDI_TO_PROCESS);
        Integer isBkEdiDone = catalogMap.get(IS_BKEDI_DONE);
        Integer isCustomerRoleId = catalogMap.get(IS_CUSTOMER_ROLE_ID);
        Integer bookingTypeUpdate = catalogMap.get(IS_BOOKING_TYPE_UPDATE);
        Integer containerDryAlias = catalogMap.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS);
        Integer containerHCAlias = catalogMap.get(CATALOG_TYPE_CONTAINER_HC_ALIAS);
        Integer ediCoparnId = input.getEdiCoparnId();
        Integer businessUnitId = input.getBusinessUnitId();
        Integer subBusinessUnitId = input.getSubBusinessUnitId();
        Integer vesselProgrammingDetailId = input.getVesselProgrammingDetailId();
        String booking = input.getBooking();
        Integer containerDimensionId = input.getContainerDimensionId();
        Integer containerTypeId = input.getContainerTypeId();
        Integer reservedQuantity = input.getReservedQuantity();
        Integer secondaryContainerDimensionId = input.getSecondaryContainerDimensionId();
        Integer secondaryContainerTypeId = input.getSecondaryContainerTypeId();
        Integer secondaryReservedQuantity = input.getSecondaryReservedQuantity();
        Integer clientId = input.getClientId();
        String clientRS = input.getClientRS();
        String productGroupDescription = input.getProductGroupDescription();
        Integer productId = input.getProductId();
        Integer portOfLoadingId = input.getPortOfLoadingId();
        Integer portOfDestinationId = input.getPortOfDestinationId();
        Integer portOfDischargeId = input.getPortOfDischargeId();
        String temperature = input.getTemperature();
        Integer imoId = input.getImoId();
        Integer lineBkId = input.getLineBkId();
        Integer grossWeightEdi = input.getGrossWeightEdi();
        Integer secondaryGrossWeightEdi = input.getSecondaryGrossWeightEdi();
        Boolean coldTreatment = input.getColdTreatment();
        Boolean controlledAtmosphere = input.getControlledAtmosphere();
        Integer userRegistrationId = input.getUserRegistrationId();
        String paramSequenceDetails = input.getParamSequenceDetails();

        refrigeratedCargo = getRefrigeratedCargo(containerTypeId);

        if (isNumeric(temperature) && Boolean.TRUE.equals(controlledAtmosphere) && Boolean.TRUE.equals(refrigeratedCargo)) {
            BigDecimal parsedTemperature = new BigDecimal(temperature.replace(",", "."));
            if (parsedTemperature.compareTo(BigDecimal.ZERO) > 0) {
                containerTypeId = 31048;

                if (secondaryContainerTypeId != null) {
                    String code = catalogRepository.findCodeByCatalogId(secondaryContainerTypeId);
                    if ("1".equals(code)) {
                        secondaryContainerTypeId = 31048;
                    }
                }
            }
        }

        if (bookingRepository.existsByBookingNumberAndSubBusinessUnitId(booking, subBusinessUnitId)) {
            ediRemarkRulesName = bookingEdiRepository.findRemarkRulesNameById(ediCoparnId);
            productId = 0;
            bookingId = getTopBookingByBookingNumberAndVesselProgrammingDetailId(booking, vesselProgrammingDetailId);
            if (bookingId == null) {
                Booking booking1 = bookingRepository.findTop1ByBookingNumberAndSubBusinessUnitId(booking, subBusinessUnitId).orElse(null);
                if(booking1 != null) {
                    bookingId = booking1.getId();
                    catBookingStatus = booking1.getCatBookingStatus().getId();   
                }
                if (catBookingStatus != null && catBookingStatus == 43062) {
                    Integer bookingIdTemp = bookingId;
                    bookingId = null;
                    Booking alternativeBooking = bookingRepository.findTopBookingByFilters(booking, subBusinessUnitId,
                            43062).orElse(null);
                    if (alternativeBooking != null) {
                        bookingId = alternativeBooking.getId();
                    }
                    if (bookingId == null) {
                        bookingId = bookingIdTemp;
                    } else {
                        updateEmissionDate = true;
                    }
                }
            }
            if(bookingId != null) {
                Optional<Booking> bookingByIdOptional = bookingRepository.findById(bookingId);
                if (bookingByIdOptional.isPresent()) {
                    Booking bookingById = bookingByIdOptional.get();
                    vesselProgrammingDetailBkId = bookingById.getVesselProgrammingDetail().getId();
                    bookingApproved = bookingById.getApprovedBooking();
                    bkRemarkRulesName = bookingById.getRemarkRulesName();
                    ptoDestinationBkId = bookingById.getDestinationPort().getId();
                    ptoDischargeBkId = bookingById.getDischargePort().getId();
                    clientBkId = bookingById.getClientCompany().getId();
                    groupProductDesBk = bookingById.getCommodity() == null
                            ? ""
                            : bookingById.getCommodity().replace(" | OK TO FLEX", "");
                    codeImoBkId = bookingById.getImo().getId();
                    temperatureBk = bookingById.getTemperatureC();
                    coldTreatmentBk = bookingById.getIsColdtreatment();
                    controlledAtmosphereBk = bookingById.getIsControlledAtmosphere();
                }
            }

            VesselDetailDTO vesselDetailDTO = getVesselDetailById(vesselProgrammingDetailId);
            if (vesselDetailDTO != null) {
                vesselProgrammingId = vesselDetailDTO.getVesselProgrammingId();
                vesselDetail = vesselDetailDTO.getVesselDetail();
            }

            cargoDocumentId = bookingDetailRepository.findCargoDocumentIdByBookingId(bookingId);
            if (Objects.isNull(cargoDocumentId)) {
                cargoDocumentId = cargoDocumentRepository.findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(booking, vesselProgrammingDetailBkId);
            }
            proceedChange = true;
            if (bookingApproved) {
                doDetails = Optional.ofNullable(bookingDetailRepository.findDODetailsByBookingId(bookingId))
                        .orElse(Collections.emptyList());
                updateDoNotTouchFlag(doDetails);
                updateDoNotTouch(doDetails);
            }

            if (proceedChange) {

                if (!areEqualGeneric(vesselProgrammingDetailBkId, vesselProgrammingDetailId)) {
                    bookingRepository.updateBookingDetails(vesselProgrammingDetailId, userRegistrationId, ediCoparnId, bookingId);
                    if (bookingApproved && cargoDocumentId != null) {
                        cargoDocumentRepository.updateCargoDocumentDetails(vesselProgrammingDetailId, userRegistrationId, cargoDocumentId);
                    }
                    Optional<String> vesselDetailOptional = vesselProgrammingDetailRepository.findVesselDetail(vesselProgrammingDetailBkId);
                    String vesselDetailO = vesselDetailOptional.orElse("");
                    messageUpdated = "Vessel from " + vesselDetailO + " to " + vesselDetail;

                    messageUpdatedI = "Vessel from " + vesselDetailO + " to " + vesselDetail;
                    if (bookingApproved) {
                        messageUpdatedI += UPD_DOC1;
                    }
                    messageUpdatedI += ",";
                }

                if (!Objects.equals(ptoDestinationBkId, portOfDestinationId)
                        && (Objects.nonNull(portOfDestinationId) && portOfDestinationId > 0)) {

                    createUpdateVesselProgrammingPortDetail(vesselProgrammingId, portOfDestinationId, userRegistrationId);
                    bookingRepository.updateBooking(portOfDestinationId, userRegistrationId, ediCoparnId, bookingId);
                    if (bookingApproved && cargoDocumentId != null) {
                        cargoDocumentRepository.updateCargoDocument(portOfDestinationId, userRegistrationId, cargoDocumentId);
                    }
                    tempMessage1 = getPortName(ptoDestinationBkId);
                    tempMessage2 = getPortName(portOfDestinationId);
                    messageUpdated += "Destination Port from " + tempMessage1 + " to " + tempMessage2;

                    messageUpdatedI += "Destination Port from " + tempMessage1 + " to " + tempMessage2 +
                            (bookingApproved ? UPD_DOC1 : "") + ",";

                }

                if (!Objects.equals(ptoDischargeBkId, portOfDischargeId)
                        && (Objects.nonNull(portOfDischargeId) && portOfDischargeId > 0)) {
                    createUpdateVesselProgrammingPortDetail(vesselProgrammingId, portOfDischargeId, userRegistrationId);
                    bookingRepository.updateBookingDischargeDetails(portOfDischargeId, userRegistrationId, ediCoparnId, bookingId);
                    if (bookingApproved && cargoDocumentId != null) {
                        cargoDocumentRepository.updateCargoDischargeDocument(portOfDestinationId, userRegistrationId, cargoDocumentId);
                    }
                    tempMessage1 = getPortName(ptoDischargeBkId);
                    tempMessage2 = getPortName(portOfDischargeId);
                    messageUpdated += "Discharge Port from " + tempMessage1 + " to " + tempMessage2;

                    messageUpdatedI += "Discharge Port from " + tempMessage1 + " to " + tempMessage2 +
                            (bookingApproved ? UPD_DOC1 : "") + ",";

                }

                Integer clienteDummyId = companyRepository.findTopByDocument("DUMMY").orElse(null);
                if ((clientBkId == null ? 0 : clientBkId) != (clientId == null ? 0 : clientId)
                        && (clientId == null ? 0 : clientId) > 0
                        && (clientId == null ? 0 : clientId) != clienteDummyId) {
                    boolean exist = companyRoleRepository.doesNotExistByCompanyAndRole(clientId, isCustomerRoleId);
                    if (exist) {
                        CompanyRole companyRole = CompanyRole.builder()
                                .company(Company.builder().id(clientId).build())
                                .catRoleType(Catalog.builder().id(isCustomerRoleId).build())
                                .build();
                        companyRoleRepository.save(companyRole);
                    }
                    bookingRepository.updateBookingByCustIdAndBookingId(clientId, userRegistrationId, ediCoparnId, bookingId);
                    if (bookingApproved) {
                        cargoDocumentRepository.updateCargoDocumentByClientId(clientId, userRegistrationId, cargoDocumentId);
                    }
                    tempMessage1 = companyRepository.findById(clientBkId).map(Company::getDocument).orElse(null);
                    tempMessage2 = companyRepository.findById(clientId).map(Company::getDocument).orElse("null");

                    messageUpdated += "Customer from " + tempMessage1 + " to " + tempMessage2;

                    messageUpdatedI += "Customer from " + tempMessage1 + " to " + tempMessage2 +
                            (bookingApproved ? UPD_DOC1 : "") + ",";
                }

                if (isGroupDescriptionNotEqual(groupProductDesBk, productGroupDescription)) {
                    bookingRepository.updateBooking(productGroupDescription, productId, userRegistrationId, ediCoparnId,
                            bookingId);
                    if (bookingApproved && cargoDocumentId != null) {
                        cargoDocumentDetailRepository.updateCargoDocumentDetail(productGroupDescription, productId,
                                userRegistrationId, cargoDocumentId);
                    }
                    messageUpdated += "Commodity from [" +
                            (groupProductDesBk != null ? groupProductDesBk.trim() : "") +
                            TO +
                            (productGroupDescription != null ? productGroupDescription.trim() : "") +
                            "]";

                    messageUpdatedI += "Commodity from [" +
                            (groupProductDesBk != null ? groupProductDesBk.trim() : "") +
                            TO +
                            (productGroupDescription != null ? productGroupDescription.trim() : "") +
                            "]" +
                            (bookingApproved ? UPD_DOC : "") +
                            ",";
                }

                if (!areEqualGeneric(Optional.ofNullable(codeImoBkId).orElse(0), Optional.ofNullable(imoId).orElse(0))) {
                    bookingRepository.updateBookingImoDetails(imoId, userRegistrationId, ediCoparnId, bookingId);
                    tempMessage1 = getImoCode(codeImoBkId);
                    tempMessage2 = getImoCode(imoId);
                    messageUpdated += "IMO number from " + tempMessage1 + " to " + tempMessage2;

                    messageUpdatedI += "IMO number from " + tempMessage1 + " to " + tempMessage2 +
                            (bookingApproved ? UPD_DOC : "") + ",";
                }

                if (!Optional.ofNullable(temperatureBk).map(String::trim).orElse("").equals(Optional.ofNullable(temperature).map(String::trim).orElse(""))) {
                    bookingRepository.updateBookingTemperature(temperature, userRegistrationId, ediCoparnId, bookingId);
                    messageUpdated += "Temperature from [" +
                            (temperatureBk != null ? temperatureBk.trim() : "") +
                            TO +
                            (temperature != null ? temperature.trim() : "") +
                            "]";

                    messageUpdatedI += "Temperature from [" +
                            (temperatureBk != null ? temperatureBk.trim() : "") +
                            TO +
                            (temperature != null ? temperature.trim() : "") +
                            "]" +
                            (bookingApproved ? UPD_DOC : "") +
                            ",";
                }

                if (!areEqualGeneric(Optional.ofNullable(coldTreatmentBk).orElse(false), Optional.ofNullable(coldTreatment).orElse(false))) {
                    bookingRepository.updateBookingColdTreatment(coldTreatment, userRegistrationId, ediCoparnId, bookingId, "EDI-Replace.COT");
                    String coldTreatmentBKLabel = (Boolean.TRUE.equals(coldTreatmentBk)) ? YES : "[No]";
                    String coldTreatmentLabel = (Boolean.TRUE.equals(coldTreatment)) ? YES : "[No]";
                    messageUpdated += "ColdTreatment from " + coldTreatmentBKLabel + " to " + coldTreatmentLabel;
                    messageUpdatedI += "ColdTreatment from " + coldTreatmentBKLabel + " to " + coldTreatmentLabel + (bookingApproved ? UPD_DOC : "") + ",";
                }

                if (!Objects.equals(Optional.ofNullable(controlledAtmosphereBk).orElse(false), Optional.ofNullable(controlledAtmosphere).orElse(false))) {
                    bookingRepository.updateBookingColdTreatment(controlledAtmosphereBk, userRegistrationId, ediCoparnId, bookingId, "EDI-Replace.COA");
                    String controlledAtmosphereBKLabel = (Boolean.TRUE.equals(controlledAtmosphereBk)) ? YES : "[No]";
                    String controlledAtmosphereLabel = (Boolean.TRUE.equals(controlledAtmosphere)) ? YES : "[No]";
                    messageUpdated += "Controlled Atmosphere from " + controlledAtmosphereBKLabel + " to " + controlledAtmosphereLabel;
                    messageUpdatedI += "Controlled Atmosphere from " + controlledAtmosphereBKLabel + " to " + controlledAtmosphereLabel + (bookingApproved ? UPD_DOC : "") + ",";
                }

                if (Objects.equals(ediRemarkRulesName, FLAG_TO_FLEX) && bkRemarkRulesName.isEmpty()) {
                    bookingRepository.updateBookingRemarkRulesWithCommodityLogic(ediRemarkRulesName, userRegistrationId,
                            "bk_convert_toflex", true, bookingId);
                    bookingDetailRepository.updateBookingDetailsRemarkRules(ediRemarkRulesName, userRegistrationId,
                            "bk_convert_toflex", bookingId);
                    messageUpdated += " Converted TO_FLEX,";
                    messageUpdatedI += " Converted TO_FLEX,";
                }

                if (ediRemarkRulesName.isEmpty() && bkRemarkRulesName.equals(FLAG_TO_FLEX)) {
                    bookingRepository.updateBookingRemarkRulesWithCommodityLogic(ediRemarkRulesName, userRegistrationId,
                            "bk_toflex_to_nomral", false, bookingId);
                    bookingDetailRepository.updateBookingDetailsRemarkRules(ediRemarkRulesName, userRegistrationId,
                            "bk_toflex_to_nomral", bookingId);
                    messageUpdated += " Converted not TO_FLEX,";
                    messageUpdatedI += " Converted not TO_FLEX,";
                }

                String messageUpdatedDetail = "";
                String messageUpdatedDetailI = "";
                if ((containerDimensionId == null ? 0 : containerDimensionId) > 0 && (containerTypeId == null ? 0 : containerTypeId) > 0 &&
                        (reservedQuantity == null ? 0 : reservedQuantity) > 0) {
                    List<MyDetail> bookingDetailsAggregated = bookingDetailRepository.findBookingDetailsAggregated(bookingId);
                    if (existsInMyDetail(bookingDetailsAggregated, containerTypeId, containerDimensionId, reservedQuantity)) {
                        actBkDetail = "N";
                    }
                    if (isValidContainerCondition(secondaryContainerDimensionId, secondaryContainerTypeId, secondaryReservedQuantity)) {
                        if (existsInMyDetail(bookingDetailsAggregated, secondaryContainerTypeId, secondaryContainerDimensionId, secondaryReservedQuantity)) {
                            actBkDetail = "N";
                        } else {
                            actBkDetail = "";
                        }
                    }
                    if (bookingDetailsAggregated.stream().map(detail -> detail.getCatContainerTypeId().toString()
                            + detail.getCatSizeId().toString()).distinct().count()
                            != (Objects.requireNonNullElse(secondaryContainerDimensionId, 0) != 0 ? 2 : 1)) {
                        actBkDetail = "";
                    }

                    if ("N".equals(actBkDetail)
                            && ((areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName) && "".equals(bkRemarkRulesName))
                            || ("".equals(ediRemarkRulesName) && FLAG_TO_FLEX.equals(bkRemarkRulesName)))) {
                        actBkDetail = "";
                    }

                    if ("".equals(actBkDetail)) {
                        updateDetail = 1;
                        if (!bookingApproved) {
                            bookingDetailRepository.deleteByBookingId(bookingId);
                            if (areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName)
                                    && (areEqualGeneric(containerTypeId, containerDryAlias)
                                    || areEqualGeneric(containerTypeId, containerHCAlias))) {
                                for (int qq = 1; qq <= reservedQuantity; qq++) {
                                    BookingDetail bookingDetail = BookingDetail.builder()
                                            .booking(Booking.builder().id(bookingId).build())
                                            .catSize(Catalog.builder().id(containerDimensionId).build())
                                            .catContainerType(Catalog.builder().id(containerTypeId).build())
                                            .reservationQuantity(1)
                                            .attendedQuantity(0)
                                            .maximumLoadRequired(grossWeightEdi)
                                            .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                            .active(true)
                                            .registrationUser(User.builder().id(userRegistrationId).build())
                                            .traceBkDetail("edi_5_ins_toflex1")
                                            .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                            .remarkRulesName(ediRemarkRulesName)
                                            .build();

                                    bookingDetailRepository.save(bookingDetail);
                                }
                            } else {
                                BookingDetail bookingDetail = BookingDetail.builder()
                                        .booking(Booking.builder().id(bookingId).build())
                                        .catSize(Catalog.builder().id(containerDimensionId).build())
                                        .catContainerType(Catalog.builder().id(containerTypeId).build())
                                        .reservationQuantity(reservedQuantity)
                                        .attendedQuantity(0)
                                        .maximumLoadRequired(grossWeightEdi)
                                        .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                        .active(true)
                                        .registrationUser(User.builder().id(userRegistrationId).build())
                                        .traceBkDetail("edi5_ins_rebuilt1")
                                        .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                        .build();

                                bookingDetailRepository.save(bookingDetail);
                            }
                            qUpdated = reservedQuantity;
                            if (isValidContainerCondition(secondaryContainerDimensionId, secondaryContainerTypeId,
                                    secondaryReservedQuantity)) {
                                if (areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName) &&
                                        (areEqualGeneric(secondaryContainerTypeId, containerDryAlias) || areEqualGeneric(secondaryContainerTypeId, containerHCAlias))) {

                                    for (int qq2 = 1; qq2 <= secondaryReservedQuantity; qq2++) {
                                        BookingDetail bookingDetail = BookingDetail.builder()
                                                .booking(Booking.builder().id(bookingId).build())
                                                .catSize(Catalog.builder().id(secondaryContainerDimensionId).build())
                                                .catContainerType(Catalog.builder().id(secondaryContainerTypeId).build())
                                                .reservationQuantity(1)
                                                .attendedQuantity(0)
                                                .maximumLoadRequired(grossWeightEdi)
                                                .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                .active(true)
                                                .registrationUser(User.builder().id(userRegistrationId).build())
                                                .traceBkDetail("edi_5_ins_toflex2")
                                                .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                .remarkRulesName(ediRemarkRulesName)
                                                .build();

                                        bookingDetailRepository.save(bookingDetail);
                                    }
                                } else {
                                    BookingDetail bookingDetail = BookingDetail.builder()
                                            .booking(Booking.builder().id(bookingId).build())
                                            .catSize(Catalog.builder().id(secondaryContainerDimensionId).build())
                                            .catContainerType(Catalog.builder().id(secondaryContainerTypeId).build())
                                            .reservationQuantity(secondaryReservedQuantity)
                                            .attendedQuantity(0)
                                            .maximumLoadRequired(grossWeightEdi)
                                            .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                            .active(true)
                                            .registrationUser(User.builder().id(userRegistrationId).build())
                                            .traceBkDetail("edi5_ins_rebuilt2")
                                            .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                            .build();

                                    bookingDetailRepository.save(bookingDetail);
                                }
                                qUpdated += secondaryReservedQuantity;
                            }
                            updateDetail = 2;
                            messageUpdatedDetail = SIZE_TYPE_QTY_REBUILT;
                            messageUpdatedDetailI = SIZE_TYPE_QTY_REBUILT;
                        }
                        if (bookingApproved) {
                            Integer existsQ;
                            Integer existsQ2;
                            Integer qxEliminate;
                            if (!doNotTouchAndActiveExist(doDetails)) {
                                updateActiveDetails(ediCoparnId, userRegistrationId, doDetails);
                                bookingDetailRepository.updateActiveBookingDetails(String.format(FORMAT, ediCoparnId), userRegistrationId, bookingId);
                                if (areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName) &&
                                        (areEqualGeneric(containerTypeId, containerDryAlias) || areEqualGeneric(containerTypeId, containerHCAlias))) {
                                    for (int qq3 = 1; qq3 <= reservedQuantity; qq3++) {
                                        BookingDetail bookingDetail = BookingDetail.builder()
                                                .booking(Booking.builder().id(bookingId).build())
                                                .catSize(Catalog.builder().id(containerDimensionId).build())
                                                .catContainerType(Catalog.builder().id(containerTypeId).build())
                                                .reservationQuantity(1)
                                                .attendedQuantity(0)
                                                .maximumLoadRequired(grossWeightEdi)
                                                .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                .active(true)
                                                .registrationUser(User.builder().id(userRegistrationId).build())
                                                .traceBkDetail("edi5_ins_toflex3")
                                                .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                .remarkRulesName(ediRemarkRulesName)
                                                .build();

                                        bookingDetailRepository.save(bookingDetail);
                                    }
                                } else {
                                    BookingDetail bookingDetail = BookingDetail.builder()
                                            .booking(Booking.builder().id(bookingId).build())
                                            .catSize(Catalog.builder().id(containerDimensionId).build())
                                            .catContainerType(Catalog.builder().id(containerTypeId).build())
                                            .reservationQuantity(reservedQuantity)
                                            .attendedQuantity(0)
                                            .maximumLoadRequired(grossWeightEdi)
                                            .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                            .active(true)
                                            .registrationUser(User.builder().id(userRegistrationId).build())
                                            .registrationDate(LocalDateTime.now())
                                            .traceBkDetail("edi5_ins_rebuilt3")
                                            .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                            .build();

                                    bookingDetailRepository.save(bookingDetail);
                                }
                                qUpdated = reservedQuantity;
                                if (isValidContainerCondition(secondaryContainerDimensionId, secondaryContainerTypeId,
                                        secondaryReservedQuantity)) {
                                    if (areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName) &&
                                            (areEqualGeneric(secondaryContainerTypeId, containerDryAlias) || areEqualGeneric(secondaryContainerTypeId, containerHCAlias))) {

                                        for (int qq4 = 1; qq4 <= secondaryReservedQuantity; qq4++) {
                                            BookingDetail bookingDetail = BookingDetail.builder()
                                                    .booking(Booking.builder().id(bookingId).build())
                                                    .catSize(Catalog.builder().id(secondaryContainerDimensionId).build())
                                                    .catContainerType(Catalog.builder().id(secondaryContainerTypeId).build())
                                                    .reservationQuantity(1)
                                                    .attendedQuantity(0)
                                                    .maximumLoadRequired(grossWeightEdi)
                                                    .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                    .active(true)
                                                    .registrationUser(User.builder().id(userRegistrationId).build())
                                                    .traceBkDetail("edi_5_ins_toflex4")
                                                    .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                    .remarkRulesName(ediRemarkRulesName)
                                                    .build();

                                            bookingDetailRepository.save(bookingDetail);
                                        }
                                    } else {
                                        BookingDetail bookingDetail = BookingDetail.builder()
                                                .booking(Booking.builder().id(bookingId).build())
                                                .catSize(Catalog.builder().id(secondaryContainerDimensionId).build())
                                                .catContainerType(Catalog.builder().id(secondaryContainerTypeId).build())
                                                .reservationQuantity(secondaryReservedQuantity)
                                                .attendedQuantity(0)
                                                .maximumLoadRequired(secondaryGrossWeightEdi)
                                                .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                .active(true)
                                                .registrationUser(User.builder().id(userRegistrationId).build())
                                                .registrationDate(LocalDateTime.now())
                                                .traceBkDetail("edi5_ins_rebuilt4")
                                                .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                .build();

                                        bookingDetailRepository.save(bookingDetail);
                                    }
                                    qUpdated += secondaryReservedQuantity;
                                }
                                updateDetail = 2;
                                messageUpdatedDetail = SIZE_TYPE_QTY_REBUILT;
                                messageUpdatedDetailI = "Size-Type-Qty: rebuilt|Upd DOC,";
                            } else {
                                qxEliminate = countMatchingCargoDocumentWithConditions(doDetails);
                                if (qxEliminate > 1) {
                                    updateDetail = 2;
                                }
                                updateActiveDetailsWhenDoNotTouchIsFalse(ediCoparnId, userRegistrationId, doDetails);
                                doDetails.clear();
                                doDetails = Optional.ofNullable(bookingDetailRepository.findDODetailsByBookingId(bookingId))
                                        .orElse(Collections.emptyList());
                                updateBookingDetails(doDetails, userRegistrationId, ediCoparnId);
                                bookingDetailRepository.updateInactiveBookingDetails(userRegistrationId, ediCoparnId);
                                bookingDetailsAggregated.clear();
                                bookingDetailsAggregated = bookingDetailRepository.findBookingDetailsAggregated(bookingId);
                                if (updateDetail == 2) {
                                    messageUpdatedDetail = "Size-Type-Qty: to less";
                                    messageUpdatedDetailI = "Size-Type-Qty (to_less)";
                                }
                                if (existsInMyDetailmatchingContainerTypeAndSizeId(bookingDetailsAggregated, containerTypeId, containerDimensionId)) {
                                    existsQ = getQuantity(bookingDetailsAggregated, containerDimensionId, containerTypeId);
                                    existsQ2 = countDetails(doDetails, containerTypeId, containerDimensionId);
                                    int quantityToInsert = reservedQuantity - existsQ;
                                    if (isExistQConditionMet(existsQ, existsQ2) && !areEqualGeneric(existsQ, reservedQuantity) && existsQ < reservedQuantity) {
                                        if (areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName)
                                                && (areEqualGeneric(containerTypeId, containerDryAlias)
                                                || areEqualGeneric(containerTypeId, containerHCAlias))) {
                                            List<BookingDetail> detailsToInsert = new ArrayList<>();
                                            for (int i = 0; i < quantityToInsert; i++) {
                                                BookingDetail newBookingDetail = BookingDetail.builder()
                                                        .booking(Booking.builder().id(bookingId).build())
                                                        .catSize(Catalog.builder().id(containerDimensionId).build())
                                                        .catContainerType(Catalog.builder().id(containerTypeId).build())
                                                        .reservationQuantity(1)
                                                        .attendedQuantity(0)
                                                        .maximumLoadRequired(grossWeightEdi)
                                                        .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                        .active(true)
                                                        .registrationUser(User.builder().id(userRegistrationId).build())
                                                        .traceBkDetail("edi_5_ins_toflex5")
                                                        .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                        .remarkRulesName(ediRemarkRulesName)
                                                        .build();
                                                detailsToInsert.add(newBookingDetail);
                                            }

                                            bookingDetailRepository.saveAll(detailsToInsert);
                                        } else {
                                            BookingDetail newBookingDetail = BookingDetail.builder()
                                                    .booking(Booking.builder().id(bookingId).build())
                                                    .catSize(Catalog.builder().id(containerDimensionId).build())
                                                    .catContainerType(Catalog.builder().id(containerTypeId).build())
                                                    .reservationQuantity(quantityToInsert)
                                                    .attendedQuantity(0)
                                                    .maximumLoadRequired(grossWeightEdi)
                                                    .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                    .active(true)
                                                    .registrationUser(User.builder().id(userRegistrationId).build())
                                                    .registrationDate(LocalDateTime.now())
                                                    .traceBkDetail("edi5_ins_rebuilt5")
                                                    .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                    .build();
                                            bookingDetailRepository.save(newBookingDetail);
                                        }
                                        qUpdated = quantityToInsert;
                                        updateDetail = 2;
                                        messageUpdatedDetail = "Size-Type-Qty (rebuilt): " + quantityToInsert + "cnts";
                                        messageUpdatedDetailI = messageUpdatedDetail;
                                    } else {
                                        if (updateDetail.equals(1)) {
                                            updateDetail = 4;
                                        }
                                    }
                                }

                                if (isValidContainerCondition(secondaryContainerDimensionId, secondaryContainerTypeId,
                                        secondaryReservedQuantity) && existsInMyDetailmatchingContainerTypeAndSizeId(bookingDetailsAggregated, secondaryContainerTypeId, secondaryContainerDimensionId)) {
                                        existsQ = getQuantity(bookingDetailsAggregated, secondaryContainerDimensionId, secondaryContainerTypeId);
                                        existsQ2 = countDetails(doDetails, secondaryContainerTypeId, secondaryContainerDimensionId);
                                        int quantityToInsert = secondaryReservedQuantity - existsQ;
                                        if (isExistQConditionMet(existsQ, existsQ2) && !areEqualGeneric(existsQ, secondaryReservedQuantity) && existsQ < secondaryReservedQuantity) {
                                            if (areEqualGeneric(FLAG_TO_FLEX, ediRemarkRulesName)
                                                    && (areEqualGeneric(secondaryContainerTypeId, containerDryAlias)
                                                    || areEqualGeneric(secondaryContainerTypeId, containerHCAlias))) {
                                                List<BookingDetail> detailsToInsert = new ArrayList<>();
                                                for (int i = 0; i < quantityToInsert; i++) {
                                                    BookingDetail newBookingDetail = BookingDetail.builder()
                                                            .booking(Booking.builder().id(bookingId).build())
                                                            .catSize(Catalog.builder().id(secondaryContainerDimensionId).build())
                                                            .catContainerType(Catalog.builder().id(secondaryContainerTypeId).build())
                                                            .reservationQuantity(1)
                                                            .attendedQuantity(0)
                                                            .maximumLoadRequired(grossWeightEdi)
                                                            .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                            .active(true)
                                                            .registrationUser(User.builder().id(userRegistrationId).build())
                                                            .traceBkDetail("edi_5_ins_toflex6")
                                                            .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                            .remarkRulesName(ediRemarkRulesName)
                                                            .build();
                                                    detailsToInsert.add(newBookingDetail);
                                                }

                                                bookingDetailRepository.saveAll(detailsToInsert);
                                            } else {
                                                BookingDetail newBookingDetail = BookingDetail.builder()
                                                        .booking(Booking.builder().id(bookingId).build())
                                                        .catSize(Catalog.builder().id(secondaryContainerDimensionId).build())
                                                        .catContainerType(Catalog.builder().id(secondaryContainerTypeId).build())
                                                        .reservationQuantity(quantityToInsert)
                                                        .attendedQuantity(0)
                                                        .maximumLoadRequired(secondaryGrossWeightEdi)
                                                        .catOriginBookingCreation(Catalog.builder().id(originCreationBookingId).build())
                                                        .active(true)
                                                        .registrationUser(User.builder().id(userRegistrationId).build())
                                                        .registrationDate(LocalDateTime.now())
                                                        .traceBkDetail("edi5_ins_rebuilt6")
                                                        .bookingEdiReference(BookingEdi.builder().id(ediCoparnId).build())
                                                        .build();
                                                bookingDetailRepository.save(newBookingDetail);
                                            }
                                            qUpdated += quantityToInsert;
                                            updateDetail = 2;
                                            messageUpdatedDetail = "Size-Type-Qty (rebuilt): " + quantityToInsert + "cnts";
                                            messageUpdatedDetailI = "Size-Type-Qty (rebuilt_2): " + quantityToInsert + "cnts";
                                        } else {
                                            if (updateDetail.equals(1)) {
                                                updateDetail = 4;
                                            }
                                        }
                                    }


                            }
                            if (updateDetail.equals(2)) {
                                List<NewDODetail> newDODetails = new ArrayList<>();

                                int position;
                                int posx;
                                int nItem;
                                int requestedQuantity;
                                Integer bookingDetailId;
                                Integer catContainerType;
                                Integer catSize;

                                Integer catPackagingId = 43009;
                                Integer catUnitMeasureWeightId = 43012;
                                Integer catUnitMeasureQuantityId = 43014;
                                BookingDataDTO bookingDataByBookingId = bookingRepository.findBookingDetailsByBookingId(bookingId);
                                if (Optional.ofNullable(bookingDataByBookingId.getImoId()).orElse(0) > 0) {
                                    hazardousCargo = true;
                                }
                                List<NewBKDetail> newBookingDetailsByBookingId = bookingDetailRepository.findNewBookingDetailsByBookingId(bookingId);
                                if (!CollectionUtils.isEmpty(newBookingDetailsByBookingId)) {
                                    nItem = 1;
                                    for (position = 0; position < newBookingDetailsByBookingId.size(); position++) {
                                        NewBKDetail currentDetail = newBookingDetailsByBookingId.get(position);

                                        bookingDetailId = currentDetail.getBookingDetailId();
                                        requestedQuantity = currentDetail.getReservationQuantity();
                                        catContainerType = currentDetail.getCatContainerType();
                                        catSize = currentDetail.getCatSize();
                                        boolean isRefrigeratedCargo = isRefrigeratedContainer(catContainerType);

                                        for (posx = 1; posx <= requestedQuantity; posx++) {
                                            NewDODetail newDODetail = NewDODetail.builder()
                                                    .bookingId(bookingId)
                                                    .bookingDetailId(bookingDetailId)
                                                    .doItem(nItem)
                                                    .catContainerType(catContainerType)
                                                    .catSize(catSize)
                                                    .refrigeratedCargo(isRefrigeratedCargo)
                                                    .build();
                                            newDODetails.add(newDODetail);
                                            nItem++;
                                        }
                                    }

                                    Optional<Booking> optionalBooking = bookingRepository.findById(bookingId);

                                    if (optionalBooking.isPresent()) {
                                        Booking bookingData = optionalBooking.get();
                                        List<NewDODetail> filteredDetails = new ArrayList<>();
                                        for (NewDODetail detail : newDODetails) {
                                            if (detail.getDoItem() != null && detail.getBookingId() != null && detail.getBookingId().equals(bookingId)) {
                                                filteredDetails.add(detail);
                                            }
                                        }

                                        filteredDetails.sort(Comparator.comparing(NewDODetail::getDoItem));

                                        List<CargoDocumentDetailDTO> cargoDocumentDetailDTOS = new ArrayList<>();
                                        for (NewDODetail detail : filteredDetails) {
                                            CargoDocumentDetailDTO migratedData = CargoDocumentDetailDTO.builder()
                                                    .cargoDocumentId(cargoDocumentId)
                                                    .productId(bookingData.getProduct() != null ? bookingData.getProduct().getId() : null)
                                                    .packagingCategoryId(catPackagingId)
                                                    .manifestedQuantity(BigDecimal.valueOf(1))
                                                    .manifestedWeight(BigDecimal.valueOf(1))
                                                    .manifestedVolume(BigDecimal.valueOf(0))
                                                    .weightUnitCategoryId(catUnitMeasureWeightId)
                                                    .declaredContent(1)
                                                    .hazardousCargo(hazardousCargo)
                                                    .refrigeratedCargo(detail.getRefrigeratedCargo())
                                                    .productGroupDescription(bookingDataByBookingId.getProductGroupDescription())
                                                    .cargoConditionCategoryId(bookingDataByBookingId.getCatConditionLoadId())
                                                    .clearedEmptyGateout(false)
                                                    .active(true)
                                                    .creationOriginCategoryId(47772)
                                                    .registrationUserId(userRegistrationId)
                                                    .registrationDate(LocalDateTime.now())
                                                    .containerTypeCategoryId(detail.getCatContainerType())
                                                    .sizeCategoryId(detail.getCatSize())
                                                    .quantityUnitCategoryId(catUnitMeasureQuantityId)
                                                    .bookingDetailId(detail.getBookingDetailId())
                                                    .traceCargoDocumentDetail("edi5_ins_rebuilt7")
                                                    .build();

                                            cargoDocumentDetailDTOS.add(migratedData);
                                        }
                                        if (!CollectionUtils.isEmpty(cargoDocumentDetailDTOS)) {
                                            List<CargoDocumentDetail> detailsToInsert = new ArrayList<>();
                                            for (CargoDocumentDetailDTO cargoDocumentDetailDTO : cargoDocumentDetailDTOS) {
                                                CargoDocumentDetail newCargoDocumentDetail = CargoDocumentDetail.builder()
                                                        .cargoDocument(CargoDocument.builder().id(cargoDocumentDetailDTO.getCargoDocumentId()).build())
                                                        .product(Product.builder().id(cargoDocumentDetailDTO.getProductId()).build())
                                                        .catPackaging(Catalog.builder().id(cargoDocumentDetailDTO.getPackagingCategoryId()).build())
                                                        .manifestedQuantity(cargoDocumentDetailDTO.getManifestedQuantity())
                                                        .manifestedWeight(cargoDocumentDetailDTO.getManifestedWeight())
                                                        .manifestedVolume(cargoDocumentDetailDTO.getManifestedVolume())
                                                        .catWeightMeasureUnit(Catalog.builder().id(cargoDocumentDetailDTO.getWeightUnitCategoryId()).build())
                                                        .saysContain(cargoDocumentDetailDTO.getDeclaredContent())
                                                        .isDangerousCargo(cargoDocumentDetailDTO.getHazardousCargo())
                                                        .isRefrigeratedCargo(cargoDocumentDetailDTO.getRefrigeratedCargo())
                                                        .commodity(cargoDocumentDetailDTO.getProductGroupDescription())
                                                        .catCargoCondition(Catalog.builder().id(cargoDocumentDetailDTO.getCargoConditionCategoryId()).build())
                                                        .emptyGateoutSettled(cargoDocumentDetailDTO.getClearedEmptyGateout())
                                                        .active(cargoDocumentDetailDTO.getActive())
                                                        .catCreationOrigin(Catalog.builder().id(cargoDocumentDetailDTO.getCreationOriginCategoryId()).build())
                                                        .registrationUser(User.builder().id(cargoDocumentDetailDTO.getRegistrationUserId()).build())
                                                        .registrationDate(cargoDocumentDetailDTO.getRegistrationDate())
                                                        .catManifestedContainerType(Catalog.builder().id(cargoDocumentDetailDTO.getContainerTypeCategoryId()).build())
                                                        .catManifestedSize(Catalog.builder().id(cargoDocumentDetailDTO.getSizeCategoryId()).build())
                                                        .catMeasureUnitQuantity(Catalog.builder().id(cargoDocumentDetailDTO.getQuantityUnitCategoryId()).build())
                                                        .bookingDetail(BookingDetail.builder().id(cargoDocumentDetailDTO.getBookingDetailId()).build())
                                                        .traceCargoDocumentDetail(cargoDocumentDetailDTO.getTraceCargoDocumentDetail())
                                                        .build();
                                                detailsToInsert.add(newCargoDocumentDetail);
                                            }

                                            cargoDocumentDetailRepository.saveAll(detailsToInsert);
                                        }

                                    }
                                }
                            }
                            if (qUpdated.equals(0) && updateDetail.equals(1)) {
                                updateDetail = 0;
                            }
                        }
                        if (updateDetail.equals(2)) {
                            messageUpdated += " " + messageUpdatedDetail;
                            messageUpdatedI += " " + messageUpdatedDetailI + (bookingApproved ? UPD_DOC : "") + ",";
                        }

                    }
                }
                if (isNullOrZero(portOfDestinationId) || isNullOrZero(portOfDischargeId) || isNullOrZero(clientId) || updateDetail == 1) {
                    edi5Processing = "N";
                }
                if (messageUpdated != null && !messageUpdated.isEmpty()) {
                    messageUpdated = (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdated)
                            .substring(0, Math.min(250, (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdated).length()));

                    messageUpdatedI = (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdatedI)
                            .substring(0, Math.min(250, (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdatedI).length()));
                    bookingRepository.updateBooking(bookingId, bookingTypeUpdate, ediCoparnId, userRegistrationId, updateEmissionDate);
                    bookingEdiRepository.updateBookingEdi(messageUpdated, messageUpdatedI, ediCoparnId);

                }
                if (messageUpdated != null && !messageUpdated.isEmpty()) {
                    messageUpdated = (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdated)
                            .substring(0, Math.min(250, (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdated).length()));

                    messageUpdatedI = (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdatedI)
                            .substring(0, Math.min(250, (BOOKING_UPDATED_CHANGES_EXECUTED + messageUpdatedI).length()));
                    bookingRepository.updateBooking(bookingId, bookingTypeUpdate, ediCoparnId, userRegistrationId, updateEmissionDate);
                    bookingEdiRepository.updateBookingEdi(messageUpdated, messageUpdatedI, ediCoparnId);

                }
                if (StringUtils.isEmpty(messageUpdated) && updateDetail == 0) {
                    bookingEdiRepository.updateNoChanges(ediCoparnId);
                }
                if (edi5Processing.isEmpty()) {
                    bookingEdiRepository.updateProcessed(ediCoparnId, isBkEdiDone, userRegistrationId);
                }
                if (StringUtils.isEmpty(messageUpdated) && (updateDetail == 4 || updateDetail == 6)) {
                    String formattedActualizoDetalle = String.format("%d", updateDetail);
                    bookingEdiRepository.updateInconsistency(ediCoparnId, isBkEdiToProcess, userRegistrationId, formattedActualizoDetalle);
                }
            }

        } else {
            ServiceCoparnProcessFile9Input serviceCoparnProcessFile9Input = new ServiceCoparnProcessFile9Input();
            serviceCoparnProcessFile9Input.setBooking(booking);
            serviceCoparnProcessFile9Input.setEdiCoparnId(ediCoparnId);
            serviceCoparnProcessFile9Input.setUnitBusinessId(businessUnitId);
            serviceCoparnProcessFile9Input.setSubUnitBusinessId(subBusinessUnitId);
            serviceCoparnProcessFile9Input.setProgrammingNaveDetailId(vesselProgrammingDetailId);
            serviceCoparnProcessFile9Input.setCntDimenId(containerDimensionId);
            serviceCoparnProcessFile9Input.setCntTypeId(containerTypeId);
            serviceCoparnProcessFile9Input.setQuantityReserve(reservedQuantity);
            serviceCoparnProcessFile9Input.setCntDimen2Id(secondaryContainerDimensionId);
            serviceCoparnProcessFile9Input.setCntType2Id(secondaryContainerTypeId);
            serviceCoparnProcessFile9Input.setQuantityReserve2(secondaryReservedQuantity);
            serviceCoparnProcessFile9Input.setCustomerId(clientId);
            serviceCoparnProcessFile9Input.setClientRS(clientRS);
            serviceCoparnProcessFile9Input.setProductGroupDescription(productGroupDescription);
            serviceCoparnProcessFile9Input.setProductId(productId);
            serviceCoparnProcessFile9Input.setPortOfEmbarqueId(portOfLoadingId);
            serviceCoparnProcessFile9Input.setPortOfDestinoId(portOfDestinationId);
            serviceCoparnProcessFile9Input.setPortOfDescargaId(portOfDischargeId);
            serviceCoparnProcessFile9Input.setTemperature(temperature);
            serviceCoparnProcessFile9Input.setImoId(imoId);
            serviceCoparnProcessFile9Input.setLineABkId(lineBkId);
            serviceCoparnProcessFile9Input.setGrossWeightEDI(0);
            serviceCoparnProcessFile9Input.setGrossWeightEDI2(0);
            serviceCoparnProcessFile9Input.setColdTreatment(coldTreatment);
            serviceCoparnProcessFile9Input.setControlledAtmosphere(controlledAtmosphere);
            serviceCoparnProcessFile9Input.setUserRecordId(userRegistrationId);
            serviceCoparnProcessFile9Input.setPassCoparn5x9(true);
            serviceCoparnProcessFile9Input.setParamSequenceDetails(paramSequenceDetails);
            serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(serviceCoparnProcessFile9Input);
        }
    }

    private void createUpdateVesselProgrammingPortDetail(Integer vesselProgrammingId, Integer portId, Integer userRegistrationId) {
        boolean exists = vesselProgrammingPortRepository
                .existsByVesselProgrammingIdAndPortId(vesselProgrammingId, portId);
        if (!exists) {
            VesselProgrammingPort vesselProgrammingPort = new VesselProgrammingPort();
            vesselProgrammingPort.setVesselProgramming(VesselProgramming.builder().id(vesselProgrammingId).build());
            vesselProgrammingPort.setPort(Port.builder().id(portId).build());
            vesselProgrammingPort.setActive(true);
            vesselProgrammingPort.setRegistrationUser(User.builder().id(userRegistrationId).build());
            vesselProgrammingPort.setRegistrationDate(LocalDateTime.now());
            vesselProgrammingPortRepository.save(vesselProgrammingPort);
        } else {
            vesselProgrammingPortRepository.updateVesselProgrammingPort(vesselProgrammingId, portId, userRegistrationId);
        }
    }

    private <T> boolean areEqualGeneric(T first, T second) {
        return Objects.equals(first, second);
    }

    private List<String> getCatalogParameters() {
        return List.of(IS_BKEDI_PENDING, IS_BKEDI_TO_PROCESS, IS_BKEDI_DONE, IS_BKEDI_REJECTED, IS_BKEDI_NOT_VALID,
                IS_BKEDI_REJECTED_DUPLICATE, IS_CUSTOMER_ROLE_ID, IS_BOOKING_TYPE_UPDATE, CATALOG_TYPE_CONTAINER_DRY_ALIAS,
                CATALOG_TYPE_CONTAINER_HC_ALIAS);
    }

    private Boolean getRefrigeratedCargo(Integer containerTypeId) {
        String code = catalogRepository.findCodeByCatalogId(containerTypeId);
        return code != null && code.equals("1");
    }

    private boolean isNumeric(String temperature) {
        if (temperature == null || temperature.trim().isEmpty()) {
            return false;
        }
        try {
            new BigDecimal(temperature.replace(",", "."));
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private Integer getTopBookingByBookingNumberAndVesselProgrammingDetailId(String bookingNumber, Integer vesselProgrammingDetailId) {
        return bookingRepository.findTop1ByBookingNumberAndVesselProgrammingDetailId(bookingNumber, vesselProgrammingDetailId)
                .map(Booking::getId)
                .orElse(null);
    }

    private VesselDetailDTO getVesselDetailById(Integer programmingDetailId) {
        return vesselProgrammingDetailRepository.findVesselDetailByProgrammingDetailId(programmingDetailId);
    }

    private void updateDoNotTouchFlag(List<DODetail> doDetails) {
        doDetails.stream()
                .filter(detail -> detail.getContainerId() != null ||
                        (detail.getReceivedQuantity() != null && detail.getReceivedQuantity().compareTo(BigDecimal.ZERO) > 0))
                .forEach(detail -> detail.setDoNotTouch(1));
    }

    private void updateDoNotTouch(List<DODetail> doDetails) {

        Set<Integer> detailIds = getCargoDocumentDetailIds(doDetails);
        if (detailIds == null) return;

        List<Integer> matchingDetailIds = eirDocumentCargoDetailRepository
                .findActiveEirCargoDocumentDetailByIds(List.copyOf(detailIds));

        doDetails.stream()
                .filter(Objects::nonNull)
                .forEach(detail -> {
                    if (matchingDetailIds.contains(detail.getCargoDocumentDetailId())) {
                        detail.setDoNotTouch(1);
                    }
                });
    }

    private Set<Integer> getCargoDocumentDetailIds(List<DODetail> doDetails) {
        if (CollectionUtils.isEmpty(doDetails)) {
            return Collections.emptySet();
        }

        return doDetails.stream()
                .map(DODetail::getCargoDocumentDetailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private String getPortName(Integer portId) {
        return portRepository.findById(portId)
                .map(Port::getPort)
                .orElse(null);
    }

    private boolean isGroupDescriptionNotEqual(String groupProductDesBk, String productGroupDescription) {
        String normalizedGrupoProductoDesBK = Optional.ofNullable(groupProductDesBk).orElse("").toUpperCase();
        String normalizedGrupoProductoDes = Optional.ofNullable(productGroupDescription).orElse("").toUpperCase();
        return !normalizedGrupoProductoDesBK.equals(normalizedGrupoProductoDes);
    }

    private String getImoCode(Integer imoId) {
        return imoRepository.findById(imoId)
                .map(Imo::getImoCode)
                .orElse("");
    }

    private boolean existsInMyDetail(List<MyDetail> myDetails, Integer catTypeId, Integer catSizeId,
                                     Integer quantity) {
        return myDetails.stream().anyMatch(detail -> Objects.equals(detail.getCatContainerTypeId(), catTypeId)
                && Objects.equals(detail.getCatSizeId(), catSizeId) && Objects.equals(detail.getQuantity(), quantity));
    }

    private boolean doNotTouchAndActiveExist(List<DODetail> doDetails) {
        return doDetails.stream()
                .anyMatch(detail -> detail.getDoNotTouch() != null
                        && detail.getDoNotTouch() == 1 && detail.getActive() != null && detail.getActive());
    }

    private void updateActiveDetails(Integer ediCoparnId, Integer userRegistrationId, List<DODetail> doDetails) {
        Set<Integer> detailIds = getCargoDocumentDetailIds(doDetails);
        if (detailIds.isEmpty()) {
            return;
        }
        cargoDocumentDetailRepository.updateActiveDetails(String.format(FORMAT, ediCoparnId), userRegistrationId,
                List.copyOf(detailIds), "del_by_edi ");
    }

    private Integer countMatchingCargoDocumentWithConditions(List<DODetail> doDetails) {
        Set<Integer> detailIds = getCargoDocumentDetailIdsWithDoNotTouchFlagFalse(doDetails);

        if (detailIds.isEmpty()) {
            return 0;
        }
        return cargoDocumentDetailRepository.findActiveCargoDocumentDetailCount(List.copyOf(detailIds));
    }

    private void updateActiveDetailsWhenDoNotTouchIsFalse(Integer ediCoparnId, Integer userRegistrationId, List<DODetail> doDetails) {
        Set<Integer> detailIds = getCargoDocumentDetailIdsWithDoNotTouchFlagFalse(doDetails);
        if (detailIds.isEmpty()) {
            return;
        }
        cargoDocumentDetailRepository.updateActiveDetails(String.format(FORMAT, ediCoparnId), userRegistrationId,
                List.copyOf(detailIds), "del_by_edi2 ");
    }

    private Set<Integer> getCargoDocumentDetailIdsWithDoNotTouchFlagFalse(List<DODetail> doDetails) {
        if (CollectionUtils.isEmpty(doDetails)) {
            return Collections.emptySet();
        }
        return doDetails.stream()
                .filter(detail -> detail.getCargoDocumentDetailId() != null
                        && detail.getDoNotTouch() != null && detail.getDoNotTouch().equals(0))
                .map(DODetail::getCargoDocumentDetailId)
                .collect(Collectors.toSet());
    }

    private void updateBookingDetails(List<DODetail> doDetails, Integer userId, Integer ediCoparnId) {

        if (CollectionUtils.isEmpty(doDetails)) {
            return;
        }

        Map<Integer, Long> groupedDetails = doDetails.stream()
                .collect(Collectors.groupingBy(
                        DODetail::getBookingDetailId,
                        Collectors.mapping(
                                DODetail::getCargoDocumentDetailId,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        list -> list.stream().filter(Objects::nonNull).count()
                                )
                        )
                ));

        for (Map.Entry<Integer, Long> entry : groupedDetails.entrySet()) {
            Integer bookingDetailId = entry.getKey();
            Integer quantity = entry.getValue().intValue();

            bookingDetailRepository.updateReservationQuantity(
                    quantity,
                    userId,
                    "edi_upd_Qty",
                    ediCoparnId,
                    bookingDetailId
            );
        }
    }

    private boolean existsInMyDetailmatchingContainerTypeAndSizeId(List<MyDetail> myDetails, Integer catTypeId,
                                                                   Integer catSizeId) {
        return myDetails.stream().anyMatch(detail -> Objects.equals(detail.getCatContainerTypeId(), catTypeId)
                && Objects.equals(detail.getCatSizeId(), catSizeId));
    }

    private Integer getQuantity(List<MyDetail> myDetails, Integer catSizeId, Integer catContainerTypeId) {

        if (myDetails == null) {
            return null;
        }

        MyDetail matchingDetail = myDetails.stream()
                .filter(detail -> detail != null
                        && catSizeId.equals(detail.getCatSizeId())
                        && catContainerTypeId.equals(detail.getCatContainerTypeId()))
                .findFirst()
                .orElse(null);
        return (matchingDetail != null) ? matchingDetail.getQuantity() : null;
    }

    private Integer countDetails(List<DODetail> doDetails, Integer catContainerTypeId, Integer catSizeId) {
        if (doDetails == null) {
            return 0;
        }

        long count = doDetails.stream()
                .filter(detail -> detail != null &&
                        catContainerTypeId.equals(detail.getContainerTypeManifestedId()) &&
                        catSizeId.equals(detail.getSizeManifestedId()) &&
                        Objects.equals(detail.getActive(), true))
                .count();

        return (int) count;
    }

    private boolean isExistQConditionMet(Integer existQ, Integer existQ2) {
        int value1 = (existQ != null) ? existQ : 0;
        int value2 = (existQ2 != null) ? existQ2 : 0;

        return value1 == value2 && value1 > 0;
    }

    private boolean isValidContainerCondition(Integer secondaryContainerDimensionId, Integer secondaryContainerTypeId,
                                              Integer secondaryReservedQuantity) {
        return (Optional.ofNullable(secondaryContainerDimensionId).orElse(0) > 0) &&
                (Optional.ofNullable(secondaryContainerTypeId).orElse(0) > 0) &&
                (secondaryReservedQuantity > 0);
    }

    private boolean isRefrigeratedContainer(Integer catalogId) {
        return catalogRepository.isRefrigeratedContainer(catalogId);
    }

    private boolean isNullOrZero(Integer value) {
        return value == null || value == 0;
    }
}
