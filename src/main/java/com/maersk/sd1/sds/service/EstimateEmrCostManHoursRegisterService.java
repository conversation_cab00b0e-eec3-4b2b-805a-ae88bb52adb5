package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.EstimateEmrCostManHours;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursRegisterInput;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class EstimateEmrCostManHoursRegisterService {

    private static final Logger logger = LogManager.getLogger(EstimateEmrCostManHoursRegisterService.class);

    private final EstimateEmrCostManHoursRepository estimateEmrCostManHoursRepository;
    private final MessageLanguageRepository messageLanguageService;
    private final UserRepository userRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final CatalogRepository catalogRepository;
    private final CurrencyRepository currencyRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final CompanyRepository companyRepository;

    @Transactional
    public EstimateEmrCostManHoursRegisterOutput registerCostManHour(EstimateEmrCostManHoursRegisterInput.Input input) {
        EstimateEmrCostManHoursRegisterOutput output = new EstimateEmrCostManHoursRegisterOutput();
        try {
            logger.info("Starting registerCostManHour with input: {}", input);
            Integer existingId = findExistingRecord(input);
            if (existingId != null) {
                // Record exists
                output.setRespEstado(2);
                String translatedMessage = messageLanguageService.fnTranslatedMessage("ESTIMATE_COST_MAN_HOUR", 2, input.getLanguageId());
                output.setRespMensaje(translatedMessage + " ID: " + existingId);
                output.setRespNewId(existingId);
            } else {
                // No record, so we insert
                EstimateEmrCostManHours entity = createAndSaveEntity(input);
                output.setRespNewId(entity.getId());
                output.setRespEstado(1);
                String translatedMessage = messageLanguageService.fnTranslatedMessage("ESTIMATE_COST_MAN_HOUR", 1, input.getLanguageId());
                output.setRespMensaje(translatedMessage);
            }
        } catch (Exception e) {
            logger.error("Error in registerCostManHour", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }

    private Integer findExistingRecord(EstimateEmrCostManHoursRegisterInput.Input input) {
        // This method replicates the SELECT check from the stored procedure.
        if (input.getLineaNavieraId() == null && input.getChassisOwnerId() == null) {
            // If both are null, the condition "(linea_naviera_id = x OR chassis_owner_id = y)" can't match.
            return null;
        }
        return estimateEmrCostManHoursRepository.findExistingId(
                input.getUnidadNegocioId(),
                input.getSubUnidadNegocioId(),
                input.getCatTipoEstimadoId(),
                input.getCatEquipmentCategory(),
                input.getLineaNavieraId(),
                input.getChassisOwnerId()
        );
    }

    private EstimateEmrCostManHours createAndSaveEntity(EstimateEmrCostManHoursRegisterInput.Input input) {
        // This method replicates the INSERT logic from the stored procedure.
        EstimateEmrCostManHours estimateEmrCostManHours = new EstimateEmrCostManHours();

        estimateEmrCostManHours.setBusinessUnit(
                businessUnitRepository.findById(input.getUnidadNegocioId()).orElseThrow(() -> new RuntimeException("BusinessUnit not found"))
        );
        estimateEmrCostManHours.setSubBusinessUnit(
                businessUnitRepository.findById(input.getSubUnidadNegocioId()).orElseThrow(() -> new RuntimeException("SubBusinessUnit not found"))
        );

        if (input.getLineaNavieraId() != null) {
            ShippingLine shippingLine = shippingLineRepository.findById(input.getLineaNavieraId()).orElse(null);
            estimateEmrCostManHours.setShippingLine(shippingLine);
        }

        estimateEmrCostManHours.setCatTypeEstimate(
                catalogRepository.findById(input.getCatTipoEstimadoId()).orElseThrow(() -> new RuntimeException("Catalog not found"))
        );

        estimateEmrCostManHours.setCurrency(
                currencyRepository.findById(input.getMonedaId()).orElseThrow(() -> new RuntimeException("Currency not found"))
        );

        double costoHoraHombre = input.getCostoHoraHombre();
        if (costoHoraHombre > 999.99) {
            throw new IllegalArgumentException("CostoHoraHombre exceeds the allowed limit.");
        }
        estimateEmrCostManHours.setCostManHour((int) Math.round(costoHoraHombre));        estimateEmrCostManHours.setActive(input.getActivo());
        estimateEmrCostManHours.setRegistrationDate(LocalDateTime.now());

        estimateEmrCostManHours.setCatCategoryEquipment(input.getCatEquipmentCategory());
        if (input.getChassisOwnerId() != null) {
            Company chassisOwner = companyRepository.findById(input.getChassisOwnerId()).orElse(null);
            estimateEmrCostManHours.setChassisOwnerCompany(chassisOwner);
        }

        // We need the user registration.
        User user = userRepository.findById(input.getUsuarioRegistroId()).orElseThrow(() -> new RuntimeException("User not found"));
        estimateEmrCostManHours.setRegistrationUser(user);

        // Save to get the generated ID:
        EstimateEmrCostManHours saved = estimateEmrCostManHoursRepository.save(estimateEmrCostManHours);
        return saved;
    }
}