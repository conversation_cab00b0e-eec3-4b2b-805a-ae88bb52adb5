package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.VesselProgramming;
import com.maersk.sd1.common.repository.VesselProgrammingRepository;
import com.maersk.sd1.sds.controller.dto.ShipProgramGetShipTripInput;
import com.maersk.sd1.sds.controller.dto.ShipProgramGetShipTripOutput;
import com.maersk.sd1.sds.dto.ShipProgramGetShipTripOutputProjection;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

//sds.programacion_nave_obtener_nave_viaje
@RequiredArgsConstructor
@Service
public class ShipProgramGetShipTripService {

    private static final Logger logger = LogManager.getLogger(ShipProgramGetShipTripService.class.getName());

    private final VesselProgrammingRepository vesselProgrammingRepository;

    public ShipProgramGetShipTripOutput getScheduleDetails(ShipProgramGetShipTripInput.Input input) {

        logger.info("Entering getScheduleDetails with input: {}", input);
        if(input.getBusinessUnitId() == null || input.getSubBusinessUnitId() == null){
            logger.error("Business Unit Id or Sub Business Unit Id is null");
            return null;
        }

        ShipProgramGetShipTripOutput output = null;
        List<ShipProgramGetShipTripOutputProjection> finalOutput = vesselProgrammingRepository.findVesselandName(input.getBusinessUnitId(), input.getSubBusinessUnitId(), input.getName(), input.getVoyage());

        output = generateOutput(finalOutput);
        logger.info("Exiting getScheduleDetails with output: {}", output);
        return output;
    }

    private ShipProgramGetShipTripOutput generateOutput(List<ShipProgramGetShipTripOutputProjection> projections) {
        ShipProgramGetShipTripOutput finalOutput = new ShipProgramGetShipTripOutput();
        List<ShipProgramGetShipTripOutput.Output> outputList = new ArrayList<>();

        logger.info("Generating output for {} projections", projections.size());
        for (ShipProgramGetShipTripOutputProjection projection : projections) {
            ShipProgramGetShipTripOutput.Output output = new ShipProgramGetShipTripOutput.Output();
            VesselProgramming vesselProgramming = projection.getVesselClass();

            output.setId(vesselProgramming.getId());
            output.setName(projection.getName());
            output.setVoyage(vesselProgramming.getVoyage());
            output.setEtaDate(vesselProgramming.getEtaDate());
            output.setEtdDate(vesselProgramming.getEtdDate());
            output.setActive(vesselProgramming.getActive());
            output.setRegistrationDate(vesselProgramming.getRegistrationDate());
            output.setModificationDate(vesselProgramming.getModificationDate());

            if(vesselProgramming.getBusinessUnit()!=null){
                output.setBusinessUnitId(vesselProgramming.getBusinessUnit().getId());
            }
            if(vesselProgramming.getSubBusinessUnit()!=null){
                output.setBusinessUnitId(vesselProgramming.getSubBusinessUnit().getId());
            }
            if(vesselProgramming.getVessel()!=null){
                output.setVesselId(vesselProgramming.getVessel().getId());
            }
            if(vesselProgramming.getShippingAgencyCompany()!=null){
                output.setShippingAgencyCompanyId(vesselProgramming.getShippingAgencyCompany().getId());
            }
            if(vesselProgramming.getOpePortCompany()!=null){
                output.setOpePortCompanyId(vesselProgramming.getOpePortCompany().getId());
            }
            if(vesselProgramming.getCatCreationOrigin()!=null){
                output.setCatCreationOriginId(vesselProgramming.getCatCreationOrigin().getId());
            }

            outputList.add(output);
        }

        finalOutput.setOutputList(outputList);
        finalOutput.setCount(List.of(List.of(projections.size())));
        logger.info("Generated output for {} projections", projections.size());
        return finalOutput;
    }
}