package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.BookingEdiRepository;
import com.maersk.sd1.sds.dto.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class BookingEdiProcessService {

    private final BookingEdiRepository bookingEdiRepository;

    public BookingEdiProcessService(BookingEdiRepository bookingEdiRepository) {
        this.bookingEdiRepository = bookingEdiRepository;
    }

    @Transactional
    public void processUpdatesDocumentDetail(int bookingId, int documentoCargaId, int usuarioRegistroId) {
        List<ImoIdAndGrupoProductoDesDTO> imoIdAndGrupoProductoDes = bookingEdiRepository.findImoIdAndGrupoProductoDesByBookingId(bookingId);
        if (imoIdAndGrupoProductoDes.isEmpty()) {
            throw new IllegalArgumentException("Invalid booking ID: " + bookingId);
        }

        ImoIdAndGrupoProductoDesDTO result = imoIdAndGrupoProductoDes.get(0);
        Integer imoId = result.getImoId();
        String grupoProductoDes = result.getMercaderia();
        Integer catCondicionCargaId = result.getCatCondicionCargaId();


        Boolean cargaPeligrosa = imoId != null && imoId > 0;

        List<NewBKDetalleDTO> bookingDetails = bookingEdiRepository.fetchNewBKDetalle(bookingId);

        if (bookingDetails.isEmpty()) {
            return;
        }

        int nItem = 1;
        List<NewDODetalleDTO> doDetalleDTOs = new ArrayList<>();

        for (int position = 1; position <= bookingDetails.size(); position++) {
            List<BookingDetailPositionDTO> detailsByPosition = bookingEdiRepository.fetchDetails(bookingId);
            if (detailsByPosition.isEmpty()) {
                continue;
            }

            BookingDetailPositionDTO detail = detailsByPosition.get(0);

            int bookingDetalleId = detail.getBookingDetalleId();
            int cantidadSolicitada = detail.getCantidadReserva().intValue();
            int catTamanoId = detail.getCatTamanoId();
            Boolean cargaRefrigerada = bookingEdiRepository.isCargaRefrigerada(detail.getCatTipoContenedorId());


            for (int i = 0; i < cantidadSolicitada; i++) {
                NewDODetalleDTO doDetalleDTO = new NewDODetalleDTO();
                doDetalleDTO.setBookingId(bookingId);
                doDetalleDTO.setBookingDetalleId(bookingDetalleId);
                doDetalleDTO.setDoItem((short) nItem++);
                doDetalleDTO.setCatTamanoId((double) catTamanoId);
                doDetalleDTO.setCatTipoContenedorId((double)detail.getCatTipoContenedorId());
                doDetalleDTO.setCargaRefrigerada(cargaRefrigerada);
                doDetalleDTOs.add(doDetalleDTO);
            }
        }

        for (NewBKDetalleDTO bookingDetail : bookingDetails) {
            int tempBookingId = bookingDetail.getBookingDetalleId();

            List<DocumentoCargaDetalleDTO> documentoCargaData = bookingEdiRepository.fetchDocumentoCargaDetalleData(
                    documentoCargaId,
                    bookingId,
                    usuarioRegistroId,
                    catCondicionCargaId,
                    cargaPeligrosa,
                    grupoProductoDes,
                    tempBookingId
            );

            for (DocumentoCargaDetalleDTO row : documentoCargaData) {
                bookingEdiRepository.insertDocumentoCargaDetalle(
                        row.getDocumentoCargaId(), row.getProductoId(), row.getCatEmbalajeId(), row.getCantidadManifestada(), row.getPesoManifestado(),
                        row.getVolumenManifestado(), row.getCatUnidadMedidaPesoId(), row.getDiceContener(), row.getEsCargaPeligrosa(), row.getCargaRefrigerada(),
                        row.getMercaderia(), row.getCatCondicionCargaId(), row.getGateoutEmptyLiquidado(), row.getActivo(), row.getCatOrigenCreacionId(),
                        row.getUsuarioRegistroId(), row.getFechaRegistro().toString(), row.getCatTipoContenedorId(), row.getCatTamanoId(),
                        row.getCatUnidadMedidaCantidadId(), row.getBookingDetalleId(), row.getTraceDocCargaDetalle()
                );
            }
        }
    }
}
