package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.sds.dto.TemplateGetInputDTO;
import com.maersk.sd1.sds.dto.TemplateGetOutputDTO;
import com.maersk.sd1.sds.exception.TemplateGetException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class TemplateGetService {

    private final SystemRuleRepository systemRuleRepository;

    @Autowired
    public TemplateGetService(SystemRuleRepository systemRuleRepository) {
        this.systemRuleRepository = systemRuleRepository;
    }

    public ResponseEntity<ResponseController<TemplateGetOutputDTO>> templateGetService(TemplateGetInputDTO.Root input) {
        if (input == null || input.getPrefix() == null || input.getPrefix().getInput() == null ||
                input.getPrefix().getInput().getTemplateName() == null) {
            throw new IllegalArgumentException("Invalid input");
        }

        try {
            String url = systemRuleRepository.findTemplateUrl(
                    input.getPrefix().getInput().getTemplateName(),
                    input.getPrefix().getInput().getLanguageId()
            );

            TemplateGetOutputDTO outputDTO = new TemplateGetOutputDTO();
            outputDTO.setUrl(Optional.ofNullable(url).map(u -> List.of(u)).orElse(null));

            ResponseController<TemplateGetOutputDTO> responseController = new ResponseController<>(outputDTO);
            return new ResponseEntity<>(responseController, HttpStatus.OK);
        } catch (Exception e) {
            throw new TemplateGetException("Database error", e);
        }
    }
}