package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.VesselProgrammingPortRepository;
import com.maersk.sd1.sds.dto.BookingPortSearchOutputDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BookingPortSearchService {

        private final VesselProgrammingPortRepository vesselProgrammingPortRepository;

        public List<BookingPortSearchOutputDTO> searchPorts(List<Integer> portIds, String portName, Integer shipProgrammingDetailId) {
                return vesselProgrammingPortRepository.searchPorts(portIds, portName, shipProgrammingDetailId,
                        PageRequest.of(0, 10));
        }
}