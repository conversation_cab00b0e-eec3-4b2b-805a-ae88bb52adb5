package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdiSetting;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ListOriginSiteHsdService {
    private final BookingEdiSettingRepository bookingEdiSettingRepository;

    @Autowired
    public ListOriginSiteHsdService(BookingEdiSettingRepository bookingEdiSettingRepository) {
        this.bookingEdiSettingRepository = bookingEdiSettingRepository;
    }

    private static final Integer MAERSK_LINE_ID = 4104;
    private static final boolean NOT_ACTIVE = false;

    public List<BookingEdiSetting> listOriginSiteHsdService() {

        return bookingEdiSettingRepository.findBookingDetails(MAERSK_LINE_ID, NOT_ACTIVE);
    }
}
