package com.maersk.sd1.sds.service;


import com.maersk.sd1.common.repository.VesselProgrammingPortRepository;
import com.maersk.sd1.sds.dto.VesselProgrammingPortListOutput;
import com.maersk.sd1.common.model.VesselProgrammingPort;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class VesselProgrammingPortService {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingPortService.class);

    private final VesselProgrammingPortRepository vesselProgrammingPortRepository;

    @Autowired
    public VesselProgrammingPortService(VesselProgrammingPortRepository vesselProgrammingPortRepository) {
        this.vesselProgrammingPortRepository = vesselProgrammingPortRepository;
    }

    @Transactional(readOnly = true)
    public List<VesselProgrammingPortListOutput> listActiveVesselProgrammingPorts(Integer shipProgrammingDetailId) {
        if (shipProgrammingDetailId == null) {
            logger.error("shipProgrammingDetailId cannot be null");
            throw new IllegalArgumentException("shipProgrammingDetailId cannot be null");
        }

        logger.info("Fetching VesselProgrammingPort list for shipProgrammingDetailId: {}", shipProgrammingDetailId);
        List<VesselProgrammingPort> vppList = vesselProgrammingPortRepository.findActiveByVesselProgrammingId(shipProgrammingDetailId);

        List<VesselProgrammingPortListOutput> dtoList = vppList.stream().map(vpp -> {
            VesselProgrammingPortListOutput dto = new VesselProgrammingPortListOutput();
            dto.setProgramacionNavePuertoId(vpp.getId());
            dto.setPuertoId(vpp.getPort().getId());
            dto.setPuerto(vpp.getPort().getPort());
            String portName = vpp.getPort().getName();
            String countryName = (vpp.getPort().getCountry() != null) ? vpp.getPort().getCountry().getName() : null;
            String puertoDescripcion = (countryName == null) ? portName : portName + " / " + countryName;
            dto.setPuertoDescripcion(puertoDescripcion);
            return dto;
        }).toList();

        return dtoList;
    }
}

