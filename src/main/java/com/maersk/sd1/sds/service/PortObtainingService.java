package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Port;
import com.maersk.sd1.sds.dto.PortObtainingOutput;
import com.maersk.sd1.common.repository.PortRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class PortObtainingService {

    private static final Logger logger = LogManager.getLogger(PortObtainingService.class);

    private final PortRepository portRepository;

    public PortObtainingService(PortRepository portRepository) {
        this.portRepository = portRepository;
    }

    @Transactional(readOnly = true)
    public List<PortObtainingOutput> getPort(Integer portId) {

        List<PortObtainingOutput> outputList = new ArrayList<>();

        PortObtainingOutput output = new PortObtainingOutput();
        try {
            // Attempt to find the port by ID
            Optional<Port> optionalPort = portRepository.findById(portId);
            if (optionalPort.isPresent()) {
                Port portEntity = optionalPort.get();
                // Map entity fields to output
                output.setPortId(portEntity.getId());
                output.setPort(portEntity.getPort());
                output.setName(portEntity.getName());
                if (portEntity.getCountry() != null) {
                    // We store the country ID if available
                    output.setCountryId(portEntity.getCountry().getId());
                }
                output.setActive(portEntity.getActive());
                output.setRegistrationDate(portEntity.getRegistrationDate());
                output.setModificationDate(portEntity.getModificationDate());
                logger.info("Port fetched successfully.");
            } else {
                logger.info("No port found for the provided ID.");
            }
        } catch (Exception e) {
            logger.error("Error while fetching port with ID {} : {}", portId, e.getMessage(), e);
        }

        outputList.add(output);
        return outputList;
    }
}

