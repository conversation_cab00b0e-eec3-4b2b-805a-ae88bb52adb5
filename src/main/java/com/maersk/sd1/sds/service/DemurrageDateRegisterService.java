package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.exception.ContainerProcessingException;
import com.maersk.sd1.sds.controller.dto.DemurrageDateRegisterInput;
import com.maersk.sd1.sds.controller.dto.DemurrageDateRegisterOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class DemurrageDateRegisterService {

    private static final Logger logger = LogManager.getLogger(DemurrageDateRegisterService.class);

    private final  CargoDocumentRepository cargoDocumentRepository;
    private final DemurrageReceptionRepository demurrageReceptionRepository;
    private final DemurrageReceptionSettingBURepository demurrageReceptionSettingBURepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final ContainerRepository containerRepository;

    @Autowired
    public DemurrageDateRegisterService(CargoDocumentRepository cargoDocumentRepository, DemurrageReceptionRepository demurrageReceptionRepository, DemurrageReceptionSettingBURepository demurrageReceptionSettingBURepository, VesselProgrammingContainerRepository vesselProgrammingContainerRepository, ContainerRepository containerRepository) {
        this.cargoDocumentRepository = cargoDocumentRepository;
        this.demurrageReceptionRepository = demurrageReceptionRepository;
        this.demurrageReceptionSettingBURepository = demurrageReceptionSettingBURepository;
        this.vesselProgrammingContainerRepository = vesselProgrammingContainerRepository;
        this.containerRepository = containerRepository;
    }


    private static final Long CAT_ORIGEN_CREACION_SOBRESTADIA_ID = 42990L;

    @Transactional
    public DemurrageDateRegisterOutput registerDemurrageDate(DemurrageDateRegisterInput.Input input) {
        DemurrageDateRegisterOutput output = new DemurrageDateRegisterOutput();
        output.setRespEstado(0);
        output.setRespNewId(0);
        output.setRespMensaje("");
        try {
            CargoDocument cargoDocument = cargoDocumentRepository
                    .findActiveCargoDocumentById(input.getDocumentoCargaId())
                    .orElseThrow(() -> new IllegalArgumentException("No active CargoDocument found for documento_carga_id=" + input.getDocumentoCargaId()));

            Integer shippingLineId = (cargoDocument.getShippingLine() != null)
                    ? cargoDocument.getShippingLine().getId()
                    : null;

            if (shippingLineId != null) {
                if (shippingLineId == 4106) {
                    shippingLineId = 4104;
                } else if (shippingLineId == 4107 || shippingLineId == 4108) {
                    shippingLineId = 4102;
                }
            }

            Integer businessUnitId = (cargoDocument.getCatCargoOrigin() != null && cargoDocument.getCatCargoOrigin().getBusinessUnit() != null)
                    ? cargoDocument.getCatCargoOrigin().getBusinessUnit().getId()
                    : input.getSubUnidadNegocioId().intValue();

            DemurrageReceptionSettingBU foundSetting = demurrageReceptionSettingBURepository
                    .findAllMatching(BigDecimal.valueOf(businessUnitId), shippingLineId)
                    .stream()
                    .findFirst()
                    .orElse(null);

            DemurrageReceptionSetting setting = null;
            if (foundSetting != null) {
                setting = foundSetting.getSeteoRecepcionSobreestadia();
            }

            for (DemurrageDateRegisterInput.ContainerInfo containerInfo : input.getContenedores()) {
                processContainerInfo(input, cargoDocument, setting, containerInfo);
            }

            output.setRespEstado(1);
            output.setRespMensaje("");

        } catch (Exception e) {
            logger.error("Error in registerDemurrageDate", e);
            output.setRespEstado(2);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }

    public void processContainerInfo(DemurrageDateRegisterInput.Input input, CargoDocument cargoDocument, DemurrageReceptionSetting setting, DemurrageDateRegisterInput.ContainerInfo containerInfo) {
        try {
            String dateStr = containerInfo.getDemurrageDate();
            LocalDate demurrageLocalDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));

            VesselProgrammingContainer vesselProgrammingContainer = vesselProgrammingContainerRepository
                    .findByVesselProgrammingDetailIdAndContainerIdAndActive(
                            cargoDocument.getVesselProgrammingDetail().getId(),
                            containerInfo.getContainerId(),
                            true
                    )
                    .orElseThrow(
                            () -> new IllegalArgumentException("No active VesselProgrammingContainer found for container_id="
                                    + containerInfo.getContainerId())
                    );

            vesselProgrammingContainer.setDemurrageDate(demurrageLocalDate.atStartOfDay());
            vesselProgrammingContainer.setTraceProgVesCnt("upd_sobrestadía");
            vesselProgrammingContainer.setModificationDate(LocalDateTime.now());

            vesselProgrammingContainerRepository.save(vesselProgrammingContainer);

            if (setting != null) {
                Container containerDB = containerRepository.findById(containerInfo.getContainerId())
                        .orElseThrow(() -> new IllegalArgumentException("No Container found with id=" + containerInfo.getContainerId()));

                DemurrageReception reception = new DemurrageReception();
                reception.setRegistrationDate(LocalDateTime.now());
                reception.setDateSobretadia(demurrageLocalDate.atStartOfDay());

                BusinessUnit subBusinessUnit = new BusinessUnit();
                subBusinessUnit.setId(Math.toIntExact(input.getSubUnidadNegocioId()));
                reception.setSubBusinessUnit(subBusinessUnit);

                Container container = new Container();
                container.setId(containerInfo.getContainerId());
                reception.setContainer(container);

                VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
                vesselProgrammingDetail.setId(cargoDocument.getVesselProgrammingDetail().getId());
                reception.setVesselProgrammingDetail(vesselProgrammingDetail);

                reception.setCoreorBl(cargoDocument.getCargoDocument());

                DemurrageReceptionSetting settingEntity = new DemurrageReceptionSetting();
                settingEntity.setId(setting.getId());
                reception.setSeteoRecepcionSobrestadia(settingEntity);

                reception.setActive(true);

                User registrationUser = new User();
                registrationUser.setId(Math.toIntExact(input.getUsuarioRegistroId()));
                reception.setRegistrationUser(registrationUser);

                Catalog catOrigenCreacionSobrestadia = new Catalog();
                catOrigenCreacionSobrestadia.setId(Math.toIntExact(CAT_ORIGEN_CREACION_SOBRESTADIA_ID));
                reception.setCatOrigenCreacionSobrestadia(catOrigenCreacionSobrestadia);

                reception.setCoreorCommentUser(input.getComentarioUsuario());
                reception.setSobrestadiaConformable(true);
                reception.setCatReasonIncomeSobrestadia(Math.toIntExact(input.getMotivoId()));

                reception.setCoreorContainer(containerDB.getContainerNumber());

                demurrageReceptionRepository.save(reception);
            }
        }catch (Exception e) {
            throw new ContainerProcessingException("Error processing container info", e);
        }
    }
}