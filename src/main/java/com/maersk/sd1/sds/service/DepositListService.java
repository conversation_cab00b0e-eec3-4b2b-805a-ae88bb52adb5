package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Depot;
import com.maersk.sd1.sds.dto.DepositListInput;
import com.maersk.sd1.sds.dto.DepositListOutput;
import com.maersk.sd1.common.repository.DepotRepository;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class DepositListService {

    private final DepotRepository depotRepository;

    @Transactional(readOnly = true)
    public DepositListOutput findDepots(@Valid DepositListInput.Input inputData) {
        // Null-check and set defaults for page and size as in SP logic
        Integer page = (inputData.getPage() == null) ? 1 : inputData.getPage();
        // We'll do a preliminary fetch of total elements if size is null, to replicate the SP logic
        Long totalElements = 0L;
        if (inputData.getSize() == null) {
            // find how many possible results in total, ignoring pagination.
            totalElements = depotRepository.count(buildSpecifications(inputData));
        }
        Integer size;
        if (inputData.getSize() == null) {
            size = (totalElements == 0) ? 1 : totalElements.intValue();
        } else {
            size = inputData.getSize();
        }

        Pageable pageable = PageRequest.of(page - 1, size);
        Specification<Depot> spec = buildSpecifications(inputData);
        Page<Depot> depotPage = depotRepository.findAll(spec, pageable);

        // Prepare the output object
        DepositListOutput output = new DepositListOutput();
        List<Long> count = List.of(depotPage.getTotalElements());
        output.setTotalRegistros(List.of(count));
        List<DepositListOutput.DepotData> depotDataList = new ArrayList<>();

        for (Depot depot : depotPage) {
            DepositListOutput.DepotData depotData = new DepositListOutput.DepotData();
            depotData.setDepositoId(depot.getId());
            depotData.setUnidadNegocioId(
                    depot.getBusinessUnit() != null ? depot.getBusinessUnit().getId() : null
            );
            depotData.setCodigoDeposito(depot.getCodeDeposit());
            depotData.setNombreDeposito(depot.getNameDeposit());
            depotData.setDireccionDeposito(depot.getAddressDeposit());
            depotData.setActivo(depot.getActive());
            depotData.setFechaRegistro(depot.getRegistrationDate());
            depotData.setFechaModificacion(depot.getModificationDate());
            depotData.setSubUnidadNegocioId(
                    depot.getSubBusinessUnit() != null ? depot.getSubBusinessUnit().getId() : null
            );
            depotData.setDepositoDefault(depot.getDepositDefault());
            depotData.setCatCodigoAduanaId(
                    depot.getCatCodigoAduana() != null ? depot.getCatCodigoAduana().getId() : null
            );
            depotData.setCatClaseOperadorAduanaId(
                    depot.getCatClaseOperadorAduana() != null ? depot.getCatClaseOperadorAduana().getId() : null
            );
            depotData.setUsuarioRegistroId(
                    depot.getRegistrationUser() != null ? depot.getRegistrationUser().getId() : null
            );
            depotData.setUsuarioModificacionId(
                    depot.getModificationUser() != null ? depot.getModificationUser().getId() : null
            );
            if (depot.getRegistrationUser() != null) {
                depotData.setUsuarioRegistroNombres(depot.getRegistrationUser().getNames());
                // Combine firstLastName + secondLastName
                StringBuilder regApellidos = new StringBuilder();
                if (depot.getRegistrationUser().getFirstLastName() != null) {
                    regApellidos.append(depot.getRegistrationUser().getFirstLastName());
                }
                if (depot.getRegistrationUser().getSecondLastName() != null) {
                    regApellidos.append(" ").append(depot.getRegistrationUser().getSecondLastName());
                }
                depotData.setUsuarioRegistroApellidos(regApellidos.toString().trim());
            }
            if (depot.getModificationUser() != null) {
                depotData.setUsuarioModificacionNombres(depot.getModificationUser().getNames());
                // Combine firstLastName + secondLastName
                StringBuilder modApellidos = new StringBuilder();
                if (depot.getModificationUser().getFirstLastName() != null) {
                    modApellidos.append(depot.getModificationUser().getFirstLastName());
                }
                if (depot.getModificationUser().getSecondLastName() != null) {
                    modApellidos.append(" ").append(depot.getModificationUser().getSecondLastName());
                }
                depotData.setUsuarioModificacionApellidos(modApellidos.toString().trim());
            }

            depotDataList.add(depotData);
        }
        output.setDepots(depotDataList);
        return output;
    }

    public Specification<Depot> buildSpecifications(DepositListInput.Input inputData) {
        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

            // deposito_id
            if (inputData.getDepositoId() != null) {
                predicates.add(cb.equal(root.get("id"), inputData.getDepositoId()));
            }
            // unidad_negocio_id
            if (inputData.getUnidadNegocioId() != null) {
                predicates.add(
                        cb.equal(root.get("businessUnit").get("id"), inputData.getUnidadNegocioId())
                );
            }
            // codigo_deposito
            if (inputData.getCodigoDeposito() != null && !inputData.getCodigoDeposito().isEmpty()) {
                predicates.add(
                        cb.like(cb.lower(root.get("codeDeposit")), "%" + inputData.getCodigoDeposito().toLowerCase() + "%")
                );
            }
            // nombre_deposito
            if (inputData.getNombreDeposito() != null && !inputData.getNombreDeposito().isEmpty()) {
                predicates.add(
                        cb.like(cb.lower(root.get("nameDeposit")), "%" + inputData.getNombreDeposito().toLowerCase() + "%")
                );
            }
            // direccion_deposito
            if (inputData.getDireccionDeposito() != null && !inputData.getDireccionDeposito().isEmpty()) {
                predicates.add(
                        cb.like(cb.lower(root.get("addressDeposit")), "%" + inputData.getDireccionDeposito().toLowerCase() + "%")
                );
            }
            // activo
            if (inputData.getActivo() != null) {
                predicates.add(cb.equal(root.get("active"), inputData.getActivo()));
            }

            // fecha_registro range
            LocalDate fRegMin = inputData.getFechaRegistroMin();
            LocalDate fRegMax = inputData.getFechaRegistroMax();
            if (fRegMin != null && fRegMax != null) {
                // registrationDate >= fRegMin at 00:00 and < fRegMax + 1 day
                predicates.add(
                        cb.greaterThanOrEqualTo(root.get("registrationDate"), fRegMin.atStartOfDay())
                );
                predicates.add(
                        cb.lessThan(root.get("registrationDate"), fRegMax.plusDays(1).atStartOfDay())
                );
            }

            // fecha_modificacion range
            LocalDate fModMin = inputData.getFechaModificacionMin();
            LocalDate fModMax = inputData.getFechaModificacionMax();
            if (fModMin != null && fModMax != null) {
                // modificationDate >= fModMin at 00:00 and < fModMax + 1 day
                predicates.add(
                        cb.greaterThanOrEqualTo(root.get("modificationDate"), fModMin.atStartOfDay())
                );
                predicates.add(
                        cb.lessThan(root.get("modificationDate"), fModMax.plusDays(1).atStartOfDay())
                );
            }

            // sub_unidad_negocio_id
            if (inputData.getSubUnidadNegocioId() != null) {
                predicates.add(
                        cb.equal(root.get("subBusinessUnit").get("id"), inputData.getSubUnidadNegocioId())
                );
            }
            // deposito_default
            if (inputData.getDepositoDefault() != null) {
                predicates.add(
                        cb.equal(root.get("depositDefault"), inputData.getDepositoDefault())
                );
            }
            // cat_codigo_aduana_id.
            if (inputData.getCatCodigoAduanaId() != null) {
                predicates.add(
                        cb.equal(root.get("catCodigoAduana").get("id"), inputData.getCatCodigoAduanaId())
                );
            }
            // cat_clase_operador_aduana_id
            if (inputData.getCatClaseOperadorAduanaId() != null) {
                predicates.add(
                        cb.equal(root.get("catClaseOperadorAduana").get("id"), inputData.getCatClaseOperadorAduanaId())
                );
            }

            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
        };
    }
}