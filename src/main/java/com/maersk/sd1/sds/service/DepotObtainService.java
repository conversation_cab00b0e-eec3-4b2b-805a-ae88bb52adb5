package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.DepotObtainOutput;
import com.maersk.sd1.common.repository.DepotRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DepotObtainService {

    private static final Logger logger = LogManager.getLogger(DepotObtainService.class.getName());

    private final DepotRepository depotRepository;

    public DepotObtainService(DepotRepository depotRepository) {
        this.depotRepository = depotRepository;
    }

    @Transactional(readOnly = true)
    public DepotObtainOutput getDepotById(Integer depotId) {
        logger.info("Fetching Depot info for depot_id: {}", depotId);
        DepotObtainOutput output = depotRepository.findDepotObtainDataById(depotId);
        if (output == null) {
            logger.warn("No Depot found for depot_id: {}", depotId);
        }
        return output;
    }
}