package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdi;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.BookingEdiRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class BookingEditService {

    private final BookingEdiRepository bookingEdiRepository;

    public BookingEditService(BookingEdiRepository bookingEdiRepository) {
        this.bookingEdiRepository = bookingEdiRepository;
    }

    public LocalDateTime getCurrentTime() {
        return LocalDateTime.now();
    }

    @Transactional
    public int updateDismissedRecords() {
        LocalDateTime thresholdDate = getCurrentTime().minusDays(20);

        List<BookingEdi> recordsToUpdate = bookingEdiRepository.findRecordsToDismiss(thresholdDate);

        for (BookingEdi bookingEdiRecord : recordsToUpdate) {
            Catalog dismissedStatus = new Catalog();
            dismissedStatus.setId(48274);

            User systemUser = new User();
            systemUser.setId(1);

            bookingEdiRecord.setCatBkEdiStatus(dismissedStatus);
            bookingEdiRecord.setBkEdiProcessedComment("Dismissed by old (20 days)");
            bookingEdiRecord.setProcessedUser(systemUser);
            bookingEdiRecord.setDateProcessedCoparn(LocalDateTime.now());
            bookingEdiRecord.setRegistrationUser(systemUser);
        }

        bookingEdiRepository.saveAll(recordsToUpdate);

        return recordsToUpdate.size();
    }
}
