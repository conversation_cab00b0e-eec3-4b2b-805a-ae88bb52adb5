package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.BookingEdiSettingInputDTO;
import com.maersk.sd1.sds.dto.BookingEdiSettingOutputDTO;
import com.maersk.sd1.common.model.BookingEdiSetting;
import com.maersk.sd1.common.model.BookingEdiSettingBU;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import com.maersk.sd1.common.repository.BookingEdiSettingBURepository;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class BookingEdiSettingService {

    private static final Logger logger = LogManager.getLogger(BookingEdiSettingService.class);

    private final BookingEdiSettingRepository bookingEdiSettingRepository;
    private final BookingEdiSettingBURepository bookingEdiSettingBURepository;
    private final ShippingLineRepository shippingLineRepository;
    private final UserRepository userRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final CatalogRepository catalogRepository;

    @Transactional
    public BookingEdiSettingOutputDTO createBookingEdiSetting(BookingEdiSettingInputDTO.Input input) {
        BookingEdiSettingOutputDTO output = new BookingEdiSettingOutputDTO();
        try {
            logger.info("Starting createBookingEdiSetting with input: {}", input);

            BookingEdiSetting bookingEdiSetting = new BookingEdiSetting();
            setBasicDetails(input, bookingEdiSetting);
            setCatalogDetails(input, bookingEdiSetting);
            setUserAndBusinessUnit(input, bookingEdiSetting);

            bookingEdiSetting = bookingEdiSettingRepository.save(bookingEdiSetting);
            saveBookingEdiSettingDetails(input, bookingEdiSetting);

            output.setRespEstado(1);
            output.setRespMensaje("Registration completed successfully.");
            output.setRespNewId(bookingEdiSetting.getId());

        } catch (Exception e) {
            logger.error("Error creating BookingEdiSetting", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }

    void setBasicDetails(BookingEdiSettingInputDTO.Input input, BookingEdiSetting bookingEdiSetting) {
        bookingEdiSetting.setBkEdiDescription(input.getBkEdiDescription());
        bookingEdiSetting.setAzureId(input.getAzureId());
        bookingEdiSetting.setBkEdiSftpId(input.getBkEdiSftpId());
        bookingEdiSetting.setBkEdiFtpId(input.getBkEdiFtpId());
        bookingEdiSetting.setBkEdiFolderRoute(input.getBkEdiFolderRoute());
        bookingEdiSetting.setDownloadFileExtension(input.getDownloadFileExtension());
        bookingEdiSetting.setEdi_move_route(input.getEdiMoveRoute());
        bookingEdiSetting.setAllowCreateAutomaticVesselProgramming(input.getAllowCreateAutomaticVesselProgramming());
        bookingEdiSetting.setAllowCreateAutomaticCustomer(input.getAllowCreateAutomaticCustomer());
        bookingEdiSetting.setIsHistorical(input.getIsHistorical());
        bookingEdiSetting.setDeactivationDate(input.getDeactivationDate());
        bookingEdiSetting.setDeactivationReason(input.getDeactivationReason());
        bookingEdiSetting.setActive(input.getActive());
        bookingEdiSetting.setFilenameMask(input.getFilenameMask());
    }

    void setCatalogDetails(BookingEdiSettingInputDTO.Input input, BookingEdiSetting bookingEdiSetting) {
        bookingEdiSetting.setShippingLine(shippingLineRepository.findById(input.getShippingLineId()).orElse(null));
        bookingEdiSetting.setCatCanalRecepcionCoparn(catalogRepository.findById(input.getCatReceptionChId()).orElse(null));
        bookingEdiSetting.setCatModoProcesarCoparn(catalogRepository.findById(input.getCatProcessModeId()).orElse(null));
        bookingEdiSetting.setCatBkEdiMessageType(catalogRepository.findById(input.getCatBkEdiMessageTypeId()).orElse(null));
        bookingEdiSetting.setCatOwnerEdiBooking(catalogRepository.findById(input.getCatOwnerEdiBookingId()).orElse(null));
    }

    void setUserAndBusinessUnit(BookingEdiSettingInputDTO.Input input, BookingEdiSetting bookingEdiSetting) {
        User registrationUser = userRepository.findById(input.getUserRegistrationId()).orElse(null);
        bookingEdiSetting.setRegistrationUser(registrationUser);
        bookingEdiSetting.setRegistrationDate(LocalDateTime.now());

        if (input.getBusinessUnitId() != null) {
            BusinessUnit businessUnit = businessUnitRepository.findById(input.getBusinessUnitId()).orElse(null);
            bookingEdiSetting.setBusinessUnit(businessUnit);
        }
    }

    void saveBookingEdiSettingDetails(BookingEdiSettingInputDTO.Input input, BookingEdiSetting bookingEdiSetting) {
        if (input.getDetail() != null && !input.getDetail().isEmpty()) {
            for (BookingEdiSettingInputDTO.BookingEdiSettingDetailInput d : input.getDetail()) {
                BookingEdiSettingBU bu = new BookingEdiSettingBU();
                bu.setBookingEdiSetting(bookingEdiSetting);

                if (d.getSubBusinessUnitId() != null) {
                    bu.setSubBusinessUnit(businessUnitRepository.findById(d.getSubBusinessUnitId()).orElse(null));
                }
                bu.setBkEdiApplyCopySend(d.getApplySendCopy());

                if (d.getCatForwardChannelId() != null) {
                    bu.setCatBkEdiForwardChannel(catalogRepository.findById(d.getCatForwardChannelId()).orElse(null));
                }
                bu.setBkEdiForwardSftpId(d.getForwardSftpId());
                bu.setBkEdiForwardFftpId(d.getForwardFtpId());
                bu.setBkEdiForwardFolderRoute(d.getForwardFolderRoute());
                bu.setActive(true);
                bu.setRegistrationUser(bookingEdiSetting.getRegistrationUser());
                bu.setRegistrationDate(LocalDateTime.now());
                bookingEdiSettingBURepository.save(bu);
            }
        }
    }
}
