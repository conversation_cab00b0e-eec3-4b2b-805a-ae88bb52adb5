package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ShippingLineInput;
import com.maersk.sd1.sds.controller.dto.ShippingLineOutput;
import com.maersk.sd1.sds.service.ShippingLineService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/ModuleSDS/module/sds/SDSLineaNavieraServiceImp")
@RestController
public class ShippingLineController {

    private static final Logger logger = LogManager.getLogger(ShippingLineController.class.getName());

    private final ShippingLineService shippingLineService;

    public ShippingLineController(ShippingLineService shippingLineService) {
        this.shippingLineService = shippingLineService;
    }

    @PostMapping("/sdslineaNavieraListar")
    public ResponseEntity<ResponseController<ShippingLineOutput>> shippingLineSearch(@RequestBody ShippingLineInput.Root request) {
        try {
            if (request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>(Constants.INVALID_INPUT));
            }

            ShippingLineInput.Input input = request.getPrefix().getInput();

            ShippingLineOutput shippingLineOutputs = shippingLineService.listShippingLines(input);

            return ResponseEntity.ok(new ResponseController<>(shippingLineOutputs));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(null));
        }
    }
}