package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.PortDeleteInput;
import com.maersk.sd1.sds.dto.PortDeleteOutput;
import com.maersk.sd1.sds.service.PortDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPuertoServiceImp")
public class PortDeleteController {

    private static final Logger logger = LogManager.getLogger(PortDeleteController.class);

    private final PortDeleteService portDeleteService;

    @Autowired
    public PortDeleteController(PortDeleteService portDeleteService) {
        this.portDeleteService = portDeleteService;
    }

    @PostMapping("/sdspuertoEliminar")
    public ResponseEntity<ResponseController<PortDeleteOutput>> deletePort(@RequestBody @Valid PortDeleteInput.Root request) {
        try {
            PortDeleteInput.Input input = request.getPrefix().getInput();
            PortDeleteOutput output = portDeleteService.deletePort(
                    input.getPortId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            PortDeleteOutput output = new PortDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}