package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingEdiSettingInputDTO;
import com.maersk.sd1.sds.dto.BookingEdiSettingOutputDTO;
import com.maersk.sd1.sds.service.BookingEdiSettingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
public class BookingEdiSettingController {

    private static final Logger logger = LogManager.getLogger(BookingEdiSettingController.class);

    private final BookingEdiSettingService bookingEdiSettingService;

    @PostMapping("/sdsseteoEdiCoparnRegistrar")
    public ResponseEntity<ResponseController<BookingEdiSettingOutputDTO>> createBookingEdiSettingRegister(
            @RequestBody @Valid BookingEdiSettingInputDTO.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(new BookingEdiSettingOutputDTO()));
            }

            logger.info("Request received createBookingEdiSettingRegister: {}", request);
            BookingEdiSettingInputDTO.Input input = request.getPrefix().getInput();
            BookingEdiSettingOutputDTO output = bookingEdiSettingService.createBookingEdiSetting(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            BookingEdiSettingOutputDTO output = new BookingEdiSettingOutputDTO();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            output.setRespNewId(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
