package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.service.BookingEditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/ediCoparn")
public class BookingEditController {

    private final BookingEditService bookingEdiService;

    @Autowired
    public BookingEditController(BookingEditService bookingEdiService) {
        this.bookingEdiService = bookingEdiService;
    }

    @PostMapping("/dismissOldRecords")
    public ResponseEntity<String> dismissOldRecords() {
        int updatedCount = bookingEdiService.updateDismissedRecords();
        return ResponseEntity.ok("Successfully updated " + updatedCount + " records.");
    }
}
