package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.controller.dto.ListBookingShipsInput;
import com.maersk.sd1.sds.controller.dto.ListBookingShipsOutput;
import com.maersk.sd1.sds.service.ListBookingShipsService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSBookingServiceImp")
public class ListBookingShipsController {

    private static final Logger logger = LogManager.getLogger(ListBookingShipsController.class.getName());

    private final ListBookingShipsService bookingNavesListarService;

    @PostMapping("/sdsbookingNavesListar")
    public ResponseEntity<ResponseController<List<ListBookingShipsOutput>>> listBookings(@RequestBody ListBookingShipsInput.Root request) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            ListBookingShipsInput.Input input = request.getPrefix().getInput();

            if (input.getSubBusinessUnitId() == null) {
                logger.warn("subBusinessUnitId is null. Returning empty result.");
                return ResponseEntity.badRequest().body(new ResponseController<>("Sub Business Unit Id is required"));
            }

            List<ListBookingShipsOutput> output = bookingNavesListarService.listBookings(input);
            return ResponseEntity.ok(new ResponseController<>(output));

        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(null));
        }
    }
}