package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselProgrammingUpdateInput;
import com.maersk.sd1.sds.dto.VesselProgrammingUpdateOutput;
import com.maersk.sd1.sds.service.VesselProgrammingUpdateService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalTime;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class VesselProgrammingUpdateController {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingUpdateController.class);

    private final VesselProgrammingUpdateService vesselProgrammingUpdateService;

    public VesselProgrammingUpdateController(VesselProgrammingUpdateService vesselProgrammingUpdateService) {
        this.vesselProgrammingUpdateService = vesselProgrammingUpdateService;
    }

    @PostMapping("/sdsactualizarProgramacionNave")
    public ResponseEntity<ResponseController<VesselProgrammingUpdateOutput>> updateVesselProgramming(@RequestBody @Valid VesselProgrammingUpdateInput.Root request) {
        try {
            VesselProgrammingUpdateInput.Input input = request.getPrefix().getInput();

            VesselProgrammingUpdateOutput output = vesselProgrammingUpdateService.updateVesselProgramming(
                    input.getProgramacionNaveId(),
                    input.getVesselId(),
                    input.getVoyage(),
                    input.getEtaDate().atStartOfDay(),
                    input.getEtdDate().atTime(LocalTime.MAX),
                    input.getShippingAgencyCompanyId(),
                    input.getOpePortCompanyId(),
                    input.getActive(),
                    input.getUserId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            VesselProgrammingUpdateOutput errorOutput = new VesselProgrammingUpdateOutput();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}

