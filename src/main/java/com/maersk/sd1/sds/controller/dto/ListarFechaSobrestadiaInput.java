package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ListarFechaSobrestadiaInput {

    @Data
    public static class Input {

        @JsonProperty("documento_carga_id")
        @NotNull
        private Integer documentoCargaId;

        @JsonProperty("idioma_id")
        private Integer idiomaId = 2; // default to Spanish if not provided
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}

