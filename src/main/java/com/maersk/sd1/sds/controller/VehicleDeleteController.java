package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VehicleDeleteInput;
import com.maersk.sd1.sds.dto.VehicleDeleteOutput;
import com.maersk.sd1.sds.service.VehicleDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSVehiculoServiceImp")
public class VehicleDeleteController {

    private static final Logger logger = LogManager.getLogger(VehicleDeleteController.class);

    private final VehicleDeleteService vehicleDeleteService;
    public VehicleDeleteController(VehicleDeleteService vehicleDeleteService) {
        this.vehicleDeleteService = vehicleDeleteService;
    }
    

    @PostMapping("/sdsvehiculoEliminar")
    public ResponseEntity<ResponseController<VehicleDeleteOutput>> sdgVehicleDelete(@RequestBody @Valid VehicleDeleteInput.Root request) {
        VehicleDeleteOutput output = new VehicleDeleteOutput();
        try {
            VehicleDeleteInput.Input input = request.getPrefix().getInput();
            output = vehicleDeleteService.deleteVehicle(
                    input.getVehicleId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing deleteVehicle.", e);
            output.setResponseStatus(0);
            output.setResponseMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
