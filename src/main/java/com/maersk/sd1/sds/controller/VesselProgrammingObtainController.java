package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingObtainInputDTO;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingObtainOutputDTO;
import com.maersk.sd1.sds.service.VesselProgrammingObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class VesselProgrammingObtainController {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingObtainController.class);

    private final VesselProgrammingObtainService vesselProgrammingObtainService;

    public VesselProgrammingObtainController(VesselProgrammingObtainService vesselProgrammingObtainService) {
        this.vesselProgrammingObtainService = vesselProgrammingObtainService;
    }
    @PostMapping("/sdsprogramacionNaveObtener")
    public ResponseEntity<ResponseController<List<VesselProgrammingObtainOutputDTO>>> vesselProgrammingObtain(
            @RequestBody @Valid VesselProgrammingObtainInputDTO.Root request) {
        try {
            Integer programacionNaveId = request.getPrefix().getInput().getProgrammingShipId();
            logger.info("Received request for VesselProgramming with programacionNaveId: {}", programacionNaveId);

            List<VesselProgrammingObtainOutputDTO> outputDTO = vesselProgrammingObtainService.obtainVesselProgramming(programacionNaveId);

            return ResponseEntity.ok(new ResponseController<>(outputDTO));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(e.getMessage()));
        }
    }
}