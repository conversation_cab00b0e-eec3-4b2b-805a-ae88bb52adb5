package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ContainerDeleteInput;
import com.maersk.sd1.sds.controller.dto.ContainerDeleteOutput;
import com.maersk.sd1.sds.service.ContainerDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSContenedorServiceImp")
public class ContainerDeleteController {

    private static final Logger logger = LogManager.getLogger(ContainerDeleteController.class);

    private final ContainerDeleteService containerDeleteService;

    public ContainerDeleteController(ContainerDeleteService containerDeleteService) {
        this.containerDeleteService = containerDeleteService;
    }

    @PostMapping("/sdscontenedorEliminar")
    public ResponseEntity<ResponseController<ContainerDeleteOutput>> containerDelete(@RequestBody @Valid ContainerDeleteInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            ContainerDeleteInput.Input input = request.getPrefix().getInput();
            if(input.getContainerId() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("containerId cannot be null."));
            }
            ContainerDeleteOutput output = containerDeleteService.deleteContainer(
                    input.getContainerId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the delete request.", e);
            ContainerDeleteOutput output = new ContainerDeleteOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}