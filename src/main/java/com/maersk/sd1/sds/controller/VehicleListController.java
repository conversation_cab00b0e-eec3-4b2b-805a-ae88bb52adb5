package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VehicleListInput;
import com.maersk.sd1.sds.dto.VehicleListOutput;
import com.maersk.sd1.sds.service.VehicleListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSVehiculoServiceImp")
public class VehicleListController {

    private static final Logger logger = LogManager.getLogger(VehicleListController.class);

    private final VehicleListService vehicleListService;

    @Autowired
    public VehicleListController(VehicleListService vehicleListService) {
        this.vehicleListService = vehicleListService;
    }

    @PostMapping("/sdsvehiculoListar")
    public ResponseEntity<ResponseController<VehicleListOutput>> sdsVehicleList(@RequestBody @Valid VehicleListInput.Root request) {
        try {
            VehicleListInput.Input input = request.getPrefix().getInput();
            VehicleListOutput output = vehicleListService.getVehicles(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the VehicleList request.", e);
            VehicleListOutput output = new VehicleListOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
