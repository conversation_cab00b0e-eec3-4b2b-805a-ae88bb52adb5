package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.CoparnMonitoringViewEdiInput;
import com.maersk.sd1.sds.controller.dto.CoparnMonitoringEdiViewOutput;
import com.maersk.sd1.sds.service.CoparnMonitoringViewEdiService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSMonitoringServiceImp")
public class CoparnMonitoringViewEdiController {

    private static final Logger logger = LogManager.getLogger(CoparnMonitoringViewEdiController.class);

    private final CoparnMonitoringViewEdiService coparnMonitoringViewEdiService;

    public CoparnMonitoringViewEdiController(CoparnMonitoringViewEdiService coparnMonitoringViewEdiService) {
        this.coparnMonitoringViewEdiService = coparnMonitoringViewEdiService;
    }

    @PostMapping("/sdscoparnMonitoreoVistaEdi")
    public ResponseEntity<ResponseController<CoparnMonitoringEdiViewOutput>> coparnMonitoringViewEdi(@RequestBody @Valid CoparnMonitoringViewEdiInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }

            CoparnMonitoringViewEdiInput.Input input = request.getPrefix().getInput();
            if(input.getEdiCoparnId() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("ediCoparnId cannot be null."));
            }
            CoparnMonitoringEdiViewOutput output = coparnMonitoringViewEdiService.getCoparnMonitoreoVistaEdi(input.getEdiCoparnId());
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing coparnMonitoringViewEdi.", e);
            CoparnMonitoringEdiViewOutput errorOutput = new CoparnMonitoringEdiViewOutput();
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}