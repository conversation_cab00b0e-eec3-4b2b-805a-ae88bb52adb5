package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingCancellationBlockingInput;
import com.maersk.sd1.sds.dto.BookingCancellationBlockingOutput;
import com.maersk.sd1.sds.service.CancelBlockBookingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCancelacionBloqueoBookingServiceImp")
@RequiredArgsConstructor
public class CancelBlockBookingController {

    private static final Logger logger = LogManager.getLogger(CancelBlockBookingController.class);

    private final CancelBlockBookingService cancelBlockBookingService;

    @PostMapping("/sdscancelacionBloqueoBookingUbicar")
    public ResponseEntity<ResponseController<List<BookingCancellationBlockingOutput>>> cancelBlockBooking(@RequestBody @Valid BookingCancellationBlockingInput.Root request) {
        try {
            BookingCancellationBlockingInput.Input input = request.getPrefix().getInput();

            List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getCatType(),
                    input.getBookingNumber(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(Collections.emptyList()));
        }
    }
}

