package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.SDSBookingBlockCancellationReleaseInput;
import com.maersk.sd1.sds.controller.dto.SDSBookingBlockCancellationReleaseOutput;
import com.maersk.sd1.sds.service.SDSBookingBlockCancellationReleaseService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ModuleSDS/module/sds/SDSCancelacionBloqueoBookingServiceImp")
@RequiredArgsConstructor
public class SDSBookingBlockCancellationReleaseController {
    private static final Logger logger = LogManager.getLogger(SDSBookingBlockCancellationReleaseController.class);

    private final SDSBookingBlockCancellationReleaseService service;

    @PostMapping("/sdecancelacionBloqueoBookingLiberacionRegistrar")
    public ResponseEntity<ResponseController<SDSBookingBlockCancellationReleaseOutput>> releaseBooking(@RequestBody @Valid SDSBookingBlockCancellationReleaseInput.Root request) {
        try {
            SDSBookingBlockCancellationReleaseInput.Input input = request.getPrefix().getInput();
            SDSBookingBlockCancellationReleaseOutput output = service.releaseBooking(
                    input.getCancelBlockBookingId(),
                    input.getComment(),
                    input.getReasonCategory(),
                    input.getUserId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the releaseBooking request.", e);
            SDSBookingBlockCancellationReleaseOutput output = new SDSBookingBlockCancellationReleaseOutput();
            output.setResponseStatus(0);
            output.setResponseMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
