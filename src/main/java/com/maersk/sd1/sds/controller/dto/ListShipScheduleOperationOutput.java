package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ListShipScheduleOperationOutput {

    @JsonProperty("programacion_nave_detalle_id")
    private Integer shipScheduleDetailId;

    @JsonProperty("cat_operacion_id")
    private Long operationCategoryId;

    @JsonProperty("operacion")
    private String operation;

    @JsonProperty("manifiesto_ano")
    private String manifestYear;

    @JsonProperty("manifiesto_numero")
    private String manifestNumber;

    @JsonProperty("inicio_operacion")
    private LocalDateTime operationStart;

    @JsonProperty("fin_operacion")
    private LocalDateTime operationEnd;

    @JsonProperty("fecha_manifiesto_aduana")
    private LocalDateTime customsManifestDate;

    @JsonProperty("inicio_citas_devolucion")
    private LocalDateTime returnAppointmentsStart;

    @JsonProperty("retiro_vacio")
    private String emptyWithdrawal; // Changed from List to String

    @JsonProperty("fecha_cutoff_puerto_dry")
    private LocalDateTime dryPortCutoffDate;

    @JsonProperty("fecha_cutoff_puerto_reefer")
    private LocalDateTime reeferPortCutoffDate;

    @JsonProperty("fecha_cutoff_deposito_dry")
    private LocalDateTime dryDepotCutoffDate;

    @JsonProperty("fecha_cutoff_deposito_reefer")
    private LocalDateTime reeferDepotCutoffDate;

    @JsonProperty("fecha_registro_local")
    private LocalDateTime localRegistrationDate;

    @JsonProperty("usuario_registro_id")
    private Integer registrationUserId;

    @JsonProperty("usuario_registro_nombres")
    private String registrationUserFirstName;

    @JsonProperty("usuario_registro_apellidos")
    private String registrationUserLastName;

    @JsonProperty("fecha_modificacion_local")
    private LocalDateTime localModificationDate;

    @JsonProperty("usuario_modificacion_id")
    private Integer modificationUserId;

    @JsonProperty("usuario_modificacion_nombres")
    private String modificationUserFirstName;

    @JsonProperty("usuario_modificacion_apellidos")
    private String modificationUserLastName;

    @JsonProperty("origen_creacion")
    private String origenCreacion;

    @Data
    public static class EmptyWithdrawal {

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("retiro_dry")
        private LocalDateTime dryWithdrawal;

        @JsonProperty("retiro_reefer")
        private LocalDateTime reeferWithdrawal;
    }
}
