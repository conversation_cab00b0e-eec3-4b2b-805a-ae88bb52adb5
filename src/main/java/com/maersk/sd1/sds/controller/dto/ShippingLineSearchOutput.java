package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ShippingLineSearchOutput {

    @JsonProperty("linea_naviera_id")
    private Integer shippingLineId;

    @JsonProperty("nombre")
    private String name;

    @JsonProperty("activo")
    private Boolean active;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("linea_naviera")
    private String shippingLineCompany;

    @JsonProperty("color")
    private String color;

    @JsonProperty("usuario_registro_id")
    private Integer registrationUserId;

    @JsonProperty("usuario_modificacion_id")
    private Integer modificationUserId;

}