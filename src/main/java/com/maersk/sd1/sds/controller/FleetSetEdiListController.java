package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.FleetSetEdiListInput;
import com.maersk.sd1.sds.dto.FleetSetEdiListOutput;
import com.maersk.sd1.sds.service.FleetSetEdiListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
public class FleetSetEdiListController {

    private static final Logger logger = LogManager.getLogger(FleetSetEdiListController.class);

    private final FleetSetEdiListService fleetSetEdiListService;

    @PostMapping("/sdsfleetSetEdiList")
    public ResponseEntity<ResponseController<FleetSetEdiListOutput>> getFleetSetEdiList(@RequestBody @Valid FleetSetEdiListInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Request received for getFleetSetEdiList is null or empty.");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(null));
            }

            logger.info("Request received for getFleetSetEdiList: {}", request);
            FleetSetEdiListInput.Input input = request.getPrefix().getInput();
            FleetSetEdiListOutput output = fleetSetEdiListService.getFleetSetEdiList(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while fetching FleetSetEdiList.", e);
            FleetSetEdiListOutput fallback = new FleetSetEdiListOutput();
            fallback.setTotal(List.of(List.of(0)));
            fallback.setList(null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(fallback));
        }
    }
}
