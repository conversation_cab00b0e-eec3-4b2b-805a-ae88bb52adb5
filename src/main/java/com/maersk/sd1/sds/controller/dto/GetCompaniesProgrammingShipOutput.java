package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.sds.dto.GetCompaniesProgrammingShipProcessDTO;
import lombok.Data;

import java.util.List;

@Data
public class GetCompaniesProgrammingShipOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("empresas")
    private List<GetCompaniesProgrammingShipProcessDTO> programOutput;
}