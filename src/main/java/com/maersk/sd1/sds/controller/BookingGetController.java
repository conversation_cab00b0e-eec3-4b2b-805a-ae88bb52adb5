package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingGetInputDTO;
import com.maersk.sd1.sds.dto.BookingGetOutputDTO;
import com.maersk.sd1.sds.service.BookingGetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSBookingServiceImp")
public class  BookingGetController {

    private final BookingGetService bookingGetService;

    @Autowired
    public BookingGetController(BookingGetService bookingGetService) {
        this.bookingGetService = bookingGetService;
    }

    @PostMapping("/sdsbookingGet")
    public ResponseEntity<ResponseController<BookingGetOutputDTO>> bookingGetService(@RequestBody BookingGetInputDTO.Root input) {
        return bookingGetService.bookingGetService(input);
    }
}
