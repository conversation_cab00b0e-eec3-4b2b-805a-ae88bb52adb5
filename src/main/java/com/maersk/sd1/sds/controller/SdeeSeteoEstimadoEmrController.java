package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.SdeeSeteoEstimadoEmrInput;
import com.maersk.sd1.sds.controller.dto.SdeeSeteoEstimadoEmrOutput;
import com.maersk.sd1.sds.controller.dto.SdeseteoEstimadoEmrRegistrarInput;
import com.maersk.sd1.sds.controller.dto.SdeseteoEstimadoEmrRegistrarOutput;
import com.maersk.sd1.sds.service.SdeeSeteoEstimadoEmrService;
import com.maersk.sd1.sds.service.SdeseteoEstimadoEmrRegistrarService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller that accepts the input in the same structure (SDEE -> F -> Input)
 * and returns the output in the ResponseController.
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
@Log4j2
public class SdeeSeteoEstimadoEmrController {

    private final SdeeSeteoEstimadoEmrService sdeeSeteoEstimadoEmrService;
    private final SdeseteoEstimadoEmrRegistrarService sdeseteoEstimadoEmrRegistrarService;

    @PostMapping("/sdeseteoEstimadoEmrListar")
    public ResponseEntity<ResponseController<SdeeSeteoEstimadoEmrOutput>> sdeeSeteoEstimadoEmrList(
            @Valid @RequestBody SdeeSeteoEstimadoEmrInput.Root request) {
        try {
            log.info("Request received sdeeSeteoEstimadoEmrList: {}", request);
            SdeeSeteoEstimadoEmrInput.Input input = request.getPrefix().getInput();

            // Basic null validation as requested
            if (input == null) {
                SdeeSeteoEstimadoEmrOutput output = new SdeeSeteoEstimadoEmrOutput();
                return ResponseEntity.badRequest().body(new ResponseController<>(output));
            }

            // Service call
            SdeeSeteoEstimadoEmrOutput output = sdeeSeteoEstimadoEmrService.findEstimateEmrSetting(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("An error occurred while processing the request: ", e);
            // Return an error message in the output.
            SdeeSeteoEstimadoEmrOutput errorOutput = new SdeeSeteoEstimadoEmrOutput();
            errorOutput.setTotalRegistros(List.of(List.of(0L)));
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }

    @PostMapping("/sdeseteoEstimadoEmrRegistrar")
    public ResponseEntity<ResponseController<SdeseteoEstimadoEmrRegistrarOutput>> sdeseteoEstimadoEmrRegistrar(
            @Valid @RequestBody SdeseteoEstimadoEmrRegistrarInput.Root request) {
        try {
            log.info("Request received sdeseteoEstimadoEmrRegistrar: {}", request);
            SdeseteoEstimadoEmrRegistrarInput.Input input = request.getPrefix().getInput();

            // Basic null validation as requested
            if (input == null) {
                SdeseteoEstimadoEmrRegistrarOutput output = new SdeseteoEstimadoEmrRegistrarOutput();
                return ResponseEntity.badRequest().body(new ResponseController<>(output));
            }

            // Service call
            SdeseteoEstimadoEmrRegistrarOutput output = sdeseteoEstimadoEmrRegistrarService.registrarEstimadoEmr(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("An error occurred while processing the request: ", e);
            // Return an error message in the output.
            SdeseteoEstimadoEmrRegistrarOutput errorOutput = new SdeseteoEstimadoEmrRegistrarOutput();
            errorOutput.setMessage(e.toString());
            errorOutput.setResponseNewId(0);
            errorOutput.setStatus(0);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
