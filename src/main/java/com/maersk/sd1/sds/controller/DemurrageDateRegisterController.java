package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.DemurrageDateRegisterInput;
import com.maersk.sd1.sds.controller.dto.DemurrageDateRegisterOutput;
import com.maersk.sd1.sds.service.DemurrageDateRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSDocumentoCargaServiceImp")
@RequiredArgsConstructor
public class DemurrageDateRegisterController {

    private static final Logger logger = LogManager.getLogger(DemurrageDateRegisterController.class);

    private final DemurrageDateRegisterService demurrageDateRegisterService;

    @PostMapping("/sderegistrarFechaSobestadia")
    public ResponseEntity<ResponseController<DemurrageDateRegisterOutput>> registerDemurrageDate(
            @RequestBody @Valid DemurrageDateRegisterInput.Root request) {
        try {
            DemurrageDateRegisterInput.Prefix prefix = request.getPrefix();
            if (prefix == null) {
                throw new IllegalArgumentException("Prefix cannot be null");
            }
            DemurrageDateRegisterInput.Input input = prefix.getInput();
            if (input == null) {
                throw new IllegalArgumentException("Input cannot be null");
            }
            DemurrageDateRegisterInput.Input input1 = request.getPrefix().getInput();
            DemurrageDateRegisterOutput output = demurrageDateRegisterService.registerDemurrageDate(input1);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            DemurrageDateRegisterOutput output = new DemurrageDateRegisterOutput();
            output.setRespEstado(2);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

