package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ListShipsInput;
import com.maersk.sd1.sds.dto.ListShipsOutput;
import com.maersk.sd1.sds.service.ListShipsService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCreateBLServiceImp")
@RequiredArgsConstructor
public class ListShipsController {
    private static final Logger logger = LogManager.getLogger(ListShipsController.class.getName());

    private final ListShipsService service;

    @PostMapping("/sdsblNavesListar")
    public ResponseEntity<ResponseController<List<ListShipsOutput>>> listShipsService(@RequestBody ListShipsInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Received request for sdsblNavesListar with invalid input.");
                return ResponseEntity.badRequest().body(new ResponseController<>(null));
            }

            return service.listShipsService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
