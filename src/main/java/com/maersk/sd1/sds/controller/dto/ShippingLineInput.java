package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.utils.BooleanDeserializer;
import lombok.*;

import java.time.LocalDateTime;

@Data
public class ShippingLineInput {

    @Data
    public static class Input {

        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("nombre")
        private String name;

        @JsonDeserialize(using = BooleanDeserializer.class)
        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro_min")
        private LocalDateTime registrationDateMin;

        @JsonProperty("fecha_registro_max")
        private LocalDateTime registrationDateMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDateTime modificationDateMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDateTime modificationDateMax;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}