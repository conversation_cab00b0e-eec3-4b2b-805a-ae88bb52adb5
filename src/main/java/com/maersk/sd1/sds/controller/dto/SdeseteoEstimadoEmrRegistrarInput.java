package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SdeseteoEstimadoEmrRegistrarInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("cat_modo_generar_archivo_esimado_id")
        private Integer catModeGenerateFileEstimatedId;

        @JsonProperty("descripcion_servicio")
        private String serviceDescription;

        @JsonProperty("archivo_correlativo")
        private Integer fileCorrelative;

        @JsonProperty("archivo_extension")
        private String fileExtension;

        @JsonProperty("correo_envio")
        private String sendEmail;

        @JsonProperty("minutos_transcurridos")
        private Integer minutesElapsed;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("usuario_registro")
        private Integer userRegistration;

        @JsonProperty("shopcode_merc")
        private String shopcodeMerc;

        @JsonProperty("azure_id")
        private String azureId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private SdeseteoEstimadoEmrRegistrarInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private SdeseteoEstimadoEmrRegistrarInput.Prefix prefix;
    }
    
}
