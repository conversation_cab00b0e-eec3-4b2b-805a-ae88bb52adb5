package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.BlManualSearchInputDTO;
import com.maersk.sd1.sds.controller.dto.BlManualSearchOutputDTO;
import com.maersk.sd1.sds.service.BlManualSearchService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCreateBLServiceImp")
@RequiredArgsConstructor
public class BlManualSearchController {

    private static final Logger logger = LogManager.getLogger(BlManualSearchController.class);

    private final BlManualSearchService blManualSearchService;

    @PostMapping("/sdsblManualSearch")
    public ResponseEntity<ResponseController<List<BlManualSearchOutputDTO>>> searchManualBl(@RequestBody BlManualSearchInputDTO.Root request) {
        try {
            List<BlManualSearchOutputDTO> result = blManualSearchService.searchManualBl(request);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the BL manual search.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(Collections.emptyList()));
        }
    }
}
