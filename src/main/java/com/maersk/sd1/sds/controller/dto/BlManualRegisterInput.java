package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class BlManualRegisterInput {

    @Builder
    @Data
    public static class Input {
        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("documento_carga_id")
        private Integer documentoCargaId;

        @JsonProperty("numero_bl")
        private String numeroBl;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("programacion_nave_detalle_id")
        private Integer programacionNaveDetalleId;

        @JsonProperty("puerto_embarque_id")
        private Integer puertoEmbarqueId;

        @JsonProperty("puerto_descarga_id")
        private Integer puertoDescargaId;

        @JsonProperty("empresa_embarcador_id")
        private Integer empresaEmbarcadorId;

        @JsonProperty("empresa_consignatario_id")
        private Integer empresaConsignatarioId;

        @JsonProperty("embarcador_detalle")
        private String embarcadorDetalle;

        @JsonProperty("consignatario_detalle")
        private String consignatarioDetalle;

        @JsonProperty("deposito_vacio_id")
        private Integer depositoVacioId;

        @JsonProperty("contenedores_json")
        private String contenedoresJson;

        @JsonProperty("idioma_id")
        private Integer idiomaId;

        @JsonProperty("usuario_registro_id")
        private Integer usuarioRegistroId;

        @JsonProperty("pass_restriction")
        private Integer passRestriction;

        @JsonProperty("cat_move_type_id")
        private Integer catMoveTypeId;

        @JsonProperty("maersk_depot_with_sd1")
        private String maerskDepotWithSd1;

        @JsonProperty("origin_destination_depot_id")
        private Integer originDestinationDepotId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
