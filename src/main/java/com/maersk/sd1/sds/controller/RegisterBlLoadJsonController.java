package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;

import com.maersk.sd1.sds.dto.RegisterBlLoadJsonInputDTO;
import com.maersk.sd1.sds.dto.RegisterBlLoadJsonOutputDTO;
import com.maersk.sd1.sds.service.RegisterBlLoadJsonService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCargaDatosServiceImp")
public class RegisterBlLoadJsonController {

    private final Logger logger = LogManager.getLogger(RegisterBlLoadJsonController.class);

    private final RegisterBlLoadJsonService registerBlLoadJsonService;

    @PostMapping("/sdscargarBlRegistrarJson")
    public ResponseEntity<ResponseController<RegisterBlLoadJsonOutputDTO>> registerBlLoadJson(@RequestBody RegisterBlLoadJsonInputDTO.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure"));
            }

            RegisterBlLoadJsonInputDTO.Input input = request.getPrefix().getInput();
            RegisterBlLoadJsonOutputDTO output = registerBlLoadJsonService.registerBlLoadJson(input);
            return ResponseEntity.ok(new ResponseController<>(output));

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(e.getMessage()));
        }
    }
}
