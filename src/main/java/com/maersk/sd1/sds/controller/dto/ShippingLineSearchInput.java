package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class ShippingLineSearchInput {
    @Data
    public static class Input {

        @JsonProperty("name")
        private String name;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ShippingLineSearchInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ShippingLineSearchInput.Prefix prefix;
    }
}