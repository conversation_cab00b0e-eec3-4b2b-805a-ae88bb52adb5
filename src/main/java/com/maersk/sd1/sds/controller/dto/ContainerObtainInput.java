package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ContainerObtainInput {
    @Data
    public static class Input {
        @JsonProperty("contenedor_id")
        @NotNull(message = "contenedor_id cannot be null")
        private Integer containerId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }

    private ContainerObtainInput() {
        // private constructor
    }
}