package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ValidateBookingExistenceInput;
import com.maersk.sd1.sds.dto.ValidateBookingExistenceOutput;
import com.maersk.sd1.sds.service.ValidateBookingExistenceService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSBookingServiceImp")
@RequiredArgsConstructor
public class ValidateBookingExistenceController {

    private static final Logger logger = LogManager.getLogger(ValidateBookingExistenceController.class.getName());

    private final ValidateBookingExistenceService service;

    @PostMapping("/sdsbookingValidarExistencia")
    public ResponseEntity<ResponseController<List<ValidateBookingExistenceOutput>>> validateBookingExistenceService(@RequestBody ValidateBookingExistenceInput.Root request) {
        try {
            if(request==null || request.getPrefix()== null || request.getPrefix().getInput()==null){
                logger.error("Request is null");
                return ResponseEntity.badRequest().body(new ResponseController<>("Request is null"));
            }
            List<ValidateBookingExistenceOutput> validateBookingExistenceOutputs = service.validateBookingExistenceService(request);
            return ResponseEntity.ok(new ResponseController<>(validateBookingExistenceOutputs));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
