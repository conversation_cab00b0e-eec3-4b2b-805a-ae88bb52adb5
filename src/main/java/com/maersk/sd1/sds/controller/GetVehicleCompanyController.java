package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.GetVehicleCompanyInput;
import com.maersk.sd1.sds.dto.GetVehicleCompanyOutput;
import com.maersk.sd1.sds.service.GetVehicleCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSVehiculoServiceImp")
public class GetVehicleCompanyController {

    private static final Logger logger = LogManager.getLogger(GetVehicleCompanyController.class.getName());

    private final GetVehicleCompanyService service;

    @Autowired
    public GetVehicleCompanyController(GetVehicleCompanyService service) {
        this.service = service;
    }

    @PostMapping("/sdsvehicleCompanyGet")
    public ResponseEntity<ResponseController<List<GetVehicleCompanyOutput>>> getVehicleCompanyService(@RequestBody GetVehicleCompanyInput.Root request) {
        try {

            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Input is required");
                return ResponseEntity.badRequest().body(new ResponseController<>("Input is required"));
            }

            GetVehicleCompanyInput.Input input = request.getPrefix().getInput();
            List<GetVehicleCompanyOutput> output = service.getVehicleCompanyService(input);

            if (output == null) {
                return ResponseEntity.ok(new ResponseController<>(List.of()));
            }

            return ResponseEntity.ok(new ResponseController<>(output));

        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
