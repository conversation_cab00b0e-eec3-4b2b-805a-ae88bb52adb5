package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.GetCatalogsByTablesInput;
import com.maersk.sd1.sds.dto.GetCatalogsByTablesOutput;
import com.maersk.sd1.sds.service.GetCatalogsByTablesService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPrincipalServiceImp")
public class GetCatalogsByTablesController {

    private static final Logger logger = LogManager.getLogger(GetCatalogsByTablesController.class.getName());

    private final GetCatalogsByTablesService catalogoTablasService;

    public GetCatalogsByTablesController(GetCatalogsByTablesService catalogoTablasService) {
        this.catalogoTablasService = catalogoTablasService;
    }

    @PostMapping("/sdsobtenerCatalogoPorTablas")
    public ResponseEntity<ResponseController<GetCatalogsByTablesOutput>> getCatalogsByTables(
            @RequestBody @Valid GetCatalogsByTablesInput.Root request) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            GetCatalogsByTablesInput.Input input = request.getPrefix().getInput();
            GetCatalogsByTablesOutput output = catalogoTablasService.getCatalogsByTables(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            GetCatalogsByTablesOutput errorOutput = new GetCatalogsByTablesOutput();


            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }
}

