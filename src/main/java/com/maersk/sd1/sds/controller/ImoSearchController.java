package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ImoSearchInput;
import com.maersk.sd1.sds.controller.dto.ImoSearchOutput;
import com.maersk.sd1.sds.service.ImoSearchService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSImoServiceImp")
public class ImoSearchController {

    private static final Logger logger = LogManager.getLogger(ImoSearchController.class);
    private final ImoSearchService imoSearchService;

    @PostMapping("/sdsimoSearch")
    public ResponseEntity<ResponseController<List<ImoSearchOutput>>> sdgImoSearch(@RequestBody ImoSearchInput.Root request) {
        try {
            logger.info("Request received sdsimoSearch: {}", request);
            ImoSearchInput.Input input = request.getPrefix().getInput();

            List<ImoSearchOutput> imoSearchOutput = imoSearchService.searchImo(input.getLanguageId(), input.getImoId(), input.getImoName());
            return ResponseEntity.ok(new ResponseController<>(imoSearchOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(Collections.emptyList()));
        }
    }
}
