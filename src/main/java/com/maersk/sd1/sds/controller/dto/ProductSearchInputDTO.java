package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ProductSearchInputDTO {

    @Data
    public static class Input {
        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("product_id")
        private Integer productId;

        @JsonProperty("product_name")
        private String productName;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ProductSearchInputDTO.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ProductSearchInputDTO.Prefix prefix;
    }
}