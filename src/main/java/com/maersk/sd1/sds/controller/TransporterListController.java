package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.TransporterListInput;
import com.maersk.sd1.sds.controller.dto.TransporterListOutput;
import com.maersk.sd1.sds.service.TransporterListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSVehiculoServiceImp")
public class TransporterListController {

    private static final Logger logger = LogManager.getLogger(TransporterListController.class);

    private final TransporterListService transporterListService;

    public TransporterListController(TransporterListService transporterListService) {
        this.transporterListService = transporterListService;
    }

    @PostMapping("/sdstransportistasListar")
    public ResponseEntity<ResponseController<TransporterListOutput>> listTransporters(
            @Valid @RequestBody TransporterListInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null ) {
                return ResponseEntity.status(400).body(new ResponseController<>("Request is null"));
            }
            TransporterListInput.Input input = request.getPrefix().getInput();
            if(input.getBusinessUnitId() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("businessUnitId should not be null"));
            }
            TransporterListOutput output = transporterListService.getTransporters(
                    input.getBusinessUnitId(), 
                    input.getViewType()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing transporters.", e);
            TransporterListOutput errorOutput = new TransporterListOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}