package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingEdiDeleteInput;
import com.maersk.sd1.sds.dto.BookingEdiDeleteOutput;
import com.maersk.sd1.sds.service.BookingEdiDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSMonitoringServiceImp")
public class BookingEdiDeleteController {

    private static final Logger logger = LogManager.getLogger(BookingEdiDeleteController.class.getName());

    private final BookingEdiDeleteService bookingEdiDeleteService;

    @Autowired
    public BookingEdiDeleteController(BookingEdiDeleteService bookingEdiDeleteService) {
        this.bookingEdiDeleteService = bookingEdiDeleteService;
    }

    @PostMapping("/sdscoparnMonitoreoEliminarEdi")
    public ResponseEntity<ResponseController<BookingEdiDeleteOutput>> deleteEdiCoparn(@RequestBody @Valid BookingEdiDeleteInput.Root request) {
        try {

            if (request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>(new BookingEdiDeleteOutput()));
            }

            BookingEdiDeleteInput.Input input = request.getPrefix().getInput();
            BookingEdiDeleteOutput output = bookingEdiDeleteService.deleteEdiCoparn(
                    input.getEdiCoparnId(),
                    input.getUsuarioId(),
                    input.getIdiomaId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the EDI Coparn deletion.", e);
            BookingEdiDeleteOutput output = new BookingEdiDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
