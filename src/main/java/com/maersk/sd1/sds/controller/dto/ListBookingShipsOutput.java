package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.sds.dto.ListBookingShipsOutputProjection;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class ListBookingShipsOutput implements ListBookingShipsOutputProjection {

    @JsonProperty("programacion_nave_detalle_id")
    private Integer vesselProgrammingDetailId;

    @JsonProperty("nombre_nave")
    private String shipName;

    @JsonProperty("viaje")
    private String voyage;

    @JsonProperty("operacion_descripcion")
    private String operationDescription;

    @JsonProperty("manifiesto")
    private String manifest;

    @JsonProperty("fecha_eta")
    private String etaDate;

    @JsonProperty("fecha_etd")
    private String etdDate;
}