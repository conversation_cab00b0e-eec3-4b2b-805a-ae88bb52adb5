package com.maersk.sd1.sds.repository;

import com.maersk.sd1.common.model.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * Repository interface for products, allowing dynamic filter usage.
 * We will rely on the Specifications in the service to replicate the logic.
 */
public interface ProductListRepository extends JpaRepository<Product, Integer>, JpaSpecificationExecutor<Product> {
}
