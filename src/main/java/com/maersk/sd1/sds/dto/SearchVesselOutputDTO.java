package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class SearchVesselOutputDTO {

    private Integer id;
    private String ship;
    private String callSign;
    private String imoNumber;
    private Boolean active;
    private String name;
    private Integer registrationUserId;
    private Integer modificationUserId;
}
