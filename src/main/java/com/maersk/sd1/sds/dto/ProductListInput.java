package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

public class ProductListInput {

    @Data
    public static class Input {

        @JsonProperty("product_id")
        private Integer productId;

        @JsonProperty("business_unit_id")
        private Long businessUnitId;

        @JsonProperty("product_code")
        @Size(max = 10)
        private String productCode;

        @JsonProperty("product_name")
        @Size(max = 100)
        private String productName;

        @JsonProperty("product_group_id")
        private Long productGroupId;

        @JsonProperty("weight_measure_unit_id")
        private Long weightMeasureUnitId;

        @JsonProperty("packaging_id")
        private Long packagingId;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("registration_date_min")
        @PastOrPresent
        private LocalDate registrationDateMin;

        @JsonProperty("registration_date_max")
        @PastOrPresent
        private LocalDate registrationDateMax;

        @JsonProperty("modification_date_min")
        @PastOrPresent
        private LocalDate modificationDateMin;

        @JsonProperty("modification_date_max")
        @PastOrPresent
        private LocalDate modificationDateMax;

        @JsonProperty("booking_edi_commodity1")
        @Size(max = 100)
        private String bookingEdiCommodity1;

        @JsonProperty("booking_edi_commodity2")
        @Size(max = 100)
        private String bookingEdiCommodity2;

        @JsonProperty("refrigeration_type_id")
        private Long refrigerationTypeId;

        @JsonProperty("unidad_negocio_usuario_id")
        private Integer businessUnitUserId;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        @NotNull
        private Prefix prefix;
    }
}
