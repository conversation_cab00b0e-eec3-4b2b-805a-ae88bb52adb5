package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class PrinterListOutputDTO {

    @JsonProperty("total_records")
    private List<List<Long>> totalRecords;

    @JsonProperty("printers")
    private List<PrinterDetails> printers;

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class PrinterDetails {

        @JsonProperty("printer_id")
        private Integer printerId;

        @JsonProperty("sub_business_unit_id")
        private Integer subBusinessUnitId;

        @JsonProperty("printer_name")
        private String printerName;

        @JsonProperty("active")
        private Character active;
    }
}
