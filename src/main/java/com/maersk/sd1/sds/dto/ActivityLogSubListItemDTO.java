package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ActivityLogSubListItemDTO {

    @JsonProperty("activity_log_id")
    private Integer activityLogId;

    @JsonProperty("activity_alias")
    private String activityAlias;

    @JsonProperty("cat_status_id")
    private Integer catStatusId;

    @JsonProperty("cat_status")
    private String catStatusDescription;

    @JsonProperty("cat_alias")
    private String catStatusAlias;

    @JsonProperty("data_input")
    private String dataInput;

    @JsonProperty("data_output")
    private String dataOutput;

    @JsonProperty("user_registration_id")
    private Integer userRegistrationId;

    @JsonProperty("registration_date")
    private LocalDateTime registrationDate;

    @JsonProperty("user_registration_name")
    private String userRegistrationName;

    @JsonProperty("user_registration_lastname")
    private String userRegistrationLastname;

    public ActivityLogSubListItemDTO(Integer activityLogId,
                                     String activityAlias,
                                     Integer catStatusId,
                                     String catStatusDescription,
                                     String catStatusAlias,
                                     String dataInput,
                                     String dataOutput,
                                     Integer userRegistrationId,
                                     LocalDateTime registrationDate,
                                     String userRegistrationName,
                                     String userRegistrationLastname) {
        this.activityLogId = activityLogId;
        this.activityAlias = activityAlias;
        this.catStatusId = catStatusId;
        this.catStatusDescription = catStatusDescription;
        this.catStatusAlias = catStatusAlias;
        this.dataInput = dataInput;
        this.dataOutput = dataOutput;
        this.userRegistrationId = userRegistrationId;
        this.registrationDate = registrationDate;
        this.userRegistrationName = userRegistrationName;
        this.userRegistrationLastname = userRegistrationLastname;
    }
}