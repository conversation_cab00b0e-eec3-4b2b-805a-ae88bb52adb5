package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

@Data
public class CompanyListDTO {
    private List<CompanyData> data;

    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class CompanyData{
        private Integer companyId;
        private String document;
        private String companyName;
    }
}
