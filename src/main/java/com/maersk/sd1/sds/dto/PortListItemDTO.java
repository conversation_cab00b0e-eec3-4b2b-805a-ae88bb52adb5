package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortListItemDTO {

    @JsonProperty("port_id")
    private Integer portId;

    @JsonProperty("port")
    private String port;

    @JsonProperty("name")
    private String name;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("active")
    private Boolean active;

    @JsonProperty("registration_date")
    private LocalDateTime registrationDate;

    @JsonProperty("registration_user_id")
    private Integer registrationUserId;

    @JsonProperty("registration_user_names")
    private String registrationUserNames;

    @JsonProperty("registration_user_last_names")
    private String registrationUserLastNames;

    @JsonProperty("modification_user_names")
    private String modificationUserNames;

    @JsonProperty("modification_user_last_names")
    private String modificationUserLastNames;

    @Setter
    @JsonProperty("registration_date_local")
    private String registrationDateLocal;

    @Setter
    @JsonProperty("modification_date_local")
    private String modificationDateLocal;

    @JsonProperty("country_name")
    private String countryName;

    @JsonProperty("modification_date")
    private LocalDateTime modificationDate;

    @JsonProperty("modification_user_id")
    private Integer modificationUserId;


    public PortListItemDTO(Integer portId, String port, String name, Integer countryId, String countryName, Boolean active,
                           LocalDateTime registrationDate, LocalDateTime modificationDate, Integer registrationUserId,
                           String registrationUserNames, String registrationUserLastNames, Integer modificationUserId,
                           String modificationUserNames, String modificationUserLastNames) {
        this.portId = portId;
        this.port = port;
        this.name = name;
        this.countryId = countryId;
        this.countryName = countryName;
        this.active = active;
        this.registrationDate = registrationDate;
        this.modificationDate = modificationDate;
        this.registrationUserId = registrationUserId;
        this.registrationUserNames = registrationUserNames;
        this.registrationUserLastNames = registrationUserLastNames;
        this.modificationUserId = modificationUserId;
        this.modificationUserNames = modificationUserNames;
        this.modificationUserLastNames = modificationUserLastNames;
    }

}