package com.maersk.sd1.sds.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ShippingLineCountFilterDTO {
    private Integer shippingLineId;
    private String name;
    private Boolean active;
    private LocalDateTime registrationDateMin;
    private LocalDateTime registrationDateMax;
    private LocalDateTime modificationDateMin;
    private LocalDateTime modificationDateMax;
    private String shippingLineCompany;
}