package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class VesselProgrammingPortRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("programacion_nave_id")
        @NotNull(message = "vessel_programming_id cannot be null")
        private Integer vesselProgrammingId;

        @JsonProperty("puerto_id")
        @NotNull(message = "port_id cannot be null")
        private Integer portId;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "registration_user_id cannot be null")
        private Long registrationUserId;

        @JsonProperty("idioma_id")
        @NotNull(message = "language_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}