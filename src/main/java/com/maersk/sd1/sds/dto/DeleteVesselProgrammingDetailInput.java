package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;
import java.math.BigDecimal;

@UtilityClass
public class DeleteVesselProgrammingDetailInput {
    @Data
    public static class Input {

        @JsonProperty("programacion_nave_detalle_id")
        @NotNull
        private Integer vesselProgrammingDetailId;

        @JsonProperty("usuario_id")
        @NotNull
        private BigDecimal userId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
