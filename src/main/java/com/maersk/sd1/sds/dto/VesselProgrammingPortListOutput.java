package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class VesselProgrammingPortListOutput {

        @JsonProperty("programacion_nave_puerto_id")
        private Integer programacionNavePuertoId;

        @JsonProperty("puerto_id")
        private Integer puertoId;

        @JsonProperty("puerto")
        private String puerto;

        @JsonProperty("puerto_descripcion")
        private String puertoDescripcion;
}
