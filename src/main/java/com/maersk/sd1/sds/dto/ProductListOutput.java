package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Output DTO containing the result of the product listing
 * + any status or message fields.
 */
@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class ProductListOutput {

    @JsonProperty("total_records")
    private List<List<Long>> totalRecords;

    @JsonProperty("products")
    private List<ProductDetailOutput> products;

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class ProductDetailOutput {
        @JsonProperty("product_id")
        private Integer productId;

        @JsonProperty("business_unit_id")
        private Long businessUnitId;

        @JsonProperty("product_code")
        private String productCode;

        @JsonProperty("product_name")
        private String productName;

        @JsonProperty("product_group_id")
        private Long productGroupId;

        @JsonProperty("weight_measure_unit_id")
        private Long weightMeasureUnitId;

        @JsonProperty("packaging_id")
        private Long packagingId;

        @JsonProperty("active")
        private Boolean active;

        @JsonProperty("registration_date_local")
        private String registrationDateLocal;

        @JsonProperty("modification_date_local")
        private String modificationDateLocal;

        @JsonProperty("booking_edi_commodity1")
        private String bookingEdiCommodity1;

        @JsonProperty("booking_edi_commodity2")
        private String bookingEdiCommodity2;

        @JsonProperty("refrigeration_type_id")
        private Long refrigerationTypeId;

        @JsonProperty("user_registration_id")
        private Integer userRegistrationId;

        @JsonProperty("user_modification_id")
        private Integer userModificationId;

        @JsonProperty("user_registration_first_name")
        private String userRegistrationFirstName;

        @JsonProperty("user_registration_last_name")
        private String userRegistrationLastName;

        @JsonProperty("user_modification_first_name")
        private String userModificationFirstName;

        @JsonProperty("user_modification_last_name")
        private String userModificationLastName;
    }
}
