package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BookingDetailEditInputDTO {

    @Data
    public static class Input {
        @JsonProperty("booking_detalle_id")
        private Integer bookingDetailId;

        @JsonProperty("booking_id")
        private Integer bookingId;

        @JsonProperty("cat_tamano_id")
        private Integer sizeCategoryId;

        @JsonProperty("cat_tipo_contenedor_id")
        private Integer containerTypeId;

        @JsonProperty("cantidad_reserva")
        private Double reservedQuantity;

        @JsonProperty("cantidad_atendida")
        private Double attendedQuantity;

        @JsonProperty("carga_maxima_requerido")
        private Double maxRequiredLoad;

        @JsonProperty("cat_origen_creacion_booking_id")
        private Double bookingCreationSourceId;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        private Integer modifiedByUserId;

        @JsonProperty("idioma_id")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private BookingDetailEditInputDTO.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private BookingDetailEditInputDTO.Prefix prefix;

        public Root() {
            this.prefix = new BookingDetailEditInputDTO.Prefix();
            this.prefix.setInput(new BookingDetailEditInputDTO.Input());
        }
    }
}
