package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class ShipListOutputDTO {

    @JsonProperty("total_registros")
    private List<List<Long>> totalRecords;

    @JsonProperty("data")
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    private List<ShipListRowDTO> data;

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;
}
