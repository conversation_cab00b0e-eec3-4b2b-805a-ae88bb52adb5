package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SearchVesselInput {

    @Data
    public static class Input {

        @JsonProperty("nombre")
        private String name;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private SearchVesselInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private SearchVesselInput.Prefix prefix;
    }
}

