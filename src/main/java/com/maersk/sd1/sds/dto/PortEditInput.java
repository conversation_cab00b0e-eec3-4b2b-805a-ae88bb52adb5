package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class PortEditInput {

    @Data
    public static class Input {
        @JsonProperty("puerto_id")
        @NotNull(message = "puerto_id cannot be null")
        private Integer puertoId;

        @JsonProperty("puerto")
        @Size(max = 10)
        @NotNull(message = "puerto cannot be null")
        private String puerto;

        @JsonProperty("nombre")
        @Size(max = 100)
        @NotNull(message = "nombre cannot be null")
        private String nombre;

        @JsonProperty("pais_id")
        @NotNull(message = "pais_id cannot be null")
        private Integer paisId;

        @JsonProperty("activo")
        @NotNull(message = "activo cannot be null")
        private Boolean activo;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "usuario_modificacion_id cannot be null")
        private Integer usuarioModificacionId;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id cannot be null")
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
