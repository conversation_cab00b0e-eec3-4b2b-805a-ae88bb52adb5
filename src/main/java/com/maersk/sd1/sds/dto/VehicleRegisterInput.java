package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class VehicleRegisterInput {

    @Data
    public static class Input {
        @JsonProperty("placa")
        @NotNull(message = "Plate cannot be null")
        @Size(max = 10, message = "Plate cannot exceed 10 characters")
        private String plate;

        @JsonProperty("carga_util")
        @NotNull(message = "Payload cannot be null")
        private BigDecimal payload;

        @JsonProperty("modelo")
        @Size(max = 50, message = "Model cannot exceed 50 characters")
        private String model;

        @JsonProperty("peso_neto")
        @NotNull(message = "Net weight cannot be null")
        private BigDecimal netWeight;

        @JsonProperty("peso_bruto")
        @NotNull(message = "Brute weight cannot be null")
        private BigDecimal bruteWeight;

        @JsonProperty("empresa_transporte_id")
        private Integer transportCompanyId;

        @JsonProperty("company_document")
        private String companyDocument;

        @JsonProperty("activo")
        @JsonDeserialize(using = VehicleRegisterInputActiveBooleanDeserializer.class)
        @NotNull(message = "Active cannot be null")
        private Boolean active;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "Registration user ID cannot be null")
        private Integer userRegistrationId;

        @JsonProperty("idioma_id")
        @NotNull(message = "Language ID cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
