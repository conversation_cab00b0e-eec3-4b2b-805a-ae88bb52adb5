package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RegisterBookingDetailsInput {
    @Data
    public static class Input {
        @JsonProperty("booking_id")
        private Integer bookingId;

        @JsonProperty("cat_tamano_id")
        private Integer containerSizeCategoryId;

        @JsonProperty("cat_tipo_contenedor_id")
        private Integer containerTypeCategoryId;

        @JsonProperty("cantidad_reserva")
        private Integer reservedQuantity;

        @JsonProperty("carga_maxima_requerido")
        private Integer requiredMaxLoad;

        @JsonProperty("cat_origen_creacion_booking_id")
        private Integer bookingCreationSourceCategoryId;

        @JsonProperty("usuario_registro_id")
        private Integer registeredUserId;

        @JsonProperty("idioma_id")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private RegisterBookingDetailsInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private RegisterBookingDetailsInput.Prefix prefix;
    }
}
