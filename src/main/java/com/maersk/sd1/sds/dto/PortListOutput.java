package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class PortListOutput {

//    @JsonProperty("resp_status")
//    private Integer respStatus;
//
//    @JsonProperty("resp_message")
//    private String respMessage;

    @JsonProperty("total_records")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<List<Long>> totalRecords;

    @JsonProperty("ports")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<PortListItemDTO> ports;
}