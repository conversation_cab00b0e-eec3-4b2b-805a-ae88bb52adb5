package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetCompaniesProgrammingShipProcessDTO {

    @JsonProperty("empresa_id")
    private Integer companyId;

    @JsonProperty("documento")
    private String document;

    @JsonProperty("razon_social")
    private String legalName;

    @JsonProperty("razon_comercial")
    private String commercialName;

    @JsonProperty("direccion")
    private String address;

    @JsonProperty("longitud")
    private String longitude;

    @JsonProperty("latitud")
    private String latitude;

    @JsonProperty("estado")
    private Boolean status;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("suspendido")
    private Boolean suspended;

    @JsonProperty("telefono")
    private String phone;

    @JsonProperty("correo")
    private String mail;

    @JsonProperty("abreviatura")
    private String abbreviation;

    @JsonProperty("tipo_rol_id")
    private Integer roleTypeId;

    public GetCompaniesProgrammingShipProcessDTO(Integer companyId,
                                                 String document,
                                                 String legalName,
                                                 String commercialName,
                                                 String address,
                                                 String longitude,
                                                 String latitude,
                                                 Boolean status,
                                                 LocalDateTime registrationDate,
                                                 LocalDateTime modificationDate,
                                                 Boolean suspended,
                                                 String phone,
                                                 String mail,
                                                 String abbreviation,
                                                 Integer roleTypeId) {
        this.companyId = companyId;
        this.document = document;
        this.legalName = legalName;
        this.commercialName = commercialName;
        this.address = address;
        this.longitude = longitude;
        this.latitude = latitude;
        this.status = status;
        this.registrationDate = registrationDate;
        this.modificationDate = modificationDate;
        this.suspended = suspended;
        this.phone = phone;
        this.mail = mail;
        this.abbreviation = abbreviation;
        this.roleTypeId = roleTypeId;
    }
}