package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ValidateBookingExistenceInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("numero_booking")
        private String bookingNumber;

        @JsonProperty("idioma_id")
        private Integer languageId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ValidateBookingExistenceInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ValidateBookingExistenceInput.Prefix prefix;
    }
}
