package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class CoparnPendingResendInput {
    @Data
    public static class Input  {

        @JsonProperty("seteo_edi_coparn_id")
        @NotNull(message = "seteoEdiCoparnId cannot be null")
        private Integer seteoEdiCoparnId;
    }

    @Data
    public static class Prefix  {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
