package com.maersk.sd1.sds.dto;


import java.time.LocalDateTime;

public interface FleetEquipmentEdiSettingDTO {

    Integer getFleetEquipmentEdiSettingId();
    String getFleetEquipEdiDescription();
    Integer getBusinessUnitId();
    String getBusinessUnit();
    Integer getShippingLineId();
    String getShippingLine();
    Integer getCatRecepModeFleetEquipSettingId();
    String getCatRecepModeFleetEquipSetting();
    String getFleetEquipEdiFileExtension();
    Integer getAzureStorageFleetEquipEdiId();
    String getAzureStorageFleetEquipEdiAliasId();
    Integer getSftpConfigFleetEquipEdiId();
    String getSftpConfigFleetEquipEdiAliasId();
    Boolean getActive();
    LocalDateTime getRegistrationDate();
    Integer getUserRegistrationId();
    String getUserRegistrationNames();
    String getUserRegistrationLastname();
    LocalDateTime getModificationDate();
    Integer getUserModificationId();
    String getUserModificationNames();
    String getUserModificationLastname();
}

