package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class EmptyBlContainerRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("bl_number")
        @NotNull
        private String blNumber;

        @JsonProperty("container_number")
        @NotNull
        @Size(max = 11)
        private String containerNumber;

        @JsonProperty("container_size")
        @NotNull
        private Integer containerSize;

        @JsonProperty("container_type")
        @NotNull
        private Integer containerType;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("sub_business_unit_local_id")
        @NotNull
        private Integer subBusinessUnitLocalId;

        @JsonProperty("user_registration_id")
        @NotNull
        private Integer userRegistrationId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        @NotNull
        private Prefix prefix;
    }
}

