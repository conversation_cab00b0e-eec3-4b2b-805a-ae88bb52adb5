package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class GetVehicleCompanyOutput {
    private Integer vehicleId;
    private String plate;
    private String model;
    private String document;
    private String companyName;
    private Integer transportCompanyId;

    public static List<GetVehicleCompanyOutput> createListOfLists(GetVehicleCompanyOutput output) {
        return Collections.singletonList(output);
    }
}