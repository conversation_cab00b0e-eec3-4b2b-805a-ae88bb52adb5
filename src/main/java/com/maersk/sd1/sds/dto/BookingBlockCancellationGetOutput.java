package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
@AllArgsConstructor
@NoArgsConstructor
public class BookingBlockCancellationGetOutput {

    @JsonProperty("cat_tipo")
    private Integer catType;

    @JsonProperty("numero_booking")
    private String fancyBookingNumber;

    @JsonProperty("cat_motivo")
    private Integer catReason;

    @JsonProperty("comentario")
    private String comment;

    @JsonProperty("fecha_cancel_bloqueo")
    private LocalDateTime localBlockCancellationDate;

    @JsonProperty("numero_booking_real")
    private String bookingNumber;
}
