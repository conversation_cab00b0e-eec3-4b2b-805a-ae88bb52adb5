package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class EstimateEmrCostManHoursRegisterOutput {

    @JsonProperty("resp_new_id")
    private Integer respNewId;

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;
}