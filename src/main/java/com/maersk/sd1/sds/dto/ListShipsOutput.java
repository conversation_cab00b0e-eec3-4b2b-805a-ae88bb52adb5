package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ListShipsOutput {

    private Integer shipScheduleDetailId;
    private String shipName;
    private String voyage;
    private String operationDescription;
    private String manifest;
    private String etaDate;
    private String etdDate;

}
