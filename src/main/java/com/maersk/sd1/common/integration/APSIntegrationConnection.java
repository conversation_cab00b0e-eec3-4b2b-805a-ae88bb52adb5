package com.maersk.sd1.common.integration;

import com.google.gson.Gson;
import com.maersk.sd1.common.dto.GateInInputContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import java.util.HashMap;
import java.util.Map;

public class APSIntegrationConnection {

    @Autowired
    private Environment env;

    public void attendAppointmentAPS(String userRegistrationId, String equipmentsJSON) throws Exception {

        SD1IntegrationConnection connection = new SD1IntegrationConnection(
                env.getProperty("msk.api.apiUserLogin.aps"),
                env.getProperty("msk.api.apiUserLogin.aps.user"),
                env.getProperty("msk.api.apiUserLogin.aps.password"),
                env.getProperty("msk.api.apiUserLogin.aps.system")
        );

        try {
            Gson gson = new Gson();

            GateInInputContainer[] userArray = gson.fromJson(equipmentsJSON, GateInInputContainer[].class);

            if(userArray.length > 0) {
                for(int i = 0; i < userArray.length; i++) {
                    if(userArray[i].getApsId() != null && !userArray[i].getApsId().equals("")) {

                        Map<String, String> data = new HashMap<String, String>();

                        data.put("cita_id", userArray[i].getApsId());
                        data.put("usuario_atencion_id", userRegistrationId);

                        HashMap<String, Object> resp = connection.postMap(env.getProperty("msk.sdg.api.url.attendAppointment"), "APS", data);
                    }
                }
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }
}
