package com.maersk.sd1.common.integration;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.request.HttpRequestWithBody;
import com.mashape.unirest.request.body.RequestBodyEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SD1IntegrationConnection {

    @Autowired
    private Environment env;

    private String urlProperty = "msk.api.apiUserLogin";
    private String userProperty = "msk.api.apiUserLogin.user";
    private String passwordProperty = "msk.api.apiUserLogin.password";
    private String systemProperty = "msk.api.apiUserLogin.system";
    private String clientIdProperty = "msk.api.apiUserLogin.clientId";
    private String clientSecretProperty = "msk.api.apiUserLogin.clientSecret";
    private String defaultClientId = "API_MAERSK_EXT";
    private String defaultClientSecret = "33caa750333af31d49d39e9251ecb121";
    private String clientId = "API_MAERSK_EXT";
    private String clientSecret = "33caa750333af31d49d39e9251ecb121";

    private String url;
    private String user;
    private String password;
    private String system;
    private boolean disableAuth;
    private String token;

    public SD1IntegrationConnection(boolean disableAuth) {
        this.disableAuth = disableAuth;
    }

    public SD1IntegrationConnection(String user, String password, String system) {
        this.user = user;
        this.password = password;
        this.system = system;
    }

    public SD1IntegrationConnection(String url, String user, String password, String system) {
        this.url = url;
        this.user = user;
        this.password = password;
        this.system = system;
    }

    public SD1IntegrationConnection(String clientId, String clientSecret, String user, String password, String system) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.user = user;
        this.password = password;
        this.system = system;
    }

    public SD1IntegrationConnection(String url, String clientId, String clientSecret, String user, String password, String system) {
        this.url = url;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.user = user;
        this.password = password;
        this.system = system;
    }

    public String getTokenAPI() throws Exception {
        if (this.disableAuth) {
            return null;
        } else if (this.token != null) {
            return this.token;
        } else {
            this.url = Optional.ofNullable(this.url).or(() -> Optional.ofNullable(env.getProperty(this.urlProperty))).orElseThrow();
            this.user = Optional.ofNullable(this.user).or(() -> Optional.ofNullable(env.getProperty(this.userProperty))).orElseThrow();
            this.password = Optional.ofNullable(this.password).or(() -> Optional.ofNullable(env.getProperty(this.passwordProperty))).orElseThrow();
            this.system = Optional.ofNullable(this.system).or(() -> Optional.ofNullable(env.getProperty(this.systemProperty))).orElseThrow();
            this.clientId = Optional.ofNullable(this.clientId).or(() -> Optional.ofNullable(env.getProperty(this.clientIdProperty))).orElseThrow();
            this.clientSecret = Optional.ofNullable(this.clientSecret).or(() -> Optional.ofNullable(env.getProperty(this.clientSecretProperty))).orElseThrow();

            Map<String, Object> bodyT = new HashMap<>();
            Map<String, Object> f = new HashMap<>();
            Map<String, String> data = new HashMap<>();
            data.put("id", this.user);
            data.put("password", this.password);
            data.put("sistem_id", this.system);
            f.put("F", data);
            bodyT.put("SEG", f);
            Map<String, Object> authF = new HashMap<>();
            Map<String, String> authData = new HashMap<>();
            authData.put("clientId", this.clientId);
            authData.put("clientSecret", this.clientSecret);
            authF.put("F", authData);
            bodyT.put("AUT", authF);
            Gson prettyGson = (new GsonBuilder()).setPrettyPrinting().create();
            String bodyF = prettyGson.toJson(bodyT);

            try {
                RequestBodyEntity resp = Unirest.post(this.url).body(bodyF);
                HttpResponse<String> response = resp.asString();
                JSONObject body = new JSONObject((String) response.getBody());

                if (!body.isEmpty()) {
                    if (response.getStatus() != HttpStatus.SC_OK) {
                        throw new Exception(body.getString("message"));
                    }

                    if (!body.getBoolean("isCorrect")) {
                        throw new Exception(body.getString("message"));
                    }

                    String result = body.get("result").toString();
                    if (result.equals("false")) {
                        throw new Exception("User or password incorrect");
                    }

                    JSONObject resultJSON = new JSONObject(result);
                    if (!resultJSON.isEmpty()) {
                        return resultJSON.get("token").toString();
                    }
                }
                return null;
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception("ohrest SD1IntegrationConnection apiUserLogin: " + e.getMessage());
            }
        }
    }

    public HashMap<String, Object> post(String url, String data, HttpHeaders headers) throws Exception {
        if (!this.disableAuth && this.token == null) {
            this.token = this.getTokenAPI();
        }

        try {
            HttpRequestWithBody req = Unirest.post(url);
            if (!this.disableAuth) {
                req.header("Authorization", "Bearer " + this.token);
            }

            if (headers != null && !headers.isEmpty()) {
                headers.forEach((key, value) -> req.header(key, value.toString()));
            }

            req.header("Content-Type", "application/json");
            RequestBodyEntity resp = req.body(data);
            HttpResponse<String> response = resp.asString();
            String body = (String) response.getBody();
                if (body != null && !body.isEmpty() && body.charAt(0) == '{') {
                JSONObject result = new JSONObject(body);
                if (!result.isEmpty() && response.getStatus() == HttpStatus.SC_OK) {
                    return (HashMap<String, Object>) result.toMap();
                } else {
                    throw new Exception();
                }
            } else {
                throw new Exception("Html Error");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("Integration Error");
        }
    }

    public HashMap<String, Object> post(String url, String data) throws Exception {
        return this.post(url, data, new HttpHeaders());
    }

    public HashMap<String, Object> postMap(String url, String prefix, Map<String, String> data, HttpHeaders headers) throws Exception {
        Map<String, Object> bodyT = new HashMap<>();
        Map<String, Object> f = new HashMap<>();
        f.put("F", data);
        bodyT.put(prefix, f);
        Gson prettyGson = (new GsonBuilder()).setPrettyPrinting().create();
        String bodyF = prettyGson.toJson(bodyT);
        return this.post(url, bodyF, headers);
    }

    public HashMap<String, Object> postMap(String url, String prefix, Map<String, String> data) throws Exception {
        return this.postMap(url, prefix, data, new HttpHeaders());
    }
}
