package com.maersk.sd1.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.maersk.sd1.sdf.controller.dto.GateOutFullSchedulingEditOutput;

import java.io.IOException;

public class GateOutFullSchedulingEditOutputSerializer extends StdSerializer<GateOutFullSchedulingEditOutput> {

    public GateOutFullSchedulingEditOutputSerializer() {
        this(null);
    }

    public GateOutFullSchedulingEditOutputSerializer(Class<GateOutFullSchedulingEditOutput> t) {
        super(t);
    }

    @Override
    public void serialize(GateOutFullSchedulingEditOutput value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        Object serializedArray = new Object[]{
                value.getRespResult(),
                value.getRespMessage()
        };
        gen.writeObject(serializedArray);
    }
}
