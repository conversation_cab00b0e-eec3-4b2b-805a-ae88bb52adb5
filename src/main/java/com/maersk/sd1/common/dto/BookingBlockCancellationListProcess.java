package com.maersk.sd1.common.dto;

public interface BookingBlockCancellationListProcess {
    Long getCancellationBlockBookingId();

    Long getCategoryType();

    String getCategoryTypeDescription();

    String getBookingReleased();

    Long getDocumentLoadId();

    String getBookingNumber();

    String getVesselVoyage();

    Long getOperationCategoryId();

    String getOperationDescription();

    Long getCancellationReason();

    String getCancellationReasonDescription();

    String getCancellationDate();

    Long getRegisteredUserId();

    String getRegisteredUserFirstName();

    String getRegisteredUserLastName();

    Long getBookingReleaseReason();

    String getBookingReleaseReasonDescription();

    String getBookingReleaseDate();

    Long getBookingReleaseUserId();

    String getBookingReleaseUserFirstName();

    String getBookingReleaseUserLastName();

    Long getCancellationOrigin();

    String getCancellationOriginDescription();

    Long getBookingReleaseOriginId();

    String getBookingReleaseOriginDescription();
}