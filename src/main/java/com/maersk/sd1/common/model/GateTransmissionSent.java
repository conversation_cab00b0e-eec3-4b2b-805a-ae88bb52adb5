package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.model.PendingSendEmail;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "edi_codeco_envio", schema = "sde", indexes = {
        @Index(name = "EDI_GATE_SENDX_STATE_SETTING_ID", columnList = "cat_estado_envio_codeco_id, seteo_edi_codeco_id, cat_structure_format_id")
})
public class GateTransmissionSent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "edi_codeco_envio_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_estado_envio_codeco_id", nullable = false)
    private Catalog catGateTransmissionStatus;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "seteo_edi_codeco_id", nullable = false)
    private GateTransmissionSetting gateTransmissionSetting;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre_archivo_codeco", nullable = false, length = 100)
    private String gateTransmissionNameFile;

    @Nationalized
    @Lob
    @Column(name = "contenido_archivo_codeco")
    private String gateTransmissionContent;

    @Column(name = "fecha_envio_archivo_codeco")
    private LocalDateTime gateTransmissionSentFileDate;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Lob
    @Column(name = "lista_edi_codeco_id")
    private String listGateTransmissionId;

    @Size(max = 200)
    @Column(name = "correo_destinatario", length = 200)
    private String recipientMail;

    @Nationalized
    @Lob
    @Column(name = "archivos_procesar")
    private String processRecords;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_pendiente_enviar_id")
    private PendingSendEmail pendingSendEmail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sftp_pendiente_enviar_id")
    private SftpPendingSend sftpPendingSend;

    @Size(max = 250)
    @Column(name = "codeco_envio_comentario", length = 250)
    private String gateTransmissionSentComment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_structure_format_id")
    private Catalog catStructureFormat;

}