package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "dashboard", schema = "ges", uniqueConstraints = {
        @UniqueConstraint(name = "U_dashboard_id", columnNames = {"id"})
})
public class Dashboard {
    @Id
    @Column(name = "dashboard_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "menu_id")
    private Menu menu;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre", nullable = false, length = 100)
    private String name;

    @NotNull
    @Lob
    @Column(name = "descripcion", nullable = false)
    private String description;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre_store", nullable = false, length = 100)
    private String nameStore;

    @Lob
    @Column(name = "parametros")
    private String parameters;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_dashboard_id", nullable = false)
    private Catalog tipoDashboard;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "estado", nullable = false)
    private Boolean status = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 50)
    @Column(name = "id", length = 50)
    private String id1;

}