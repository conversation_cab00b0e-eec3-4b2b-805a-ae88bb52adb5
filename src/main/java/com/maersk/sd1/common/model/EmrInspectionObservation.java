package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "emr_inspection_observation", schema = "sdf", indexes = {
        @Index(name = "INSP_FULL_COMM_INSP_IDX", columnList = "emr_inspection_id")
})
public class EmrInspectionObservation {

    @Id
    @Column(name = "emr_inspection_observation_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "emr_inspection_id", nullable = false)
    private EmrInspection emrInspection;

    @Size(max = 250)
    @NotNull
    @Column(name = "comment", nullable = false, length = 250)
    private String comment;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

}