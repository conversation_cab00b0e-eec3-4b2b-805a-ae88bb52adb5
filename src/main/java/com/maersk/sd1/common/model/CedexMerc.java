package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "CedexMerc.cedexSetupCorrect",
        procedureName = "[sde].cedex_setup_correct",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "@sub_business_unit_id", type = java.math.BigDecimal.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "@inspection_type", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "@shipping_line_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "@container_type_id", type = java.math.BigDecimal.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "@container_size_id", type = java.math.BigDecimal.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "@cedex_setup", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "@resp_status", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "@resp_message", type = String.class)
        }
)
@Table(name = "cedex_merc", schema = "sde")
public class CedexMerc {
    @Id
    @Column(name = "cedex_merc_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit businessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tipo_estimado_id", nullable = false)
    private Catalog catEstimateType;

    @Size(max = 1)
    @NotNull
    @Column(name = "tipo_estimado", nullable = false, length = 1)
    private String estimateType;

    @NotNull
    @Column(name = "es_reefer", nullable = false)
    private Boolean isReefer = false;

    @Size(max = 3)
    @Column(name = "tamano_cnt", length = 3)
    private String containerSize;

    @Size(max = 6)
    @Column(name = "codigo_componente", length = 6)
    private String componentCode;

    @Size(max = 6)
    @Column(name = "codigo_metodo_rep", length = 6)
    private String codeRepMethod;

    @Size(max = 100)
    @Column(name = "ubicaciones_dano", length = 100)
    private String damageLocations;

    @Column(name = "merc_valor_min")
    private Integer mercMinValue;

    @Column(name = "merc_valor_max")
    private Integer mercMaxValue;

    @Size(max = 1)
    @Column(name = "merc_dimension", length = 1)
    private String mercDimension;

    @Size(max = 6)
    @Column(name = "codigo_marca_motor", length = 6)
    private String engineBrandCode;

    @Column(name = "merc_piezas_max")
    private Integer mercMaxParts;

    @Column(name = "merc_hh")
    private Integer mercManHours;

    @Column(name = "merc_costo_material")
    private Integer mercMaterialCost;

    @Size(max = 6)
    @Column(name = "cedex_merc_codigo", length = 6)
    private String cedexMercCode;

    @Size(max = 240)
    @Column(name = "cedex_merc_descripcion", length = 240)
    private String cedexMercDescription;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "moneda_id")
    private Currency currency;

    @Size(max = 240)
    @Column(name = "merc_observacion", length = 240)
    private String mercObservation;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_unidad_negocio_id")
    private BusinessUnit subBusinessUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_repair_group_code_id")
    private Catalog catRepairGroupCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_type_code_group_id")
    private Catalog catTypeCodeGroup;

    @Column(name = "flag_show_cleaning_section_interior")
    private Boolean flagShowCleaningSectionInterior;

    @Column(name = "flag_show_cleaning_section_bottom")
    private Boolean flagShowCleaningSectionBottom;

    @Column(name = "flag_show_cleaning_section_right")
    private Boolean flagShowCleaningSectionRight;

    @Column(name = "flag_show_cleaning_section_left")
    private Boolean flagShowCleaningSectionLeft;

    @Column(name = "flag_show_cleaning_section_top")
    private Boolean flagShowCleaningSectionTop;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shipping_line_id")
    private ShippingLine shippingLine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id")
    private Company customer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_equipment_category_id")
    private Catalog catCategoryEquipment;

    @Size(max = 150)
    @Column(name = "damage_code", length = 150)
    private String damageCode;

}