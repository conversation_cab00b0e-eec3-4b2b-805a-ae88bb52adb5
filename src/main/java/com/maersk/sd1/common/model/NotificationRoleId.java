package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class NotificationRoleId implements Serializable {
    private static final long serialVersionUID = -4339043540211735418L;
    @NotNull
    @Column(name = "notificacion_id", nullable = false)
    private Integer notificationId;

    @NotNull
    @Column(name = "rol_id", nullable = false)
    private Integer roleId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        NotificationRoleId entity = (NotificationRoleId) o;
        return Objects.equals(this.roleId, entity.roleId) &&
                Objects.equals(this.notificationId, entity.notificationId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleId, notificationId);
    }

}