package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@IdClass(UserRoleBusinessUnitId.class)
@Table(name = "usuario_rol_unidad_negocio", schema = "seg")
public class UserRoleBusinessUnit {

    @Id
    @NotNull
    @Column(name = "usuario_id", nullable = false)
    private Integer usuario;

    @Id
    @NotNull
    @Column(name = "rol_id", nullable = false)
    private Integer rol;

    @Id
    @NotNull
    @Column(name = "unidad_negocio_id", nullable = false)
    private Integer unidadNegocio;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("usuario")
    @JoinColumn(name = "usuario_id", nullable = false)
    private User usuario1;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("rol")
    @JoinColumn(name = "rol_id", nullable = false)
    private Role role;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("unidadNegocio")
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit unidadNegocio1;
}