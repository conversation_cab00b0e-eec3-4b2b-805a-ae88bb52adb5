package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion", schema = "seg")
public class Notification {
    @Id
    @Column(name = "notificacion_id", nullable = false)
    private Integer id;

    @NotNull
    @Lob
    @Column(name = "subtitulo", nullable = false)
    private String subtitle;

    @NotNull
    @Lob
    @Column(name = "descripcion", nullable = false)
    private String description;

    @NotNull
    @Column(name = "nivel", nullable = false)
    private Character level;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "unica_vez", nullable = false)
    private Character onlyTime;

    @NotNull
    @Column(name = "unica_alerta", nullable = false)
    private Character onlyAlert;

    @Column(name = "fecha_caducidad")
    private LocalDateTime expirationDate;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "estado", nullable = false)
    private Character status;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Size(max = 100)
    @Column(name = "icono", length = 100)
    private String icon;

    @Size(max = 50)
    @Column(name = "titulo", length = 50)
    private String title;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "business_unit_id")
    private BusinessUnit businessUnit;

}