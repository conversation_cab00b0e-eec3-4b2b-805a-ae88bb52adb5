package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "Chassis.validateChassisStock",
        procedureName = "sdh.validate_chassis_stock",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name="business_unit_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name="sub_business_unit_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name="cat_movement_type_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name="chassis_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name="chassis_number", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name="user_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name="languaje_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name="message_out", type = String.class)
        }
)
@Table(name = "chassis", schema = "sdh", indexes = {
        @Index(name = "chax_number", columnList = "chassis_number")
}, uniqueConstraints = {
        @UniqueConstraint(name = "UQ_SDH_CHASSIS", columnNames = {"business_unit_id", "chassis_number"})
})
public class Chassis {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "chassis_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "business_unit_id")
    private BusinessUnit businessUnit;

    @Size(max = 12)
    @Column(name = "chassis_number", length = 12)
    private String chassisNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_company_id")
    private Company ownerCompany;

    @NotNull
    @Column(name = "tare", nullable = false, precision = 6, scale = 2)
    private BigDecimal tare;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tare_unit_id", nullable = false)
    private Catalog catTareUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_type_id", nullable = false)
    private Catalog catChassisType;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @Column(name = "registration_date")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_source_creation_chassis_id")
    private Catalog catSourceCreationChassis;

    @Size(max = 20)
    @Column(name = "trace_chassis", length = 20)
    private String traceChassis;

}