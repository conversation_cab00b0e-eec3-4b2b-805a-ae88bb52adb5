package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Company;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "empresa_direccion", schema = "ges")
public class CompanyAddress {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "empresa_direccion_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "empresa_id", nullable = false)
    private Company empresa;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ubigeo_id")
    private Ubigeo ubigeo;

    @Size(max = 200)
    @Column(name = "direccion", length = 200)
    private String address;

    @Size(max = 200)
    @Column(name = "telefono", length = 200)
    private String phone;

    @Size(max = 200)
    @Column(name = "correo", length = 200)
    private String mail;

    @Size(max = 20)
    @Column(name = "longitud", length = 20)
    private String longitude;

    @Size(max = 20)
    @Column(name = "latitud", length = 20)
    private String latitude;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre", nullable = false, length = 100)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tipo_direccion_id")
    private Catalog addressType;

}