package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "programacion_nave_contenedor", schema = "sds", indexes = {
        @Index(name = "PNCX_PROG_NAVE_CNT", columnList = "contenedor_id, programacion_nave_detalle_id")
})
public class VesselProgrammingContainer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "programacion_nave_contenedor_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "programacion_nave_detalle_id", nullable = false)
    private VesselProgrammingDetail vesselProgrammingDetail;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "contenedor_id", nullable = false)
    private Container container;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tipo_contenedor_manifestado_id", nullable = false)
    private Catalog catManifestedContainerType;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tamano_manifestado_id", nullable = false)
    private Catalog catManifestedSize;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_contenedor_recepcionado_id")
    private Catalog catReceivedContainerType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tamano_recepcionado_id")
    private Catalog catReceivedSize;

    @Column(name = "cantidad_recepcionado")
    private Integer receivedQuantity;

    @Column(name = "peso_recepcionado", precision = 15, scale = 3)
    private BigDecimal receivedWeight;

    @Size(max = 10)
    @Column(name = "temperatura_manifestada_c", length = 10)
    private String manifestedTemperatureC;

    @Size(max = 10)
    @Column(name = "temperatura_recepcionada_c", length = 10)
    private String receivedTemperatureC;

    @Size(max = 10)
    @Column(name = "temperatura_despachada_c", length = 10)
    private String dispatchedTemperatureC;

    @Size(max = 20)
    @Column(name = "precinto_manifestado_1", length = 20)
    private String manifestedSeal1;

    @Size(max = 20)
    @Column(name = "precinto_manifestado_2", length = 20)
    private String manifestedSeal2;

    @Size(max = 20)
    @Column(name = "precinto_manifestado_3", length = 20)
    private String manifestedSeal3;

    @Size(max = 20)
    @Column(name = "precinto_manifestado_4", length = 20)
    private String manifestedSeal4;

    @Size(max = 20)
    @Column(name = "precinto_recepcionado_1", length = 20)
    private String receivedSeal1;

    @Size(max = 20)
    @Column(name = "precinto_recepcionado_2", length = 20)
    private String receivedSeal2;

    @Size(max = 20)
    @Column(name = "precinto_recepcionado_3", length = 20)
    private String receivedSeal3;

    @Size(max = 20)
    @Column(name = "precinto_recepcionado_4", length = 20)
    private String receivedSeal4;

    @Size(max = 20)
    @Column(name = "precinto_despachado_1", length = 20)
    private String dispatchedSeal1;

    @Size(max = 20)
    @Column(name = "precinto_despachado_2", length = 20)
    private String dispatchedSeal2;

    @Size(max = 20)
    @Column(name = "precinto_despachado_3", length = 20)
    private String dispatchedSeal3;

    @Size(max = 20)
    @Column(name = "precinto_despachado_4", length = 20)
    private String dispatchedSeal4;

    @NotNull
    @Column(name = "es_retiro_indirecto", nullable = false)
    private Boolean indirectDelivery = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_devolucion_vacio_id")
    private BusinessUnit emptyReturnBusinessUnit;

    @NotNull
    @Column(name = "gatein_empty_liquidado", nullable = false)
    private Boolean emptyGateInSettled = false;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_origen_creacion_id", nullable = false)
    private Catalog catCreationOrigin;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @NotNull
    @Column(name = "es_carga_peligrosa", nullable = false)
    private Boolean isDangerousCargo = false;

    @NotNull
    @Column(name = "es_carga_refrigerada", nullable = false)
    private Boolean isRefrigeratedCargo = false;

    @Size(max = 20)
    @Column(name = "trace_prog_nav_cnt", length = 20)
    private String traceProgVesCnt;

    @Size(max = 20)
    @Column(name = "precinto1_salida_vacio", length = 20)
    private String emptyExitSeal1;

    @Size(max = 20)
    @Column(name = "precinto2_salida_vacio", length = 20)
    private String emptyExitSeal2;

    @Column(name = "fecha_sobrestadia")
    private LocalDateTime demurrageDate;

    @Size(max = 20)
    @Column(name = "precinto3_salida_vacio", length = 20)
    private String emptyExitSeal3;

    @Size(max = 20)
    @Column(name = "precinto4_salida_vacio", length = 20)
    private String emptyExitSeal4;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_contenedor_despachado_id")
    private Catalog catDispatchedContainerType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tamano_despachado_id")
    private Catalog catDispatchedSize;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_received_weight_measure_id")
    private Catalog catReceivedWeightMeasure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_manifest_temperature_measure_id")
    private Catalog catManifestTemperatureMeasure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_received_temperature_measure_id")
    private Catalog catReceivedTemperatureMeasure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_dispatched_temperature_measure_id")
    private Catalog catDispatchedTemperatureMeasure;

}