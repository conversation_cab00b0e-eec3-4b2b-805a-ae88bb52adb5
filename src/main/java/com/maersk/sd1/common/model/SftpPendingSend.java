package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.SftpConfig;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "sftp_pendiente_enviar", schema = "ges", indexes = {
        @Index(name = "nci_msft_1_sftp_pendiente_enviar_0CDF197BB9DD34A06136824E25AFC130", columnList = "indicador_enviado, sftp_config_id")
})
public class SftpPendingSend {
    @Id
    @Column(name = "sftp_pendiente_enviar_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sftp_config_id", nullable = false)
    private SftpConfig sftpConfig;

    @Size(max = 200)
    @Column(name = "origen", length = 200)
    private String origin;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "indicador_enviado", nullable = false)
    private Boolean sentIndicator = false;

    @Size(max = 200)
    @Column(name = "referencia", length = 200)
    private String reference;

    @Nationalized
    @Lob
    @Column(name = "archivos_subir")
    private String uploadRecords;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}