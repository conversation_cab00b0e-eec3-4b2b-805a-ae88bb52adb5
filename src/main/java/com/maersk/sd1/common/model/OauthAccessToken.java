package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "oauth_access_token", schema = "dbo")
public class OauthAccessToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "oauth_access_token_id")
    private Long oauthAccessTokenId;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "authentication_id")
    private String authenticationId;

    @Column(name = "token")
    private byte[] token;

    @Column(name = "token_id")
    private String tokenId;

    @Column(name = "refresh_token")
    private String refreshToken;

    @Column(name = "refresh_token_id")
    private String refreshTokenId;

    @Column(name = "client_id")
    private String clientId;

    @Column(name = "authentication")
    private byte[] authentication;

    @Column(name = "creation_date")
    private LocalDateTime creationDate;
}

