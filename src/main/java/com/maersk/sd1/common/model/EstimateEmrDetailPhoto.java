package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "estimado_emr_detalle_foto", schema = "sde", indexes = {
        @Index(name = "ESTEMRDET_FOTO", columnList = "estimado_emr_detalle_id")
})
public class EstimateEmrDetailPhoto {

    @Id
    @Column(name = "estimado_emr_detalle_foto_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "estimado_emr_detalle_id", nullable = false)
    private EstimateEmrDetail estimateEmrDetail;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "adjunto_id", nullable = false)
    private Attachment attachment;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_estimado_origen_creacion_id")
    private Catalog catCreationOriginEstimate;

}