package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@IdClass(ReportRoleId.class)
@Table(name = "reporte_rol", schema = "ges")
public class ReportRole {

    @Id
    @NotNull
    @Column(name = "reporte_id", nullable = false)
    private Integer report;

    @Id
    @NotNull
    @Column(name = "rol_id", nullable = false)
    private Integer rol;


    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "rol_id", nullable = false)
    private Role role;


    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "reporte_id", nullable = false)
    private Report report1;
}