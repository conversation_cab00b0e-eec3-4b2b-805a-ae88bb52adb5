package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.NotificationJob;
import com.maersk.sd1.common.model.NotificationJobWebBrandId;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_job_web_marca", schema = "seg")
public class NotificationJobWebBrand {
    @EmbeddedId
    private NotificationJobWebBrandId id;

    @MapsId("notificationJobId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_job_id", nullable = false)
    private NotificationJob notificacionJob;

}