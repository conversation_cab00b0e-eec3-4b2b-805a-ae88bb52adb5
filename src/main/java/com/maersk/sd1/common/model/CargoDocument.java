package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "CargoDocument.validateDocumentAvailability",
        procedureName = "sdg.validate_document_availability",
        parameters = {
                @StoredProcedureParameter(name = "business_unit_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "cat_empty_full_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "booking_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "documento_carga_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "document_chassis_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "languaje_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "message", mode = ParameterMode.OUT, type = String.class)
        }
)
@Table(name = "documento_carga", schema = "sds", indexes = {
        @Index(name = "DOCCARX_NUM_DOC", columnList = "documento_carga"),
        @Index(name = "DOCCARX_PROG_NAVE_DET_ID", columnList = "programacion_nave_detalle_id"),
        @Index(name = "doccarga_fecha_registro", columnList = "fecha_registro")
})
public class CargoDocument {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "documento_carga_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "programacion_nave_detalle_id", nullable = false)
    private VesselProgrammingDetail vesselProgrammingDetail;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_origen_carga_id", nullable = false)
    private Catalog catCargoOrigin;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_via_transporte_id", nullable = false)
    private Catalog catTransportRoute;

    @Size(max = 25)
    @NotNull
    @Column(name = "documento_carga", nullable = false, length = 25)
    private String cargoDocument;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "es_desglosado", nullable = false)
    private Boolean isBroken = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "documento_carga_madre_id")
    private CargoDocument masterCargoDocument; //CHECK

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_origen_id")
    private Port originPort;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "puerto_embarque_id", nullable = false)
    private Port loadingPort;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "puerto_descarga_id", nullable = false)
    private Port dischargePort;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_destino_id")
    private Port destinationPort;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_transbordo_id")
    private Port transferPort; //CHECK

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_id")
    private ShippingLine shippingLine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "deposito_id")
    private Depot depot;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_embarcador_id")
    private Company shipperCompany;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_consignatario_id")
    private Company consigneeCompany;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_notificante_id")
    private Company notifierCompany; //CHECK

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_agencia_aduana_id")
    private Company customsAgencyCompany;

    @Size(max = 300)
    @Column(name = "embarcador_detalle", length = 300)
    private String shipperDetail;

    @Size(max = 300)
    @Column(name = "consignatario_detalle", length = 300)
    private String consigneeDetail;

    @Size(max = 300)
    @Column(name = "notificante_detalle", length = 300)
    private String notifierDetail;

    @Size(max = 10)
    @Column(name = "numero_detalle_aduana", length = 10)
    private String customsDetailsNumber; //CHECK

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_estado_documento_carga_id", nullable = false)
    private Catalog catDocumentCargoStatus;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_origen_creacion_id", nullable = false)
    private Catalog catOriginCreation;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "deposito_vacio_id")
    private Depot emptyDepot;

    @Size(max = 20)
    @Column(name = "trace_doc_carga", length = 20)
    private String traceCargoDocument;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_cargo_document_type_id")
    private Catalog catCargoDocumentType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_move_type_id")
    private Catalog catMoveType;

    @Column(name = "maersk_depot_with_sd1")
    private Boolean maerskDepotWithSd1;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "origin_destination_depot_id")
    private Depot originDestinationDepot;

}