package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.MenuServiceId;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "menu_servicio", schema = "seg")
public class MenuService {
    @EmbeddedId
    private MenuServiceId id;

    @MapsId("menuId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "menu_id", nullable = false)
    private Menu menu;

    @MapsId("serviceId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "servicio_id", nullable = false)
    private Service service;

}