package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "EirChassisZone.chassisFirstActivityZone",
        procedureName = "sdh.chassis_first_activity_zone",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "eir_chasis_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "cat_movimiento_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "chassis_type_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "user_registration_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "creation_origen_id", type = Integer.class)
        }
)
@Table(name = "eir_chassis_zone", schema = "sdh", indexes = {
        @Index(name = "chassiszonex_eir_chassis_id", columnList = "eir_chassis_id")
})
public class EirChassisZone {

    @Id
    @Column(name = "eir_chassis_zone_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_chassis_id", nullable = false)
    private EirChassis eirChassis;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_zone_id", nullable = false)
    private Catalog catChassisZone;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 20)
    @Column(name = "trace_eir_chassis_zone", length = 20)
    private String traceEirChassisZone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_createsource_eirchazone_id")
    private Catalog catCreatesourceEirchazone;

}