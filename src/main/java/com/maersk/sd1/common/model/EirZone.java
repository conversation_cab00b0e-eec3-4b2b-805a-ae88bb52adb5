package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "EirZone.firstActivityZoneEmptyContainer",
        procedureName = "sde.first_activity_zone_empty_container",
        parameters = {
                @StoredProcedureParameter(name = "eir_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "cat_movimiento_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "cat_empty_full_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "user_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "creation_origen_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "type_cnt_id", mode = ParameterMode.IN, type = Integer.class)
        }
)
@Table(name = "eir_zona", schema = "sde", indexes = {
        @Index(name = "EIZOX_EIR", columnList = "eir_id")
})
public class EirZone {

    @Id
    @Column(name = "eir_zona_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_zona_contenedor_id", nullable = false)
    private Catalog catContainerZone;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "ok_secado")
    private Boolean flagDried;

    @Column(name = "fecha_ok_secado")
    private LocalDateTime flagDriedDate;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modicacion")
    private LocalDateTime dateModicacion;

    @Size(max = 20)
    @Column(name = "trace_eir_zona", length = 20)
    private String traceEirZone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_origen_creacion_eirzona_id")
    private Catalog catOrigenCreacionEirzona;

}