package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "log_contecon_bloqueado", schema = "sde", indexes = {
        @Index(name = "LOGCONTECON_BLO_EIR", columnList = "eir_id")
})
public class BlockedConteconLog {
    @Id
    @Column(name = "log_contecon_bloqueado_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_id")
    private Eir eir;

    @Size(max = 100)
    @Column(name = "webservice", length = 100)
    private String webservice;

    @Column(name = "nroEnvios")
    private Integer shippingNumber;

    @Column(name = "liberado")
    private Boolean released;

    @Column(name = "fechaPrimerEnvio")
    private LocalDateTime firstShipmentDate;

    @Column(name = "fechaUltimoEnvio")
    private LocalDateTime lastShippingDate;

    @Column(name = "fecha_registro")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_registro_id")
    private User registrationUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}