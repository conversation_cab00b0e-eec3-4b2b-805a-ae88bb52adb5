package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.DocumentTemplate;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "documento_plantilla_atributo", schema = "ges")
public class DocumentTemplateAttribute {

    @Id
    @Column(name = "documento_plantilla_atributo_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "documento_plantilla_id", nullable = false)
    private DocumentTemplate documentTemplate;

    @Size(max = 80)
    @NotNull
    @Column(name = "campo", nullable = false, length = 80)
    private String field;

}