package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "persona", schema = "ges", indexes = {
        @Index(name = "PERSX_NRODCTO", columnList = "documento_identidad")
})
public class Person {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "persona_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_documento_identidad_id", nullable = false)
    private Catalog catIdentificationDocumentType;

    @Size(max = 15)
    @NotNull
    @Column(name = "documento_identidad", nullable = false, length = 15)
    private String identificationDocument;

    @Size(max = 100)
    @NotNull
    @Column(name = "apellido_parterno", nullable = false, length = 100)
    private String firstLastName;

    @Size(max = 100)
    @Column(name = "apellido_materno", length = 100)
    private String secondLastName;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombres", nullable = false, length = 100)
    private String names;

    @NotNull
    @Column(name = "fecha_nacimiento", nullable = false)
    private LocalDateTime birthDate;

    @Size(max = 100)
    @Column(name = "correo", length = 100)
    private String mail;

    @Size(max = 50)
    @Column(name = "telefono", length = 50)
    private String phone;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_estado_persona_id", nullable = false)
    private Catalog catPersonStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tipo_area_trabajo_id")
    private Catalog catWorkArea;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 50)
    @Column(name = "licencia_conducir", length = 50)
    private String driversLicense;

    @Column(name = "fecha_vigencia_licencia_conducir")
    private LocalDateTime driversLicenseValidityDate;

    @Size(max = 50)
    @Column(name = "carnet", length = 50)
    private String card;

    @Column(name = "fecha_vigencia_carnet")
    private LocalDateTime cardValidityDate;

    @Column(name = "genero")
    private Character gender;

    @Column(name = "situacion_laboral")
    private Integer employmentStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_origen_creacion_pers_id")
    private Catalog catPersonCreationOrigin;

}