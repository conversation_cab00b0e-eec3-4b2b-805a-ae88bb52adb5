package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "unidad_negocio", schema = "seg", indexes = {
        @Index(name = "business_unit_alias", columnList = "alias_unidad_negocio"),
        @Index(name = "BISUNIX_BU_PARENT_ID", columnList = "unidad_negocio_padre_id"),
        @Index(name = "BISUNIX_daylight_id", columnList = "daylight_saving_id")
}, uniqueConstraints = {
        @UniqueConstraint(name = "UniqueaAliasUN", columnNames = {"sistema_id", "alias_unidad_negocio"})
})
public class BusinessUnit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "unidad_negocio_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre", nullable = false, length = 100)
    private String name;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "estado", nullable = false)
    private Boolean status = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_padre_id")
    private BusinessUnit parentBusinessUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sistema_id")
    private System system;

    @Size(max = 50)
    @NotNull
    @Column(name = "alias_unidad_negocio", nullable = false, length = 50)
    private String businesUnitAlias;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "daylight_saving_id")
    private DaylightSaving daylightSaving;

    public BusinessUnit(Integer id) {
        this.id = id;
    }

}
