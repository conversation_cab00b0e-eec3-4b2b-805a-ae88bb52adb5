package com.maersk.sd1.common.model;

import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "machinery_inspection_checklist", schema = "sde")
public class MachineryInspectionChecklist {
    @Id
    @Column(name = "machinery_inspection_checklist_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "machinery_eir_id")
    private Eir machineryEir;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inspection_checklist_id")
    private InspectionChecklist inspectionChecklist;

    @Column(name = "flag_checked")
    private Boolean flagChecked;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_zone_id")
    private EirZone eirZone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_standard_inspection_id")
    private Catalog catStandardInspection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_ca_inspection_id")
    private Catalog catCaInspection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_actividad_zona_id")
    private EirActivityZone eirActivityZone;

}