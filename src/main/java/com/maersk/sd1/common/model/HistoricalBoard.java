package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "tabla_historico", schema = "seg")
public class HistoricalBoard {
    @Id
    @Column(name = "tabla_historico_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @NotNull
    @Column(name = "tabla", nullable = false, length = 100)
    private String board;

    @Column(name = "id_value")
    private Integer idValue;

    @NotNull
    @Column(name = "tipo", nullable = false)
    private Character type;

    @Nationalized
    @Lob
    @Column(name = "historico")
    private String historical;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

}