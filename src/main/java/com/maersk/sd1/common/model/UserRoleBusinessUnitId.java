package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserRoleBusinessUnitId implements Serializable {

    private Integer usuario;
    private Integer rol;
    private Integer unidadNegocio;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserRoleBusinessUnitId that = (UserRoleBusinessUnitId) o;

        return Objects.equals(usuario, that.usuario) &&
                Objects.equals(rol, that.rol) &&
                Objects.equals(unidadNegocio, that.unidadNegocio);
    }

    @Override
    public int hashCode() {
        return Objects.hash(usuario, rol, unidadNegocio);
    }
}