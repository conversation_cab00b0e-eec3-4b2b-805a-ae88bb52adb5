package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.InspectionGate;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "inspeccion_gate_foto", schema = "sde", indexes = {
        @Index(name = "inspectiongatephotox_inspeccion_gate_id", columnList = "inspeccion_gate_id")
})
public class InspectionGatePhoto {
    @Id
    @Column(name = "inspeccion_gate_foto_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "inspeccion_gate_id", nullable = false)
    private InspectionGate inspeccionGate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "adjunto_id", nullable = false)
    private Attachment attachment;

    @Size(max = 10)
    @Column(name = "tarea", length = 10)
    private String task;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}