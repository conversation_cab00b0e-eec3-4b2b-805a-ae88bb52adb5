package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "chassis_edi_send_email", schema = "sdh")
public class ChassisEdiSendEmail {
    @Id
    @Column(name = "chassis_edi_send_email_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_pending_send_id")
    private PendingSendEmail emailPendingSend;

    @Lob
    @Column(name = "list_chassis_edi_send_id")
    private String listChassisEdiSendId;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

}