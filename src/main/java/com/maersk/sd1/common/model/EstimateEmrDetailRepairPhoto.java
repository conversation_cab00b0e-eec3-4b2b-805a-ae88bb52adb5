package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Attachment;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "estimate_emr_detail_repair_photo", schema = "sde", indexes = {
        @Index(name = "ESTEMRDETREP_PHOTO", columnList = "estimate_emr_detail_id")
})
public class EstimateEmrDetailRepairPhoto {

    @Id
    @Column(name = "estimate_emr_detail_repair_photo_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "estimate_emr_detail_id", nullable = false)
    private EstimateEmrDetail estimateEmrDetail;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "attached_id", nullable = false)
    private Attachment attachment;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

}