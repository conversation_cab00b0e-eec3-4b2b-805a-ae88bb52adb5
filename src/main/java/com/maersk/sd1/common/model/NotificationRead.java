package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.NotificationReadId;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_leido", schema = "seg")
public class NotificationRead {
    @EmbeddedId
    private NotificationReadId id;

    @MapsId("userId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User user;

    @MapsId("notificationId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_id", nullable = false)
    private Notification notification;

    @NotNull
    @Column(name = "fecha_leido", nullable = false)
    private LocalDateTime dateRead;

}