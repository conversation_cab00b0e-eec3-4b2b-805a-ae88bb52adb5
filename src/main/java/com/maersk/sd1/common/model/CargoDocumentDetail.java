package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "CargoDocumentDetail.validateContainerStock",
        procedureName = "sds.validate_container_stock",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "business_unit_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "sub_business_unit_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "cat_movimiento_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "cat_empty_full_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "container_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "container_number", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "user_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "creation_origen_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "message_trace", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "languaje_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "message", type = String.class)
        }
)
@NamedStoredProcedureQuery(
        name = "CargoDocumentDetail.validateDemurrageDate",
        procedureName = "sds.validate_demurrage_date",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "business_unit_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "sub_business_unit_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "shipping_line_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "container_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "programacion_nave_detalle_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "languaje_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "DemurrageDate", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "resul_restrictive_message", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "result_informative_message", type = String.class)
        }
)
@Table(name = "documento_carga_detalle", schema = "sds", indexes = {
        @Index(name = "DCDX_BK_DET_ID", columnList = "booking_detalle_id"),
        @Index(name = "DCDX_CNT_ID", columnList = "contenedor_id"),
        @Index(name = "DCDX_DOC_CARGA", columnList = "documento_carga_id")
})
public class CargoDocumentDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "documento_carga_detalle_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "documento_carga_id", nullable = false)
    private CargoDocument cargoDocument;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "producto_id", nullable = false)
    private Product product;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contenedor_id")
    private Container container;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "documento_carga_detalle_madre_id")
    private CargoDocumentDetail parentDocumentCargoDetail;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_embalaje_id", nullable = false)
    private Catalog catPackaging;

    @NotNull
    @Column(name = "cantidad_manifestada", nullable = false, precision = 15, scale = 3)
    private BigDecimal manifestedQuantity;

    @NotNull
    @Column(name = "peso_manifestado", nullable = false, precision = 15, scale = 3)
    private BigDecimal manifestedWeight;

    @NotNull
    @Column(name = "volumen_manifestado", nullable = false, precision = 15, scale = 3)
    private BigDecimal manifestedVolume;

    @Column(name = "cantidad_recepcionada", precision = 15, scale = 3)
    private BigDecimal receivedQuantity;

    @Column(name = "peso_recepcionado", precision = 15, scale = 3)
    private BigDecimal receivedWeight;

    @Column(name = "volumen_recepcionado", precision = 15, scale = 3)
    private BigDecimal receivedVolume;

    @Column(name = "cantidad_despachada", precision = 15, scale = 3)
    private BigDecimal dispatchedQuantity;

    @Column(name = "peso_despachado", precision = 15, scale = 3)
    private BigDecimal dispatchedWeight;

    @Column(name = "volumen_despachado", precision = 15, scale = 3)
    private BigDecimal dispatchedVolume;

    @Column(name = "cantidad_inmovilizado", precision = 15, scale = 3)
    private BigDecimal immobilizedQuantity;

    @Column(name = "peso_inmovilizado", precision = 15, scale = 3)
    private BigDecimal immobilizedWeight;

    @Column(name = "volumen_inmovilizado", precision = 15, scale = 3)
    private BigDecimal immobilizedVolume;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_unidad_medida_peso_id", nullable = false)
    private Catalog catWeightMeasureUnit;

    @NotNull
    @Column(name = "dice_contener", nullable = false)
    private Integer saysContain; //CHECK

    @Column(name = "cantidad_saldo", precision = 15, scale = 3)
    private BigDecimal balanceQuantity;

    @Column(name = "peso_saldo", precision = 15, scale = 3)
    private BigDecimal balanceWeight;

    @Column(name = "volumen_saldo", precision = 15, scale = 3)
    private BigDecimal balanceVolume;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "es_carga_peligrosa", nullable = false)
    private Boolean isDangerousCargo = false;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "es_carga_refrigerada", nullable = false)
    private Boolean isRefrigeratedCargo = false;

    @Size(max = 8000)
    @Column(name = "marcas", length = 8000)
    private String brands;

    @Size(max = 8000)
    @Column(name = "mercaderia", length = 8000)
    private String commodity;

    @Size(max = 25)
    @Column(name = "chasis_manifestado", length = 25)
    private String manifestedChassis;

    @Size(max = 25)
    @Column(name = "chasis_recepcionado", length = 25)
    private String receivedChassis;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_condicion_carga_id", nullable = false)
    private Catalog catCargoCondition;

    @Column(name = "cantidad_inventariado", precision = 15, scale = 3)
    private BigDecimal inventoryQuantity;

    @Column(name = "peso_inventariado", precision = 15, scale = 3)
    private BigDecimal inventoryWeight;

    @Column(name = "volumen_inventariado", precision = 15, scale = 3)
    private BigDecimal inventoryVolume;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_recojo_vacio_id")
    private BusinessUnit emptyPickUpBusinessUnit;

    @NotNull
    @Column(name = "gateout_empty_liquidado", nullable = false)
    private Boolean emptyGateoutSettled = false;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_origen_creacion_id", nullable = false)
    private Catalog catCreationOrigin;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_contenedor_manifestado_id")
    private Catalog catManifestedContainerType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tamano_manifestado_id")
    private Catalog catManifestedSize;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_unidad_medida_cantidad_id", nullable = false)
    private Catalog catMeasureUnitQuantity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "booking_detalle_id")
    private BookingDetail bookingDetail;

    @Size(max = 20)
    @Column(name = "trace_doc_carga_detalle", length = 20)
    private String traceCargoDocumentDetail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_receipt_reason_full_id")
    private Catalog catFullReceiptReason;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_received_weight_measure_id")
    private Catalog catReceivedWeightMeasure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_dispatched_weight_measure_id")
    private Catalog catDispatchedWeightMeasure;

}