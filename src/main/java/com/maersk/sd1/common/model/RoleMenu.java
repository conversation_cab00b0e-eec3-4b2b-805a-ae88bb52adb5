package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.Role;
import com.maersk.sd1.common.model.RoleMenuId;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "rol_menu", schema = "seg")
public class RoleMenu {
    @EmbeddedId
    private RoleMenuId id;

    @MapsId("roleId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "rol_id", nullable = false)
    private Role role;

    @MapsId("menuId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "menu_id", nullable = false)
    private Menu menu;

}