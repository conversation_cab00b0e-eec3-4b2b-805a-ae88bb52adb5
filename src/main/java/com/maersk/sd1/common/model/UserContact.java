package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "usuario_contacto", schema = "seg")
public class UserContact {
    @Id
    @Column(name = "usuario_contacto_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User user;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_telefono", nullable = false)
    private Catalog catPhoneType;

    @Size(max = 30)
    @NotNull
    @Column(name = "telefono", nullable = false, length = 30)
    private String phone;

    @NotNull
    @Column(name = "es_principal", nullable = false)
    private Character isPrincipal;

}