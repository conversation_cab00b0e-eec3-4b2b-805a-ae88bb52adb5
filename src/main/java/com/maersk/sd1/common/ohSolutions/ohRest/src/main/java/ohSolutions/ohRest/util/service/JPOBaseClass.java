package com.maersk.sd1.common.ohSolutions.ohRest.src.main.java.ohSolutions.ohRest.util.service;

import java.util.HashMap;
import java.util.Map;

import com.google.gson.Gson;

public class JPOBaseClass {
	
	public String toJSON(){

		Gson gson = new Gson();
		
		return gson.toJson(this);
		
	}
	
	public String toJSON(String reference){

		Map<String, Object> pathField = new HashMap<String, Object>();
		
		pathField.put("F", this);
		
		Map<String, Object> pathbase = new HashMap<String, Object>();
		
		pathbase.put(reference, pathField);
		
		Gson gson = new Gson();
		
		return gson.toJson(pathbase);
		
	}
	
}