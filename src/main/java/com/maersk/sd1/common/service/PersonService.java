package com.maersk.sd1.common.service;

import com.maersk.sd1.common.repository.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class PersonService {

    private final PersonRepository personRepository;

    @Autowired
    public PersonService(PersonRepository personRepository)
    {
        this.personRepository = personRepository;
    }

    public HashMap<Integer, String> findFullNamesByIds(List<Integer> ids) {
        HashMap<Integer, String> result = new HashMap<>();
        List<Object[]> rawResult = personRepository.findFullNamesByIds(ids);
        rawResult.stream().forEach(rr -> result.put((Integer) rr[0], (String) rr[1]));
        return result;
    }
}
