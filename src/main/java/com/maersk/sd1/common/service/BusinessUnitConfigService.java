package com.maersk.sd1.common.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.repository.BusinessUnitConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class BusinessUnitConfigService {

    BusinessUnitConfigRepository businessUnitConfigRepository;

    @Autowired
    public BusinessUnitConfigService(BusinessUnitConfigRepository businessUnitConfigRepository)
    {
        this.businessUnitConfigRepository = businessUnitConfigRepository;
    }

    public LocalDateTime getDateTime(Integer subBusinessUnitId, LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        Integer utcNumber = businessUnitConfigRepository.findUTCNumber(subBusinessUnitId, dateTime);
        if (utcNumber != null) {
            return dateTime.plusHours(utcNumber);
        } else {
            return dateTime;
        }
    }

    public String getDateTimeFormat(Integer businessUnitId) {

        String dateFormat = businessUnitConfigRepository.findConfigurationType(businessUnitId, Parameter.CATALOG_CONFIGURATION_BUSINESS_UNIT_FORMAT_DATE_TIME_ALIAS);

        if(dateFormat != null && dateFormat.contains(" ")){
            String[] splitDate = dateFormat.split(" ");
            return splitDate[0].trim().replace("mm", "MM")+" "+splitDate[1].trim().replace("hh", "HH");
        }

        return "dd/MM/yyyy HH:mm:ss";

    }

    public String getDateFormat(Integer businessUnitId) {
        String dateFormat = businessUnitConfigRepository.findConfigurationType(businessUnitId, Parameter.CATALOG_CONFIGURATION_BUSINESS_UNIT_FORMAT_DATE_ALIAS);
        if (dateFormat == null || dateFormat.isEmpty()) {
            dateFormat = "dd/MM/yyyy";
        } else {
            dateFormat = dateFormat.replace("mm", "MM");
        }
        return dateFormat;
    }

}
