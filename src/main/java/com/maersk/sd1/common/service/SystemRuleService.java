package com.maersk.sd1.common.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.dto.SystemRuleMergedShippingLinesDTO;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class SystemRuleService {


    private final SystemRuleRepository systemRuleRepository;

    @Autowired
    public SystemRuleService(SystemRuleRepository systemRuleRepository) {
        this.systemRuleRepository = systemRuleRepository;
    }

    public List<SystemRuleMergedShippingLinesDTO> mapRuleMergedShippingLines(){

        Optional<String> rule = systemRuleRepository.findRuleFromSystemRuleByAlias(Parameter.RULE_ALIAS_MERGED_SHIPPING_INES);

        if(rule.isPresent()){

            ObjectMapper objectMapper = new ObjectMapper();
            try {
                return objectMapper.readValue(rule.get(), objectMapper.getTypeFactory().constructCollectionType(List.class, SystemRuleMergedShippingLinesDTO.class));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }

        }

        return null;

    }

}