package com.maersk.sd1.common.service;

import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

@Service
public class BookingEdiUtils {

    private BookingEdiUtils() {
    }
    public static String fnLeeLineaCoparn(String psCadena, int piSegmento, int piSubSegmento) {
        if (psCadena == null) {
            psCadena = "";
        }
        psCadena = psCadena.trim();

        int i = 0;
        int n = 0;
        int q = 0;
        String lsReturn = "";

        if (piSegmento == 1) {
            List<Integer> indices = new ArrayList<>();
            indices.add(psCadena.indexOf('+'));
            indices.add(psCadena.indexOf('\''));

            i = indices.stream().filter(index -> index > 0).min(Integer::compareTo).orElse(0);
            lsReturn = (i != 0) ? psCadena.substring(0, i) : "";
        } else {
            n = 1;
            while (n <= piSegmento - 1) {
                List<Integer> indices = new ArrayList<>();
                indices.add(psCadena.indexOf('+'));
                indices.add(psCadena.indexOf('\''));

                i = indices.stream().filter(index -> index > 0).min(Integer::compareTo).orElse(0);
                if (i > 0) {
                    psCadena = psCadena.substring(i + 1);
                }
                n++;
            }

            if (piSubSegmento > 1) {
                i = psCadena.indexOf('+');
                if (i > 0) {
                    psCadena = psCadena.substring(0, i);
                }

                n = 1;
                while (n <= piSubSegmento - 1) {
                    i = psCadena.indexOf(':');
                    if (i > 0) {
                        q = 1;
                        psCadena = psCadena.substring(i + 1);
                    }
                    n++;
                }
            }

            if ((piSegmento > 0 && piSubSegmento == 0) || piSubSegmento == 1 || (piSubSegmento > 1 && q == 1)) {
                List<Integer> indices = new ArrayList<>();
                indices.add(psCadena.indexOf(':'));
                indices.add(psCadena.indexOf('+'));
                indices.add(psCadena.indexOf('\''));

                i = indices.stream().filter(index -> index > 0).min(Integer::compareTo).orElse(0);
                lsReturn = (i != 0) ? psCadena.substring(0, i) : psCadena;
            }
        }

        lsReturn = lsReturn.replace("&", " ");
        lsReturn = lsReturn.replace(">", " ");
        lsReturn = lsReturn.replace("<", " ");
        return lsReturn;
    }
}
