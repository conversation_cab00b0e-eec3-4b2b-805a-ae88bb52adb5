package com.maersk.sd1.common.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.maersk.sd1.common.model.ContainerRestriction;
import lombok.Data;

import com.maersk.sd1.common.dto.ContainerRestrictionDetailDTO;
import com.maersk.sd1.common.repository.ContainerRestrictionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Data
public class ContainerRestrictionService {

    private ContainerRestrictionRepository containerRestrictionRepository;

    @Autowired
    public ContainerRestrictionService(ContainerRestrictionRepository containerRestrictionRepository)
    {
        this.containerRestrictionRepository = containerRestrictionRepository;
    }

    public String getRestrictionDetailDescription(List<ContainerRestrictionDetailDTO> records) {

        Map<String, List<String>> groups = new HashMap<>();
        for (ContainerRestrictionDetailDTO r : records) {
            String detailRestrictionDescription = r.getDetailRestrictionDescription();
            groups.computeIfAbsent(r.getRestrictionAnnotation(), k -> new ArrayList<>()).add(detailRestrictionDescription);
        }

        return groups.entrySet().stream()
                .map(entry -> entry.getKey() + " (" + String.join(", ", entry.getValue()) + ")")
                .collect(Collectors.joining(", "));
    }

    /**
     * Return a summary of restrictions => 'anotacion_restriccion: reasons'.
     */
    public String findActiveRestrictionsSummary(Integer containerId, Integer subUniId) {
        if (containerId == null || subUniId == null) return "";
        List<ContainerRestriction> list = containerRestrictionRepository.findActiveRestrictionsOfContainer(containerId, subUniId);
        if (list.isEmpty()) {
            return "";
        }
        // In the stored procedure we do: isnull(anotacion_restriccion + ': ' + MOTIVOS,'').
        // If multiple, we can combine them. We'll do just the first one or combine.
        return list.stream()
                .map(cr -> {
                    String note = (cr.getRestrictionAnnotation() != null && !cr.getRestrictionAnnotation().isBlank())
                            ? cr.getRestrictionAnnotation() : "";
                    // Then the detail motives.
                    // The SP does a STUFF with restriccion_contenedor_detalle => cat_motivo_restriccion_id => description.
                    // We'll replicate a method in repository or we do it with a lazy get.
                    String motives = containerRestrictionRepository.findRestrictionMotives(cr.getId());
                    if (note.isEmpty() && (motives == null || motives.isBlank())) {
                        return "";
                    } else if (!note.isEmpty() && (motives == null || motives.isBlank())) {
                        return note;
                    } else {
                        return note + ": " + motives;
                    }
                })
                .filter(s -> !s.isBlank())
                .collect(Collectors.joining("; "));
    }

}
