package com.maersk.sd1.common.service;

import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CatalogService {

    private final CatalogLanguageRepository catalogLanguageRepository;

    private final CatalogRepository catalogRepository;

    @Autowired
    public CatalogService(CatalogLanguageRepository catalogLanguageRepository, CatalogRepository catalogRepository) {
        this.catalogLanguageRepository = catalogLanguageRepository;
        this.catalogRepository = catalogRepository;
    }

    public String getCatalogTranslationDesc(Integer catalogId, int languageId) {
        String message = catalogLanguageRepository.findDescriptionByCatalogIdAndLanguageId(catalogId, languageId);
        if (message == null || message.isEmpty()) {
            message = catalogRepository.findDescriptionByCatalogId(catalogId);
        }
        return message != null ? message : "";
    }

    public String getCatalogTranslationLongDesc(Integer catalogId, int languageId) {
        String message = catalogLanguageRepository.findLongDescriptionByCatalogIdAndLanguageId(catalogId, languageId);
        if (message == null || message.isEmpty()) {
            message = catalogRepository.findLongDescriptionByCatalogId(catalogId);
        }
        return message != null ? message : "";
    }

}
