package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.VesselProgrammingContainer;
import org.springframework.data.domain.Pageable;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.sds.dto.FechaSobrestadiaItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface VesselProgrammingContainerRepository extends JpaRepository<VesselProgrammingContainer, Integer> {
    @Query("select v from VesselProgrammingContainer v where v.container.id = :containerId and v.vesselProgrammingDetail.id = :vesselProgrammingDetailId")
    VesselProgrammingContainer findByContainer_IdAndVesselProgrammingDetail_Id(@Param("containerId") Integer containerId, @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Query("""
            select v from VesselProgrammingContainer v
            where v.container.id = :containerId and v.vesselProgrammingDetail.id = :vesselProgrammingDetailId and v.active = true""")
    VesselProgrammingContainer findByContainer_IdAndVesselProgrammingDetail_IdAndActiveTrue(@Param("containerId") Integer containerId, @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Modifying
    @Query("""
                UPDATE VesselProgrammingContainer vpc
                SET vpc.receivedQuantity = 0,
                    vpc.receivedWeight = 0,
                    vpc.catReceivedWeightMeasure.id = null,
                    vpc.receivedSeal1 = null,
                    vpc.receivedSeal2 = null,
                    vpc.receivedSeal3 = null,
                    vpc.receivedSeal4 = null,
                    vpc.modificationDate = :modificationDate,
                    vpc.traceProgVesCnt = 'del-assignment-gif',
                    vpc.modificationUser.id = :userModificationId
                WHERE vpc.container.id = :containerId
                  AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId
                  And vpc.active = true
            """)
    void resetReception(Integer containerId, Integer vesselProgrammingDetailId, Integer userModificationId, LocalDateTime modificationDate);

    @Modifying
    @Query("""
                UPDATE VesselProgrammingContainer vpc
                SET vpc.dispatchedSeal1 = null,
                    vpc.dispatchedSeal2 = null,
                    vpc.dispatchedSeal3 = null,
                    vpc.dispatchedSeal4 = null,
                    vpc.modificationUser.id = :userModificationId,
                    vpc.modificationDate = :modificationDate,
                    vpc.traceProgVesCnt = 'del-assignment-gif'
                WHERE vpc.container.id = :containerId
                  AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId
                  And vpc.active = true
            """)
    void resetDispatch(Integer containerId, Integer vesselProgrammingDetailId, Integer userModificationId, LocalDateTime modificationDate);

    @Query(value = "select vpc from VesselProgrammingContainer vpc " +
            "where vpc.id = :id " +
            "and vpc.container.id = :containerId " +
            "and vpc.active = true")
    VesselProgrammingContainer findByIdAndContainerIdAndActiveTrue(@Param("id") Integer id, @Param("containerId") Integer containerId);

    @Query(value = "select v from Eir e " +
            "inner join VesselProgrammingContainer v on v.vesselProgrammingDetail.id = e.vesselProgrammingDetail.id " +
            "and e.container.id = v.container.id and v.active = true")
    List<VesselProgrammingContainer> findByEirIdList(List<Integer> eirIdList);

    @Query(value = """
            SELECT  t2.contenedor_id,
                    t2.programacion_nave_detalle_id,
                    LTRIM(STUFF((SELECT ', ' + IIF(RTRIM(mimox.codigo_imo) = '9', '9 (' + pncimox.imo_others + ')', RTRIM(mimox.codigo_imo))
                                 FROM sds.programacion_nave_contenedor_imo AS pncimox (NOLOCK)
                                 INNER JOIN sds.imo AS mimox (NOLOCK) ON pncimox.imo_id = mimox.imo_id
                                 WHERE pncimox.programacion_nave_contenedor_id = t2.programacion_nave_contenedor_id
                                 AND pncimox.activo = 1
                                 ORDER BY mimox.codigo_imo ASC FOR XML PATH('')), 1, 1, '')) AS imo
            FROM [sds].[programacion_nave_contenedor] AS t2
            WHERE t2.activo = 1
            AND t2.contenedor_id IN :containerIds
            AND t2.programacion_nave_detalle_id IN :vesselProgrammingDetailIds
            """, nativeQuery = true)
    List<Object[]> findImoByContainerIdsAndVesselProgrammingDetailIds(
            @Param("containerIds") Set<Integer> containerIds,
            @Param("vesselProgrammingDetailIds") Set<Integer> vesselProgrammingDetailIds);

    @Query("SELECT v FROM VesselProgrammingContainer v " +
            "WHERE v.container.id IN :containerIds " +
            "AND v.vesselProgrammingDetail.id IN :programmingShipDetailIds " +
            "AND v.active = true")
    List<VesselProgrammingContainer> findActiveVesselContainers(
            @Param("containerIds") List<Integer> containerIds,
            @Param("programmingShipDetailIds") List<Integer> programmingShipDetailIds);


    Optional<VesselProgrammingContainer> findByContainerIdAndVesselProgrammingDetailId(Integer containerId,
                                                                                       Integer vesselProgrammingDetailId);

    @Query("""
            SELECT vpc FROM VesselProgrammingContainer vpc
            INNER JOIN CargoDocument cd ON (cd.vesselProgrammingDetail.id = vpc.vesselProgrammingDetail.id) 
            WHERE cd.id = :cargoDocumentId AND vpc.container.id = :containerId
            """)
    Optional<VesselProgrammingContainer> findByCargoDocumentIdAndContainerId(Integer cargoDocumentId, Integer containerId);

    @Query("""
            SELECT vpc FROM VesselProgrammingContainer vpc
            INNER JOIN CargoDocument cd ON (cd.vesselProgrammingDetail.id = vpc.vesselProgrammingDetail.id)
            INNER JOIN CargoDocumentDetail cdd ON (cdd.cargoDocument.id = cd.id)
            WHERE cdd.id = :cargoDocumentDetailId 
            AND cdd.active = true
            AND vpc.container.id = :containerId 
            """)
    Optional<VesselProgrammingContainer> findByActiveCargoDocumentDetailAndContainerId(Integer cargoDocumentDetailId, Integer containerId);

    @Query("""
            SELECT CASE WHEN count(vpc) > 0 THEN true ELSE false END FROM VesselProgrammingContainer vpc WHERE vpc.id = :vesselProgrammingContainerId AND vpc.container.id = :containerId
            """)
    boolean existsByIdAndContainer(Integer vesselProgrammingContainerId, Integer containerId);

    @Modifying
    @Query("""
            UPDATE VesselProgrammingContainer vpc
            SET vpc.catManifestedContainerType.id = :catContTypeId,
            vpc.catManifestedSize.id = :catContSizeId,
            vpc.active = true,
            vpc.traceProgVesCnt = :trace,
            vpc.modificationUser = :modificationUserId,
            vpc.modificationDate = CURRENT_TIMESTAMP
            WHERE vpc.id = :vesselProgrammingContainerId
            AND vpc.container.id = :containerId
            """)
    void assignContainerTypeAndSizeByContainerAndId(Integer vesselProgrammingContainerId, Integer containerId, Integer catContTypeId, Integer catContSizeId, Integer modificationUserId, String trace);

    @Query(value = "select v from VesselProgrammingContainer v " +
            "where v.container.id = :containerId and v.vesselProgrammingDetail.id = :vesselProgrammingDetailId and v.active = true")
    Optional<VesselProgrammingContainer> findTopByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(Integer containerId, Integer vesselProgrammingDetailId);

    @Query("""
            SELECT cdd FROM VesselProgrammingContainer vpc
            INNER JOIN CargoDocumentDetail cdd ON (cdd.container.id = vpc.container.id)
            INNER JOIN cdd.cargoDocument cdc
            INNER JOIN VesselProgrammingDetail vpd ON (vpd.id = cdc.vesselProgrammingDetail.id AND vpd.id = vpc.vesselProgrammingDetail.id)
            WHERE vpc.container.id = :containerId 
            AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId 
            AND vpd.active = true 
            AND cdc.active = true 
            AND cdd.active = true 
            AND vpc.active = true 
            """)
    Optional<CargoDocumentDetail> findActiveVesselProgrammingContainerByContainerIdAndProgrammingDetailId(Integer containerId, Integer vesselProgrammingDetailId);

    Optional<VesselProgrammingContainer> findByVesselProgrammingDetailIdAndContainerIdAndActive(
            Integer vesselProgrammingDetailId,
            Integer containerId,
            Boolean active);

    @Query("""
            SELECT p FROM VesselProgrammingContainer p
            WHERE p.container = :container
              AND p.vesselProgrammingDetail = :vesselProgrammingDetail
            """)
    Optional<VesselProgrammingContainer> findByContainerAndVesselProgrammingDetail(
            @Param("container") Container container,
            @Param("vesselProgrammingDetail") VesselProgrammingDetail vesselProgrammingDetail
    );


    @Query("""
            SELECT new com.maersk.sd1.sds.dto.FechaSobrestadiaItem(
                doc.cargoDocument AS cargoDocument,
                sl.shippingLineCompany AS lineaNaviera,
                c.id AS contenedorId,
                c.containerNumber AS contenedor,
                vpc.demurrageDate AS fechaSobrestadia
            )
            FROM CargoDocumentDetail docDetail
            JOIN docDetail.cargoDocument doc
            JOIN doc.vesselProgrammingDetail vpd
            JOIN vpd.catOperation catOp
            JOIN vpd.vesselProgramming vp
            JOIN docDetail.container c
            JOIN doc.shippingLine sl
            JOIN VesselProgrammingContainer vpc ON vpc.vesselProgrammingDetail = vpd AND vpc.container = c
            WHERE doc.id = :documentoCargaId
              AND catOp.id = 43001
              AND c IS NOT NULL
              AND doc.active = true
              AND docDetail.active = true
              AND vpd.active = true
              AND vp.active = true
              AND vpc.active = true
            ORDER BY c.containerNumber ASC
            """)
    List<FechaSobrestadiaItem> findFechaSobrestadiaByDocumentoCargaId(@Param("documentoCargaId") Integer documentoCargaId);


    @Query("SELECT COUNT(1) FROM VesselProgrammingContainer vpc "
            + "WHERE vpc.active = true "
            + "AND vpc.vesselProgrammingDetail.vesselProgramming.id = :programacionNaveId")
    int countActiveVesselProgrammingContainersByProgramacionNaveId(@Param("programacionNaveId") Integer programacionNaveId);


    int countByVesselProgrammingDetailAndActiveTrue(VesselProgrammingDetail detail);


    @Modifying
    @Query("UPDATE VesselProgrammingContainer v SET v.isDangerousCargo = true WHERE v.id = :id")
    int updateDangerousCargo(Integer id);

    @Modifying
    @Query("UPDATE VesselProgrammingContainer vpc SET vpc.manifestedSeal1 = :seal1, vpc.manifestedSeal2 = :seal2, "
            + "vpc.manifestedSeal3 = :seal3, vpc.manifestedSeal4 = :seal4, "
            + "vpc.modificationUser.id = :modificationUserId, vpc.modificationDate = CURRENT_TIMESTAMP, "
            + "vpc.traceProgVesCnt = 'upd_planning_gifU1' "
            + "WHERE vpc.vesselProgrammingDetail.id = :vpcId "
            + "AND vpc.container.id = :containerId")
    int updateVesselProgrammingContainerSeals(@Param("vpcId") Integer vpcId,
                                              @Param("seal1") String seal1,
                                              @Param("seal2") String seal2,
                                              @Param("seal3") String seal3,
                                              @Param("seal4") String seal4,
                                              @Param("modificationUserId") Integer modificationUserId,
                                              @Param("containerId") Integer containerId);

    @Query("""
                SELECT v.id
                FROM VesselProgrammingContainer v
                WHERE v.container.id = :containerId
                  AND v.vesselProgrammingDetail.id = :vesselProgrammingDetailId
            """)
    Optional<Integer> findId(@Param("containerId") Integer containerId,
                             @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Query("""
            SELECT vpc.id FROM VesselProgrammingContainer vpc
            JOIN vpc.vesselProgrammingDetail vpd
            JOIN CargoDocument cd ON vpd.id = cd.vesselProgrammingDetail.id
            JOIN CargoDocumentDetail cdd ON cd.id = cdd.cargoDocument.id
            JOIN TransportPlanningDetail tpd ON cdd.id = tpd.cargoDocumentDetail.id
            JOIN TransportPlanning tp ON tpd.transportPlanning.id = tp.id
            WHERE tp.id = :transportPlanningId
            AND vpc.container.id = cdd.container.id
            AND vpc.container.id = :containerId
            """)
    List<Integer> findVesselProgrammingContainerIds(
            @Param("transportPlanningId") Integer transportPlanningId,
            @Param("containerId") Integer containerId
    );

    @Modifying
    @Query("""
            UPDATE VesselProgrammingContainer vpc
            SET vpc.active = false,
            vpc.modificationUser = :modificationUserId,
            vpc.modificationDate = CURRENT_TIMESTAMP,
            vpc.traceProgVesCnt = :trace
            WHERE vpc.id = :vesselProgrammingContainerId
            AND vpc.container.id = :containerId
            AND vpc.active = true
            """)
    void disableByIdAndContainer(Integer vesselProgrammingContainerId, Integer containerId, Integer modificationUserId, String trace);

    @Query("SELECT vpc FROM VesselProgrammingContainer vpc WHERE vpc.vesselProgrammingDetail.id = :vesselProgramingDetailId")
    List<VesselProgrammingContainer> findByVesselProgrammingDetail(Pageable pageable, Integer vesselProgramingDetailId);

    @Modifying
    @Query("""
            UPDATE VesselProgrammingContainer vpc
            SET vpc.active = false,
                vpc.modificationUser.id = :userRegistrationId,
                vpc.modificationDate = CURRENT_TIMESTAMP
            WHERE vpc.id IN :containerIds
            """)
    int deactivateVesselProgrammingContainers(
            @Param("userRegistrationId") Integer userRegistrationId,
            @Param("containerIds") List<Integer> containerIds
    );

    @Query("""
                SELECT vpc.id
                FROM VesselProgrammingContainer vpc
                JOIN vpc.vesselProgrammingDetail vpd
                JOIN CargoDocument cd ON vpd.id = cd.vesselProgrammingDetail.id
                JOIN CargoDocumentDetail cdd ON cdd.cargoDocument.id = cd.id
                JOIN TransportPlanningDetail tpd ON tpd.cargoDocumentDetail.id = cdd.id
                JOIN tpd.transportPlanning tp
                JOIN Container con ON con.id = cdd.container.id
                WHERE tp.id = :transportPlanningId
                AND con.containerNumber NOT IN :tableContainers
                AND tpd.active = true
                AND cdd.active = true
                AND vpc.active = true
                AND cdd.container.id = vpc.container.id
            """)
    List<Integer> findVesselProgrammingContainerIds(
            @Param("transportPlanningId") Integer transportPlanningId,
            @Param("tableContainers") List<String> tableContainers
    );

    @Query("SELECT vpc FROM VesselProgrammingContainer vpc JOIN FETCH vpc.vesselProgrammingDetail WHERE vpc.vesselProgrammingDetail.id IN :vesselProgrammingDetailIds AND vpc.active = true")
    List<VesselProgrammingContainer> findAllActiveByVesselProgrammingDetail(List<Integer> vesselProgrammingDetailIds);

    @Query("SELECT vpc FROM VesselProgrammingContainer vpc \n           WHERE vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId \n             AND vpc.container.id = :containerId \n             AND vpc.active = true")
    VesselProgrammingContainer findActiveVesselProgrammingContainer(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                                                    @Param("containerId") Integer containerId);

    @Query("""
            SELECT vpc
            FROM VesselProgrammingContainer vpc
            WHERE vpc.container.id = :containerId
              AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId
            """)
    List<VesselProgrammingContainer> findByContainerAndVesselProgrammingDetail(
            @Param("containerId") Integer containerId,
            @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId
    );

}