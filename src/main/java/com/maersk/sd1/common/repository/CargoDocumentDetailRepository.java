package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.dto.CargoDocumentDetailReceiptDTO;
import com.maersk.sd1.common.model.BookingDetail;
import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.sdg.dto.StockDataUpdateDTO;
import org.springframework.data.domain.Pageable;
import com.maersk.sd1.sds.repository.DocumentCargoDetailObtainProjection;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Primary
@Repository
public interface CargoDocumentDetailRepository extends JpaRepository<CargoDocumentDetail, Integer> {
    @Query("select c from CargoDocumentDetail c where c.id = :id and c.active = true")
    CargoDocumentDetail findByIdAndActiveTrue(@Param("id") Integer id);

    @Query("""
            select count(c) from CargoDocumentDetail c
            where c.bookingDetail.id = :bookingDetailId and c.container.id is not null and c.active = true and c.bookingDetail.active = true""")
    Integer countFindByBookingDetailIdAndContainerIdNotNullAndActiveTrueAndBookingDetailActiveTrueAndActiveTrue(@Param("bookingDetailId") Integer bookingDetailId);

    @Query(value = "SELECT cdd FROM CargoDocumentDetail cdd WHERE cdd.container.id = :containerId AND cdd.active = :active")
    List<CargoDocumentDetail> findByContainerIdAndActive(@Param("containerId") Integer containerId, @Param("active") Boolean active);


    @Query("SELECT d.id FROM CargoDocumentDetail d " +
            "WHERE d.container.id = :containerId " +
            "AND d.active = true")
    List<Integer> findIdByContainerId(@Param("containerId") Integer containerId);

    @Query("SELECT new com.maersk.sd1.common.dto.CargoDocumentDetailReceiptDTO(d.id, " +
            "d.receivedWeight, " +
            "d.receivedQuantity, " +
            "d.balanceQuantity, " +
            "COALESCE(d.catReceivedWeightMeasure.id, :isMeasureWeightKg)) " +
            "FROM CargoDocumentDetail d " +
            "JOIN d.cargoDocument dc " +
            "WHERE d.id IN :cargoDocumentDetailIds " +
            "AND d.container.id = :containerId " +
            "AND dc.id = :cargoDocumentId " +
            "AND dc.active = true " +
            "AND d.active = true")
    CargoDocumentDetailReceiptDTO findCargoDocumentDetailReceived(@Param("cargoDocumentDetailIds") List<Integer> cargoDocumentDetailIds,
                                                                  @Param("containerId") Integer containerId,
                                                                  @Param("cargoDocumentId") Integer cargoDocumentId,
                                                                  @Param("isMeasureWeightKg") Integer isMeasureWeightKg);

    @Modifying
    @Query("""
                UPDATE CargoDocumentDetail cdd
                SET cdd.receivedQuantity = 0,
                    cdd.receivedWeight = 0,
                    cdd.catReceivedWeightMeasure.id = null,
                    cdd.receivedVolume = 0,
                    cdd.balanceQuantity = 0,
                    cdd.balanceWeight = 0,
                    cdd.balanceVolume = 0,
                    cdd.traceCargoDocumentDetail = 'del-assignment-gif',
                    cdd.modificationUser.id = :userModificationId,
                    cdd.modificationDate = :modificationDate
                WHERE cdd.id = :cargoDetailId
            """)
    void resetReception(Integer cargoDetailId, Integer userModificationId, LocalDateTime modificationDate);

    @Modifying
    @Query("""
                UPDATE CargoDocumentDetail cdd
                SET cdd.dispatchedQuantity = 0,
                    cdd.dispatchedWeight = null,
                    cdd.dispatchedVolume = 0,
                    cdd.balanceQuantity = 1,
                    cdd.balanceWeight = cdd.receivedWeight,
                    cdd.balanceVolume = 0,
                    cdd.catDispatchedWeightMeasure.id = 0,
                    cdd.traceCargoDocumentDetail = 'del-assignment-gof',
                    cdd.modificationUser.id = :userModificationId,
                    cdd.modificationDate = :modificationDate
                WHERE cdd.id = :cargoDetailId
            """)
    void resetDispatch(Integer cargoDetailId, Integer userModificationId, LocalDateTime modificationDate);


    @Query("SELECT c FROM CargoDocumentDetail c WHERE c.bookingDetail.id = :bookingDetailId AND c.container.id = :containerId")
    CargoDocumentDetail findByBookingDetailIdAndContainerId(@Param("bookingDetailId") Integer bookingDetailId, @Param("containerId") Integer containerId);

    @Query("SELECT c FROM CargoDocumentDetail c WHERE c.id = :cargoDetailId")
    CargoDocumentDetail findByCargoDetailId(@Param("cargoDetailId") Integer cargoDetailId);


    @Query(value = "select cdd from CargoDocumentDetail cdd " +
            "inner join EirDocumentCargoDetail e on e.cargoDocumentDetail.id = cdd.id " +
            "where e.eir.id = :eirId")
    CargoDocumentDetail findByEirId(@Param("eirId") Integer eirId);

    @Query("""
            SELECT COUNT(cdd) FROM CargoDocumentDetail cdd
            JOIN cdd.container cnt
            WHERE cdd.cargoDocument.id = :cargoDocumentId
            AND (COALESCE(cdd.receivedQuantity, 0) > 0 OR COALESCE(cdd.receivedWeight, 0) > 0)
            AND cdd.active = true
            """)
    Integer containersReceivedQuantitiesOrWeights(@Param("cargoDocumentId") Integer cargoDocumentId);

    @Query("SELECT c FROM CargoDocumentDetail c " +
            "WHERE c.bookingDetail.id IN :bookingDetailIds " +
            "AND c.container.id IN :containerIds " +
            "AND c.active = true")
    List<CargoDocumentDetail> findByBookingDetailIdsAndContainorIdsAndActiveTrue(
            @Param("bookingDetailIds") List<Integer> bookingDetailIds,
            @Param("containerIds") List<Integer> containerIds);

    @Query("SELECT new com.maersk.sd1.sdg.dto.StockDataUpdateDTO(" +
            "eir_docx.eir.id, " +
            "shipperx.document, " +
            "shipperx.legalName, " +
            "dodx.product.id) " +
            "FROM EirDocumentCargoDetail eir_docx " +
            "INNER JOIN CargoDocumentDetail dodx ON eir_docx.cargoDocumentDetail.id = dodx.id " +
            "INNER JOIN CargoDocument dox ON dodx.cargoDocument.id = dox.id " +
            "LEFT JOIN Company shipperx ON dox.shipperCompany.id = shipperx.id " +
            "WHERE COALESCE(shipperx.id, 0) = COALESCE(:shipperName, COALESCE(shipperx.id, 0))")
    List<StockDataUpdateDTO> fetchShipperDetail(@Param("shipperName") Integer shipperName);

    @Modifying
    @Query("UPDATE CargoDocumentDetail cdd " +
            "SET cdd.commodity = TRIM(:groupProductDescription), " +
            "    cdd.product.id = CASE WHEN :productId IS NULL OR :productId = 0 THEN cdd.product.id ELSE :productId END, " +
            "    cdd.modificationUser.id = :userRegistrationId, " +
            "    cdd.modificationDate = CURRENT_TIMESTAMP, " +
            "    cdd.traceCargoDocumentDetail = 'EDI-Replace.MER' " +
            "WHERE cdd.cargoDocument.id = :cargoDocumentId" +
            " AND cdd.active = true")
    int updateCargoDocumentDetail(@Param("groupProductDescription") String groupProductDescription,
                                  @Param("productId") Integer productId,
                                  @Param("userRegistrationId") Integer userRegistrationId,
                                  @Param("cargoDocumentId") Integer cargoDocumentId);

    @Query("SELECT COUNT(c) FROM CargoDocumentDetail c " +
            "WHERE c.id IN :cargoDocumentDetailIds AND c.active = true")
    Integer findActiveCargoDocumentDetailCount(@Param("cargoDocumentDetailIds") List<Integer> detailIds);

    @Modifying
    @Query("UPDATE CargoDocumentDetail c SET c.active = false, " +
            "c.traceCargoDocumentDetail = CONCAT(:traceDetail, :ediCoparnId), " +
            "c.modificationUser.id = :userId, " +
            "c.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE c.id IN :detailIds AND c.active = true")
    int updateActiveDetails(@Param("ediCoparnId") String ediCoparnId, @Param("userId") Integer userId,
                            @Param("detailIds") List<Integer> detailIds, @Param("traceDetail") String traceDetail);

    @Query("""
            SELECT cdd FROM CargoDocumentDetail cdd
            WHERE cdd.cargoDocument.id = :cargoDocumentId 
            AND cdd.bookingDetail.catSize.id = :catContSize
            AND cdd.bookingDetail.catContainerType IN :containerTypesList
            AND cdd.bookingDetail.reservationQuantity > 0
            AND cdd.active = true AND cdd.cargoDocument.active = true
            AND cdd.bookingDetail.active = true 
            """)
    List<CargoDocumentDetail> findActiveByCargoDocumentIdContainerTypeIdContainerSizeIdWithReservedQuantityGreaterThan(Pageable pageable, Integer cargoDocumentId, Integer catContSize, List<Integer> containerTypesList);

    @Query("""
            SELECT CASE WHEN count(cdd) > 0 THEN true ELSE false END 
            FROM CargoDocumentDetail cdd            
            WHERE cdd.container.id = :containerId
            AND cdd.bookingDetail.id = :bookingDetailId
            AND cdd.active = true
            """)
    boolean existsActiveByContainerIdAndBookingDetailId(Integer containerId, Integer bookingDetailId);

    @Query("SELECT cdd FROM CargoDocumentDetail cdd WHERE cdd.bookingDetail.id = :bookingDetailId AND cdd.active = true")
    Optional<CargoDocumentDetail> findActiveByBookingDetailId(Integer bookingDetailId);

    @Modifying
    @Query("""
            UPDATE CargoDocumentDetail cdd
            SET cdd.container.id = :containerId,
            cdd.modificationUser.id = :modificationUserId,
            cdd.modificationDate = CURRENT_TIMESTAMP,
            cdd.traceCargoDocumentDetail = :trace,
            cdd.catManifestedContainerType.id = :catContainerTypeId
            WHERE cdd.id = :cargoDocumentDetailId
            """)
    void assignContainerAndManifestedContainerTypeById(Integer cargoDocumentDetailId, Integer containerId, Integer modificationUserId, String trace, Integer catContainerTypeId);

    @Modifying
    @Query("""
            UPDATE CargoDocumentDetail cdd
            SET cdd.active = false,
            cdd.container.id = null,
            cdd.traceCargoDocumentDetail = :trace,
            cdd.modificationUser.id = :modificationUserId,
            cdd.modificationDate = CURRENT_TIMESTAMP
            WHERE cdd.id = :cargoDocumentDetailId
            AND cdd.active = true
            """)
    void disableById(Integer cargoDocumentDetailId, Integer modificationUserId, String trace);

    @Query("SELECT cdd FROM CargoDocumentDetail cdd WHERE cdd.bookingDetail.id = :bookingDetailId AND cdd.container IS NULL")
    Optional<CargoDocumentDetail> findByBookingDetailIdWithNoContainer(Integer bookingDetailId);

    @Query("select cdd.bookingDetail.booking.id from CargoDocumentDetail cdd " +
            "where cdd.cargoDocument.id = :cargoDocumentDetailId " +
            "and cdd.active = true")
    Optional<Integer> getBookingGOutByCargoDocumentDetailId(@Param("cargoDocumentDetailId") Integer cargoDocumentDetailId);

    @Procedure(name = "CargoDocumentDetail.validateContainerStock")
    String validateContainerStock(
            @Param("business_unit_id") Integer businessUnitId,
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("cat_movimiento_id") Integer catMovementId,
            @Param("cat_empty_full_id") Integer catEmptyFullId,
            @Param("container_id") Integer containerId,
            @Param("container_number") String containerNumber,
            @Param("user_id") Integer userId,
            @Param("creation_origen_id") Integer creationOriginId,
            @Param("message_trace") String messageTrace,
            @Param("languaje_id") Integer languageId
    );

    @Query("SELECT c FROM CargoDocumentDetail c WHERE c.bookingDetail.id = :bookingId AND c.bookingDetail.active = true AND c.active = true AND c.cargoDocument.active = true")
    List<CargoDocumentDetail> findAllByBookingIdAndActive(@Param("bookingId") Integer bookingId);

    @Modifying
    @Query("""
            UPDATE CargoDocumentDetail cdd
            SET cdd.active = false,
                cdd.traceCargoDocumentDetail = LEFT(CONCAT('delxrel ', :ediCoparnId), 20),
                cdd.modificationUser.id = :userModificationId,
                cdd.modificationDate = :modificationDate
            WHERE cdd.id = :cargoDocumentDetailId AND cdd.active = true
            """)
    void deactivateCargoDocumentDetail(@Param("cargoDocumentDetailId") Integer cargoDocumentDetailId,
                                       @Param("ediCoparnId") Integer ediCoparnId,
                                       @Param("userModificationId") Integer userModificationId,
                                       @Param("modificationDate") LocalDateTime modificationDate);

    @Query("""
    SELECT COALESCE(cdd.isDangerousCargo, false)
    FROM CargoDocumentDetail cdd
    INNER JOIN cdd.cargoDocument cd
    WHERE cdd.container.id = :containerId
    AND cd.vesselProgrammingDetail.id = :programacionNaveDetalleId
    AND cd.masterCargoDocument IS NULL
    AND cd.active = true
    AND cdd.active = true
    """)
    List<Boolean> findDangerousCargoByContainerIdAndProgramacionNaveDetalleId(
            @Param("containerId") Integer containerId,
            @Param("programacionNaveDetalleId") Integer programacionNaveDetalleId);

    @Query("""
        SELECT COUNT(1) FROM CargoDocumentDetail d
        WHERE d.bookingDetail = :bookingDetail
          AND d.container IS NOT NULL
          AND d.active = true
        """)
    long countAssignedByBookingDetail(@Param("bookingDetail") BookingDetail bookingDetail);

    @Query("""
            SELECT d FROM CargoDocumentDetail d
            WHERE d.bookingDetail = :bookingDetail
              AND d.container = :container
              AND d.active = true
            ORDER BY d.registrationDate DESC
            """)
    List<CargoDocumentDetail> findActiveByBookingDetailAndContainerOrderByRegistrationDateDesc(
            @Param("bookingDetail") BookingDetail bookingDetail,
            @Param("container") Container container);

    @Query("SELECT COALESCE(SUM(dcd.manifestedQuantity), 0) FROM CargoDocumentDetail dcd WHERE dcd.cargoDocument.id = :cargoDocumentId")
    BigDecimal findTotalBultos(@Param("cargoDocumentId") Integer cargoDocumentId);

    @Query("SELECT COALESCE(SUM(dcd.manifestedWeight), 0) FROM CargoDocumentDetail dcd WHERE dcd.cargoDocument.id = :cargoDocumentId")
    BigDecimal findTotalWeight(@Param("cargoDocumentId") Integer cargoDocumentId);

    long countByCargoDocument_IdAndActive(Integer cargoDocumentId, Boolean active);

    @Query("""
            SELECT d FROM CargoDocumentDetail d
                LEFT JOIN FETCH d.product pro
                LEFT JOIN FETCH d.container cnt
                LEFT JOIN FETCH cnt.catContainerType ctype
                LEFT JOIN FETCH cnt.catSize csize
                LEFT JOIN FETCH d.catWeightMeasureUnit cum
                LEFT JOIN FETCH d.catCargoCondition ccc
                LEFT JOIN FETCH d.catPackaging pack
                LEFT JOIN FETCH d.catMeasureUnitQuantity cuc
                LEFT JOIN FETCH d.catManifestedContainerType ccm
                LEFT JOIN FETCH d.catManifestedSize ctm
                LEFT JOIN FETCH d.catCreationOrigin coc
                LEFT JOIN FETCH d.registrationUser ure
                LEFT JOIN FETCH d.modificationUser umo
                LEFT JOIN FETCH d.emptyPickUpBusinessUnit unr
            WHERE d.cargoDocument.id = :documentId
                AND d.active = true
        """)
    List<CargoDocumentDetail> findCargoDocumentDetailFullData(@Param("documentId") Integer documentId);

    long countByCargoDocumentIdAndActive(Integer cargoDocumentId, Boolean active);

    @Query("""
            SELECT
                dcd.cargoDocument.id AS cargoDocumentId,
                dcd.cargoDocument.cargoDocument AS cargoDocument,
                v.id AS vesselId,
                v.name AS vesselName,
                vp.voyage AS voyage,
                dcd.id AS cargoDocumentDetailId,
                CASE WHEN dcd.parentDocumentCargoDetail IS NOT NULL THEN dcd.parentDocumentCargoDetail.id ELSE null END AS cargoDocumentDetailMotherId,
                pro.id AS productId,
                pro.productName AS productName,
                CASE WHEN cnt IS NOT NULL THEN cnt.containerNumber ELSE null END AS containerNumber,
                dcd.manifestedQuantity AS manifestedQuantity,
                dcd.manifestedWeight AS manifestedWeight,
                dcd.manifestedVolume AS manifestedVolume,
                dcd.receivedQuantity AS receivedQuantity,
                dcd.receivedWeight AS receivedWeight,
                dcd.receivedVolume AS receivedVolume,
                dcd.dispatchedQuantity AS dispatchedQuantity,
                dcd.dispatchedWeight AS dispatchedWeight,
                dcd.dispatchedVolume AS dispatchedVolume,
                dcd.immobilizedQuantity AS immobilizedQuantity,
                dcd.immobilizedWeight AS immobilizedWeight,
                dcd.immobilizedVolume AS immobilizedVolume,
                dcd.catPackaging.id AS catPackagingId,
                dcd.catPackaging.description AS catPackagingDescription,
                dcd.catWeightMeasureUnit.id AS catWeightMeasureId,
                dcd.catWeightMeasureUnit.description AS catWeightMeasureDescription,
                dcd.saysContain AS saysContain,
                dcd.balanceQuantity AS balanceQuantity,
                dcd.balanceWeight AS balanceWeight,
                dcd.balanceVolume AS balanceVolume,
                dcd.isDangerousCargo AS isDangerousCargo,
                dcd.isRefrigeratedCargo AS isRefrigeratedCargo,
                dcd.brands AS brands,
                dcd.commodity AS commodity,
                dcd.manifestedChassis AS manifestedChassis,
                dcd.receivedChassis AS receivedChassis,
                dcd.catCargoCondition.id AS catCargoConditionId,
                dcd.catCargoCondition.description AS catCargoConditionDescription,
                dcd.inventoryQuantity AS inventoriedQuantity,
                dcd.inventoryWeight AS inventoriedWeight,
                dcd.inventoryVolume AS inventoriedVolume,
                CASE WHEN dcd.emptyPickUpBusinessUnit IS NOT NULL THEN dcd.emptyPickUpBusinessUnit.id ELSE null END AS emptyPickUpBusinessUnitId,
                CASE WHEN dcd.emptyPickUpBusinessUnit IS NOT NULL THEN dcd.emptyPickUpBusinessUnit.name ELSE null END AS emptyPickUpBusinessUnitName,
                dcd.emptyGateoutSettled AS gateoutEmptySettled,
                CASE WHEN dcd.catManifestedContainerType IS NOT NULL THEN dcd.catManifestedContainerType.id ELSE null END AS catManifestedContainerTypeId,
                CASE WHEN dcd.catManifestedContainerType IS NOT NULL THEN dcd.catManifestedContainerType.description ELSE null END AS catManifestedContainerTypeDescription,
                dcd.catCreationOrigin.id AS catCreationOriginId,
                dcd.catCreationOrigin.description AS catCreationOriginDescription,
                CASE WHEN dcd.catManifestedSize IS NOT NULL THEN dcd.catManifestedSize.id ELSE null END AS catManifestedSizeId,
                CASE WHEN dcd.catManifestedSize IS NOT NULL THEN dcd.catManifestedSize.description ELSE null END AS catManifestedSizeDescription,
                dcd.catMeasureUnitQuantity.id AS catMeasureUnitQuantityId,
                dcd.catMeasureUnitQuantity.description AS catMeasureUnitQuantityDescription,
                CASE WHEN dcd.bookingDetail IS NOT NULL THEN dcd.bookingDetail.id ELSE null END AS bookingDetailId,
                dcd.traceCargoDocumentDetail AS traceCargoDocumentDetail
            FROM CargoDocumentDetail dcd
                JOIN dcd.cargoDocument cd
                JOIN cd.vesselProgrammingDetail vpd
                JOIN vpd.vesselProgramming vp
                JOIN vp.vessel v
                JOIN dcd.product pro
                LEFT JOIN dcd.container cnt
                JOIN dcd.catWeightMeasureUnit cum
                JOIN dcd.catPackaging cep
                JOIN dcd.catCargoCondition ccon
                JOIN dcd.catMeasureUnitQuantity cmu
                JOIN dcd.catCreationOrigin cor
                LEFT JOIN dcd.catManifestedContainerType ccm
                LEFT JOIN dcd.catManifestedSize ctm
                LEFT JOIN dcd.emptyPickUpBusinessUnit unr
            WHERE dcd.id = :documentoCargaDetalleId
              AND dcd.active = true
            """
    )
    Optional<DocumentCargoDetailObtainProjection> findCargoDocumentDetail(
            @Param("documentoCargaDetalleId") Integer documentoCargaDetalleId);

    @Query("SELECT cd.cargoDocument.id FROM CargoDocumentDetail cd " +
            "JOIN cd.bookingDetail bkd " +
            "JOIN bkd.booking bk " +
            "WHERE bk.id = :bookingId " +
            "AND cd.active = true " +
            "AND bkd.active = true " +
            "AND bk.active = true " +
            "ORDER BY cd.cargoDocument.id ASC")
    java.util.List<Integer> findFirstCargoDocumentId(@Param("bookingId") Integer bookingId);

    @Query("""
            SELECT cdd.id
            FROM CargoDocumentDetail cdd
            JOIN TransportPlanningDetail tpd ON cdd.id = tpd.cargoDocumentDetail.id
            JOIN TransportPlanning tp ON tpd.transportPlanning.id = tp.id
            WHERE tp.id = :transportPlanningId
            """)
    List<Integer> findCargoDocumentDetailIds(@Param("transportPlanningId") Integer transportPlanningId);

    @Modifying
    @Query("""
                UPDATE CargoDocumentDetail cdd
                SET cdd.catFullReceiptReason.id = :operation,
                    cdd.modificationUser.id = :userRegistrationId,
                    cdd.modificationDate = CURRENT_TIMESTAMP
                WHERE cdd.id IN :cargoDocumentDetailIds
            """)
    int updateCargoDocumentDetails(
            @Param("operation") Integer operation,
            @Param("userRegistrationId") Integer userRegistrationId,
            @Param("cargoDocumentDetailIds") List<Integer> cargoDocumentDetailIds
    );

    @Modifying
    @Query("UPDATE CargoDocumentDetail c SET c.isDangerousCargo = true WHERE c.id = :id")
    int updateIsDangerousCargo(Integer id);

    @Modifying
    @Query("""
            UPDATE CargoDocumentDetail c
            SET c.manifestedWeight = :reportedWeight,
                c.catWeightMeasureUnit.id = :reportedWeightUnit,
                c.modificationUser.id = :userRegistrationId,
                c.modificationDate = CURRENT_TIMESTAMP,
                c.traceCargoDocumentDetail = 'upd_planning_gifU2'
            WHERE c.id = :id
            """)
    int updateCargoDocumentDetail(
            @Param("id") Integer id,
            @Param("reportedWeight") BigDecimal reportedWeight,
            @Param("reportedWeightUnit") Integer reportedWeightUnit,
            @Param("userRegistrationId") Integer userRegistrationId
    );

    @Modifying
    @Query("UPDATE CargoDocumentDetail cdd SET cdd.active = false, cdd.modificationUser.id = :userModificationId, cdd.modificationDate = CURRENT_TIMESTAMP "
            + "WHERE cdd.id in :cargoDocumentDetailId")
    int deactivateCargoDocumentDetail(@Param("cargoDocumentDetailId") List<Integer> cargoDocumentDetailId,
                                      @Param("userModificationId") Integer userModificationId);

    @Modifying
    @Query("UPDATE CargoDocumentDetail c SET c.container.id = :containerId, " +
            "c.modificationUser.id = :userModificationId, " +
            "c.modificationDate = :modificationDate, " +
            "c.traceCargoDocumentDetail = :traceCargoDocumentDetail " +
            "WHERE c.id = :newDocumentDetailId")
    void updateContainer(@Param("newDocumentDetailId") Integer newDocumentDetailId,
                                   @Param("containerId") Integer containerId,
                                   @Param("userModificationId") Integer userModificationId,
                                   @Param("modificationDate") LocalDateTime modificationDate,
                                   @Param("traceCargoDocumentDetail") String traceCargoDocumentDetail);

    @Query("SELECT COUNT(cdd) FROM CargoDocumentDetail cdd " +
            "WHERE cdd.active = true " +
            "AND (cdd.container IS NOT NULL OR cdd.receivedQuantity > 0) " +
            "AND cdd.bookingDetail IS NOT NULL " +
            "AND cdd.bookingDetail.booking.id = :bookingId")
    Long countAssignedByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT cdd FROM CargoDocumentDetail cdd WHERE cdd.cargoDocument.id = :cargoDocId AND cdd.active = true")
    List<CargoDocumentDetail> findAllActiveByCargoDocumentId(@Param("cargoDocId") Integer cargoDocId);

    @Query("""
            SELECT cdd
            FROM CargoDocumentDetail cdd
            WHERE cdd.cargoDocument.id IN :documentoCargaIds
            AND cdd.active = true
            """)
    List<CargoDocumentDetail> findByDocumentoCargaIdIn(List<Integer> documentoCargaIds);

    @Query("SELECT CASE WHEN COUNT(cdd) > 0 THEN true ELSE false END " +
            "FROM CargoDocumentDetail cdd " +
            "WHERE cdd.container.id = :containerId " +
            "  AND cdd.cargoDocument.vesselProgrammingDetail.id = :vesselProgrammingDetailId " +
            "  AND cdd.cargoDocument.masterCargoDocument IS NULL " +
            "  AND cdd.cargoDocument.active = true " +
            "  AND cdd.active = true " +
            "  AND cdd.isDangerousCargo = true")
    boolean isDangerousCargo(@Param("containerId") Integer containerId,
                             @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Query("""
            SELECT cdd
            FROM CargoDocumentDetail cdd
            JOIN TransportPlanningDetail tpd ON tpd.cargoDocumentDetail.id = cdd.id
            WHERE tpd.transportPlanning.id = :transportPlanningId
              AND cdd.container.id = :containerId
            """)
    List<CargoDocumentDetail> findByTransportPlanningAndContainer(
            @Param("transportPlanningId") Integer transportPlanningId,
            @Param("containerId") Integer containerId
    );

    @Query("SELECT COUNT(dcd.id) FROM CargoDocumentDetail dcd "
            + "WHERE dcd.active = true "
            + "  AND dcd.container.id = :containerId "
            + "  AND dcd.bookingDetail.id = :bookingDetailId")
    long countCargoDocDetail(@Param("containerId") Integer containerId,
                             @Param("bookingDetailId") Integer bookingDetailId);

}