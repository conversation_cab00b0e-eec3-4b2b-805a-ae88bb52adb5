package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ProgrammingShipContainerImoId;
import com.maersk.sd1.common.model.VesselProgrammingContainerImo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface VesselProgrammingContainerImoRepository extends JpaRepository<VesselProgrammingContainerImo, ProgrammingShipContainerImoId> {

    @Query(value = """
    SELECT CASE
               WHEN RTRIM(i.codigo_imo) = '9' THEN '9 (' + p.imo_others + ')'
               ELSE RTRIM(i.codigo_imo)
           END
    FROM sds.programacion_nave_contenedor_imo p
    JOIN sds.imo i ON p.imo_id = i.imo_id
    WHERE p.programacion_nave_contenedor_id = :programmingVesselContainerId
      AND p.activo = 1
    ORDER BY i.codigo_imo ASC
""", nativeQuery = true)
    List<String> findActiveIMOs(@Param("programmingVesselContainerId") Integer programmingVesselContainerId);

    @Query("""
            DELETE FROM VesselProgrammingContainerImo vpci
            WHERE vpci.vesselProgrammingContainer.id = :vesselProgrammingContainerId
            AND vpci.imo.id NOT IN :imoIdsList
            """)
    void deleteByIdAndImoList(@Param("vesselProgrammingContainerId") Integer vesselProgrammingContainerId, @Param("imoIdsList")List<Integer> imoIdsList);

    @Modifying
    @Query("DELETE FROM VesselProgrammingContainerImo v WHERE v.vesselProgrammingContainer.id = :id")
    int deleteByVesselProgrammingContainerId(Integer id);

    @Modifying
    @Query("UPDATE VesselProgrammingContainerImo vpcImo SET vpcImo.active = false, vpcImo.modificationUser.id = :userModificationId, vpcImo.modificationDate = CURRENT_TIMESTAMP "
            + "WHERE vpcImo.id.programmingShipContainerId = :vpcId")
    int deactivateVesselProgrammingContainerImo(@Param("vpcId") Integer vpcId,
                                                @Param("userModificationId") Integer userModificationId);

    @Query("""
            SELECT vpci
            FROM VesselProgrammingContainerImo vpci
            WHERE vpci.vesselProgrammingContainer.id = :vesselProgrammingContainerId
            """)
    List<VesselProgrammingContainerImo> findByVesselProgrammingContainerId(
            @Param("vesselProgrammingContainerId") Integer vesselProgrammingContainerId
    );
}