package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.MenuConfig;
import com.maersk.sd1.ges.dto.InitializeOutput;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MenuConfigRepository extends JpaRepository<MenuConfig, Integer> {

    @Query("SELECT mc FROM MenuConfig mc WHERE mc.menu.id = :menuId")
        List<MenuConfig> findByMenuId(@Param("menuId") Integer menuId);

    @Query("""
    SELECT DISTINCT new com.maersk.sd1.ges.dto.InitializeOutput$ConfigurationDto(
        me.id, me.template, mc.catConfigurationType.description, mc.value )
    FROM MenuConfig mc 
    JOIN mc.menu me 
    JOIN RoleMenu rm ON rm.menu = me 
    JOIN rm.role r 
    JOIN UserRole ur ON ur.role = r 
    WHERE mc.status = true 
      AND me.parentMenu IS NULL 
      AND me.status = true 
      AND ur.user.id = :usuarioId
    """)
    List<InitializeOutput.ConfigurationDto> findDistinctConfigurations(@Param("usuarioId") Integer usuarioId);
}
