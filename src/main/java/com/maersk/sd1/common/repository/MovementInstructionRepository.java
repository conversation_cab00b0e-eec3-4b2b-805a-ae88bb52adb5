package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.MovementInstruction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface MovementInstructionRepository extends JpaRepository<MovementInstruction, Integer> {
    @Modifying
    @Transactional
    @Query("UPDATE MovementInstruction m " +
            "SET m.comment = 'RETURNED', m.modificationUser.id = :userId " +
            "WHERE m.id = :movementInstructionId")
    int markMovementInstructionReturned(@Param("userId") Integer userId,
                                        @Param("movementInstructionId") Integer movementInstructionId);


}