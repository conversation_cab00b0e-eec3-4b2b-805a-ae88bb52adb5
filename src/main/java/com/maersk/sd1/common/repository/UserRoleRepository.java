package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.UserRole;
import com.maersk.sd1.common.model.UserRoleId;
import com.maersk.sd1.seg.controller.dto.RoleObtainUserOutput;
import com.maersk.sd1.seg.dto.RoleOutputDTO;
import org.springframework.data.jpa.repository.JpaRepository;

import org.springframework.transaction.annotation.Transactional;
import com.maersk.sd1.common.model.Role;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserRoleRepository extends JpaRepository<UserRole, UserRoleId> {

    @Transactional
    @Modifying
    void deleteByRole(Role role);

    List<UserRole> findByIdUserId(Integer userId);

    @Query("SELECT CONCAT(u.names, ' ', u.firstLastName, ' ', COALESCE(u.secondLastName, '')) FROM UserRole ur JOIN ur.user u WHERE ur.role.id = :roleId")
    List<String> findUserFullNamesByRoleId(@Param("roleId") Integer roleId);

    @Query("select new com.maersk.sd1.seg.controller.dto.RoleObtainUserOutput$UserRoleDto(ur.id.roleId) "
            + "from UserRole ur "
            + "where ur.user.id = :userId")
    List<RoleObtainUserOutput.UserRoleDto> findUserRolesByUserId(Integer userId);

    @Query("SELECT new com.maersk.sd1.seg.dto.RoleOutputDTO(r.name, r.status) " +
            "FROM UserRole ur JOIN ur.role r " +
            "WHERE ur.user.id = :userId")
    List<RoleOutputDTO> findRolesByUserId(@Param("userId") Integer userId);

    @Modifying
    @Query("DELETE FROM UserRole ur WHERE ur.user.id = :userId")
    void deleteByUserId(@Param("userId") Integer userId);
}