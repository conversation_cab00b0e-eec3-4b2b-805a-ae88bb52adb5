package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.CompanyAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CompanyAddressRepository extends JpaRepository<CompanyAddress, Integer> {

    List<CompanyAddress> findByEmpresaId(Integer empresaId);

    @Modifying
    @Transactional
    @Query("DELETE FROM CompanyAddress a WHERE a.empresa.id = :companyId")
    void deleteAllByCompanyId(@Param("companyId") Integer companyId);
}