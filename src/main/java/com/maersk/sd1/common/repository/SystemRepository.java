package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.System;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SystemRepository extends JpaRepository<System, Integer>, JpaSpecificationExecutor<System> {

    @Query("SELECT s.id FROM System s WHERE s.name = :name")
    Integer findSystemIdByName(@Param("name") String name);

    System findByName(String name);
}