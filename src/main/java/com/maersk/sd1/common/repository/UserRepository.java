package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.ges.dto.SearchReportOutput;
import org.springframework.data.jpa.repository.EntityGraph;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Integer> {


    @Query("SELECT CONCAT(u.names ,' ' ,u.firstLastName, ' ', u.secondLastName) FROM User u WHERE u.id = :userId")
    String findFullNameByUserId(@Param("userId") Integer userId);

    @Query(value = "SELECT new com.maersk.sd1.common.model.User (" +
            "USU.id, " +
            "USU.alias, " +
            "USU.mail, " +
            "USU.dateChangeKey, " +
            "USU.names, " +
            "USU.firstLastName, " +
            "USU.secondLastName, " +
            "USU.company.id, " +
            "USU.status ) " +
            "FROM User USU " +
            "WHERE (:email IS NULL OR UPPER(USU.mail) = UPPER(:email)) " +
            "AND (:id IS NULL OR UPPER(USU.alias) = UPPER(:id))" +
            "ORDER BY 1 DESC")
    User findUserDetailsByIdAndEmail(@Param("id") String userId, @Param("email") String email);

    @Query(value = "SELECT contador_login FROM seg.usuario WHERE id = :username", nativeQuery = true)
    Integer getLoginCounter(@Param("username") String username);

    @Modifying
    @Transactional
    @Query(value = "UPDATE seg.usuario SET contador_login = :finalCounter WHERE id = :username", nativeQuery = true)
    void updateLoginCounter(@Param("username") String username, @Param("finalCounter") Integer finalCounter);

    @Query(value = "SELECT correo FROM seg.usuario WHERE id = :username", nativeQuery = true)
    String getEmail(@Param("username") String username);

    Optional<User> findByPersonId(Integer personId);

    @Query("select new com.maersk.sd1.ges.dto.SearchReportOutput(u.id, concat(u.names, ' ', u.firstLastName, ' ', coalesce(u.secondLastName, ''))) " +
            "from User u " +
            "where lower(concat(u.names, ' ', u.firstLastName, ' ', coalesce(u.secondLastName, ''))) like lower(concat('%', :search, '%')) " +
            "order by u.names asc")
    List<SearchReportOutput> findUsuario(@Param("search") String search);

    boolean existsByAliasIgnoreCase(String alias);

    Optional<User> findById(Integer id);

    @Modifying
    @Query("UPDATE User u SET u.key = :newKey, "
            + "u.dateChangeKey = :dateChangeKey, "
            + "u.modificationUser.id = :modificationUserId, "
            + "u.modificationDate = :modificationDate "
            + "WHERE u.id = :userId")
    int updatePasswordForINDCase(@Param("userId") Integer userId,
                                 @Param("newKey") String newKey,
                                 @Param("dateChangeKey") LocalDateTime dateChangeKey,
                                 @Param("modificationUserId") Integer modificationUserId,
                                 @Param("modificationDate") LocalDateTime modificationDate);

    Optional<User> findByIdAndKey(Integer id, String key);
  
    @Transactional
    @Query(value = "DELETE FROM seg.usuario_rol WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteRolesByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM seg.usuario_rol_unidad_negocio WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteCompaniesByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM ges.empresa_usuario WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteProjectsByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM seg.usuario_email_plantilla WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteEmailTemplatesByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM seg.notificacion_leido WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteTasksByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM seg.usuario_config WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteUserConfigByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM seg.usuario WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deletePreferencesByUserId(@Param("usuario_id") Integer userId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM seg.notificacion_plantilla_usuario WHERE usuario_id = :usuario_id", nativeQuery = true)
    void deleteNotificationTemplatesByUserId(@Param("usuario_id") Integer userId);

    @Query("SELECT u.person.id FROM User u WHERE u.id = :userId")
    Integer findPersonIdByUserId(@Param("userId") Integer userId);

    List<User> findByAliasAndStatusAndCompany_Status(String alias, Character status, Boolean companyStatus);

    List<User> findByAliasAndKeyAndStatusInAndCompany_Status(String alias, String key, List<Character> status, Boolean companyStatus);

    @EntityGraph(attributePaths = "company")
    Optional<User> findByAlias(String alias);
}