package com.maersk.sd1.common.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import com.maersk.sd1.common.model.ChassisEstimateDetail;

import java.util.List;

public interface ChassisEstimateDetailRepository extends JpaRepository<ChassisEstimateDetail, Integer> {
    @Query("select c from ChassisEstimateDetail c where c.chassisEstimate.id = :chassisEstimateId and c.active = true")
    List<ChassisEstimateDetail> findByChassisEstimateIdAndActiveTrue(@Param("chassisEstimateId") Integer chassisEstimateId);

    @Query("select c from ChassisEstimateDetail c where c.chassisEstimate.id in :estimatesIds and c.active = true")
    List<ChassisEstimateDetail> findByEstimateIds(@Param("estimatesIds") List<Integer> estimatesIds);
}