package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Parametrization;
import com.maersk.sd1.sde.dto.InspectionTimeConfigDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ParametrizationRepository extends JpaRepository<Parametrization, Integer> {

    @Query("SELECT NEW com.maersk.sd1.sde.dto.InspectionTimeConfigDto( " +
            " p.minutesInspectionATime, " +
            " p.minutesInspection, " +
            " p.minutesInspectionWithDelay) " +
            " FROM Parametrization p WHERE p.businessUnit.id = :businessUnitId")
    InspectionTimeConfigDto findInspectionTimes(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT p "
            + "FROM Parametrization p "
            + "WHERE p.businessUnit.id = :unidadNegocioId AND p.active = true")
    List<Parametrization> findPtiTimesByBusinessUnit(@Param("unidadNegocioId") Long unidadNegocioId);
}