package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Chassis;
import com.maersk.sd1.sdg.dto.ChassisDataDto;
import com.maersk.sd1.sdg.dto.ChassisDetailsDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface ChassisRepository extends JpaRepository<Chassis, Integer> {

    Optional<Chassis> findByChassisNumber(String chassis);

    @Procedure(name = "Chassis.validateChassisStock")
    String validateChassisStock(
            @Param("business_unit_id") Integer businessUnitId,
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("cat_movement_type_id") Integer catMovementTypeId,
            @Param("chassis_id") Integer chassisId,
            @Param("chassis_number") String chassisNumber,
            @Param("user_id") Integer userId,
            @Param("languaje_id") Integer languageId
    );

    @Query(value = "SELECT " +
            "CH.chassis_number AS chassisNumber, " +
            "ges.fn_CatalogTranslationDescLong(ch.cat_chassis_type_id, :languageId) AS chassisTypeCode, " +
            "CH.tare AS chassisTare, " +
            "ges.fn_CatalogTranslationDescLong(ch.cat_tare_unit_id, :languageId) AS chassisTareUnitDesc, " +
            "ges.fn_CatalogTranslationDesc(dc.cat_reference_type_id, 1) + ' ' + DC.document_chassis_number AS chassisDocRef, " +
            "RTRIM(COALESCE(ownerx.razon_social, '')) AS ownerChassis " +
            "FROM sdh.eir_chassis AS EIR " +
            "INNER JOIN sdh.document_chassis_detail AS DCD ON DCD.document_chassis_detail_id = EIR.document_chassis_detail_id " +
            "INNER JOIN sdh.chassis AS CH ON CH.chassis_id = DCD.chassis_id " +
            "LEFT OUTER JOIN ges.empresa AS ownerx ON ch.owner_company_id = ownerx.empresa_id " +
            "INNER JOIN sdh.document_chassis AS DC ON DC.document_chassis_id = DCD.document_chassis_id " +
            "WHERE EIR.eir_chassis_id = :eirChassisId", nativeQuery = true)
    ChassisDetailsDTO getChassisDetails(@Param("eirChassisId") Integer eirChassisId, @Param("languageId") Integer languageId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.ChassisDataDto(c.catChassisType.id, c.chassisNumber) FROM Chassis c WHERE c.id = :chassisId")
    ChassisDataDto findChassisDetailsById(@Param("chassisId") Integer chassisId);

    @Query("SELECT c.catChassisType.id " +
            "FROM Chassis c " +
            "WHERE c.id = :chassisId")
    Integer findChassisTypeById(@Param("chassisId") Integer chassisId);
}