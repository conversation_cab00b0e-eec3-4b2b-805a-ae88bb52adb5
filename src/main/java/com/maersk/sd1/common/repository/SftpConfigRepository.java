package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.SftpConfig;
import com.maersk.sd1.ges.controller.dto.SftpConfigListInput;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SftpConfigRepository extends JpaRepository<SftpConfig, Integer> {

    @Query("SELECT DISTINCT s FROM SftpConfig s " +
            "LEFT JOIN s.registrationUser r " +
            "WHERE (:#{#input.sftpConfigId} IS NULL OR s.id = :#{#input.sftpConfigId}) " +
            "AND (:#{#input.alias} IS NULL OR LOWER(s.alias) LIKE LOWER(CONCAT('%', :#{#input.alias}, '%'))) " +
            "AND (:#{#input.sftpHost} IS NULL OR LOWER(s.sftpHost) LIKE LOWER(CONCAT('%', :#{#input.sftpHost}, '%'))) " +
            "AND (:#{#input.sftpName} IS NULL OR LOWER(s.sftpName) LIKE LOWER(CONCAT('%', :#{#input.sftpName}, '%'))) " +
            "AND (:#{#input.sftpPass} IS NULL OR LOWER(s.sftpPass) LIKE LOWER(CONCAT('%', :#{#input.sftpPass}, '%'))) " +
            "AND (:#{#input.sftpPort} IS NULL OR LOWER(s.sftpPort) LIKE LOWER(CONCAT('%', :#{#input.sftpPort}, '%'))) " +
            "AND (:#{#input.sftpPath} IS NULL OR LOWER(s.sftpPath) LIKE LOWER(CONCAT('%', :#{#input.sftpPath}, '%'))) " +
            "AND (:#{#input.eventAfterUpload} IS NULL OR LOWER(s.eventAfterUpload) LIKE LOWER(CONCAT('%', :#{#input.eventAfterUpload}, '%'))) " +
            "AND (:#{#input.status} IS NULL OR s.status = :#{#input.status}) " +
            "AND (:#{#input.registrationDateMin} IS NULL OR :#{#input.registrationDateMax} IS NULL OR " +
            "(s.registrationDate >= :#{#input.registrationDateMin} AND s.registrationDate < :#{#input.registrationDateMax}))")
    Page<SftpConfig> findSftpConfigs(@Param("input") SftpConfigListInput.Input input, Pageable pageable);

}