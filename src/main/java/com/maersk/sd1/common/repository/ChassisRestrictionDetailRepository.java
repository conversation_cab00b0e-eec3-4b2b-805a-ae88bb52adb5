package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ChassisRestrictionDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ChassisRestrictionDetailRepository extends JpaRepository<ChassisRestrictionDetail, Integer> {

    @Query("""
            SELECT crd FROM ChassisRestrictionDetail crd 
            WHERE crd.chassisRestriction.chassis.id = :chassisId AND crd.chassisRestriction.subBusinessUnit.id = :subBusinessUnitId
            AND crd.chassisRestriction.restrictionRelease = false AND crd.chassisRestriction.active = true AND crd.active = true
            """)
    List<ChassisRestrictionDetail> findActiveRestrictionDetails(Integer chassisId, Integer subBusinessUnitId);
}