package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.RoleMenuAction;
import com.maersk.sd1.common.model.RoleMenuActionId;
import com.maersk.sd1.seg.dto.RoleObtainOutputDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface RoleMenuActionRepository extends JpaRepository<RoleMenuAction, RoleMenuActionId> {

    @Modifying
    @Query("DELETE FROM RoleMenuAction r WHERE r.id.roleId = :roleId")
    void deleteByRoleId(@Param("roleId") Integer roleId);

    @Query("SELECT new com.maersk.sd1.seg.dto.RoleObtainOutputDTO$RoleMenuActionDto(" +
            "rma.rolMenu.role.id, rma.rolMenu.menu.id, rma.id.typeActionId) " +
            "FROM RoleMenuAction rma " +
            "WHERE rma.rolMenu.role.id = :roleId")
    List<RoleObtainOutputDTO.RoleMenuActionDto> findRoleMenuActionDtoByRoleId(@Param("roleId") Integer roleId);
}