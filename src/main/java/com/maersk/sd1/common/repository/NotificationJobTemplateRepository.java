package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.NotificationJobTemplate;
import com.maersk.sd1.seg.dto.SystemValidateTemplateDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotificationJobTemplateRepository extends JpaRepository<NotificationJobTemplate, Integer> {

    @Query("SELECT " +
            "NJP AS notificationJobClass, "+
            "NPA AS notificationTemplateClass, "+
            "CASE " +
            "WHEN NPA.onlyTime = '0' AND NPA.onlyAlert = '1' AND NJPU.notificationJobTemplate.id IS NOT NULL THEN 1 " +
            "WHEN NPA.onlyTime = '0' AND (NPA.onlyAlert IS NULL OR NPA.onlyAlert = '0') THEN 0 " +
            "WHEN NPA.onlyTime = '1' THEN 0 " +
            "END AS read " +
            "FROM " +
            "NotificationJobTemplate NJP " +
            "INNER JOIN NotificationTemplate NPA ON NPA.id = NJP.notificationTemplate.id " +
            "INNER JOIN NotificationTemplateRole NPR ON NPR.notificationTemplate.id = NPA.id " +
            "INNER JOIN UserRole USR ON USR.role.id = NPR.role.id AND USR.user.id = :userId " +
            "LEFT JOIN NotificationJobTemplateUser NJPU ON NJPU.notificationJobTemplate.id = NJP.id AND NJPU.user.id = :userId " +
            "WHERE " +
            "NPA.catStatus.id = :catalogId AND " +
            "(NPA.onlyTime = '0' OR NJPU.notificationJobTemplate.id IS NULL) AND " +
            "(NJPU.notificationJobTemplate.id IS NULL OR DATEADD(DAY, NPA.duration, NJPU.dateRead) >= CURRENT_TIMESTAMP) ")
    List<SystemValidateTemplateDTO> getTemplateDetails(@Param("userId") Integer userId, @Param("catalogId") Integer catalogStatusId);
}