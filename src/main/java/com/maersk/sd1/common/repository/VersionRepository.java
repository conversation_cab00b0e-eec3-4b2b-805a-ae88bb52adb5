package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Version;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VersionRepository extends JpaRepository<Version, Integer> {

    Optional<Version> findTopBySystemIdAndStatusOrderByIdDesc(Integer systemId, Character status);
}