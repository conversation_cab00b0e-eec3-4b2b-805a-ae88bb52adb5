package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Role;
import com.maersk.sd1.common.model.RoleMenu;
import com.maersk.sd1.common.model.RoleMenuId;
import com.maersk.sd1.seg.controller.dto.RoleObtainerVariousOutput;

import com.maersk.sd1.seg.controller.dto.RoleObtainUserOutput;

import com.maersk.sd1.seg.dto.RoleObtainOutputDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface RoleMenuRepository extends JpaRepository<RoleMenu, RoleMenuId> {

    @Modifying
    @Query("DELETE FROM RoleMenu r WHERE r.id.roleId = :roleId")
    void deleteByRoleId(@Param("roleId") Integer roleId);

    @Query("SELECT rm FROM RoleMenu rm WHERE rm.menu.id = :menuId")
    List<RoleMenu> findByMenuId(@Param("menuId") Integer menuId);

    @Query("SELECT new com.maersk.sd1.seg.controller.dto.RoleObtainerVariousOutput$RoleMenuDTO(" +
            " rm.role.id, rm.menu.id) " +
            "FROM RoleMenu rm " +
            "WHERE rm.role.id IN :roleIds")
    List<RoleObtainerVariousOutput.RoleMenuDTO> findRoleMenuDTOByRoleIdIn(@Param("roleIds") List<Integer> roleIds);

    @Query("SELECT new com.maersk.sd1.seg.dto.RoleObtainOutputDTO$RoleMenuDto(" +
            "rm.id.roleId, rm.id.menuId) " +
            "FROM RoleMenu rm " +
            "WHERE rm.id.roleId = :roleId")
    List<RoleObtainOutputDTO.RoleMenuDto> findRoleMenuDtoByRoleId(@Param("roleId") Integer roleId);

    @Transactional
    @Modifying
    void deleteByRole(Role role);

    @Query("SELECT rm.menu.title FROM RoleMenu rm WHERE rm.role.id = :roleId")
    List<String> findMenuTitlesByRoleId(@Param("roleId") Integer roleId);

    @Query("select distinct new com.maersk.sd1.seg.controller.dto.RoleObtainUserOutput$SistemaMenuDto("
            + "   s.description, s.id, s.icon, rm.id.roleId ) "
            + "from RoleMenu rm "
            + "join rm.menu m "
            + "join ProjectSystem ps on ps.menu = m "
            + "join ps.system s "
            + "where rm.id.roleId in :roleIds")
    List<RoleObtainUserOutput.SistemaMenuDto> findDistinctSistemaMenuByRoleIds(List<Integer> roleIds);
}