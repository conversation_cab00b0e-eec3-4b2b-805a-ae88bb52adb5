package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EmailTemplate;
import com.maersk.sd1.sdg.dto.EmailTemplateDetailsDTO;
import com.maersk.sd1.seg.controller.dto.UserGetEditOutput.EmailTemplateDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EmailTemplateRepository extends JpaRepository<EmailTemplate, Integer>, JpaSpecificationExecutor<EmailTemplate> {

    @Query("SELECT new com.maersk.sd1.sdg.dto.EmailTemplateDetailsDTO(ep.copy, ep.copyHidden, ep.title) FROM EmailTemplate ep WHERE ep.id1 = :templateId")
    EmailTemplateDetailsDTO findEmailTemplateDetailsById(@Param("templateId") String templateId);

    @Query("SELECT new com.maersk.sd1.seg.controller.dto.UserGetEditOutput$EmailTemplateDTO(" +
            " et.id, et.title ) " +
            "FROM EmailTemplate et " +
            "WHERE et.sentUser = true")
    List<EmailTemplateDTO> findEmailTemplatesForUser();
}