package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirNotification;
import com.maersk.sd1.sdg.dto.EirNotificationsAttachmentDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface EirNotificationRepository extends JpaRepository<EirNotification, Integer> {

    @Query("SELECT E.eir.id, E.id FROM EirNotification E WHERE E.status.id = :statusId ORDER BY RAND()")
    Page<Object[]> findTop10ByStatusId(@Param("statusId") Integer statusId, Pageable pageable);

    @Query("SELECT en.eir.id FROM EirNotification en WHERE en.id = :eirNotificationId")
    Integer findEirIdByEirNotificationId(@Param("eirNotificationId") int eirNotificationId);

    @Procedure(name = "RuleGeneralGetOutProcedure")
    Map<String, Object> ruleGeneralGetOut(
            @Param("sub_business_unit_local_alias") String subBusinessUnitLocalAlias,
            @Param("system_rule_id") String systemRuleId,
            @Param("type_rule") String typeRule
    );

    @Modifying
    @Query("UPDATE EirNotification en SET en.status.id = :notificationPendingEmail, " +
            "en.modificationUser.id = 1, en.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE en.id = :eirNotificationId")
    void updateEirNotificationStatus(Integer notificationPendingEmail, Integer eirNotificationId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.EirNotificationsAttachmentDTO(" +
            "'EIR Ticket ' || eir.id || '.pdf' AS name, " +
            "eir.azureStorageUrl AS url) " +
            "FROM EirNotification eir " +
            "WHERE eir.id = :eirNotificationId")
    List<EirNotificationsAttachmentDTO> findAttachmentsByEirNotificationId(@Param("eirNotificationId") int eirNotificationId);

    @Procedure(name = "EmailProcesarProcedure")
    Map<String, Object> processEmail(
            @Param("plantilla_id") String plantillaId,
            @Param("destinatario") String destinatario,
            @Param("copia") String copia,
            @Param("copia_oculta") String copiaOculta,
            @Param("titulo") String titulo,
            @Param("campos") String campos,
            @Param("sistema_id") Integer sistemaId,
            @Param("origen") String origen,
            @Param("referencia") Integer referencia,
            @Param("indicador_enviar") String indicadorEnviar,
            @Param("adjuntos") String adjuntos
    );
}