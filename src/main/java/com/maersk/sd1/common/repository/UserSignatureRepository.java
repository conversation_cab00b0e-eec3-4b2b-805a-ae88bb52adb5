package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.UserSignature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserSignatureRepository extends JpaRepository<UserSignature, Integer> {

    @Query("SELECT us.uid FROM UserSignature us WHERE us.user.id = :userId AND us.active = '1'")
    List<String> findActiveUserSignatureUids(@Param("userId") Integer userId);
}