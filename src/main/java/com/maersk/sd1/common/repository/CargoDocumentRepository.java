package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.CargoDocument;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.sde.dto.EirManualSearchProjection;
import com.maersk.sd1.sds.dto.IHistoryDateOverStayOutput;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository

public interface CargoDocumentRepository extends JpaRepository<CargoDocument, Integer> {
    @Query("SELECT cd.cargoDocument FROM CargoDocument cd WHERE cd.id = :cargoId")
    String findcargoDocumentBycargoIdId(@Param("cargoId") Integer cargoId);

    @Query("SELECT cd FROM CargoDocument cd WHERE cd.id = :cargoId")
    CargoDocument findcargoDoc(@Param("cargoId") Integer cargoId);

    @Query("SELECT c.cargoDocument FROM CargoDocument c WHERE c.id = :cargoDocumentId")
    String findCargoDocumentByCargoDocumentId(Integer cargoDocumentId);

    @Modifying
    @Transactional
    @Query("UPDATE CargoDocument c "
            + "SET c.catDocumentCargoStatus.id = 43061, "
            + "    c.modificationUser.id = :usuarioId, "
            + "    c.modificationDate = CURRENT_TIMESTAMP, "
            + "    c.traceCargoDocument = 'liberado manual' "
            + "WHERE c.id = :cargoDocId "
            + "  AND c.catDocumentCargoStatus.id <> 43061")
    void updateCargoDocumentStatusToActive(Integer cargoDocId, Integer usuarioId);

    @Query("SELECT count(1) FROM CargoDocument c WHERE c.cargoDocument = :bookingNumber AND c.vesselProgrammingDetail.id = :shipScheduleDetailId AND c.active = true")
    Integer countByCargoDocumentAndShipScheduleDetailId(@Param("bookingNumber") String bookingNumber,@Param("shipScheduleDetailId") Integer shipScheduleDetailId);

    @Query("SELECT c.id FROM CargoDocument c WHERE c.cargoDocument = :bookingNumber AND c.vesselProgrammingDetail.id = :shipScheduleDetailId")
    Integer findIdByCargoDocumentAndShipScheduleDetailId(@Param("bookingNumber") String bookingNumber,@Param("shipScheduleDetailId") Integer shipScheduleDetailId);

    @Query("SELECT cd.id " +
            "FROM CargoDocument cd " +
            "WHERE cd.cargoDocument = :booking " +
            "AND cd.vesselProgrammingDetail.id = :vesselProgrammingDetailBkId " +
            "AND cd.active = true " +
            "ORDER BY cd.registrationDate DESC")
    Integer findCargoDocumentIdByBookingAndVesselProgrammingDetailBkId(@Param("booking") String booking,
                                                                       @Param("vesselProgrammingDetailBkId") Integer vesselProgrammingDetailBkId);

    @Modifying
    @Query("UPDATE CargoDocument cd SET cd.vesselProgrammingDetail.id = :vesselProgrammingDetailId, " +
            "cd.modificationUser.id = :userModificationId, cd.modificationDate = CURRENT_TIMESTAMP, " +
            "cd.traceCargoDocument = 'EDI-Replace.Vessel' " +
            "WHERE cd.id = :cargoDocumentId " +
            "AND cd.vesselProgrammingDetail.id <> :vesselProgrammingDetailId")
    int updateCargoDocumentDetails(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                   @Param("userModificationId") Integer userModificationId,
                                   @Param("cargoDocumentId") Integer cargoDocumentId);

    @Modifying
    @Query("UPDATE CargoDocument d " +
            "SET d.destinationPort.id = :destinationPortId, " +
            "d.modificationUser.id = :userRegistrationId, " +
            "d.modificationDate = CURRENT_TIMESTAMP, " +
            "d.traceCargoDocument = 'EDI-Replace.PD1' " +
            "WHERE d.id = :cargoDocumentId AND (d.destinationPort.id IS NULL OR d.destinationPort.id <> :destinationPortId)")
    int updateCargoDocument(@Param("destinationPortId") Integer destinationPortId,
                            @Param("userRegistrationId") Integer userRegistrationId,
                            @Param("cargoDocumentId") Integer cargoDocumentId);

    @Modifying
    @Query("UPDATE CargoDocument d " +
            "SET d.dischargePort.id = :dischargePortId, " +
            "d.modificationUser.id = :userRegistrationId, " +
            "d.modificationDate = CURRENT_TIMESTAMP, " +
            "d.traceCargoDocument = 'EDI-Replace.PD2' " +
            "WHERE d.id = :cargoDocumentId AND d.dischargePort.id <> :dischargePortId")
    int updateCargoDischargeDocument(@Param("dischargePortId") Integer dischargePortId,
                                     @Param("userRegistrationId") Integer userRegistrationId,
                                     @Param("cargoDocumentId") Integer cargoDocumentId);

    @Query("UPDATE CargoDocument dc " +
            "SET dc.shipperCompany.id = :clientId, " +
            "    dc.consigneeCompany.id = :clientId, " +
            "    dc.modificationUser.id = :modifiedById, " +
            "    dc.modificationDate = CURRENT_TIMESTAMP, " +
            "    dc.traceCargoDocument = 'EDI-Replace.CUS' " +
            "WHERE dc.id = :cargoDocumentId")
    int updateCargoDocumentByClientId(@Param("clientId") Integer clientId,
                                      @Param("modifiedById") Integer modifiedById,
                                      @Param("cargoDocumentId") Integer cargoDocumentId);


    @Procedure(name = "CargoDocument.validateDocumentAvailability")
    String validateDocumentAvailability(@Param("business_unit_id") Integer businessUnitId,
                                        @Param("cat_empty_full_id") Integer catEmptyFullId,
                                        @Param("booking_id") Integer bookingId,
                                        @Param("documento_carga_id") Integer cargoDocumentId,
                                        @Param("document_chassis_id") Integer chassisDocumentId,
                                        @Param("languaje_id") Integer languageId);

    @Query("SELECT cd FROM CargoDocument cd WHERE cd.id = :cargoDocumentId AND cd.active = :active")
    Optional<CargoDocument> findByIdAndActive(Integer cargoDocumentId, boolean active);

    @Query("""
            SELECT cdd FROM CargoDocument cd
            INNER JOIN CargoDocumentDetail cdd ON cdd.cargoDocument.id = cd.id
            INNER JOIN cdd.container cnt
            WHERE cd.id = :cargoDocumentId
            AND cnt.catSize.id = :catSizeId
            AND (cdd.balanceQuantity > 0 OR cdd.balanceWeight > 0)
            AND cdd.active = true
            AND cd.active = true
            AND cnt.active = true
            """)
    List<CargoDocumentDetail> findActiveByIdContainerSizeWithPositiveBalance(Integer cargoDocumentId, Integer catSizeId);
    boolean existsByVesselProgrammingDetailIdAndCatCargoOriginIdAndCargoDocumentAndActive(Integer programmingNaveDetailId, Integer cargoOriginCategoryId, String cargoDocument, Boolean active);

    @Modifying
    @Query("UPDATE CargoDocument c " +
            "SET c.catDocumentCargoStatus.id = :isStateBkActive, " +
            "c.modificationUser.id = :userRegistrationId, " +
            "c.modificationDate = :modificationDate, " +
            "c.traceCargoDocument = 'Released by EDI' " +
            "WHERE c.id = :cargoDocumentId " +
            "AND c.catDocumentCargoStatus.id <> :isStateBkActive")
    void updateCargoDocumentStatus(@Param("cargoDocumentId") Integer cargoDocumentId,
                                   @Param("isStateBkActive") Integer isStateBkActive,
                                   @Param("userRegistrationId") Integer userRegistrationId,
                                   @Param("modificationDate") LocalDateTime modificationDate);

    @Query("SELECT cd FROM CargoDocument cd JOIN FETCH cd.shippingLine sl " +
            "JOIN FETCH cd.vesselProgrammingDetail vpd " +
            "WHERE cd.id = :documentoCargaId AND cd.active = true")
    Optional<CargoDocument> findActiveCargoDocumentById(@Param("documentoCargaId") Integer documentoCargaId);

    int count(Specification<CargoDocument> spec);

    @Query("""
            SELECT cd FROM CargoDocument cd            
            WHERE cd.cargoDocument IN :cargoDocuments
            AND cd.vesselProgrammingDetail.vesselProgramming.id =:vesselProgrammingId
            AND cd.vesselProgrammingDetail.vesselProgramming.businessUnit = :businessUnit
            AND cd.vesselProgrammingDetail.vesselProgramming.subBusinessUnit = :subBusinessUnit
            AND cd.active = true AND cd.vesselProgrammingDetail.active = true 
            AND cd.vesselProgrammingDetail.vesselProgramming.active = true                        
            """)
    List<CargoDocument> findByCargoDocumentsList(List<String> cargoDocuments,
                                                 Integer vesselProgrammingId,
                                                 BusinessUnit businessUnit,
                                                 BusinessUnit subBusinessUnit);

    List<CargoDocument> findAll(Specification<CargoDocument> spec, Pageable pageable);

    @Query(value = """
        WITH Unidades AS (
            SELECT DISTINCT pnd.programacion_nave_detalle_id, pn.unidad_negocio_id
            FROM sds.documento_carga dc
            JOIN sds.programacion_nave_detalle pnd ON dc.programacion_nave_detalle_id = pnd.programacion_nave_detalle_id
            JOIN sds.programacion_nave pn ON pnd.programacion_nave_id = pn.programacion_nave_id
            WHERE dc.documento_carga_id = :documentoCargaId AND dc.activo = 1 AND pnd.activo=1 AND pn.activo=1
        ),
        Fmt AS (
            SELECT TOP 1 unidad_negocio_id, [ges].[fn_FormatoDate](unidad_negocio_id) AS formato
            FROM Unidades
        ),
        BaseInfo AS (
            SELECT DISTINCT
              DC.documento_carga   AS bl_master,
              LN.linea_naviera    AS linea_naviera,
              CNT.numero_contenedor AS contenedor,
              RS.fecha_sobretadia AS raw_fecha_sobrestadia,
              RS.fecha_registro   AS raw_fecha_registro,
              RS.coreor_bl        AS raw_bl_ligado,
              DC.programacion_nave_detalle_id AS docPNDId,
              PND.programacion_nave_detalle_id AS recPNDId,
              PN.nave_id              AS naveId,
              PN.viaje               AS naveViaje,
              NA.nombre             AS naveNombre,
              SU.usuario_id         AS usuario_registro_id,
              SU.nombres            AS usuario_registro_nombres,
              (SU.apellido_paterno + ' ' + ISNULL(SU.apellido_materno, '')) AS usuario_registro_apellidos,
              RS.cat_motivo_ingreso_sobrestadia   AS cat_motivo,
              RS.coreor_comentario_usuario        AS comentario_usuario,
              RS.nombre_archivo                  AS nombre_archivo,
              RS.cat_origen_creacion_sobrestadia_id AS cat_origen_id
            FROM sds.documento_carga DC
            JOIN sds.documento_carga_detalle DCD ON DC.documento_carga_id = DCD.documento_carga_id
            JOIN sds.contenedor CNT ON DCD.contenedor_id = CNT.contenedor_id
            JOIN sde.recepcion_sobrestadia RS ON RS.programacion_nave_detalle_id = DC.programacion_nave_detalle_id AND RS.contenedor_id = CNT.contenedor_id
            JOIN seg.usuario SU ON RS.usuario_registro_id = SU.usuario_id
            JOIN sds.linea_naviera LN ON DC.linea_naviera_id = LN.linea_naviera_id
            JOIN sds.programacion_nave_detalle PND ON RS.programacion_nave_detalle_id = PND.programacion_nave_detalle_id
            JOIN sds.programacion_nave PN ON PND.programacion_nave_id = PN.programacion_nave_id
            JOIN sds.nave NA ON PN.nave_id = NA.nave_id
            WHERE DC.documento_carga_id = :documentoCargaId
              AND DCD.activo=1 AND DC.activo=1 AND CNT.activo=1 AND RS.activo=1
              AND PND.activo=1 AND PN.activo=1
              AND PND.cat_operacion_id = 43001
        )
        SELECT
          BI.bl_master as blMaster,
          BI.linea_naviera as shippingLine,
          BI.contenedor as container,
          FORMAT(BI.raw_fecha_sobrestadia, Fmt.formato) as overstayDate,
          FORMAT(BI.raw_fecha_registro, Fmt.formato) as registrationDate,
          CASE WHEN BI.raw_bl_ligado = BI.bl_master THEN '' ELSE BI.raw_bl_ligado END AS linkedBl,
          CASE WHEN BI.docPNDId = BI.recPNDId THEN '' ELSE (BI.naveNombre + '/' + BI.naveViaje) END AS otherShipProgram,
          BI.usuario_registro_id as registrationUserId,
          BI.usuario_registro_nombres as registrationUserNames,
          BI.usuario_registro_apellidos as registrationUserLastNames,
          sds.fn_CatalogoTraducidoDes(BI.cat_motivo, :idiomaId) as reason,
          BI.comentario_usuario as userComment,
          BI.nombre_archivo as fileName,
          sds.fn_CatalogoTraducidoDes(BI.cat_origen_id, :idiomaId) as origin
        FROM BaseInfo BI
        CROSS JOIN Fmt
        ORDER BY BI.contenedor,
        BI.raw_fecha_registro
        """, nativeQuery = true)
    List<IHistoryDateOverStayOutput> rawNativeOverstayHistory(@Param("documentoCargaId") Integer documentoCargaId,
                                                              @Param("idiomaId") Integer idiomaId);

    @Query(value = """
            SELECT TOP 1 pn.unidad_negocio_id
            FROM sds.documento_carga dc
            JOIN sds.programacion_nave_detalle pnd ON dc.programacion_nave_detalle_id = pnd.programacion_nave_detalle_id
            JOIN sds.programacion_nave pn ON pnd.programacion_nave_id = pn.programacion_nave_id
            WHERE dc.documento_carga_id = :documentoCargaId AND dc.activo = 1 AND pnd.activo=1 AND pn.activo=1
            """, nativeQuery = true)
    Long findBusinessUnitId(@Param("documentoCargaId") Integer documentoCargaId);

    @Query(value = """
            SELECT TOP 1 pnd.programacion_nave_detalle_id
            FROM sds.documento_carga dc
            JOIN sds.programacion_nave_detalle pnd ON dc.programacion_nave_detalle_id = pnd.programacion_nave_detalle_id
            WHERE dc.documento_carga_id = :documentoCargaId AND dc.activo=1 AND pnd.activo=1
            """, nativeQuery = true)
    Integer findVesselProgrammingDetailId(@Param("documentoCargaId") Integer documentoCargaId);

    @Query("SELECT cdd FROM CargoDocumentDetail cdd WHERE cdd.cargoDocument.id = :cargoDocumentId AND cdd.active = true AND cdd.cargoDocument.active = true AND cdd.bookingDetail.active = true")
    List<CargoDocumentDetail> findActiveByCargoDocumentIdWithActiveBookingDetail(Pageable pageable, Integer cargoDocumentId);
    
    @Query("SELECT cd.id FROM CargoDocument cd " +
            " JOIN CargoDocumentDetail cdd ON cdd.cargoDocument.id = cd.id " +
            " JOIN BookingDetail bd ON bd.id = cdd.bookingDetail.id " +
            " JOIN Booking bk ON bk.id = bd.booking.id " +
            " WHERE bk.id = :bookingId " +
            "   AND cdd.active = true " +
            "   AND bd.active = true " +
            "   AND bk.active = true " +
            " ORDER BY cd.id ASC")
    Optional<Integer> findFirstCargoDocumentIdByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT cd FROM CargoDocument cd WHERE cd.id = :cargoDocId AND cd.active = true")
    Optional<CargoDocument> findActiveCargoDocById(@Param("cargoDocId") Integer cargoDocId);

    @Query("SELECT COUNT(cd.id) FROM CargoDocument cd " +
            "WHERE cd.active = true " +
            "AND cd.vesselProgrammingDetail.vesselProgramming.id = :vesselProgrammingId " +
            "AND (cd.originPort.id = :portId OR cd.loadingPort.id = :portId OR cd.dischargePort.id = :portId OR cd.destinationPort.id = :portId OR cd.transferPort.id = :portId)")
    long countByVesselProgrammingIdAndPortId(
            @Param("vesselProgrammingId") Integer vesselProgrammingId,
            @Param("portId") Integer portId
    );


    @Query("SELECT c.id FROM BookingDetail b " +
            "JOIN CargoDocumentDetail dcd ON b.id = dcd.bookingDetail.id " +
            "JOIN dcd.cargoDocument c ON dcd.cargoDocument.id = c.id " +
            "WHERE b.booking.id = :bookingId AND b.active = true AND dcd.active = true AND c.active = true")
    Integer findActiveCargoDocumentByBookingId(@Param("bookingId") Integer bookingId);

    @Query("""
                SELECT DISTINCT cdd.cargoDocument.id
                FROM CargoDocumentDetail cdd
                JOIN TransportPlanningDetail tpd ON cdd.id = tpd.cargoDocumentDetail.id
                JOIN TransportPlanning tp ON tpd.transportPlanning.id = tp.id
                WHERE tp.id = :transportPlanningId
            """)
    List<Integer> findCargoDocumentIds(@Param("transportPlanningId") Integer transportPlanningId);

    @Modifying
    @Query("""
                UPDATE CargoDocument cd
                SET cd.catCargoDocumentType.id = :referenceType,
                        cd.cargoDocument = UPPER(:reference),
                        cd.shippingLine.id = :shippingLineId,
                        cd.shipperCompany.id = :shipperId,
                        cd.consigneeCompany.id = :consigneeId,
                        cd.notifierCompany.id = :consigneeId,
                        cd.modificationUser.id = :userRegistrationId,
                        cd.modificationDate = CURRENT_TIMESTAMP
                WHERE cd.id IN :cargoDocumentIds
            """)
    int updateCargoDocuments(
            @Param("referenceType") Integer referenceType,
            @Param("reference") String reference,
            @Param("shippingLineId") Integer shippingLineId,
            @Param("shipperId") Integer shipperId,
            @Param("consigneeId") Integer consigneeId,
            @Param("userRegistrationId") Integer userRegistrationId,
            @Param("cargoDocumentIds") List<Integer> cargoDocumentIds
    );

    @Query("SELECT cd FROM CargoDocument cd \n           JOIN cd.vesselProgrammingDetail vpd \n           JOIN vpd.vesselProgramming vp \n           JOIN vpd.catOperation catOp \n           WHERE cd.catDocumentCargoStatus.id = 43061 \n             AND catOp.id IN :operationIds \n             AND vp.subBusinessUnit.id = :subBuId \n             AND cd.cargoDocument = :blNumber \n             AND cd.active = true \n             AND vpd.active = true \n             AND vp.active = true")
    List<CargoDocument> findActiveCargoDocumentForEmptyBl(@Param("blNumber") String blNumber,
                                                          @Param("operationIds") List<Integer> operationIds,
                                                          @Param("subBuId") Integer subBuId);

    @Query("SELECT cd FROM CargoDocument cd WHERE cd.cargoDocument = :docNumber " +
            "AND cd.vesselProgrammingDetail = :vesselDetail AND cd.active = true")
    CargoDocument findActiveByDocAndVessel(@Param("docNumber") String docNumber,
                                           @Param("vesselDetail") VesselProgrammingDetail vesselProgrammingDetail);


    @Query("""
    SELECT cd FROM CargoDocument cd
     JOIN cd.vesselProgrammingDetail vpd
     JOIN vpd.catOperation co
     JOIN vpd.vesselProgramming vp
     JOIN vp.vessel v
    WHERE cd.cargoDocument = :blNumber
      AND vp.subBusinessUnit.id = :subBusinessUnitId
      AND cd.masterCargoDocument IS NULL
      AND (co.variable1 IS NOT NULL AND co.variable1 = 'I')
      AND cd.active = true
      AND vpd.active = true
      AND vp.active = true
    ORDER BY cd.registrationDate DESC
    """)
    List<CargoDocument> findTopCargoDocumentForManualSearch(
            @Param("blNumber") String blNumber,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            Pageable pageable
    );

    @Query("SELECT COUNT(1) FROM CargoDocument cd "
            + "WHERE cd.active = true "
            + "AND cd.vesselProgrammingDetail.vesselProgramming.id = :programacionNaveId")
    int countActiveCargoDocumentsByProgramacionNaveId(@Param("programacionNaveId") Integer programacionNaveId);

    int countByVesselProgrammingDetailAndActiveTrue(VesselProgrammingDetail detail);

    @Query("""
            SELECT c FROM CargoDocument c
            LEFT JOIN FETCH c.vesselProgrammingDetail pnd
            LEFT JOIN FETCH pnd.vesselProgramming pna
            LEFT JOIN FETCH pna.vessel nav
            LEFT JOIN FETCH pnd.catOperation cop
            LEFT JOIN FETCH pnd.catCreationOrigin cor
            LEFT JOIN FETCH c.catCargoOrigin coc
            LEFT JOIN FETCH c.catTransportRoute cvt
            LEFT JOIN FETCH c.originPort por
            LEFT JOIN FETCH c.loadingPort pem
            LEFT JOIN FETCH c.dischargePort pdc
            LEFT JOIN FETCH c.destinationPort pdt
            LEFT JOIN FETCH c.transferPort ptr
            LEFT JOIN FETCH c.shippingLine lna
            LEFT JOIN FETCH c.depot dep
            LEFT JOIN FETCH c.shipperCompany eem
            LEFT JOIN FETCH c.consigneeCompany eco
            LEFT JOIN FETCH c.notifierCompany eno
            LEFT JOIN FETCH c.customsAgencyCompany eaa
            LEFT JOIN FETCH c.catDocumentCargoStatus ced
            LEFT JOIN FETCH c.catOriginCreation ccr
            LEFT JOIN FETCH c.emptyDepot dva
            LEFT JOIN FETCH c.originDestinationDepot dod
            LEFT JOIN FETCH c.catMoveType cmt
            WHERE c.id = :documentId
    """)
    CargoDocument findCargoDocumentFullData(@Param("documentId") Integer documentId);




    @Query("""
      SELECT c.id AS containerId,
             c.containerNumber AS containerNumber,
             sz.id AS sizeId,
             ct.id AS containerTypeId,
             CONCAT(sz.description, ' - ', ct.description, ' ', ct.longDescription) AS sizeType,
             COALESCE(c.containerTare,1) AS tare,
             'KG' AS measureTare,
             COALESCE(c.maximunPayload,1) AS payload,
             'KG' AS measurePayload,
             ci.id AS isoCodeId,
             ci.isoCode AS isoCode,
             COALESCE(cl.id,42922) AS gradeId,
             COALESCE(cl.description,'') AS grade,
             COALESCE(rf.description,'') AS reeferType,
             si.id AS shippingLineContainerId,
             si.name AS shippingLineContainer,
             nv.id AS vesselId,
             pn.voyage AS voyage,
             cl.id as catalogId
      FROM CargoDocument d
      JOIN VesselProgrammingDetail vpd ON vpd.id = d.vesselProgrammingDetail.id
      JOIN VesselProgramming pn ON pn.id = vpd.vesselProgramming.id
      JOIN ShippingLine docline ON docline.id = d.shippingLine.id
      JOIN CargoDocumentDetail cdd ON cdd.cargoDocument.id = d.id
      JOIN Container c ON c.id = cdd.container.id
      LEFT JOIN Catalog sz ON sz.id = c.catSize.id
      LEFT JOIN Catalog ct ON ct.id = c.catContainerType.id
      LEFT JOIN Catalog rf ON rf.id = c.catReeferType.id
      LEFT JOIN IsoCode ci ON ci.id = c.isoCode.id
      LEFT JOIN ShippingLine si ON si.id = c.shippingLine.id
      LEFT JOIN Catalog cl ON cl.id = c.catGrade.id
      LEFT JOIN Vessel nv ON nv.id = pn.vessel.id
      WHERE c.containerNumber = :containerNumber
        AND pn.subBusinessUnit.id = :subBusinessUnitId
        """)
    List<EirManualSearchProjection> findContainerData(@Param("containerNumber") String containerNumber,
                                                      @Param("subBusinessUnitId") Long subBusinessUnitId);

    @Query("""
            SELECT bk.id 
            FROM CargoDocument cd
            JOIN CargoDocumentDetail cdd ON cd.id = cdd.cargoDocument.id
            JOIN cdd.bookingDetail bd ON cdd.bookingDetail.id = bd.id
            JOIN bd.booking bk ON bd.booking.id = bk.id
            WHERE cd.id = :cargoDocumentId
            AND cdd.active = true
            AND bd.active = true
            AND bk.active = true
            """)
    List<Integer> findBookingIdByCargoDocumentId(@Param("cargoDocumentId") Integer cargoDocumentId);


}

