package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirObservationInspector;
import com.maersk.sd1.sde.dto.EirObservationInspectorProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EirObservationInspectorRepository extends JpaRepository<EirObservationInspector, Integer> {
    @Query(value = """
            SELECT	A.cat_observacion_eir_id as cat_observacion_eir_id,
                ROW_NUMBER() OVER(ORDER BY  [sds].[fn_CatalogoTraducidoDes](a.cat_observacion_eir_id,1)) as row,
                [sds].[fn_CatalogoTraducidoDes](a.cat_observacion_eir_id,:idiomaId)+
                iif(REPLACE(ISNULL(a.observacion_eir_otros,''),CHAR(10),' ')='','',': '+REPLACE(ISNULL(a.observacion_eir_otros,''),CHAR(10),' ')) as observacion_descripcion_secos
            FROM	[sde].[eir_observacion_inspector] AS A (NOLOCK)
            WHERE	eir_id = :eirId AND isnull(A.for_reefer,0) = :forReefer AND A.activo = 1
            ORDER BY 2
            """, nativeQuery = true)
    List<Object[]> findObservationsForEir(@Param("eirId") Integer eirId, @Param("idiomaId") Integer idiomaId, @Param("forReefer") Boolean forReefer);

    @Query(value = """
        SELECT 
            ROW_NUMBER() OVER(ORDER BY sds.fn_CatalogoTraducidoDes(a.cat_observacion_eir_id,1)) as row,
            sds.fn_CatalogoTraducidoDes(a.cat_observacion_eir_id, :languageId) +
            IIF(REPLACE(ISNULL(a.observacion_eir_otros,''), CHAR(10), ' ') = '', '', ': ' + REPLACE(ISNULL(a.observacion_eir_otros,''), CHAR(10), ' ')) 
            AS observacionDescripcion
        FROM sde.eir_observacion_inspector AS a (NOLOCK)
        WHERE a.eir_id = :eirId 
        AND ISNULL(a.for_reefer, 0) = :forReefer
        AND a.activo = 1
        ORDER BY 2
    """, nativeQuery = true)
    List<EirObservationInspectorProjection> findObservationsByEirIdAndType(
            @Param("eirId") Integer eirId,
            @Param("languageId") Integer languageId,
            @Param("forReefer") Integer forReefer
    );

    List<EirObservationInspector> findAllByEirIdAndActive(@Param("eirId") Integer eirId, @Param("active") Boolean active);
}