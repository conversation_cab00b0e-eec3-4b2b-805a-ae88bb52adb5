package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.dto.EIRDTO;
import com.maersk.sd1.common.dto.EquipmentConditionEIRContainerDTO;
import com.maersk.sd1.common.dto.EquipmentConditionEIRDTO;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.sde.dto.DocumentationEmptyListTb01;
import com.maersk.sd1.sde.dto.FindContainerPreallocateDTO;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.sde.dto.EirDataProjection;
import com.maersk.sd1.sde.dto.EirGateDriverPhotoProjection;
import com.maersk.sd1.sde.dto.*;
import com.maersk.sd1.sde.dto.EirCustomFilterDTO;
import com.maersk.sd1.sde.dto.EirDataDTO;
import com.maersk.sd1.sdg.dto.DataProjection;
import com.maersk.sd1.sdg.dto.EirDataDto;
import com.maersk.sd1.sdg.dto.Report21GeneralGateinDataProjection;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Primary
public interface EirRepository extends JpaRepository<Eir, Integer> {

    Eir findOneById(Integer id);

    @Query("select e from Eir e where e.container.id = :containerId and e.vesselProgrammingDetail.id = :vesselProgrammingDetailId and e.active = true")
    Eir findByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(@Param("containerId") Integer containerId, @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Procedure(name = "Eir.truckDepartureRegisterCreateim")
    Map<String, Object> truckDepartureRegisterCreateim(
            @Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId,
            @Param("eir_id") Integer eirId,
            @Param("container_id") Integer containerId,
            @Param("user_registration_id") Integer userRegistrationId
    );

    @Query("SELECT eirx.catMovement.id " +
            "FROM Eir eirx " +
            "WHERE eirx.container.id = :containerId " +
            "AND eirx.businessUnit.id = :businessUnitId " +
            "AND eirx.active = true " +
            "ORDER BY eirx.truckArrivalDate DESC")
    List<Integer> findFirstMovementTypeForStock(@Param("containerId") Integer containerId, @Param("businessUnitId") Integer businessUnitId);

    @Query("""
            select e from Eir e
            where e.container.id = :containerId and e.localSubBusinessUnit .id = :localSubBusinessUnitId and e.catMovement.id = :catMovementId and e.truck.id = :truckId and e.driverPerson.id = :driverPersonId and e.truckArrivalDate between :truckArrivalDateStart and :truckArrivalDateEnd and e.active = true""")
    Eir findByContainerIdAndLocalSubBusinessUnitIdAndCatMovementIdAndTruckIdAndDriverPersonIdAndTruckArrivalDateBetweenAndActiveTrue(@Param("containerId") Integer containerId, @Param("localSubBusinessUnitId") Integer localSubBusinessUnitId, @Param("catMovementId") Integer catMovementId, @Param("truckId") Integer truckId, @Param("driverPersonId") Integer driverPersonId, @Param("truckArrivalDateStart") LocalDateTime truckArrivalDateStart, @Param("truckArrivalDateEnd") LocalDateTime truckArrivalDateEnd);

    @Query("SELECT new com.maersk.sd1.common.dto.EIRDTO(te.id, te.container.id, ctis.description, ctpr.description) " +
            "FROM Eir te " +
            "JOIN Catalog ctis ON ctis.id = te.catMovement.id " +
            "JOIN Catalog ctpr ON ctpr.id = te.catEmptyFull.id " +
            "WHERE te.id = :eirId")
    EIRDTO findEirDetailsById(@Param("eirId") Integer eirId);

    @Query(value = "SELECT sdg.fn_GetEquipmentConditionID(:eirId, :catEquipmentCategoryId, :structureMachinery, :specificTask)", nativeQuery = true)
    Integer getEquipmentConditionId(@Param("eirId") Integer eirId,
                                    @Param("catEquipmentCategoryId") Integer catEquipmentCategoryId,
                                    @Param("structureMachinery") String structureMachinery,
                                    @Param("specificTask") String specificTask);

    @Query("""
                SELECT CASE WHEN COUNT(b) > 0 THEN true ELSE false END
                FROM Eir b
                WHERE b.id = :eirId
                  AND b.container.id IN (:containerDummyId, :containerNotApplicableId)
                  AND b.localSubBusinessUnit.id = :subBusinessUnitLocalId
                  AND b.catMovement.id = :isGateOut
                  AND b.catCreationOrigin.id IN (:isCreationGoGeneral, :isCreationGoLight)
                  AND b.controlAssignmentLight IN (0, 2)
                  AND b.active = true
            """)
    boolean existsGateOutGeneral(
            Integer eirId,
            Integer subBusinessUnitLocalId,
            Integer isGateOut,
            Integer isCreationGoGeneral,
            Integer isCreationGoLight,
            Integer containerDummyId,
            Integer containerNotApplicableId
    );

    @Query("""
            SELECT CASE WHEN COUNT(b) > 0 THEN true ELSE false END
            FROM Eir b
            WHERE b.id = :eirId
              AND b.container.id <> :containerDummyId
              AND b.localSubBusinessUnit.id = :subBusinessUnitLocalId
              AND b.catMovement.id = :isGateOut
              AND b.catEmptyFull.id = :isFull
              AND b.catCreationOrigin.id IN (:isCreationGoGeneral)
              AND b.controlAssignmentLight = 1
              AND b.active = true
            """)
    boolean existsGateOutFull(
            Integer eirId,
            Integer subBusinessUnitLocalId,
            Integer isGateOut,
            Integer isFull,
            Integer isCreationGoGeneral,
            Integer containerDummyId
    );

    @Query("SELECT new com.maersk.sd1.common.dto.EquipmentConditionEIRContainerDTO(e.catEmptyFull.id, e.catMovement.id, e.eirChassis.id, " +
            "CASE WHEN c.code IS NULL OR c.code = '0' THEN false ELSE true END, " +
            "e.structureWithDamage, e.machineryWithDamage) " +
            "FROM Eir e " +
            "JOIN Container cnt ON e.container.id = cnt.id " +
            "JOIN Catalog c ON cnt.catContainerType.id = c.id " +
            "WHERE e.id = :eirId")
    EquipmentConditionEIRContainerDTO findEquipmentConditionEirContainerDetails(@Param("eirId") Integer eirId);


    @Query("SELECT new com.maersk.sd1.common.dto.EquipmentConditionEIRDTO(e.structureWithDamage, e.machineryWithDamage, " +
            "COALESCE(e.catApprovalRepBox.id, 0), COALESCE(e.catApprovalRepMachine.id, 0), COALESCE(e.catCleaningStatus.id, 0)) " +
            "FROM Eir e " +
            "WHERE e.id = :eirId")
    EquipmentConditionEIRDTO findEquipmentConditionEirDetails(@Param("eirId") Integer eirId);

    @Query("SELECT a.id FROM Eir a WHERE a.id = :eirId AND " +
            "(a.active = true OR (a.active = false AND COALESCE(a.controlRevision, 0) = 2))")
    Integer findEirIdByEirIdAndConditions(@Param("eirId") Integer eirId);

    @Query(value = """
                 SELECT
                     ROW_NUMBER() OVER (ORDER BY b.eir_id) AS rowId,
                     b.sub_unidad_negocio_id AS subBusinessUnitId,
                     b.eir_id AS eirId,
                     RTRIM(VEHX.placa) AS truckPlate,
                     b.cat_procedencia_id AS originCategoryId,
                     b.fecha_ingreso_camion AS truckInDate,
                     RTRIM(cliente.documento) AS customerDocument,
                     RTRIM(et.documento) AS truckCompanyDocument,
                     RTRIM(TtnooCnt.descripcion) AS dimensionCode,
                     RTRIM(chofer.documento_identidad) AS driverDni,
                     b.tara_cnt AS containerTare,
                     RTRIM(NAVEx.nombre) + '/' + RTRIM(nvr.viaje) AS nvr,
                     RTRIM(cnt.numero_contenedor) AS containerNumber,
                     isocnt.codigo_iso AS isoCode,
                     ISNULL(RTRIM(b.precinto_1), '') AS seal1,
                     ISNULL(RTRIM(b.precinto_2), '') AS seal2,
                     ISNULL(RTRIM(b.precinto_3), '') AS seal3,
                     ISNULL(RTRIM(b.precinto_4), '') AS seal4,
                     RTRIM(TtipoCnt.descripcion) AS containerTypeCode,
                     b.carga_maxima_cnt AS payload,
                     b.fecha_fabricacion AS manufactureDate,
                     RTRIM(clscnt.descripcion) AS containerClassCode,
                     RTRIM(ET.razon_social) AS truckCompanyName,
                     ISNULL(RTRIM(chofer.nombres), '') + ' ' + ISNULL(RTRIM(chofer.apellido_parterno), '') AS driverName,
                     ISNULL(RTRIM(chofer.licencia_conducir), '') AS driverLicense,
                     RTRIM(NAVEx.nombre) AS vesselName,
                     RTRIM(nvr.viaje) + '/' + RTRIM(opex.descripcion) AS vrr,
                     CASE ISNULL(opex.variable_1, '')
                         WHEN 'I' THEN 'Import'
                         WHEN 'E' THEN 'Export'
                         ELSE 'Diversos'
                     END AS operationDepotTypeName,
                     sds.fn_CatalogoTraducidoDesLarga(b.cat_procedencia_id, :languageId) AS subMoveTypeName,
                     RTRIM(cliente.razon_social) AS customerName,
                     ISNULL(b.eir_ubicacion_yard, '') AS location,
                     IIF(ISNULL(deir.documento_carga_referencia, '') = '', DO.documento_carga, deir.documento_carga_referencia) AS doField,
                     b.observacion AS eirComments,
                     b.fecha_salida_camion AS truckOutDate,
                     b.estructura_con_dano AS structureWithDamage,
                     b.maquinaria_con_dano AS machineryWithDamage,
                     b.persona_inspector_id AS inspectorPersonId,
                     nvr.viaje AS voyage,
                     UN.nombre AS subUnitName,
                     linnav_eir.NOMBRE AS shippingLine,
                     ISNULL(b.control_revision, 0) AS controlRevision,
                     CH.chassis_number AS chassisNumberWithOwner,
                     b.chassis_number AS referentialChassis,
                     b.cat_movimiento_id AS movementCategoryId,
                     b.contenedor_id AS containerId,
                     b.eir_chassis_id AS eirChassisId,
                     b.cat_measure_tare_id AS measureTareCategoryId,
                     b.cat_measure_payload_id AS measurePayloadCategoryId,
                     b.cat_empty_full_id AS emptyFullCategoryId
                 FROM sde.eir AS b (NOLOCK)
                 INNER JOIN sds.vehiculo AS VEHX (NOLOCK) ON b.vehiculo_id = VEHX.vehiculo_id
                 LEFT JOIN ges.empresa AS cliente (NOLOCK) ON b.empresa_cliente_id = cliente.empresa_id
                 INNER JOIN ges.empresa AS ET (NOLOCK) ON b.empresa_transporte_id = ET.empresa_id
                 INNER JOIN ges.catalogo AS TtnooCnt (NOLOCK) ON b.cat_tamano_cnt_id = TtnooCnt.catalogo_id
                 INNER JOIN ges.persona AS chofer (NOLOCK) ON b.persona_conductor_id = chofer.persona_id
                 LEFT JOIN sds.programacion_nave_detalle AS nvrr (NOLOCK) ON b.programacion_nave_detalle_id = nvrr.programacion_nave_detalle_id
                 LEFT JOIN sds.programacion_nave AS nvr (NOLOCK) ON nvrr.programacion_nave_id = nvr.programacion_nave_id
                 LEFT JOIN sds.nave AS NAVEx (NOLOCK) ON nvr.nave_id = NAVEx.nave_id
                 INNER JOIN sds.contenedor AS cnt (NOLOCK) ON b.contenedor_id = cnt.contenedor_id
                 INNER JOIN sds.codigo_iso AS isocnt (NOLOCK) ON b.codigo_iso_id = isocnt.codigo_iso_id
                 INNER JOIN ges.catalogo AS TtipoCnt (NOLOCK) ON b.cat_tipo_contenedor_id = TtipoCnt.catalogo_id
                 INNER JOIN ges.catalogo AS clscnt (NOLOCK) ON b.cat_clase_cnt_id = clscnt.catalogo_id
                 INNER JOIN ges.catalogo AS opex (NOLOCK) ON nvrr.cat_operacion_id = opex.catalogo_id
                 LEFT JOIN sde.eir_documento_carga_detalle AS DEIR (NOLOCK) ON b.eir_id = DEIR.eir_id AND DEIR.activo = 1
                 LEFT JOIN sds.documento_carga_detalle AS DOD (NOLOCK) ON DEIR.documento_carga_detalle_id = DOD.documento_carga_detalle_id
                 LEFT JOIN sds.documento_carga AS DO (NOLOCK) ON DOD.documento_carga_id = DO.documento_carga_id
                 LEFT JOIN seg.unidad_negocio AS UN (NOLOCK) ON b.sub_unidad_negocio_id = UN.unidad_negocio_id
                 LEFT JOIN sds.linea_naviera AS linnav_eir (NOLOCK) ON b.linea_naviera_id = linnav_eir.linea_naviera_id
                 LEFT JOIN sdh.eir_chassis AS EIRCH (NOLOCK) ON b.eir_chassis_id = EIRCH.eir_chassis_id
                 LEFT JOIN sdh.chassis AS CH (NOLOCK) ON EIRCH.chassis_id = CH.chassis_id
                 WHERE b.eir_id IN (:eirIds)
            """,
            nativeQuery = true)
    List<DataProjection> getDataByEirIds(@Param("eirIds") List<Integer> eirIds, @Param("languageId") Integer languageId);

    @Query("SELECT e.container.containerNumber FROM Eir e " +
            "JOIN e.container CNT " +
            "WHERE e.eirChassis.id = :eirChassisId AND e.container.id <> :containerNotApplicaId")
    List<String> findContainerNumbersByEirChassisId(@Param("eirChassisId") Integer eirChassisId,
                                                    @Param("containerNotApplicaId") Integer containerNotApplicaId);

    @Query(value = "SELECT dbo.FUN_RecortarCadenaTicketGuia(:cadena, :corte, :largoLinea)", nativeQuery = true)
    String formatString(
            @Param("cadena") String cadena,
            @Param("corte") Integer corte,
            @Param("largoLinea") Integer largoLinea
    );

    @Modifying
    @Query("UPDATE Eir e SET e.signatureDriver = :driverDateSignature, e.signatureDriverURL = :urlDriverSignature, " +
            "e.signatureInspectorURL = :urlInspectorSignature, e.modificationUser.id = :userRegistrationId, " +
            "e.modificationDate = :currentTimestamp WHERE e.id IN :eirIds")
    void updateEir(List<Integer> eirIds, LocalDateTime driverDateSignature, String urlDriverSignature,
                   String urlInspectorSignature, Integer userRegistrationId, LocalDateTime currentTimestamp);


    @Query("SELECT e.eirChassis.id FROM Eir e WHERE e.id IN :eirIds")
    List<Integer> findEirChassisIdsByEirIds(List<Integer> eirIds);

    @Query("SELECT e FROM Eir e WHERE e.id = :eirId AND e.active = true")
    Eir findEirIds(@Param("eirId") Integer eirChassisId);

    @Query(value = """
            SELECT
                a.unidad_negocio_id AS businessUnitId,
                a.sub_unidad_negocio_id AS subBusinessUnitId,
                a.programacion_nave_detalle_id AS programacionNaveDetalleId,
                a.contenedor_id AS containerId,
                NULL AS chassisId,
                a.cat_empty_full_id AS catEmptyFullId,
                movx.variable_2 + ' ' + efx.descricion_larga AS tipoMov,
                Tlocal.nombre AS local,
                A.eir_id AS eirContainerId,
                A.fecha_ingreso_camion AS fechaIngresoCamion,
                A.fecha_salida_camion AS fechaSalidaCamion,
                b.numero_contenedor AS equipmentNumber,
                TtnooCnt.descripcion + ' ' + TtipoCnt.descripcion AS equipmentSizeType,
                :is_container AS catEquipmentCategory,
                a.codigo_iso_id AS isocodeId,
                isocodex.codigo_iso AS isocodeNumber,
                linnav_EIR.nombre AS ownerPropietario,
                CASE TtipoCnt.codigo WHEN '1' THEN 'Y' ELSE 'N' END AS reefer,
                a.vehiculo_id AS vehiculeId,
                vehx.placa AS plateTruckNumber,
                a.empresa_transporte_id AS transportCompanyId,
                transpx.documento AS transportCompanyCode,
                transpx.razon_social AS transportCompanyName,
                truckcomp2x.valor AS transportCompanyScac,
                a.persona_conductor_id AS driverId,
                chofx.documento_identidad AS driverDoc,
                ISNULL(chofx.nombres,'') +' '+ISNULL(chofx.apellido_parterno,'')+' '+ISNULL(chofx.apellido_materno,'') AS driverName,
                a.usuario_registro_id AS userRegisterId,
                ISNULL(usu_reg.nombres,'') +' '+ISNULL(usu_reg.apellido_paterno,'')+' '+ISNULL(usu_reg.apellido_materno,'') AS userRegisterName,
                a.fecha_registro AS fechaRegistro,
                ISNULL(a.precinto_1, '') + IIF(ISNULL(a.precinto_2, '') <> '', ', ', '') + ISNULL(a.precinto_2, '') +
                IIF(ISNULL(a.precinto_3, '') <> '', ', ', '') + ISNULL(a.precinto_3, '') +
                IIF(ISNULL(a.precinto_4, '') <> '', ', ', '') + ISNULL(a.precinto_4, '') AS seals,
                a.observacion AS observacion,
                NULL AS catCargoDocumentTypeId,
                NULL AS cargoDocumentNumber,
                NULL AS shipperNro,
                NULL AS shipperName,
                NULL AS consigneeNro,
                NULL AS consigneeName,
                NULL AS operationTypeId,
                NULL AS operationTypeName,
                0 AS isShow,
                a.usuario_salida_camion AS usuarioSalidaCamion,
                NULL AS productoId,
                eir_in.eir_id AS gateInEirNumber,
                eir_in.fecha_ingreso_camion AS gateInDate,
                a.eir_chassis_id AS eirChassisId,
                NULL AS eirChassisNumber,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'S', 'INS') AS catStructureConditionId,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'M', 'INS') AS catMachineryConditionId,
                a.chassis_number AS refChassisNumber,
                depot_operax.variable_1 AS operationGroupType,
                a.cat_clase_cnt_id AS gradeId,
                IIF(A.eir_chassis_id IS NULL, 0, A.flag_chassis_stayed) AS flagChassisPickup,
                a.numero_twr AS numeroTwr
            FROM sde.eir A (NOLOCK)
            INNER JOIN sds.contenedor B (NOLOCK) ON A.contenedor_id = B.contenedor_id
            INNER JOIN sds.linea_naviera AS linnav_EIR (NOLOCK) ON a.linea_naviera_id = linnav_EIR.linea_naviera_id
            INNER JOIN ges.catalogo AS TtipoCnt (NOLOCK) ON a.cat_tipo_contenedor_id = TtipoCnt.catalogo_id
            INNER JOIN ges.catalogo AS TtnooCnt (NOLOCK) ON a.cat_tamano_cnt_id = TtnooCnt.catalogo_id
            INNER JOIN seg.unidad_negocio AS Tlocal (NOLOCK) ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
            INNER JOIN ges.catalogo AS movx (NOLOCK) ON a.cat_movimiento_id = movx.catalogo_id
            INNER JOIN ges.catalogo AS efx (NOLOCK) ON a.cat_empty_full_id = efx.catalogo_id
            INNER JOIN sds.programacion_nave_detalle AS prnade (NOLOCK) ON a.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id
            INNER JOIN sds.programacion_nave AS prna (NOLOCK) ON prnade.programacion_nave_id = prna.programacion_nave_id
            INNER JOIN ges.catalogo AS depot_operax (NOLOCK) ON prnade.cat_operacion_id = depot_operax.catalogo_id
            LEFT JOIN sde.stock_vacio AS SV (NOLOCK) ON a.eir_id = SV.eir_salida_id AND SV.activo = 1
            LEFT JOIN sde.eir AS eir_in (NOLOCK) ON SV.eir_ingreso_id = eir_in.eir_id AND eir_in.activo = 1
            LEFT JOIN sdf.stock_full AS c (NOLOCK) ON a.eir_id = c.eir_gateout_id AND c.active = 1
            LEFT JOIN sde.eir AS eir_in_full (NOLOCK) ON c.eir_gatein_id = eir_in_full.eir_id
            LEFT JOIN sdh.eir_chassis AS eirchax (NOLOCK) ON a.eir_chassis_id = eirchax.eir_chassis_id
            LEFT JOIN sdh.chassis AS chax (NOLOCK) ON eirchax.chassis_id = chax.chassis_id
            LEFT JOIN sds.vehiculo AS vehx (NOLOCK) ON a.vehiculo_id = vehx.vehiculo_id
            LEFT JOIN ges.persona AS chofx (NOLOCK) ON a.persona_conductor_id = chofx.persona_id
            LEFT JOIN seg.usuario AS usu_reg (NOLOCK) ON a.usuario_registro_id = usu_reg.usuario_id
            LEFT JOIN sds.codigo_iso AS isocodex (NOLOCK) ON a.codigo_iso_id = isocodex.codigo_iso_id
            LEFT JOIN ges.empresa AS transpx (NOLOCK) ON a.empresa_transporte_id = transpx.empresa_id
            LEFT JOIN [ges].[empresa_config] AS truckcomp2x (NOLOCK) ON transpx.empresa_id = truckcomp2x.empresa_id AND truckcomp2x.tipo_configuracion_id = :is_catalogo_SCAC
            WHERE
                A.sub_unidad_negocio_id = :sub_business_unit_id
                AND a.cat_movimiento_id = :is_gate_out
                AND (seg.fn_datetime_get(a.sub_unidad_negocio_id, A.fecha_ingreso_camion) BETWEEN :pd_truck_in_date_desde AND CONVERT(VARCHAR(10), :pd_truck_in_date_hasta, 111)+' 23:59:59')
                AND a.cat_empty_full_id = IIF(:pd_cat_empty_full_id IS NULL, a.cat_empty_full_id, :pd_cat_empty_full_id)
                AND A.eir_id = IIF(:pd_eir_number IS NULL, A.eir_id, :pd_eir_number)
                AND A.contenedor_id <> :equipment_not_applicable_id
                AND b.numero_contenedor LIKE LTRIM(RTRIM(ISNULL(:pd_equipment_number,'')))+'%'
                AND a.linea_naviera_id = IIF(:pd_shipping_line_id IS NULL, a.linea_naviera_id, :pd_shipping_line_id)
                AND A.activo = 1;
            """, nativeQuery = true)
    List<Object[]> fetchGateoutData(
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("is_gate_out") Integer isGateOut,
            @Param("pd_cat_empty_full_id") Integer pdCatEmptyFullId,
            @Param("pd_truck_in_date_desde") LocalDate truckInDateDesde,
            @Param("pd_truck_in_date_hasta") LocalDate truckInDateHasta,
            @Param("pd_eir_number") Integer eirNumber,
            @Param("equipment_not_applicable_id") Integer equipmentNotApplicableId,
            @Param("pd_equipment_number") String equipmentNumber,
            @Param("pd_shipping_line_id") Integer shippingLineId,
            @Param("is_container") Integer isContainer,
            @Param("is_catalogo_SCAC") Integer isCatalogoSCAC
    );

    @Query(value = """
            SELECT DISTINCT
                a.unidad_negocio_id AS businessUnitId,
                a.sub_unidad_negocio_id AS subBusinessUnitId,
                NULL AS programacionNaveDetalleId,
                NULL AS containerId,
                chax.chassis_id AS chassisId,
                a.cat_empty_full_id AS catEmptyFullId,
                movx.variable_2 AS tipoMov,
                Tlocal.nombre AS local,
                A.eir_id AS eirContainerId,
                A.fecha_ingreso_camion AS fechaIngresoCamion,
                A.fecha_salida_camion AS fechaSalidaCamion,
                chax.chassis_number AS equipmentNumber,
                ges.fn_CatalogTranslationDescLong(chax.cat_chassis_type_id, :pd_idioma_id) AS equipmentSizeType,
                :is_chassis AS catEquipmentCategory,
                NULL AS isocodeId,
                isocodex.codigo_iso AS isocodeNumber,
                ownerx.razon_social AS ownerPropietario,
                '' AS reefer,
                a.vehiculo_id AS vehiculeId,
                vehx.placa AS plateTruckNumber,
                a.empresa_transporte_id AS transportCompanyId,
                transpx.documento AS transportCompanyCode,
                transpx.razon_social AS transportCompanyName,
                truckcomp2x.valor AS transportCompanyScac,
                a.persona_conductor_id AS driverId,
                chofx.documento_identidad AS driverDoc,
                ISNULL(chofx.nombres, '') + ' ' + ISNULL(chofx.apellido_parterno, '') + ' ' + ISNULL(chofx.apellido_materno, '') AS driverName,
                a.usuario_registro_id AS userRegisterId,
                ISNULL(usu_reg.nombres, '') + ' ' + ISNULL(usu_reg.apellido_paterno, '') + ' ' + ISNULL(usu_reg.apellido_materno, '') AS userRegisterName,
                eirchax.registration_date AS fechaRegistro,
                '' AS seals,
                a.observacion AS observacion,
                chadox.cat_reference_type_id AS catCargoDocumentTypeId,
                chadox.document_chassis_number AS cargoDocumentNumber,
                chaxcusx.documento AS shipperNro,
                chaxcusx.razon_social AS shipperName,
                null AS consigneeNro,
                null AS consigneeName,
                chadox.cat_chassis_operation_type_id AS operationTypeId,
                ges.fn_CatalogTranslationDesc(chadox.cat_chassis_operation_type_id, :pd_idioma_id) AS operationTypeName,
                0 AS isShow,
                a.usuario_salida_camion AS usuarioSalidaCamion,
                null AS productoId,
                eir_in.eir_id AS gateInEirNumber,
                eir_in.fecha_ingreso_camion AS gateInDate,
                a.eir_chassis_id AS eirChassisId,
                NULL AS eirChassisNumber,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :is_chassis, 'S', 'DEL') AS catStructureConditionId,
                null AS catMachineryConditionId,
                NULL AS refChassisNumber,
                null AS operationGroupType,
                null AS gradeId,
                NULL AS flagChassisPickup,
                a.numero_twr AS numeroTwr,
                (SELECT EIR.accelerate_program_number FROM sde.eir EIR WHERE A.eir_id = EIR.eir_id) AS accelerateProgramNumber
            FROM sde.eir A (NOLOCK)
            INNER JOIN sdh.eir_chassis AS eirchax (NOLOCK) ON A.eir_chassis_id = eirchax.eir_chassis_id
            INNER JOIN sdh.chassis AS chax (NOLOCK) ON eirchax.chassis_id = chax.chassis_id
            INNER JOIN sdh.document_chassis_detail AS chadodx (NOLOCK) ON eirchax.document_chassis_detail_id = chadodx.document_chassis_detail_id
            INNER JOIN sdh.document_chassis AS chadox (NOLOCK) ON chadodx.document_chassis_id = chadox.document_chassis_id
            INNER JOIN seg.unidad_negocio AS Tlocal (NOLOCK) ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
            INNER JOIN ges.catalogo AS movx (NOLOCK) ON a.cat_movimiento_id = movx.catalogo_id
            LEFT OUTER JOIN ges.empresa AS ownerx (NOLOCK) ON chax.owner_company_id = ownerx.empresa_id
            LEFT OUTER JOIN ges.empresa AS chaxcusx (NOLOCK) ON chadox.customer_compay_id = chaxcusx.empresa_id
            LEFT JOIN sdh.stock_chassis AS sc (NOLOCK) ON a.eir_chassis_id = sc.eir_chassis_gateout_id AND sc.active = 1
            LEFT JOIN sde.eir AS eir_in (NOLOCK) ON sc.eir_chassis_gatein_id = eir_in.eir_chassis_id AND eir_in.activo = 1
            LEFT JOIN sds.vehiculo AS vehx (NOLOCK) ON a.vehiculo_id = vehx.vehiculo_id
            LEFT JOIN ges.persona AS chofx (NOLOCK) ON a.persona_conductor_id = chofx.persona_id
            LEFT JOIN seg.usuario AS usu_reg (NOLOCK) ON a.usuario_registro_id = usu_reg.usuario_id
            LEFT OUTER JOIN sds.codigo_iso AS isocodex (NOLOCK) ON a.codigo_iso_id = isocodex.codigo_iso_id
            LEFT JOIN ges.empresa AS transpx (NOLOCK) ON a.empresa_transporte_id = transpx.empresa_id
            LEFT OUTER JOIN ges.empresa_config AS truckcomp2x (NOLOCK) ON transpx.empresa_id = truckcomp2x.empresa_id AND truckcomp2x.tipo_configuracion_id = :is_catalogo_SCAC
            WHERE a.sub_unidad_negocio_id = :sub_business_unit_id
            AND a.cat_movimiento_id = :is_gate_out
            AND (seg.fn_datetime_get(a.sub_unidad_negocio_id, A.fecha_ingreso_camion) BETWEEN :pd_truck_in_date_desde AND CONVERT(VARCHAR(10), :pd_truck_in_date_hasta, 111) + ' 23:59:59')
            AND A.eir_id = IIF(:pd_eir_number IS NULL, A.eir_id, :pd_eir_number)
            AND chax.chassis_number LIKE LTRIM(RTRIM(ISNULL(:pd_equipment_number, ''))) + '%'
            AND a.linea_naviera_id = IIF(ISNULL(:pd_shipping_line_id, 0) = 0, a.linea_naviera_id, :pd_shipping_line_id)
            AND chadox.document_chassis_number LIKE LTRIM(RTRIM(ISNULL(:pd_reference_document_number, ''))) + '%'
            AND ISNULL(chaxcusx.empresa_id, 0) = IIF(:pd_shipper_name IS NULL, ISNULL(chaxcusx.empresa_id, 0), :pd_shipper_name)
            AND :pd_consignee_name IS NULL
            AND (:pd_owner_chassis_name IS NULL OR chax.owner_company_id = :pd_owner_chassis_name)
            AND A.activo = 1
            """, nativeQuery = true)
    List<Object[]> fetchChassisGateoutData(
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("is_gate_out") Integer isGateOut,
            @Param("pd_truck_in_date_desde") LocalDate truckInDateDesde,
            @Param("pd_truck_in_date_hasta") LocalDate truckInDateHasta,
            @Param("pd_eir_number") Integer eirNumber,
            @Param("pd_equipment_number") String equipmentNumber,
            @Param("pd_shipping_line_id") Integer shippingLineId,
            @Param("is_chassis") Integer isChassis,
            @Param("is_catalogo_SCAC") Integer isCatalogoSCAC,
            @Param("pd_reference_document_number") String referenceDocumentNumber,
            @Param("pd_shipper_name") Integer shipperName,
            @Param("pd_consignee_name") Integer consigneeName,
            @Param("pd_idioma_id") Integer idiomaId,
            @Param("pd_owner_chassis_name") Integer ownerChassisName
    );


    @Query(value = """
            SELECT
                dox.cat_cargo_document_type_id,
                dox.documento_carga,
                shipperx.documento,
                shipperx.razon_social,
                shipperx.documento,
                consigneex.razon_social,
                dodx.cat_receipt_reason_full_id,
                dodx.producto_id,
                1 AS is_show,
                eir_docx.eir_id AS eir_container_id
            FROM
                sde.eir_documento_carga_detalle eir_docx
            INNER JOIN sds.documento_carga_detalle dodx
                ON eir_docx.documento_carga_detalle_id = dodx.documento_carga_detalle_id
            INNER JOIN sds.documento_carga dox
                ON dodx.documento_carga_id = dox.documento_carga_id
            LEFT OUTER JOIN ges.empresa consigneex
                ON dox.empresa_consignatario_id = consigneex.empresa_id
            LEFT OUTER JOIN ges.empresa shipperx
                ON dox.empresa_embarcador_id = shipperx.empresa_id
            WHERE
                eir_docx.eir_id IN :eir_container_ids
                AND dox.documento_carga LIKE LTRIM(RTRIM(ISNULL(:pd_reference_document_number, ''))) + '%'
                AND ISNULL(shipperx.empresa_id, 0) = IIF(:pd_shipper_name IS NULL, ISNULL(shipperx.empresa_id, 0), :pd_shipper_name)
                AND ISNULL(consigneex.empresa_id, 0) = IIF(:pd_consignee_name IS NULL, ISNULL(consigneex.empresa_id, 0), :pd_consignee_name)
                AND eir_docx.activo = 1
            """, nativeQuery = true)
    List<Object[]> getCargoDocumentDataInBatch(
            @Param("eir_container_ids") List<Integer> eirContainerIds,
            @Param("pd_reference_document_number") String referenceDocumentNumber,
            @Param("pd_shipper_name") Integer shipperName,
            @Param("pd_consignee_name") Integer consigneeName
    );



    @Query("SELECT c.description " +
            "FROM Eir e " +
            "JOIN e.catEmptyFull c " +
            "WHERE e.id = :eirId")
    char findTopEmptyFullDescriptionByEirId(@Param("eirId") Integer eirId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.EirDataDto(e.eirChassis.id, e.subBusinessUnit.id) FROM Eir e WHERE e.id = :eirId")
    EirDataDto findEirDataById(@Param("eirId") Integer eirId);


    @Query("""
            SELECT COUNT(1)
            FROM Eir eirx
            WHERE eirx.catEmptyFull.id = :isFull
              AND eirx.catMovement.id = :isGateOut
              AND COALESCE(eirx.documentCargoGof.id, 0) = :documentoCargaId
              AND COALESCE(eirx.controlAssignmentLight, 0) = 0
              AND eirx.active = true
            """)
    Integer countPendingAssignments(@Param("isFull") Integer isFull,
                                    @Param("isGateOut") Integer isGateOut,
                                    @Param("documentoCargaId") Integer documentoCargaId);


    @Query(value = """
                SELECT a.unidad_negocio_id AS businessUnitId,
                a.sub_unidad_negocio_id AS subBusinessUnitId,
                a.programacion_nave_detalle_id AS programacionNaveDetalleId,
                a.contenedor_id AS containerId,
                NULL AS chassisId,
                a.cat_empty_full_id AS catEmptyFullId,
                movx.variable_2 + ' ' + efx.descricion_larga AS tipoMov,
                Tlocal.nombre AS local,
                A.eir_id AS eirContainerId,
                A.fecha_ingreso_camion AS fechaIngresoCamion,
                A.fecha_salida_camion AS fechaSalidaCamion,
                b.numero_contenedor AS equipmentNumber,
                TtnooCnt.descripcion + ' ' + TtipoCnt.descripcion AS equipmentSizeType,
                :is_container AS catEquipmentCategory,
                a.codigo_iso_id AS isocodeId,
                isocodex.codigo_iso AS isocodeNumber,
                linnav_EIR.nombre AS ownerPropietario,
                CASE TtipoCnt.codigo WHEN '1' THEN 'Y' ELSE 'N' END AS reefer,
                a.vehiculo_id AS vehiculeId,
                vehx.placa AS plateTruckNumber,
                a.empresa_transporte_id AS transportCompanyId,
                transpx.documento AS transportCompanyCode,
                transpx.razon_social AS transportCompanyName,
                truckcomp2x.valor AS transportCompanyScac,
                a.persona_conductor_id AS driverId,
                chofx.documento_identidad AS driverDoc,
                ISNULL(chofx.nombres,'') +' '+ISNULL(chofx.apellido_parterno,'')+' '+ISNULL(chofx.apellido_materno,'') AS driverName,
                a.usuario_registro_id AS userRegisterId,
                ISNULL(usu_reg.nombres,'') +' '+ISNULL(usu_reg.apellido_paterno,'')+' '+ISNULL(usu_reg.apellido_materno,'') AS userRegisterName,
                a.fecha_registro AS fechaRegistro,
                ISNULL(a.precinto_1, '') + IIF(ISNULL(a.precinto_2, '') <> '', ', ', '') + ISNULL(a.precinto_2, '') +
                IIF(ISNULL(a.precinto_3, '') <> '', ', ', '') + ISNULL(a.precinto_3, '') +
                IIF(ISNULL(a.precinto_4, '') <> '', ', ', '') + ISNULL(a.precinto_4, '') AS seals,
                a.observacion AS observacion,
                NULL AS catCargoDocumentTypeId,
                NULL AS cargoDocumentNumber,
                NULL AS shipperNro,
                NULL AS shipperName,
                NULL AS consigneeNro,
                NULL AS consigneeName,
                NULL AS operationTypeId,
                NULL AS operationTypeName,
                CAST((0) AS BIT) AS isShow,
                a.usuario_salida_camion AS usuarioSalidaCamion,
                NULL AS productoId,
                eir_out.fecha_salida_camion AS gateOutDate,
                a.eir_chassis_id AS eirChassisId,
                NULL AS eirChassisNumber,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'S', 'INS') AS catStructureConditionId,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'M', 'INS') AS catMachineryConditionId,
                a.chassis_number AS refChassisNumber,
                depot_operax.variable_1 AS operationGroupType,
                a.cat_clase_cnt_id AS gradeId,
                CAST(IIF(A.eir_chassis_id IS NULL, 0, A.flag_chassis_stayed) AS BIT) AS flagChassisPickup,
                a.numero_twr AS numeroTwr
            FROM sde.eir A (NOLOCK)
                INNER JOIN sds.contenedor B (NOLOCK) ON A.contenedor_id = B.contenedor_id
                INNER JOIN sds.linea_naviera AS linnav_EIR (NOLOCK) ON a.linea_naviera_id = linnav_EIR.linea_naviera_id
                INNER JOIN ges.catalogo AS TtipoCnt (NOLOCK) ON a.cat_tipo_contenedor_id = TtipoCnt.catalogo_id
                INNER JOIN ges.catalogo AS TtnooCnt (NOLOCK) ON a.cat_tamano_cnt_id = TtnooCnt.catalogo_id
                INNER JOIN seg.unidad_negocio AS Tlocal (NOLOCK) ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
                INNER JOIN ges.catalogo AS movx (NOLOCK) ON a.cat_movimiento_id = movx.catalogo_id
                INNER JOIN ges.catalogo AS efx (NOLOCK) ON a.cat_empty_full_id = efx.catalogo_id
                INNER JOIN sds.programacion_nave_detalle AS prnade (NOLOCK) ON a.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id
                INNER JOIN sds.programacion_nave AS prna (NOLOCK) ON prnade.programacion_nave_id = prna.programacion_nave_id
                INNER JOIN ges.catalogo AS depot_operax (NOLOCK) ON prnade.cat_operacion_id = depot_operax.catalogo_id
                LEFT JOIN sde.stock_vacio AS SV (NOLOCK) ON a.eir_id = SV.eir_ingreso_id AND SV.activo = 1
                LEFT JOIN sde.eir AS eir_out (NOLOCK) ON SV.eir_salida_id = eir_out.eir_id AND eir_out.activo = 1
                LEFT JOIN sdf.stock_full AS c (NOLOCK) ON a.eir_id = c.eir_gatein_id AND c.active = 1
                LEFT JOIN sde.eir AS eir_out_full (NOLOCK) ON c.eir_gateout_id = eir_out_full.eir_id
                LEFT JOIN sdh.eir_chassis AS eirchax (NOLOCK) ON a.eir_chassis_id = eirchax.eir_chassis_id
                LEFT JOIN sdh.chassis AS chax (NOLOCK) ON eirchax.chassis_id = chax.chassis_id
                LEFT JOIN sds.vehiculo AS vehx (NOLOCK) ON a.vehiculo_id = vehx.vehiculo_id
                LEFT JOIN ges.persona AS chofx (NOLOCK) ON a.persona_conductor_id = chofx.persona_id
                LEFT JOIN seg.usuario AS usu_reg (NOLOCK) ON a.usuario_registro_id = usu_reg.usuario_id
                LEFT JOIN sds.codigo_iso AS isocodex (NOLOCK) ON a.codigo_iso_id = isocodex.codigo_iso_id
                LEFT JOIN ges.empresa AS transpx (NOLOCK) ON a.empresa_transporte_id = transpx.empresa_id
                LEFT JOIN [ges].[empresa_config] AS truckcomp2x (NOLOCK) ON transpx.empresa_id = truckcomp2x.empresa_id AND truckcomp2x.tipo_configuracion_id = :is_catalogo_SCAC
            WHERE
                A.sub_unidad_negocio_id = :sub_business_unit_id
                AND a.cat_movimiento_id = :is_gate_in
                AND (seg.fn_datetime_get(a.sub_unidad_negocio_id, A.fecha_ingreso_camion) BETWEEN :pd_truck_in_date_desde AND CONVERT(VARCHAR(10), :pd_truck_in_date_hasta, 111)+' 23:59:59')
                AND a.cat_empty_full_id = IIF(:pd_cat_empty_full_id IS NULL, a.cat_empty_full_id, :pd_cat_empty_full_id)
                AND A.eir_id = IIF(:pd_eir_number IS NULL, A.eir_id, :pd_eir_number)
                AND A.contenedor_id <> :equipment_not_applicable_id
                AND b.numero_contenedor LIKE LTRIM(RTRIM(ISNULL(:pd_equipment_number,'')))+'%'
                AND a.linea_naviera_id = IIF(:pd_shipping_line_id IS NULL, a.linea_naviera_id, :pd_shipping_line_id)
                AND A.activo = 1;
            """, nativeQuery = true)
    List<Report21GeneralGateinDataProjection> fetchGateInData(
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("is_gate_in") Integer isGateOut,
            @Param("pd_cat_empty_full_id") Integer pdCatEmptyFullId,
            @Param("pd_truck_in_date_desde") LocalDate truckInDateDesde,
            @Param("pd_truck_in_date_hasta") LocalDate truckInDateHasta,
            @Param("pd_eir_number") Integer eirNumber,
            @Param("equipment_not_applicable_id") Integer equipmentNotApplicableId,
            @Param("pd_equipment_number") String equipmentNumber,
            @Param("pd_shipping_line_id") Integer shippingLineId,
            @Param("is_container") Integer isContainer,
            @Param("is_catalogo_SCAC") Integer isCatalogoSCAC
    );

    @Query(value = """
                SELECT DISTINCT
                a.unidad_negocio_id AS businessUnitId,
                a.sub_unidad_negocio_id AS subBusinessUnitId,
                NULL AS programacionNaveDetalleId,
                NULL AS containerId,
                chax.chassis_id AS chassisId,
                a.cat_empty_full_id AS catEmptyFullId,
                movx.variable_2 AS tipoMov,
                Tlocal.nombre AS local,
                A.eir_id AS eirContainerId,
                A.fecha_ingreso_camion AS fechaIngresoCamion,
                A.fecha_salida_camion AS fechaSalidaCamion,
                chax.chassis_number AS equipmentNumber,
                ges.fn_CatalogTranslationDescLong(chax.cat_chassis_type_id,:pd_idioma_id) AS equipmentSizeType,
                :is_chassis AS catEquipmentCategory,
                NULL AS isocodeId,
                isocodex.codigo_iso AS isocodeNumber,
                ownerx.razon_social AS ownerPropietario,
                '' AS reefer,
                a.vehiculo_id AS vehiculeId,
                vehx.placa AS plateTruckNumber,
                a.empresa_transporte_id AS transportCompanyId,
                transpx.documento AS transportCompanyCode,
                transpx.razon_social AS transportCompanyName,
                truckcomp2x.valor AS transportCompanyScac,
                a.persona_conductor_id AS driverId,
                chofx.documento_identidad AS driverDoc,
                ISNULL(chofx.nombres,'') +' '+ISNULL(chofx.apellido_parterno,'')+' '+ISNULL(chofx.apellido_materno,'') AS driverName,
                a.usuario_registro_id AS userRegisterId,
                ISNULL(usu_reg.nombres,'') +' '+ISNULL(usu_reg.apellido_paterno,'')+' '+ISNULL(usu_reg.apellido_materno,'') AS userRegisterName,
                eirchax.registration_date AS fechaRegistro,
                '' AS seals,
                a.observacion AS observacion,
                chadox.cat_reference_type_id AS catCargoDocumentTypeId,
                chadox.document_chassis_number AS cargoDocumentNumber,
                chaxcusx.documento AS shipperNro,
                chaxcusx.razon_social AS shipperName,
                null AS consigneeNro,
                null AS consigneeName,
                chadox.cat_chassis_operation_type_id AS operationTypeId,
                ges.fn_CatalogTranslationDesc(chadox.cat_chassis_operation_type_id,:pd_idioma_id) AS operationTypeName,
                CAST((0) AS BIT) AS isShow,
                a.usuario_salida_camion AS usuarioSalidaCamion,
                null AS productoId,
                eir_out.fecha_salida_camion AS gateOutDate,
                a.eir_chassis_id AS eirChassisId,
                NULL AS eirChassisNumber,
                sdg.fn_GetEquipmentConditionID(a.eir_id,:is_chassis,'S','DEL') AS catStructureConditionId,
                null AS catMachineryConditionId,
                NULL AS refChassisNumber,
                null AS operationGroupType,
                null AS gradeId,
                NULL AS flagChassisPickup,
                a.numero_twr AS numeroTwr
            FROM sde.eir A (NOLOCK)
                INNER JOIN sdh.eir_chassis AS eirchax (NOLOCK) ON A.eir_chassis_id = eirchax.eir_chassis_id
                INNER JOIN sdh.chassis AS chax (NOLOCK) ON eirchax.chassis_id = chax.chassis_id
                INNER JOIN sdh.document_chassis_detail AS chadodx (NOLOCK) ON eirchax.document_chassis_detail_id = chadodx.document_chassis_detail_id
                INNER JOIN sdh.document_chassis AS chadox (NOLOCK) ON chadodx.document_chassis_id = chadox.document_chassis_id
                INNER JOIN seg.unidad_negocio AS Tlocal (NOLOCK) ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
                INNER JOIN ges.catalogo AS movx (NOLOCK) ON a.cat_movimiento_id = movx.catalogo_id
                LEFT OUTER JOIN ges.empresa AS ownerx (NOLOCK) ON chax.owner_company_id = ownerx.empresa_id
                LEFT OUTER JOIN ges.empresa AS chaxcusx (NOLOCK) ON chadox.customer_compay_id = chaxcusx.empresa_id
                LEFT JOIN sdh.stock_chassis AS sc (NOLOCK) ON a.eir_chassis_id = sc.eir_chassis_gatein_id and sc.active = 1
                LEFT JOIN sde.eir AS eir_out (NOLOCK) ON sc.eir_chassis_gateout_id = eir_out.eir_chassis_id and eir_out.activo = 1
                LEFT JOIN sds.vehiculo AS vehx (NOLOCK) ON a.vehiculo_id = vehx.vehiculo_id
                LEFT JOIN ges.persona AS chofx (NOLOCK) ON a.persona_conductor_id = chofx.persona_id
                LEFT JOIN seg.usuario AS usu_reg (NOLOCK) ON a.usuario_registro_id = usu_reg.usuario_id
                LEFT OUTER JOIN sds.codigo_iso AS isocodex (NOLOCK) ON a.codigo_iso_id = isocodex.codigo_iso_id
                LEFT JOIN ges.empresa AS transpx (NOLOCK) ON a.empresa_transporte_id = transpx.empresa_id
                LEFT OUTER JOIN [ges].[empresa_config] AS truckcomp2x (NOLOCK) ON transpx.empresa_id = truckcomp2x.empresa_id
            WHERE A.sub_unidad_negocio_id = :sub_business_unit_id
                AND a.cat_movimiento_id = :is_gate_in
                AND (seg.fn_datetime_get(a.sub_unidad_negocio_id, A.fecha_ingreso_camion) BETWEEN :pd_truck_in_date_desde AND CONVERT(VARCHAR(10), :pd_truck_in_date_hasta, 111)+' 23:59:59')
                AND A.eir_id = IIF(:pd_eir_number IS NULL,A.eir_id,:pd_eir_number)
                AND chax.chassis_number LIKE LTRIM(RTRIM(ISNULL(:pd_equipment_number,'')))+'%'
                AND a.linea_naviera_id = IIF(ISNULL(:pd_shipping_line_id,0)=0,a.linea_naviera_id,:pd_shipping_line_id)
                AND chadox.document_chassis_number LIKE LTRIM(RTRIM(ISNULL(:pd_reference_document_number,'')))+'%'
                AND ISNULL(chaxcusx.empresa_id,0) = IIF(:pd_shipper_name IS NULL, ISNULL(chaxcusx.empresa_id,0),:pd_shipper_name)
                AND :pd_consignee_name IS NULL
                AND (:pd_owner_chassis_name IS NULL OR chax.owner_company_id = :pd_owner_chassis_name)
                AND truckcomp2x.tipo_configuracion_id = :is_catalogo_SCAC
                AND A.activo =1
            """, nativeQuery = true)
    List<Report21GeneralGateinDataProjection> fetchChassisGateInData(
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("is_gate_in") Integer isGateIn,
            @Param("pd_truck_in_date_desde") LocalDate truckInDateDesde,
            @Param("pd_truck_in_date_hasta") LocalDate truckInDateHasta,
            @Param("pd_eir_number") Integer eirNumber,
            @Param("pd_equipment_number") String equipmentNumber,
            @Param("pd_shipping_line_id") Integer shippingLineId,
            @Param("is_chassis") Integer isChassis,
            @Param("is_catalogo_SCAC") Integer isCatalogoSCAC,
            @Param("pd_reference_document_number") String referenceDocumentNumber,
            @Param("pd_shipper_name") Integer shipperName,
            @Param("pd_consignee_name") Integer consigneeName,
            @Param("pd_idioma_id") Integer idiomaId,
            @Param("pd_owner_chassis_name") Integer ownerChassisName
    );

    @Procedure(name = "Eir.equipmentNoMaerskNotification")
    void equipmentNoMaerskNotification(@Param("eir_id") Integer eirId);

    @Procedure(name = "Eir.gateoutGeneralAssignmentRegisterV2")
    HashMap<String, Object> gateoutGeneralAssignmentRegisterV2(@Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId,
                                                               @Param("eir_id") Integer eirId,
                                                               @Param("container_id") Integer containerId,
                                                               @Param("chassis_id") Integer chassisId,
                                                               @Param("documento_carga_detalle_id") Integer cargoDocumentDetailId,
                                                               @Param("planning_detail_id") Integer planningDetailId,
                                                               @Param("photos") String photos,
                                                               @Param("seal_1") String seal1,
                                                               @Param("seal_2") String seal2,
                                                               @Param("seal_3") String seal3,
                                                               @Param("seal_4") String seal4,
                                                               @Param("user_registration_id") Integer userRegistrationId,
                                                               @Param("languaje_id") Integer languageId,
                                                               @Param("comments") String comments,
                                                               @Param("system_rule_id") String systemRuleId,
                                                               @Param("type_process") String typeProcess,
                                                               @Param("operation_code") String operationCode);

    @Query("SELECT eir FROM Eir eir WHERE eir.localSubBusinessUnit.id = :subBusinessUnitLocalId AND eir.truck.id = :vehicleId AND eir.catMovement.id = :catMovementId AND eir.active = :active AND eir.truckDepartureDate IS NULL")
    List<Eir> findByLocalBusinessUnitIdVehicleIdMovementIdAndActiveWithNoTruckDepartureDate(Integer subBusinessUnitLocalId, Integer vehicleId, Integer catMovementId, Boolean active);

    @Query("SELECT e.id FROM Eir e " +
            "JOIN e.bookingGout b " +
            "WHERE b.id IN :bookingIds " +
            "AND e.catMovement.id = :catMovementId " +
            "AND e.truckDepartureDate IS NULL " +
            "AND e.active = true")
    List<Integer> findEirIdsByBookingIdsAndCatMovement(@Param("bookingIds") List<Integer> bookingIds,
                                                       @Param("catMovementId") Integer catMovementId);

    @Modifying
    @Query("UPDATE Eir e SET e.bookingGout.id = :newBookingId, e.traceEir = :traceEir, " +
            "e.modificationUser.id = :userId, e.modificationDate = :currentTimestamp " +
            "WHERE e.id IN :eirIds")
    void updateEirBookingId(@Param("eirIds") List<Integer> eirIds,
                            @Param("newBookingId") Integer newBookingId,
                            @Param("traceEir") String traceEir,
                            @Param("userId") Integer userId,
                            @Param("currentTimestamp") LocalDateTime currentTimestamp);

    @Query("""
            SELECT CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END FROM Eir e
            WHERE e.container = :container
              AND e.vesselProgrammingDetail = :vesselProgrammingDetail
              AND e.active = true
            """)
    boolean existsByContainerAndVesselProgrammingDetailAndActive(
            @Param("container") Container container,
            @Param("vesselProgrammingDetail") VesselProgrammingDetail vesselProgrammingDetail
    );

    @Query("SELECT eir FROM Eir eir WHERE eir.id = :eirId AND eir.active = true")
    Eir findByIdAndActiveTrue(Integer eirId);

    @Query("SELECT e " +
            "FROM Eir e " +
            "JOIN e.container cnt " +
            "WHERE cnt.containerNumber = :containerEir " +
            "AND e.subBusinessUnit.id = :subUnId " +
            "AND e.active = true " +
            "AND e.catEmptyFull.id = 43083 " +
            "AND cnt.id <> :dummyId " +
            "ORDER BY e.truckArrivalDate DESC")
    List<Eir> findEirByContainerNumber(
            @Param("containerEir") String contenedorEir,
            @Param("subUnId") Integer subBusinessUnitId,
            @Param("dummyId") Integer contenedorDummyId);

    @Query("SELECT e FROM Eir e WHERE e.id = :eirId AND e.subBusinessUnit.id = :subUnId AND e.active = true")
    List<Eir> findByEirIdAndSubBusinessUnit(
            @Param("eirId") Integer eirId,
            @Param("subUnId") Integer subUnId);

    @Query(value = "{call sde.listar_fotos_eir(:subUnId, :eirId, :idiomaId)}", nativeQuery = true)
    List<Object[]> callListarFotosEir(@Param("subUnId") Integer subUnId, @Param("eirId") Integer eirId, @Param("idiomaId") Integer idiomaId);

    @Query("""
            SELECT eir FROM Eir eir
            WHERE eir.container.id = :containerId 
            AND eir.subBusinessUnit.id = :subBusinessUnitId
            AND eir.id != :eirId
            AND eir.active = true
            ORDER BY eir.truckArrivalDate DESC
            """)
    List<Eir> findActiveByContainerIdAndSubBusinessUnitIdAndDifferentEirId(Pageable pageable, Integer containerId, Integer subBusinessUnitId, Integer eirId);

    @Query("""
            SELECT eir FROM Eir eir
            WHERE eir.id = :eirId AND eir.transportPlanningDetailFull.active = true
            """)
    Optional<Eir> findByIdWithActiveTransportPlanningDetail(Integer eirId);
    
    @Procedure(name = "Eir.sde.find_container_preallocate")
    List<FindContainerPreallocateDTO> sdeFindContainerPreallocate(
            @Param("sub_unidad_negocio_id") Integer subBusinessUnitLocalId,
            @Param("numero_contenedor") String containerNumber,
            @Param("booking_detalle_id") Integer bookingDetailId,
            @Param("listar_datos_contenedor") Boolean listContainerData
    );

    @Query("SELECT COUNT(1) FROM Eir e "
            + "WHERE e.active = true "
            + "AND e.vesselProgrammingDetail.vesselProgramming.id = :programacionNaveId")
    int countActiveEirByProgramacionNaveId(@Param("programacionNaveId") Integer programacionNaveId);

    int countByVesselProgrammingDetailAndActiveTrue(VesselProgrammingDetail detail);

    @Query("SELECT COUNT(e) FROM Eir e WHERE e.truckArrivalDate BETWEEN :startDate AND :endDate " +
            "AND e.localSubBusinessUnit.id = :businessUnitLocalId " +
            "AND e.inspectorPerson.id = :inspectorPersonId")
    Integer countEirRecords(@Param("startDate") LocalDateTime startDate,
                            @Param("endDate") LocalDateTime endDate,
                            @Param("businessUnitLocalId") Integer businessUnitLocalId,
                            @Param("inspectorPersonId") Integer inspectorPersonId);

    @Modifying
    @Query("UPDATE Eir e SET e.vesselProgrammingDetail.id = :vesselProgrammingDetailId, " +
            "e.shippingLine.id = :shippingLineId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateVesselProgrammingDetailAndShippingLine(@Param("eirId") Integer eirId,
                                                      @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                                      @Param("shippingLineId") Integer shippingLineId,
                                                      @Param("userModificationId") Integer userModificationId,
                                                      @Param("modificationDate") LocalDateTime modificationDate,
                                                      @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.vesselProgrammingDetail.id = :vesselProgrammingDetailId, " +
            "e.transportPlanningDetailFull.id = :transportPlanningDetailFullId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateVesselProgrammingDetailAndTransportPlanningDetail(@Param("eirId") Integer eirId,
                                                                 @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                                                 @Param("transportPlanningDetailFullId") Integer transportPlanningDetailFullId,
                                                                 @Param("userModificationId") Integer userModificationId,
                                                                 @Param("modificationDate") LocalDateTime modificationDate,
                                                                 @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.truckArrivalDate = :truckArrivalDate, " +
            "e.truckDepartureDate = :truckDepartureDate, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateTruckDates(@Param("eirId") Integer eirId,
                          @Param("truckArrivalDate") LocalDateTime truckArrivalDate,
                          @Param("truckDepartureDate") LocalDateTime truckDepartureDate,
                          @Param("userModificationId") Integer userModificationId,
                          @Param("modificationDate") LocalDateTime modificationDate,
                          @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.transportCompany.id = :newTruckCompanyId, " +
            "e.isoCode.id = :newIsoCodeId, " +
            "e.catSizeCnt.id = :newEquipmentSizeId, " +
            "e.catContainerType.id = :newEquipmentTypeId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateTruckCompanyIsoCodeSizeAndType(@Param("eirId") Integer eirId,
                                              @Param("newTruckCompanyId") Integer newTruckCompanyId,
                                              @Param("newIsoCodeId") Integer newIsoCodeId,
                                              @Param("newEquipmentSizeId") Integer newEquipmentSizeId,
                                              @Param("newEquipmentTypeId") Integer newEquipmentTypeId,
                                              @Param("userModificationId") Integer userModificationId,
                                              @Param("modificationDate") LocalDateTime modificationDate,
                                              @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.catTypeReefer.id = :newRefeerTypeId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateReeferType(@Param("eirId") Integer eirId,
                          @Param("newRefeerTypeId") Integer newRefeerTypeId,
                          @Param("userModificationId") Integer userModificationId,
                          @Param("modificationDate") LocalDateTime modificationDate,
                          @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.catEngineBrand.id = :newRefeerEngineBrandId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateReeferEngineBrand(@Param("eirId") Integer eirId,
                                 @Param("newRefeerEngineBrandId") Integer newRefeerEngineBrandId,
                                 @Param("userModificationId") Integer userModificationId,
                                 @Param("modificationDate") LocalDateTime modificationDate,
                                 @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.catTypeReefer = null, " +
            "e.catEngineBrand = null, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateDisableReeferAndEngineBrand(@Param("eirId") Integer eirId,
                                               @Param("userModificationId") Integer userModificationId,
                                               @Param("modificationDate") LocalDateTime modificationDate,
                                               @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.truck.id = :newVehicleId, " +
            "e.transportCompany.id = :newTruckCompanyId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateTruckAndTruckCompany(@Param("eirId") Integer eirId,
                                      @Param("newVehicleId") Integer newVehicleId,
                                      @Param("newTruckCompanyId") Integer newTruckCompanyId,
                                      @Param("userModificationId") Integer userModificationId,
                                      @Param("modificationDate") LocalDateTime modificationDate,
                                      @Param("traceEir") String traceEir);

    @Modifying
    @Query("UPDATE Eir e SET e.driverPerson.id = :newPersonDriverId, " +
            "e.modificationUser.id = :userModificationId, " +
            "e.modificationDate = :modificationDate, " +
            "e.traceEir = :traceEir " +
            "WHERE e.id = :eirId")
    void updateDriver(@Param("eirId") Integer eirId,
                                @Param("newPersonDriverId") Integer newPersonDriverId,
                                @Param("userModificationId") Integer userModificationId,
                                @Param("modificationDate") LocalDateTime modificationDate,
                                @Param("traceEir") String traceEir);

    @Query(value = """
                SELECT
                    a.unidad_negocio_id AS businessUnitId,
                    a.sub_unidad_negocio_id AS subBusinessUnitId,
                    a.programacion_nave_detalle_id AS programacionNaveDetalleId,
                    a.contenedor_id AS containerId,
                    null AS chassisId,
                    a.cat_empty_full_id AS catEmptyFullId,
                    movx.variable_2 AS tipoMov,
                    Tlocal.nombre AS local,
                    A.eir_id AS eirId,
                    A.fecha_ingreso_camion AS fechaIngresoCamion,
                    A.fecha_salida_camion AS fechaSalidaCamion,
                    b.numero_contenedor AS contenedor,
                    TtnooCnt.descripcion + ' ' + TtipoCnt.descripcion AS equipmentSizeType,
                    :is_container AS isContainer,
                    TIsoCode.codigo_iso AS isoCode,
                    linnav_EIR.nombre AS ownerPropietario,
                    vehx.placa AS plateTruckNumber,
                    a.empresa_transporte_id AS transportCompanyId,
                    NULL AS transportCompanyName,
                    chofx.nombres + ' ' + chofx.apellido_parterno + ' ' + chofx.apellido_materno AS driverName,
                    usu_mod.usuario_id AS userModificationId,
                    usu_mod.nombres AS userModificationName,
                    usu_mod.apellido_paterno + ' ' + usu_mod.apellido_materno AS userModificationLastName,
                    a.fecha_modificacion AS fechaModificacion,
                    usu_reg.usuario_id AS userRegistrationId,
                    usu_reg.nombres AS userRegistrationName,
                    usu_reg.apellido_paterno + ' ' + usu_reg.apellido_materno AS userRegistrationLastName,
                    a.fecha_registro AS fechaRegistro,
                    ISNULL(precinto_1, '') +
                    IIF(ISNULL(precinto_2, '') <> '', ', ', '') + ISNULL(precinto_2, '') +
                    IIF(ISNULL(precinto_3, '') <> '', ', ', '') + ISNULL(precinto_3, '') +
                    IIF(ISNULL(precinto_4, '') <> '', ', ', '') + ISNULL(precinto_4, '') AS seals,
                    null AS catCargoDocumentTypeId,
                    null AS cargoDocumentNumber,
                    null AS operationTypeId,
                    null AS operationTypeName,
                    0 AS isShow,
                    A.eir_chassis_id AS eirChassisId,
                    sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'S', 'INS') AS catStructureConditionId,
                    sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'M', 'INS') AS catMachineryConditionId,
                    depot_operax.variable_1 AS operationGroupType,
                    a.cat_clase_cnt_id AS gradeId,
                    sds.fn_CatalogoTraducidoDesLarga(dcd.cat_receipt_reason_full_id, :language_id) AS containerOperationType,
                    nav.nombre AS nombre,
                    prna.viaje AS viaje,
                    sds.fn_CatalogoTraducidoDes(prnade.cat_operacion_id, :language_id) AS depotOperationType,
                    a.estructura_con_dano AS estructuraConDano,
                    a.maquinaria_con_dano AS maquinariaConDano,
                    isnull(b.para_venta, 0) AS paraVenta,
                    iif(a.camion_multiple_carga = 0, 'N', iif(:language_id = 2, 'S', 'Y')) AS equiposDobles,
                    usu_trde.usuario_id AS userDepartureId,
                    usu_trde.nombres AS userDepartureName,
                    usu_trde.apellido_paterno + ' ' + usu_trde.apellido_materno AS userDepartureLastName,
                    sds.fn_CatalogoTraducidoDes(a.cat_origen_creacion_id, :language_id) AS catOrigenCreacionDescripcion,
                    movx.catalogo_id AS catalogoId,
                    sds.fn_CatalogoTraducidoDes(prnade.cat_operacion_id, :language_id) AS catOperacionDescripcion,
                    movx.variable_2 + ' ' + efx.descricion_larga AS tipoGate,
                    CASE WHEN a.cat_empty_full_id = :is_empty THEN
                        (SELECT ESTF.estimado_emr_eir_foto_id, ESTF.estimado_emr_id, ADJ.adjunto_id, ADJ.id, ADJ.url
                         FROM sde.estimado_emr_eir_foto ESTF
                         INNER JOIN ges.adjunto ADJ ON ADJ.adjunto_id = ESTF.adjunto_id
                         WHERE ESTF.eir_id = a.eir_id and ESTF.activo = 1 FOR JSON PATH)
                    ELSE
                        (SELECT EIRP.eir_photo_id, EIRP.eir_id, ADJ.adjunto_id, ADJ.id, ADJ.url
                         FROM sdf.eir_photo EIRP
                         INNER JOIN ges.adjunto ADJ ON ADJ.adjunto_id = EIRP.attached_id
                         WHERE EIRP.eir_id = a.eir_id and EIRP.active = 1 FOR JSON PATH)
                    END AS inspectionPhotos,
                    CASE WHEN a.cat_empty_full_id = :is_empty THEN
                        (SELECT count(IG.inspeccion_gate_id)
                         from sde.inspeccion_gate AS IG (NOLOCK)
                         INNER JOIN sde.eir AS EIR_AUX (NOLOCK) ON IG.eir_id = EIR_AUX.eir_id
                         INNER JOIN sds.contenedor AS B ON IG.contenedor_original_id = B.contenedor_id
                         where IG.eir_id = A.eir_id)
                    ELSE
                        (SELECT count(*)
                         FROM sdf.eir_photo ESTF
                         WHERE ESTF.eir_id = a.eir_id and ESTF.active = 1)
                    END AS inspectionsQuantity,
                    (SELECT EPH.eir_photo_id, EPH.task, ADJ.adjunto_id, ADJ.id, ADJ.url
                     FROM sds.eir_photo EPH
                     INNER JOIN ges.adjunto ADJ ON ADJ.adjunto_id = EPH.attachment_id
                     WHERE EPH.eir_id = A.eir_id AND EPH.active = 1 AND ADJ.estado = 1
                     ORDER BY ADJ.adjunto_id ASC FOR JSON PATH) AS gatePhotos
                FROM sde.eir A (NOLOCK)
                INNER JOIN sds.contenedor B (NOLOCK) ON A.contenedor_id = B.contenedor_id
                INNER JOIN sds.linea_naviera as linnav_EIR (NOLOCK) on a.linea_naviera_id = linnav_EIR.linea_naviera_id
                INNER JOIN ges.catalogo as TtipoCnt (nolock) on a.cat_tipo_contenedor_id = TtipoCnt.catalogo_id
                INNER JOIN ges.catalogo as TtnooCnt (nolock) on a.cat_tamano_cnt_id = TtnooCnt.catalogo_id
                INNER JOIN seg.unidad_negocio as Tlocal (nolock) on a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
                INNER JOIN ges.catalogo as movx (nolock) on a.cat_movimiento_id = movx.catalogo_id
                INNER JOIN ges.catalogo as efx (nolock) on a.cat_empty_full_id = efx.catalogo_id
                INNER JOIN sds.programacion_nave_detalle as prnade (nolock) on a.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id
                INNER JOIN sds.programacion_nave AS prna (nolock) on prnade.programacion_nave_id = prna.programacion_nave_id
                INNER JOIN [sds].[vehiculo] as vehx (nolock) on a.vehiculo_id = vehx.vehiculo_id
                INNER JOIN ges.catalogo as depot_operax (nolock) on prnade.cat_operacion_id = depot_operax.catalogo_id
                INNER JOIN ges.persona as chofx (nolock) on a.persona_conductor_id = chofx.persona_id
                INNER JOIN seg.usuario as usu_reg (nolock) on a.usuario_registro_id = usu_reg.usuario_id
                LEFT JOIN seg.usuario as usu_mod (nolock) on a.usuario_modificacion_id = usu_mod.usuario_id
                INNER JOIN sde.eir_documento_carga_detalle edcd ON a.eir_id = edcd.eir_id
                INNER JOIN sds.documento_carga_detalle dcd ON edcd.documento_carga_detalle_id = dcd.documento_carga_detalle_id
                INNER JOIN sds.nave as nav (NOLOCK) ON prna.nave_id = nav.nave_id
                LEFT JOIN seg.usuario as usu_trde (NOLOCK) ON a.usuario_salida_camion = usu_trde.usuario_id
                LEFT OUTER JOIN sds.codigo_iso as TIsoCode (nolock) on a.codigo_iso_id = TIsoCode.codigo_iso_id
                WHERE A.sub_unidad_negocio_id = :sub_business_unit_id
                    AND (:eir_number IS NULL OR A.eir_id = :eir_number)
                    AND A.contenedor_id <> :equipment_not_applicable_id
                    AND b.numero_contenedor LIKE '%' + LTRIM(RTRIM(ISNULL(:equipment_number, ''))) + '%'
                    AND (:shipping_line_chassis_owner IS NULL OR linnav_EIR.linea_naviera LIKE '%' + :shipping_line_chassis_owner + '%')
                    AND ((:truck_in_date_min IS NULL AND :truck_in_date_max IS NULL)
                    OR seg.fn_datetime_get(:sub_business_unit_id, A.fecha_ingreso_camion) BETWEEN :truck_in_date_min AND CONVERT(VARCHAR(10), :truck_in_date_max, 111) + ' 23:59:59')
                    AND A.activo = 1 AND edcd.activo = 1
                order by b.numero_contenedor
            """, nativeQuery = true)
    List<EquipmentListTempInterface> fetchGateInDetails(
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("eir_number") Integer eirNumber,
            @Param("equipment_not_applicable_id") Integer equipmentNotApplicableId,
            @Param("equipment_number") String equipmentNumber,
            @Param("shipping_line_chassis_owner") String shippingLineChassisOwner,
            @Param("truck_in_date_min") LocalDate truckInDateMin,
            @Param("truck_in_date_max") LocalDate truckInDateMax,
            @Param("language_id") Integer languageId,
            @Param("is_container") Integer isContainer,
            @Param("is_empty") Integer isEmpty
    );

    @Query(value = """
            SELECT 
                dox.cat_cargo_document_type_id,
                dox.documento_carga,
                dodx.cat_receipt_reason_full_id
            FROM 
                sde.eir_documento_carga_detalle eir_docx
            INNER JOIN 
                sds.documento_carga_detalle dodx (nolock) 
                ON eir_docx.documento_carga_detalle_id = dodx.documento_carga_detalle_id
            INNER JOIN 
                sds.documento_carga dox (nolock) 
                ON dodx.documento_carga_id = dox.documento_carga_id
            LEFT OUTER JOIN 
                ges.empresa consigneex (nolock) 
                ON dox.empresa_consignatario_id = consigneex.empresa_id
            WHERE 
                eir_docx.eir_id = :eirId
                AND dox.documento_carga LIKE LTRIM(RTRIM(ISNULL(:reference_document_number, ''))) + '%'
                AND eir_docx.activo = 1
            """, nativeQuery = true)
    List<Object[]> findCargoDocumentByEirIdAndReferenceDocumentNumber(
            @Param("eirId") Integer eirId,
            @Param("reference_document_number") String referenceDocumentNumber
    );

    @Query(value = """
            SELECT 
                t2.cat_empty_full_id AS catEmptyFullId,
                t2.contenedor_id AS contenedorId,
                ISNULL(t2.anotacion_restriccion, '') AS anotacionRestriccion,
                LTRIM(STUFF(
                    (SELECT ', ' + RTRIM(motresx.descripcion)
                     FROM sde.restriccion_contenedor_detalle AS restdet (NOLOCK)
                     INNER JOIN ges.catalogo AS motresx (NOLOCK) 
                         ON restdet.cat_motivo_restriccion_id = motresx.catalogo_id
                     WHERE restdet.restriccion_cnt_id = t2.restriccion_cnt_id
                         AND restdet.activo = 1
                     ORDER BY motresx.descripcion ASC
                     FOR XML PATH('')
                    ), 1, 1, ''
                )) AS motivoRestriccionDescripcion
            FROM sde.restriccion_contenedor AS t2 (NOLOCK)
            WHERE t2.contenedor_id = :containerId
                AND t2.activo = 1
                AND t2.cat_empty_full_id = :catEmptyFullId
                AND t2.sub_unidad_negocio_id = :subBusinessUnitId
                AND t2.restriccion_liberada = 0
            """, nativeQuery = true)
    List<RestrictionTempInterface> fetchRestrictionContainerDetails(
            @Param("containerId") Integer containerId,
            @Param("catEmptyFullId") Long catEmptyFullId,
            @Param("subBusinessUnitId") Long subBusinessUnitId
    );

    @Query(value = """
            SELECT DISTINCT 
                :eirId AS eirId,
                t2.contenedor_id AS contenedorId,
                t2.programacion_nave_detalle_id AS programacionNaveDetalleId,
                LTRIM(STUFF(
                    (SELECT ', ' + 
                        IIF(RTRIM(mimox.codigo_imo) = '9', 
                            '9 (' + pncimox.imo_others + ')', 
                            RTRIM(mimox.codigo_imo)
                        )
                     FROM sds.programacion_nave_contenedor_imo AS pncimox (NOLOCK)
                     INNER JOIN sds.imo AS mimox (NOLOCK) 
                         ON pncimox.imo_id = mimox.imo_id
                     WHERE pncimox.programacion_nave_contenedor_id = t2.programacion_nave_contenedor_id 
                         AND pncimox.activo = 1
                     ORDER BY mimox.codigo_imo ASC
                     FOR XML PATH('')
                    ), 1, 1, ''
                )) AS imoValues
            FROM sds.programacion_nave_contenedor AS t2 (NOLOCK) 
            WHERE t2.contenedor_id = :containerId
                AND t2.activo = 1
                AND t2.programacion_nave_detalle_id = :programacionNaveDetalleId
            """, nativeQuery = true)
    List<ImoInterface> fetchImoValues(
            @Param("eirId") Integer eirId,
            @Param("containerId") Integer containerId,
            @Param("programacionNaveDetalleId") Integer programacionNaveDetalleId
    );


    @Query(value = """
            SELECT DISTINCT
                a.unidad_negocio_id AS unidadNegocioId,
                a.sub_unidad_negocio_id AS subUnidadNegocioId,
                NULL AS programacionNaveDetalleId,
                NULL AS contenedorId,
                chax.chassis_id AS chassisId,
                a.cat_empty_full_id AS catEmptyFullId,
                movx.variable_2 AS tipoMov,
                Tlocal.nombre AS local,
                A.eir_id AS eirId,
                A.fecha_ingreso_camion AS fechaIngresoCamion,
                A.fecha_salida_camion AS fechaSalidaCamion,
                chax.chassis_number AS contenedor,
                ges.fn_CatalogTranslationDescLong(chax.cat_chassis_type_id, :languageId) AS equipmentSizeType,
                :isChassis AS isContainer,
                '' AS isoCode,
                ownerx.razon_social AS ownerPropietario,
                vehx.placa AS plateTruckNumber,
                a.empresa_transporte_id AS transportCompanyId,
                NULL AS transportCompanyName,
                CONCAT(chofx.nombres, ' ', chofx.apellido_parterno, ' ', chofx.apellido_materno) AS driverName,
                usu_mod.usuario_id AS userModificationId,
                usu_mod.nombres AS userModificationName,
                CONCAT(usu_mod.apellido_paterno, ' ', usu_mod.apellido_materno) AS userModificationLastName,
                eirchax.modification_date AS fechaModificacion,
                usu_reg.usuario_id AS userRegistrationId,
                usu_reg.nombres AS userRegistrationName,
                CONCAT(usu_reg.apellido_paterno, ' ', usu_reg.apellido_materno) AS userRegistrationLastName,
                eirchax.registration_date AS fechaRegistro,
                '' AS seals,
                chadox.cat_reference_type_id AS catCargoDocumentTypeId,
                chadox.document_chassis_number AS cargoDocumentNumber,
                chadox.cat_chassis_operation_type_id AS operationTypeId,
                ges.fn_CatalogTranslationDesc(chadox.cat_chassis_operation_type_id, :languageId) AS operationTypeName,
                0 AS isShow,
                A.eir_chassis_id AS eirChassisId,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :isChassis, 'S', 'INS') AS catStructureConditionId,
                NULL AS catMachineryConditionId,
                NULL AS operationGroupType,
                NULL AS gradeId,
                sds.fn_CatalogoTraducidoDesLarga(dcd.cat_receipt_reason_full_id, :languageId) AS containerOperationType,
                nav.nombre AS nombre,
                prna.viaje AS viaje,
                sds.fn_CatalogoTraducidoDes(prnade.cat_operacion_id, :languageId) AS depotOperationType,
                a.estructura_con_dano AS estructuraConDano,
                a.maquinaria_con_dano AS maquinariaConDano,
                '' AS paraVenta,
                CASE WHEN a.camion_multiple_carga = 0 THEN 'N' ELSE IIF(:languageId = 2, 'S', 'Y') END AS equiposDobles,
                usu_trde.usuario_id AS userDepartureId,
                usu_trde.nombres AS userDepartureName,
                CONCAT(usu_trde.apellido_paterno, ' ', usu_trde.apellido_materno) AS userDepartureLastName,
                sds.fn_CatalogoTraducidoDes(a.cat_origen_creacion_id, :languageId) AS catOrigenCreacionDescripcion,
                movx.catalogo_id AS catalogoId,
                sds.fn_CatalogoTraducidoDes(prnade.cat_operacion_id, :languageId) AS catOperacionDescripcion,
                CONCAT(movx.variable_2, ' ', efx.descricion_larga) AS tipoGate,
                (SELECT JSON_QUERY((SELECT ESTF.chassis_estimate_eir_photo_id, ESTF.chassis_estimate_id, ADJ.adjunto_id, ADJ.id, ADJ.url
                                    FROM sdh.chassis_estimate_eir_photo ESTF
                                    INNER JOIN ges.adjunto ADJ ON ADJ.adjunto_id = ESTF.adjunto_id
                                    WHERE ESTF.eir_chassis_id = eirchax.eir_chassis_id AND ESTF.active = 1 FOR JSON PATH))) AS inspectionPhotos,
                (SELECT COUNT(CE.chassis_estimate_id)
                 FROM sdh.chassis_estimate CE
                 INNER JOIN sdh.eir_chassis EIR_AUX ON CE.eir_chassis_id = EIR_AUX.eir_chassis_id
                 INNER JOIN sdh.chassis B ON CE.chassis_id = B.chassis_id
                 WHERE CE.eir_chassis_id = eirchax.eir_chassis_id) AS inspectionsQuantity,
                NULL AS gatePhotos
            FROM sde.eir A
            INNER JOIN sdh.eir_chassis eirchax ON A.eir_chassis_id = eirchax.eir_chassis_id
            INNER JOIN sdh.chassis chax ON eirchax.chassis_id = chax.chassis_id
            INNER JOIN sdh.document_chassis_detail chadodx ON eirchax.document_chassis_detail_id = chadodx.document_chassis_detail_id
            INNER JOIN sdh.document_chassis chadox ON chadodx.document_chassis_id = chadox.document_chassis_id
            INNER JOIN seg.unidad_negocio Tlocal ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
            INNER JOIN ges.catalogo movx ON a.cat_movimiento_id = movx.catalogo_id
            INNER JOIN sds.vehiculo vehx ON a.vehiculo_id = vehx.vehiculo_id
            INNER JOIN ges.persona chofx ON a.persona_conductor_id = chofx.persona_id
            INNER JOIN seg.usuario usu_reg ON a.usuario_registro_id = usu_reg.usuario_id
            LEFT JOIN seg.usuario usu_mod ON a.usuario_modificacion_id = usu_mod.usuario_id
            LEFT JOIN sde.eir_documento_carga_detalle edcd ON a.eir_id = edcd.eir_id
            LEFT JOIN sds.documento_carga_detalle dcd ON edcd.documento_carga_detalle_id = dcd.documento_carga_detalle_id
            LEFT JOIN sds.programacion_nave_detalle prnade ON a.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id
            LEFT JOIN sds.programacion_nave prna ON prnade.programacion_nave_id = prna.programacion_nave_id
            LEFT JOIN sds.nave nav ON prna.nave_id = nav.nave_id
            LEFT JOIN seg.usuario usu_trde ON a.usuario_salida_camion = usu_trde.usuario_id
            LEFT JOIN ges.catalogo efx ON a.cat_empty_full_id = efx.catalogo_id
            LEFT JOIN ges.empresa ownerx ON chax.owner_company_id = ownerx.empresa_id
            LEFT JOIN ges.empresa chaxcusx ON chadox.customer_compay_id = chaxcusx.empresa_id
            WHERE A.sub_unidad_negocio_id = :subBusinessUnitId
              AND (:eirNumber IS NULL OR A.eir_id = :eirNumber)
              AND chax.chassis_number LIKE CONCAT('%', LTRIM(RTRIM(ISNULL(:equipmentNumber, ''))), '%')
              AND (:shippingLineChassisOwner IS NULL OR ownerx.razon_social LIKE CONCAT('%', :shippingLineChassisOwner, '%'))
              AND chadox.document_chassis_number LIKE LTRIM(RTRIM(ISNULL(:referenceDocumentNumber, ''))) + '%'
              AND ((:truckInDateMin IS NULL AND :truckInDateMax IS NULL)
                   OR seg.fn_datetime_get(:subBusinessUnitId, A.fecha_ingreso_camion)
                   BETWEEN :truckInDateMin AND DATEADD(SECOND, 86399, CAST(:truckInDateMax AS DATETIME)))
              AND A.activo = 1
            """, nativeQuery = true)
    List<EquipmentListTempInterface> getEquipmentDetails(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("languageId") Integer languageId,
            @Param("eirNumber") Integer eirNumber,
            @Param("equipmentNumber") String equipmentNumber,
            @Param("shippingLineChassisOwner") String shippingLineChassisOwner,
            @Param("referenceDocumentNumber") String referenceDocumentNumber,
            @Param("truckInDateMin") LocalDate truckInDateMin,
            @Param("truckInDateMax") LocalDate truckInDateMax,
            @Param("isChassis") Integer isChassis
    );

    @Query(value = """
                SELECT transpx.razon_social
                FROM ges.empresa AS transpx
                LEFT OUTER JOIN ges.empresa_config AS truckcomp2x (NOLOCK)
                    ON transpx.empresa_id = truckcomp2x.empresa_id
                    AND truckcomp2x.tipo_configuracion_id = :is_catalogo_SCAC
                WHERE transpx.empresa_id = :transportCompanyId
            """, nativeQuery = true)
    String getTransportCompanyName(
            @Param("transportCompanyId") Long transportCompanyId,
            @Param("is_catalogo_SCAC") Integer isCatalogoSCAC
    );

    @Query(value = """
        SELECT
            ESA.eir_send_appeir_id AS eirSendAppeirId,
            ESA.flag_send AS flagSend,
            ESA.result_message AS resultMessage,
            ESA.status_code AS statusCode
        FROM sde.eir_send_appeir AS ESA
        WHERE ESA.eir_id = :eirId
    """, nativeQuery = true)
    List<EirSendAppeirProjection> findEirSendAppeir(
            @Param("eirId") Integer eirId
    );

    @Query(value = "SELECT seg.fn_datetime_get(:subBusinessUnitId, :fecha_ingreso_camion)",nativeQuery = true)
    String getGateInTruckArrival(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("fecha_ingreso_camion") LocalDateTime fechaIngresoCamion
    );

    @Query("SELECT " +
            "e.catMovement.id AS gateType, " +
            "COALESCE(sds.fn_CatalogoTraducidoDesLarga(e.catOrigin.id, :languageId), '') AS moveType, " +
            "e.localSubBusinessUnit.name AS localName, " +
            "e.id AS eirNumber, " +
            "CASE WHEN se.gateInEir.id IS NOT NULL THEN FORMAT(se.gateInEir.truckArrivalDate, 'yyyy-MM-dd HH:mm:ss') ELSE '' END AS gateInDepotDate, "  +
            "FORMAT(e.truckArrivalDate, 'yyyy-MM-dd HH:mm:ss') AS truckIn, " +
            "CASE WHEN e.assignmentLighDatet IS NOT NULL THEN FORMAT(e.assignmentLighDatet, 'yyyy-MM-dd HH:mm:ss') ELSE '' END AS assignmentDate, " +
            "CASE WHEN e.dateGateOutInspection IS NOT NULL THEN FORMAT(e.dateGateOutInspection, 'yyyy-MM-dd HH:mm:ss') " +
            "WHEN e.dateRevision IS NOT NULL THEN FORMAT(e.dateRevision, 'yyyy-MM-dd HH:mm:ss') ELSE '' END AS inspectionDate, " +
            "CASE WHEN e.truckDepartureDate IS NOT NULL THEN FORMAT(e.truckDepartureDate, 'yyyy-MM-dd HH:mm:ss') ELSE '' END AS truckOut, " +
            "e.container.containerNumber AS container, " +
            "e.catSizeCnt.description AS sizeEir, " +
            "e.catContainerType.description AS typeEir, " +
            "e.catContainerType.longDescription AS containerTypeEir, " +
            "CASE WHEN c.description IS NOT NULL THEN c.description ELSE '' END AS reeferTypeEir, " +
            "CASE WHEN cc.description IS NOT NULL THEN cc.description ELSE '' END  AS gradeEir, " +
            "e.taraCnt AS tareEir, " +
            "e.cargoMaximumCnt AS payloadEir, " +
            "CASE WHEN i.isoCode IS NOT NULL THEN i.isoCode ELSE '' END AS isoCodeEir, " +
            "CASE WHEN e.dateManufacture IS NOT NULL THEN FORMAT(e.dateManufacture, 'yyyy-MM-dd') ELSE '' END AS manufactureDate, " +
            "CASE WHEN e.catSizeCnt.code = '1' THEN 'S' ELSE 'N' END AS reefer, " +
            "COALESCE(e.clientCompany.legalName, '') AS customerName, " +
            "e.vesselProgrammingDetail.vesselProgramming.vessel.name AS vesselName, " +
            "e.vesselProgrammingDetail.vesselProgramming.voyage AS voyage, " +
            "e.vesselProgrammingDetail.catOperation.description AS operationType, " +
            "e.vesselProgrammingDetail.catOperation.longDescription AS operationName, " +
            "CONCAT(COALESCE(e.vesselProgrammingDetail.manifestYear, ''), '-', COALESCE(e.vesselProgrammingDetail.manifestNumber, '')) AS manifestNumber, " +
            " '' AS documentNumberBk, " +
            " '' AS dischargePort, " +
            " '' AS deliveryPort, " +
            " '' AS shippingLineBk, " +
            "e.seal1 AS seal1, " +
            "e.seal2 AS seal2, " +
            "e.seal3 AS seal3, " +
            " '' AS consigneeName, " +
            " '' AS commodity, " +
            " '' AS inspectionControl, " +
            " '' AS documentReference, " +
            "CASE WHEN vpd.beginningOperation IS NOT NULL THEN FORMAT(vpd.beginningOperation, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END AS startOperation, " +
            "CASE WHEN vpd.endingOperation IS NOT NULL THEN FORMAT(vpd.endingOperation, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END AS endOperation, " +
            "CASE WHEN cm.abbreviation IS NOT NULL AND cm.abbreviation <> '' " +
            "THEN cm.abbreviation " +
            "ELSE cm.legalName END AS portTerminal, " +
            "t.plate AS truckPlate, " +
            "tc.legalName AS truckCompany, " +
            "p.identificationDocument AS driver, " +
            "CONCAT(p.names, ' ', p.firstLastName, ' ', COALESCE(p.secondLastName, '')) AS driverName, " +
            "CONCAT(u.names, ' ', u.firstLastName, ' ', COALESCE(u.secondLastName, '')) AS registerUser, " +
            "FORMAT(e.registrationDate, 'YYYY-MM-DD HH24:MI:SS') AS registerDate, " +
            "CASE WHEN tu.id IS NOT NULL THEN CONCAT(tu.names, ' ', tu.firstLastName, ' ', COALESCE(tu.secondLastName, '')) ELSE '' END AS truckDepartureUser, " +
            "ccc.description AS originOfRegister, " +
            "e.externalDocumentNumber AS aps, " +
            "e.externalDocumentNumberRef AS contecon, " +
            " '' AS pregateCsga " +
            "FROM Eir e " +
            "LEFT JOIN StockEmpty se ON se.gateOutEir.id = e.id " +
            "LEFT JOIN Catalog c ON c.id = e.catTypeReefer.id " +
            "LEFT JOIN Catalog cc ON cc.id = e.catClassCnt.id " +
            "LEFT JOIN IsoCode i ON i.id = e.isoCode.id " +
            "LEFT JOIN VesselProgrammingDetail vpd ON vpd.id = e.vesselProgrammingDetail.id " +
            "LEFT JOIN VesselProgramming vp ON vp.id = vpd.vesselProgramming.id " +
            "LEFT JOIN Company cm ON cm.id = vp.opePortCompany.id " +
            "LEFT JOIN Truck t ON t.id = e.truck.id " +
            "LEFT JOIN Company tc ON tc.id = e.transportCompany.id " +
            "LEFT JOIN Person p ON p.id = e.driverPerson.id " +
            "LEFT JOIN User u ON u.id = e.registrationUser.id " +
            "LEFT JOIN User tu ON tu.id = e.truckDepartureUser.id " +
            "LEFT JOIN Catalog ccc ON ccc.id = e.catCreationOrigin.id " +
            "WHERE e.localSubBusinessUnit.id = :subBusinessUnitId " +
            "AND e.catMovement.id = 43081 " +
            "AND e.catEmptyFull.id = 43083 " +
            "AND (e.container.containerNumber NOT IN (:containerDummyId,:equipmentNotApplicableIduipmentId)) " +
            "AND (e.catOrigin.id IS NULL OR e.catOrigin.id <> 43095) " +
            "AND e.active = true " +
            "AND ((e.catCreationOrigin.id = 48214 AND e.controlAssignmentLight = 3) OR (e.catCreationOrigin.id <> 48214)) " +
            "AND (e.truckArrivalDate BETWEEN :startDate AND :endDate) " +
            "ORDER BY e.truckArrivalDate ASC")
    Page<Object[]> findAllGateOutEmptyExport(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("languageId") Integer languageId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("containerDummyId") Integer containerDummyId,
            @Param("equipmentNotApplicableIduipmentId") Long equipmentNotApplicableIduipmentId,
            Pageable pageable
    );

    @Query("""
                SELECT new com.maersk.sd1.sde.dto.EirDataDTO(
                    b.truckArrivalDate AS truckEntryDate,
                    b.id AS eir,
                    cnt.containerNumber AS container,
                    ttc.id AS catSizeContainer,
                    tc.id AS containerType,
                    b.catTypeReefer.id AS reeferTypeCode,
                    reefType.description AS reeferType,
                    b.catEngineBrand.id AS motorBrandCode,
                    engineBrand.description AS motorBrand,
                    classCnt.id AS containerClass,
                    b.taraCnt AS tareWeight,
                    b.cargoMaximumCnt AS maxLoad,
                    isoCnt.id AS isoCodeId,
                    CONCAT(isoCnt.isoCode, ' - ', isoCnt.description) AS isoCode,
                    l.id AS shippingLineCode,
                    CONCAT(l.shippingLineCompany, ' - ', l.name) AS shippingLineDescription,
                    :#{#filter.description} AS movement,
                    cli.legalName AS client,
                    CONCAT(CONCAT(TRIM(navex.name), '/'), TRIM(pronav.voyage)) AS nvr,
                    ea.name AS location,
                    b.dateManufacture AS manufacturingDate,
                    b.catTypeReefer.id AS reeferTypeCode1,
                    reefType.description AS reeferType1,
                    b.catEngineBrand.id AS motorBrandCode1,
                    engineBrand.description AS motorBrand1,
                    CASE WHEN :#{#filter.hazardousCargoFlag} = true THEN 'El contenedor ha transportado CARGA PELIGROSA.' ELSE '' END AS hazardousCargoMessage,
                    :#{#filter.activityZoneId} AS zoneActivityId,
                    COALESCE(:#{#filter.inspectorComment}, '') AS inspectionComments,
                    :#{#filter.isCleanFlag} AS flagSweep,
                    NULL,
                    :#{#filter.complexInspectionFlag} AS flagComplexInspection,
                    :#{#filter.rcdFlag} AS flagRCD,
                    CASE
                        WHEN :#{#filter.sensorFlag} = 1 AND :#{#filter.sensorDamageFlag} = 0 THEN '1'
                        WHEN :#{#filter.sensorFlag} = 1 AND :#{#filter.sensorDamageFlag} = 1 THEN '2'
                        WHEN :#{#filter.sensorFlag} = 0 AND :#{#filter.sensorDamageFlag} = 0 THEN '3'
                    END AS flagSensor,
                    (SELECT e.includesSpareParts FROM EstimateEmr e WHERE e.id = :#{#filter.emrNumber}) AS flagPendingSpareParts,
                    COALESCE(ese.flagStandardInspection, false) AS flagStandardInspection,
                    COALESCE(ese.flagCAInspection, false) AS flagCaInspection,
                    ese.serviceDate AS serviceDate,
                    ese.catStandardInspection.id AS standardInspectionCategoryId,
                    ese.catCAInspection.id AS caInspectionCategoryId,
                    cnt.catEngineBrand.id AS motorBrandCategoryId,
                    ese.catMachineryModel.id AS machineryModelCategoryId,
                    ese.catControllerType.id AS controllerTypeCategoryId,
                    ese.catRefrigerantType.id AS refrigerantTypeCategoryId,
                    ese.engineSerialNumber AS engineSerialNumber,
                    ese.softwareNumber AS softwareNumber,
                    ese.availableCoolant AS availableCoolant
                )
                FROM EirActivityZone a
                JOIN a.eir b
                LEFT JOIN EstimateEmr ese
                    ON b.id = ese.eir.id
                    AND ese.id = :#{#filter.emrNumber}
                    AND ese.active = true
                JOIN b.localSubBusinessUnit ea
                JOIN b.vesselProgrammingDetail prnade
                JOIN prnade.vesselProgramming pronav
                JOIN b.container cnt
                JOIN b.catContainerType tc
                JOIN b.catSizeCnt ttc
                JOIN b.catOrigin proced
                JOIN pronav.vessel navex
                LEFT JOIN b.shippingLine l
                LEFT JOIN b.clientCompany cli
                LEFT JOIN b.catTypeReefer reefType
                LEFT JOIN b.catEngineBrand engineBrand
                LEFT JOIN b.isoCode isoCnt
                LEFT JOIN b.catClassCnt classCnt
                WHERE a.eir.id = :#{#filter.eirId}
                  AND a.id = :#{#filter.activityZoneId}
                  AND b.active = true
                  AND a.catZoneActivity.id = :#{#filter.isActivityPti}
                  AND a.active = true
                  AND a.concluded = false
            """)
    List<EirDataDTO> findEirData(@Param("filter") EirCustomFilterDTO filter);


    @Query(value = """
            SELECT EIR.eir_id AS eirId,
                   CMO.variable_2 + ' ' + CEF.descricion_larga AS tipoGate,
                   sds.fn_CatalogoTraducidoDesLarga(EIR.cat_procedencia_id, :languageId) AS tipoMovimiento,
                   FORMAT(seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_ingreso_camion), :formatDatetime) AS fechaIngresoCamion,
                   seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_ingreso_camion) AS truckInDate,
                   ISNULL(FORMAT(seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_salida_camion), :formatDatetime), '') AS fechaSalidaCamion,
                   seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_salida_camion) AS truckOutDate,
                   UNL.nombre AS eirLocal,
                   NAV.nombre + ' / ' + PRN.viaje + ' / ' + COP.descripcion + ' (' + sds.fn_CatalogoTraducidoDesLarga(PND.cat_operacion_id, :languageId) + ')' AS naveViajeOperacion,
                   CNT.contenedor_id AS contenedorId,
                   CNT.numero_contenedor AS numeroContenedor,
                   CMC.catalogo_id AS cntSizeCatalogId,
                   CMC.descripcion AS tamanoCnt,
                   CTC.catalogo_id AS cntTypeCatalogId,
                   CMC.descripcion + ' ' + CTC.descripcion + ' - ' + CTC.descricion_larga AS tipoContenedor,
                   CIS.codigo_iso_id AS codigoIsoId,
                   CIS.codigo_iso AS codigoIsoDesc,
                   LNA.nombre AS lineaNavieraCnt,
                   CCC.descripcion + ' - ' + sds.fn_CatalogoTraducidoDesLarga(EIR.cat_clase_cnt_id, :languageId) AS claseCnt,
                   EIR.tara_cnt AS taraCnt,
                   EIR.carga_maxima_cnt AS cargaMaximaCnt,
                   FORMAT(EIR.fecha_fabricacion, :formatDate) AS fFabricacion,
                   CTR.catalogo_id AS tipoReeferId,
                   CTR.descripcion AS tipoReefer,
                   CMM.catalogo_id AS marcaMotorId,
                   CMM.descripcion AS marcaMotor,
                   EIR.precinto_1 AS precinto1,
                   EIR.precinto_2 AS precinto2,
                   EIR.precinto_3 AS precinto3,
                   EIR.precinto_4 AS precinto4,
                   ECC.documento + ' - ' + ECC.razon_social AS cliente,
                   VEH.placa AS vehiculo,
                   ECN.empresa_id AS empresaTransporteId,
                   ECN.documento + ' - ' + ECN.razon_social AS empresaTransporteNombre,
                   PER.documento_identidad + ' - ' + PER.nombres + ' ' + PER.apellido_parterno + ' ' + IIF(PER.apellido_materno IS NULL, '', PER.apellido_materno) AS conductor,
                   PER.documento_identidad AS driverDocument,
                   PER.nombres AS driverName,
                   PER.apellido_parterno + ' ' + IIF(PER.apellido_materno IS NULL, '', PER.apellido_materno) AS driverLastname,
                   EIR.estructura_con_dano AS estructuraConDano,
                   EIR.maquinaria_con_dano AS maquinariaConDano,
                   EIR.observacion AS comentarios,
                   '' AS fFinalizeGrua,
                   '' As ubicacionPatio,
                   FORMAT(EIR.fecha_devolucion_h,:formatDate) As fSobrestadia,
                   CASE 
                       WHEN EIR.cat_movimiento_id = 43080 AND EIR.cat_empty_full_id = 43083 THEN FORMAT(seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_revision), :formatDatetime)
                       WHEN EIR.cat_movimiento_id = 43080 AND EIR.cat_empty_full_id = 43083 THEN FORMAT(seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_gout_inspeccion), :formatDatetime)
                       WHEN EIR.cat_movimiento_id = 43080 AND EIR.cat_empty_full_id = :isFull THEN FORMAT(seg.fn_datetime_get(:subBusinessUnitId, EIR.fecha_inspeccion), :formatDatetime)
                   END AS fInspeccion,
                   IIF(PIN.persona_id IS NULL, '', PIN.documento_identidad + ' - ' + PIN.nombres + ' ' + PIN.apellido_parterno + ' ' + PIN.apellido_materno) AS nombreInspector,
                   IIF(PTE.persona_id IS NULL, '', PTE.documento_identidad + ' - ' + PTE.nombres + ' ' + PTE.apellido_parterno + ' ' + PTE.apellido_materno) AS nombreTecnico,
                   IIF(EIR.cat_movimiento_id = 43081 AND EIR.cat_empty_full_id = 43083 AND EIR.contenedor_id <> :containerDummyId, FORMAT(seg.fn_datetime_get(:subBusinessUnitId, ISNULL(EIR.fecha_asignacion_light, EIR.fecha_ingreso_camion)), :formatDatetime), '') AS fAsignacion,
                   CASE 
                       WHEN EIR.cat_origen_creacion_id <> 48214 AND EIR.cat_procedencia_id = 43097 THEN
                           CASE ISNULL(EIR.control_asignacion, 0)
                               WHEN 0 THEN ges.fn_MensajeTraducido('PRC_GATE_OUT_EMPTY', 1, :languageId)
                               WHEN 1 THEN ges.fn_MensajeTraducido('PRC_GATE_OUT_EMPTY', 2, :languageId)
                               WHEN 2 THEN ges.fn_MensajeTraducido('PRC_GATE_OUT_EMPTY', 3, :languageId)
                               WHEN 3 THEN ges.fn_MensajeTraducido('PRC_GATE_OUT_EMPTY', 4, :languageId)
                           END
                       WHEN EIR.cat_origen_creacion_id = 48214 THEN ges.fn_MensajeTraducido('PRC_GATE_OUT_EMPTY', 2, :languageId)
                       ELSE '' 
                   END AS controlInspection,
                   IIF(ISNULL(EIR.numero_documento_externo, '') = '', '', IIF(EIR.unidad_negocio_id = 31, 'Comodato #' + EIR.numero_documento_externo, IIF(EIR.unidad_negocio_id = 2, ges.fn_MensajeTraducido('EIR_ECU_CITA', 1, :languageId) + ' #' + EIR.numero_documento_externo + IIF(ISNULL(EIR.numero_documento_externo_ref, '') = '', '', ' | ' + ges.fn_MensajeTraducido('EIR_ECU_CITA', 2, :languageId) + ' #' + EIR.numero_documento_externo_ref) + IIF(ISNULL(:idPreGateReception, 0) = 0, '', ' | PreGate CGSA' + ' #' + FORMAT(:idPreGateReception, '0')), EIR.numero_documento_externo))) AS documentoReferencia,
                   EIR.cita_id AS cita,
                   ISNULL(CNT.para_venta, 0) AS paraVenta,
                   sds.fn_CatalogoTraducidoDes(EIR.cat_aprobacion_rep_caja, :languageId) AS situacionRepEstructura,
                   EIR.obs_aprob_rep_caja AS obsAprobRepEstructura,
                   sds.fn_CatalogoTraducidoDes(EIR.cat_aprobacion_rep_maquina, :languageId) AS situacionRepMaquinaria,
                   EIR.obs_aprob_rep_maquina AS obsAprobRepMaquinaria,
                   EIR.estructura_danada AS estructuraDanadaHistorico,
                   EIR.maquinaria_danada AS maquinariaDanadaHistorico,
                   '' AS citasOnlineDpwCallao,
                   '' AS citasApmtCallaoExpo,
                   '' AS numeracion,
                   '' AS rectificacion,
                   '' AS eliminacion,
                   eir.weight_goods AS weightGoods,
                   eir.cat_measure_weight_id AS catMeasureWeightId,
                   sds.fn_CatalogoTraducidoDesLarga(eir.cat_measure_weight_id, :languageId) AS tipoUnidadPeso,
                   CAST(eir.weight_goods AS VARCHAR(20)) + ' ' + sds.fn_CatalogoTraducidoDesLarga(eir.cat_measure_weight_id, :languageId) AS peso,
                   sds.fn_CatalogoTraducidoDesLarga(dcd.cat_receipt_reason_full_id, :languageId) AS operation,
                   CAST(EIR.tara_cnt AS VARCHAR(20)) + ' ' + sds.fn_CatalogoTraducidoDesLarga(eir.cat_measure_tare_id, :languageId) AS tara,
                   CAST(EIR.carga_maxima_cnt AS VARCHAR(20)) + ' ' + sds.fn_CatalogoTraducidoDesLarga(eir.cat_measure_payload_id, :languageId) AS cargaMaxima,
                   eir.cat_means_transport_id AS catMeansTransportId,
                   sds.fn_CatalogoTraducidoDes(eir.cat_means_transport_id, 1) AS medioTransporte,
                   eir.transport_planning_detail_full_id AS transportPlanningDetailFullId,
                   eir.flag_chassis_stayed AS flagChassisStayed,
                   eir.flag_chassis_pickup AS flagChassisPickup,
                   IIF((eir.chassis_number) = '' OR eir.chassis_number IS NULL, IIF((eir.eir_chassis_id) IS NULL, '', CONCAT('EIR Chassis: ', EICHA.eir_chassis_id, '-', CHASS.chassis_number)), eir.chassis_number) AS eirChassisReference,
                   eir.cancel_reason AS departureCancelReason,
                   CMO.catalogo_id AS catMovementId,
                   CEF.catalogo_id AS catEmptyFullId,
                   CHASS.chassis_number AS chassisNumber,
                   EICHA.eir_chassis_id AS eirChassisId
            FROM sde.eir AS EIR (NOLOCK)
            LEFT JOIN sde.eir_documento_carga_detalle edcd ON eir.eir_id = edcd.eir_id
            LEFT JOIN sds.documento_carga_detalle dcd ON edcd.documento_carga_detalle_id = dcd.documento_carga_detalle_id
            INNER JOIN sds.programacion_nave_detalle AS PND (NOLOCK) ON EIR.programacion_nave_detalle_id = PND.programacion_nave_detalle_id
            INNER JOIN sds.programacion_nave AS PRN (NOLOCK) ON PND.programacion_nave_id = PRN.programacion_nave_id
            INNER JOIN sds.nave AS NAV (NOLOCK) ON PRN.nave_id = NAV.nave_id
            INNER JOIN ges.catalogo CMO (NOLOCK) ON CMO.catalogo_id = EIR.cat_movimiento_id
            INNER JOIN ges.catalogo CEF (NOLOCK) ON CEF.catalogo_id = EIR.cat_empty_full_id
            INNER JOIN ges.catalogo COP (NOLOCK) ON COP.catalogo_id = PND.cat_operacion_id
            INNER JOIN sds.contenedor CNT (NOLOCK) ON CNT.contenedor_id = EIR.contenedor_id
            INNER JOIN ges.empresa ECN (NOLOCK) ON ECN.empresa_id = EIR.empresa_transporte_id
            INNER JOIN sds.vehiculo VEH (NOLOCK) ON VEH.vehiculo_id = EIR.vehiculo_id
            INNER JOIN ges.persona PER (NOLOCK) ON PER.persona_id = EIR.persona_conductor_id
            INNER JOIN sds.linea_naviera LNA (NOLOCK) ON LNA.linea_naviera_id = EIR.linea_naviera_id
            INNER JOIN sds.codigo_iso CIS (NOLOCK) ON CIS.codigo_iso_id = EIR.codigo_iso_id
            INNER JOIN ges.catalogo CTC (NOLOCK) ON CTC.catalogo_id = EIR.cat_tipo_contenedor_id
            INNER JOIN ges.catalogo CMC (NOLOCK) ON CMC.catalogo_id = EIR.cat_tamano_cnt_id
            INNER JOIN ges.catalogo CCC (NOLOCK) ON CCC.catalogo_id = EIR.cat_clase_cnt_id
            LEFT JOIN sdh.eir_chassis EICHA (NOLOCK) ON EICHA.eir_chassis_id = EIR.eir_chassis_id
            LEFT JOIN sdh.chassis CHASS (NOLOCK) ON CHASS.chassis_id = EICHA.chassis_id
            LEFT OUTER JOIN ges.empresa ECC (NOLOCK) ON ECC.empresa_id = EIR.empresa_cliente_id
            LEFT OUTER JOIN ges.catalogo CTR (NOLOCK) ON EIR.cat_tipo_reefer_id = CTR.catalogo_id
            LEFT OUTER JOIN ges.catalogo CMM (NOLOCK) ON EIR.cat_marca_motor_id = CMM.catalogo_id
            INNER JOIN seg.unidad_negocio AS UNL (NOLOCK) ON EIR.sub_unidad_negocio_local_id = UNL.unidad_negocio_id
            LEFT JOIN ges.persona PIN (NOLOCK) ON EIR.persona_inspector_id = PIN.persona_id
            LEFT JOIN ges.persona PTE (NOLOCK) ON EIR.persona_tecnico_id = PTE.persona_id
            WHERE EIR.eir_id = :eirId
            AND EIR.sub_unidad_negocio_id = :subBusinessUnitId
            AND EIR.activo = 1
            AND edcd.activo = 1
            """, nativeQuery = true)
    List<EirDataProjection> findEirData(@Param("eirId") Integer eirId,
                                                   @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                   @Param("languageId") Integer languageId,
                                                   @Param("formatDatetime") String formatDatetime,
                                                   @Param("formatDate") String formatDate,
                                                   @Param("isFull") Integer isFull,
                                                   @Param("containerDummyId") Integer containerDummyId,
                                                   @Param("idPreGateReception") Integer idPreGateReception);

    @Query(value = """
        SELECT EIR.eir_id,
               EIGADRPH.eir_gate_driver_photo_id,
               ADJ.adjunto_id,
               ADJ.id,
               ADJ.url
        FROM sde.eir EIR
        INNER JOIN sde.eir_gate_driver_photo EIGADRPH (NOLOCK) ON EIR.eir_id = EIGADRPH.eir_id
        INNER JOIN ges.adjunto ADJ (NOLOCK) ON EIGADRPH.attached_id = ADJ.adjunto_id
        WHERE EIR.eir_id = :eirId
          AND EIR.activo = 1
          AND EIGADRPH.active = 1
    """, nativeQuery = true)
    List<EirGateDriverPhotoProjection> findDriverPhotosByEirId(@Param("eirId") Integer eirId);

    @Query("SELECT COUNT(e.id) FROM Eir e "
            + "WHERE e.truckArrivalDate >= :startDay "
            + "AND e.truckArrivalDate <= :endDay "
            + "AND e.localSubBusinessUnit.id = :subUnidadNegocioLocalId "
            + "AND e.technicalPerson.id = :personaId "
            + "AND e.active = true")
    Integer countFinishedEirByUser(@Param("startDay") LocalDateTime startDay,
                                   @Param("endDay") LocalDateTime endDay,
                                   @Param("subUnidadNegocioLocalId") Long subUnidadNegocioLocalId,
                                   @Param("personaId") Integer personaId);

    @Query(value = "SELECT bkdetx.booking_id AS bookingBlId, " +
            "bkdetx.cat_tamano_id AS sizeContainerId, " +
            "iif(isnull(bkdetx.remark_rules_name,'') = 'FLAG_TO_FLEX', :isTypeContainerDry,bkdetx.cat_tipo_contenedor_id) as typeContainerId, " +
            "sum(cantidad_reserva) AS totalQuantity, " +
            "isnull(bkdetx.remark_rules_name,'') AS remarkRule " +
            "from sds.booking_detalle as bkdetx (NOLOCK) " +
            "WHERE bkdetx.activo = 1 AND bkdetx.booking_id IN :emptyDocumentIds", nativeQuery = true)
    List<DocumentationEmptyListTb01> getDocumentationEmptyListTb01(@Param("isTypeContainerDry") Integer isTypeContainerDry,
                                                                   @Param("emptyDocumentIds") List<Integer> emptyDocumentIds);

    @Query("SELECT e FROM Eir e WHERE e.bookingGout.id IN :emptyDocumentIds AND e.catMovement.id = :isGateOut AND e.active = true")
    List<Eir> getEirByBookingGout(@Param("emptyDocumentIds") List<Integer> emptyDocumentIds,
                                  @Param("isGateOut") Integer isGateOut);

    @Query("SELECT e.eirChassis.id FROM Eir e WHERE e.id = :eirId")
    Integer findEirChassisIdByEirId(Integer eirId);

    @Query(value = """
    SELECT 
        EIR.eir_id AS eirId,
        CNT.numero_contenedor AS numeroContenedor,
        sds.fn_CatalogoTraducidoDesLarga(EIR.cat_procedencia_id, :idiomaId) AS procedenciaDestino,
        EIR.fecha_ingreso_camion AS fechaEir,
        EIR.observacion AS observacion,
        movimiento.descripcion AS tipo,
        CLI.razon_social AS cliente,
        movimiento.variable_2 AS tipoEirDescripcion,
        eir.unidad_negocio_id AS unidadNegocioId,
        RTRIM(TtnooCnt.descripcion) + ' ' + RTRIM(TtipoCnt.descripcion) AS typeCnt,
        EIR.fecha_salida_camion AS fechaSalida,
        ET.razon_social AS empresaTransporte,
        EIR.linea_naviera_id AS lineaNavieraId,
        EIR.persona_conductor_id AS personaConductorId,
        EIR.persona_inspector_id AS personaInspectorId,
        EIR.fecha_firma_conductor AS fechaFirmaConductor,
        EIR.url_firma_conductor AS urlFirmaConductor,
        EIR.url_firma_inspector AS urlFirmaInspector,
        EIRCHAS.eir_chassis_id AS eirChassisId,
        EIRCHAS.person_inspector_chassis_id AS personChassisInspectorId,
        EIRCHAS.url_signature_chassis_inspector AS urlSignatureInspectorChassis
    FROM sde.eir AS EIR
    INNER JOIN sds.contenedor AS CNT ON EIR.contenedor_id = CNT.contenedor_id
    INNER JOIN ges.catalogo AS movimiento ON EIR.cat_movimiento_id = movimiento.catalogo_id
    INNER JOIN sds.programacion_nave_detalle AS nave_detalle ON EIR.programacion_nave_detalle_id = nave_detalle.programacion_nave_detalle_id
    INNER JOIN ges.empresa AS ET ON EIR.empresa_transporte_id = ET.empresa_id
    LEFT JOIN ges.empresa AS CLI ON EIR.empresa_cliente_id = CLI.empresa_id
    INNER JOIN ges.catalogo AS TtipoCnt ON eir.cat_tipo_contenedor_id = TtipoCnt.catalogo_id
    INNER JOIN ges.catalogo AS TtnooCnt ON eir.cat_tamano_cnt_id = TtnooCnt.catalogo_id
    LEFT JOIN sdh.eir_chassis AS EIRCHAS ON EIR.eir_chassis_id = EIRCHAS.eir_chassis_id
    WHERE CNT.numero_contenedor = :contenedorEir
      AND EIR.sub_unidad_negocio_id = :subUnId
      AND EIR.contenedor_id <> :contenedorDummyId
      AND eir.cat_empty_full_id = 43083
      AND eir.activo = 1
    ORDER BY EIR.fecha_ingreso_camion DESC
    """, nativeQuery = true)
    List<EirTempProjection> fetchEirTempData(
            @Param("contenedorEir") String contenedorEir,
            @Param("subUnId") Integer subUnId,
            @Param("idiomaId") Integer idiomaId,
            @Param("contenedorDummyId") Integer contenedorDummyId
    );

    @Query(value = """
    SELECT 
        a.eir_id,
        ISNULL(LTRIM(STUFF((
            SELECT ', ' + RTRIM(dc.documento_carga)
            FROM sde.eir_documento_carga_detalle edcd
            JOIN sds.documento_carga_detalle dcd ON edcd.documento_carga_detalle_id = dcd.documento_carga_detalle_id
            JOIN sds.documento_carga dc ON dcd.documento_carga_id = dc.documento_carga_id
            WHERE edcd.eir_id = a.eir_id
            ORDER BY edcd.documento_carga_detalle_id DESC
            FOR XML PATH('')), 1, 1, '')), '') AS documento,
        ln.nombre AS linea_naviera,
        ch.persona_id as conductor_id,
        ch.nombres + ' ' + ch.apellido_parterno + ' ' + ch.apellido_materno AS conductor,
        isp.persona_id as inspector_id,
        isp.nombres + ' ' + isp.apellido_parterno + ' ' + isp.apellido_materno AS inspector,
        inspc.nombres + ' ' + inspc.apellido_parterno + ' ' + inspc.apellido_materno AS inspector_chassis
    FROM sde.eir a
    LEFT JOIN sds.linea_naviera ln ON a.linea_naviera_id = ln.linea_naviera_id
    LEFT JOIN ges.persona ch ON a.persona_conductor_id = ch.persona_id
    LEFT JOIN ges.persona isp ON a.persona_inspector_id = isp.persona_id
    LEFT JOIN ges.persona inspc ON a.person_chassis_inspector_id = inspc.persona_id
    WHERE a.eir_id IN :eirIds
""", nativeQuery = true)
    List<EirExtraInfoProjection> fetchExtraInfoForEirIds(@Param("eirIds") List<Integer> eirIds);



}