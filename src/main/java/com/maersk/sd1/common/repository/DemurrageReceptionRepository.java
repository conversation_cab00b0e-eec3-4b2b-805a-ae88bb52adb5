package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.DemurrageReception;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DemurrageReceptionRepository extends JpaRepository<DemurrageReception, Integer> {

    @Query("SELECT dr FROM DemurrageReception dr WHERE dr.seteoRecepcionSobrestadia.id IN :demurrageReceptionSettingIds AND dr.active = true")
    List<DemurrageReception> findAllBydemurrageReceptionSetting(List<Integer> demurrageReceptionSettingIds);
}