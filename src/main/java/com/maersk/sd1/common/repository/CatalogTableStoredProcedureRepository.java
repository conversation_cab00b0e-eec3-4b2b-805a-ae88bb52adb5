package com.maersk.sd1.common.repository;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;

import java.math.BigDecimal;
import java.util.List;

@Repository
@Transactional(readOnly = true)
public interface CatalogTableStoredProcedureRepository extends JpaRepository<DummyEntityForJpaRequirement, Integer> {

    @Query(value = "EXEC sds.linea_naviera_listar null, null, :p1, null, null, null, null, null, null, null, null", nativeQuery = true)
    List<Object[]> call_shipping_Line_List(@Param("p1") String p1);

    @Query(value = "EXEC sds.deposito_listar null, :businessUnitId, null, null, null, :activeStatusBit, null, null, null, null, :subBusinessUnitId, null, null, null, null, null", nativeQuery = true)
    List<Object[]> call_list_deposits(@Param("businessUnitId") BigDecimal businessUnitId,
                                      @Param("activeStatusBit") Boolean activeStatusBit,
                                      @Param("subBusinessUnitId") BigDecimal subBusinessUnitId);

    @Query(value = "EXEC sds.producto_listar null, null, null, :activeStatusBit, null, null, null, null, null, null, null, null, null, null, null, null, null, null", nativeQuery = true)
    List<Object[]> call_product_list(
            @Param("activeStatusBit") Boolean activeStatusBit);

    @Query(value = "EXEC sds.imo_listar null, null, null, null, :activeStatusBit, null, null, null, null, null, null, :languageId", nativeQuery = true)
    List<Object[]> call_imo_List(@Param("activeStatusBit") Boolean activeStatusBit,
                                 @Param("languageId") Integer languageId);

    @Query(value = "EXEC seg.unidad_negocio_listar null, null, null, null, null", nativeQuery = true)
    List<Object[]> call_bussiness_list();

    @Query(value = "EXEC ges.listar_monedas :activeCatalogStatus", nativeQuery = true)
    List<Object[]> call_list_currencies(
            @Param("activeCatalogStatus") BigDecimal activeCatalogStatus);

    @Query(value = "EXEC sds.codigo_iso_listar null, null, null, null, null, null, :activeStatusBit, null, null, null, null, null, null, null", nativeQuery = true)
    List<Object[]> call_list_iso_codes(@Param("activeStatusBit") Boolean activeStatusBit);
}
@Entity
class DummyEntityForJpaRequirement {
    @Id
    private Integer id;
}
