package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Person;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PersonRepository extends JpaRepository<Person, Integer> {
    @Query(value = """
            SELECT 
            p.id,
            CONCAT(COALESCE(p.names, ''), ' ', COALESCE(p.firstLastName, ''), ' ', COALESCE(p.secondLastName, '')) 
            FROM Person p WHERE p.id IN :ids
            """)
    List<Object[]> findFullNamesByIds(@Param("ids") List<Integer> ids);

    @Query("SELECT COUNT(p) FROM Person p " +
            "WHERE (:licenseIsEmpty = true OR p.driversLicense = :driversLicense) " +
            "AND (:docIsEmpty = true OR (p.identificationDocument = :docNumber AND p.catIdentificationDocumentType.id = :docTypeId))")
    Long countExistingPerson(
            @Param("driversLicense") String driversLicense,
            @Param("docNumber") String docNumber,
            @Param("docTypeId") Integer docTypeId,
            @Param("licenseIsEmpty") boolean licenseIsEmpty,
            @Param("docIsEmpty") boolean docIsEmpty
    );

    @Query("""
    SELECT DISTINCT p
    FROM Person p
    LEFT JOIN CompanyPerson cp ON cp.person.id = p.id
    LEFT JOIN Company c ON c.id = cp.company.id
    LEFT JOIN PersonRole pr ON pr.person.id = p.id
    WHERE p.active = true
          AND (cp IS null OR cp.active = true)
          AND (:empresaId IS null OR c.id = :empresaId)
          AND (:personaRolId IS null OR pr.catPersonRole.id = :personaRolId)
          AND (:perbTipoDocumentoIdentidadId IS null OR p.catIdentificationDocumentType.id = :perbTipoDocumentoIdentidadId)
          AND (:perbDocumentoIdentidad IS null OR p.identificationDocument LIKE CONCAT('%', :perbDocumentoIdentidad, '%'))
          AND (:perbApellidoPaterno IS null OR p.firstLastName LIKE CONCAT('%', :perbApellidoPaterno, '%'))
          AND (:perbApellidoMaterno IS null OR p.secondLastName LIKE CONCAT('%', :perbApellidoMaterno, '%'))
          AND (:perbNombres IS null OR p.names LIKE CONCAT('%', :perbNombres, '%'))
          AND (:perbNombreCompleto IS null OR CONCAT(p.names, ' ', p.firstLastName, ' ', p.secondLastName) LIKE CONCAT('%', :perbNombreCompleto, '%'))
          AND (:perbCorreo IS null OR p.mail LIKE CONCAT('%', :perbCorreo, '%'))
          AND (:perbTelefono IS null OR p.phone LIKE CONCAT('%', :perbTelefono, '%'))
          AND (:fechaNacimiento IS null OR p.birthDate = :fechaNacimiento)
          AND (:cliente IS null OR c.legalName LIKE CONCAT('%', :cliente, '%'))
          AND (:rolPersonaId IS null OR pr.catPersonRole.id = :rolPersonaId)
          AND (:situacionLaboral IS null OR p.employmentStatus = :situacionLaboral)
    ORDER BY p.id DESC
    """)
    Page<Person> findPersonaList(
            @Param("empresaId") Integer empresaId,
            @Param("personaRolId") Integer personaRolId,
            @Param("perbTipoDocumentoIdentidadId") Integer perbTipoDocumentoIdentidadId,
            @Param("perbDocumentoIdentidad") String perbDocumentoIdentidad,
            @Param("perbApellidoPaterno") String perbApellidoPaterno,
            @Param("perbApellidoMaterno") String perbApellidoMaterno,
            @Param("perbNombres") String perbNombres,
            @Param("perbNombreCompleto") String perbNombreCompleto,
            @Param("perbCorreo") String perbCorreo,
            @Param("perbTelefono") String perbTelefono,
            @Param("fechaNacimiento") LocalDateTime fechaNacimiento,
            @Param("cliente") String cliente,
            @Param("rolPersonaId") Integer rolPersonaId,
            @Param("situacionLaboral") Integer situacionLaboral,
            Pageable pageable
    );


}