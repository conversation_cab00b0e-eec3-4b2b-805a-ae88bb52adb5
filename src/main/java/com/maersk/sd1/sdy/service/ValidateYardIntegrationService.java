package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.ValidateYardIntegrationOutput;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.sdy.exception.ValidateYardIntegrationException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ValidateYardIntegrationService {

    private static final Logger logger = LogManager.getLogger(ValidateYardIntegrationService.class);

    private final BusinessUnitRepository businessUnitRepository;
    private final CheckYardIntegrationService checkYardIntegrationService;

    @Autowired
    public ValidateYardIntegrationService(BusinessUnitRepository businessUnitRepository,
                                          CheckYardIntegrationService checkYardIntegrationService) {
        this.businessUnitRepository = businessUnitRepository;
        this.checkYardIntegrationService = checkYardIntegrationService;
    }


    public ValidateYardIntegrationOutput validateYardIntegration(Integer subBusinessUnitLocalId,
                                                                 String subBusinessUnitLocalAlias,
                                                                 String typeProcess) {
        ValidateYardIntegrationOutput output = new ValidateYardIntegrationOutput();
        try {
            logger.info("Starting yard integration validation flow...");
            // If ID is present, fetch alias from DB
            if (subBusinessUnitLocalId != null) {
                subBusinessUnitLocalAlias = businessUnitRepository
                        .findAliasByBusinessUnitIdOrNull(subBusinessUnitLocalId)
                        .orElse(null);
            }

            // Call the check_yard_integration function
            Boolean result = checkYardIntegrationService.checkYardIntegration(subBusinessUnitLocalAlias, typeProcess);
            output.setResult(result);

            logger.info("Yard integration validation flow completed successfully. Result: {}", result);
        } catch (Exception e) {
            logger.error("Error occurred while validating yard integration: {}", e.getMessage(), e);
            throw new ValidateYardIntegrationException("Error in validateYardIntegration", e);
        }
        return output;
    }
}

