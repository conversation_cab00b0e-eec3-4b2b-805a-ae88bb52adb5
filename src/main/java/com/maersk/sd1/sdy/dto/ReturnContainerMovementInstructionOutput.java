package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ReturnContainerMovementInstructionOutput {

    @JsonProperty("result_state")
    private Integer resultState;

    @JsonProperty("result_message")
    private String resultMessage;

    @JsonProperty("result_sdy_location")
    private String resultSdyLocation;
}
