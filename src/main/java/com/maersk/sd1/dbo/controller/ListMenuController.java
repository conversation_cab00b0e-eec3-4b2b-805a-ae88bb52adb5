package com.maersk.sd1.dbo.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.dbo.controller.dto.ListMenuInput;
import com.maersk.sd1.dbo.controller.dto.ListMenuOutput;
import com.maersk.sd1.dbo.service.ListMenuService;
import com.maersk.sd1.seg.dto.MenuLoginInput;
import com.maersk.sd1.seg.dto.MenuLoginOutput;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("Inlandnet/security/LoginServiceImp")
public class ListMenuController {

    private static final Logger logger = LogManager.getLogger(ListMenuController.class);

    private final ListMenuService listMenuService;

    @PostMapping("/dbowevLisMenu")
    public ResponseEntity<ResponseController<List<MenuLoginOutput>>> getMenu(@RequestBody @Valid ListMenuInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                logger.info("Invalid input payload structure.");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }

            ListMenuInput.Input input = request.getPrefix().getInput();
            MenuLoginInput menuLoginInput = new MenuLoginInput();
            menuLoginInput.setOrigin(input.getOrigin());
            menuLoginInput.setUserId(input.getUserId());
            menuLoginInput.setBusinessUnitId(input.getBusinessUnitId());
            menuLoginInput.setMenuId(input.getSystemId());

            List<MenuLoginOutput> output = listMenuService.getMenu(
                    menuLoginInput
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            List<MenuLoginOutput> errorOutput = Collections.singletonList(new MenuLoginOutput());
//            errorOutput.setRespStatus(0);
//            errorOutput.setRespMessage(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}