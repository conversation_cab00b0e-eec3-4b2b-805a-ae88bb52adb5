package com.maersk.sd1.sdf.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdf.dto.IncomeProgrammingEditInput;
import com.maersk.sd1.sdf.dto.IncomeProgrammingEditOutput;
import com.maersk.sd1.sdf.service.IncomeProgrammingEditService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDF/module/sdf/SDFIncomeProgrammingServiceImp")
@RequiredArgsConstructor
public class IncomeProgrammingEditController {

    private static final Logger logger = LogManager.getLogger(IncomeProgrammingEditController.class.getName());

    private final IncomeProgrammingEditService incomeProgrammingEditService;

    @PostMapping(value = "/sdfincomeProgrammingEdit", consumes = "application/json", produces = "application/json")
    public ResponseEntity<ResponseController<List<IncomeProgrammingEditOutput>>> processIncomeProgrammingEdit(@RequestBody IncomeProgrammingEditInput.Root request) {
        try {
            IncomeProgrammingEditOutput incomeProgrammingEditOutput = incomeProgrammingEditService.processIncomeProgrammingEdit(request);
            return ResponseEntity.ok(new ResponseController<>(List.of(incomeProgrammingEditOutput)));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            IncomeProgrammingEditOutput incomeProgrammingEditOutput = IncomeProgrammingEditOutput.builder()
                    .message(e.toString())
                    .code(0)
                    .id(0)
                    .build();
            return ResponseEntity.status(500).body(new ResponseController<>(List.of(incomeProgrammingEditOutput)));
        }
    }
}
