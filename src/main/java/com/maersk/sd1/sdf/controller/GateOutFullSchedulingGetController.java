package com.maersk.sd1.sdf.controller;

import com.maersk.sd1.sdf.service.GateOutFullSchedulingGetService;
import com.maersk.sd1.sdf.dto.GateOutFullSchedulingInput;
import com.maersk.sd1.sdf.dto.GateOutFullSchedulingOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDF/module/sdf/SDFgateOutFullSchedulingServiceImp")
public class GateOutFullSchedulingGetController {

    private static final Logger logger = LogManager.getLogger(GateOutFullSchedulingGetController.class);

    private final GateOutFullSchedulingGetService gateOutFullSchedulingService;

    // Constructor injection
    public GateOutFullSchedulingGetController(GateOutFullSchedulingGetService gateOutFullSchedulingService) {
        this.gateOutFullSchedulingService = gateOutFullSchedulingService;
    }

    @PostMapping("/sdfgateOutFullSchedulingGet")
    public ResponseEntity<ResponseController<List<GateOutFullSchedulingOutput>>> getGateOutFullScheduling(
            @RequestBody @Valid GateOutFullSchedulingInput.Root request) {
        try {
            GateOutFullSchedulingInput.Input input = request.getPrefix().getInput();

            List<GateOutFullSchedulingOutput> resultList = gateOutFullSchedulingService.getGateOutFullScheduling(
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getTransportPlanningId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(resultList));
        } catch (Exception e) {
            logger.error("Error occurred while fetching gate out full scheduling.", e);
            return ResponseEntity.internalServerError()
                    .body(new ResponseController<>(e.getMessage()));
        }
    }
}
