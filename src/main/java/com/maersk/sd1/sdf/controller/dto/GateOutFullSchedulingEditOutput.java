package com.maersk.sd1.sdf.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.serializer.GateOutFullSchedulingEditOutputSerializer;
import lombok.Builder;
import lombok.Data;

@JsonSerialize(using = GateOutFullSchedulingEditOutputSerializer.class)
@Builder
@Data
public class GateOutFullSchedulingEditOutput {

    @JsonProperty("resp_result")
    private Integer respResult;

    @JsonProperty("resp_message")
    private String respMessage;
}
