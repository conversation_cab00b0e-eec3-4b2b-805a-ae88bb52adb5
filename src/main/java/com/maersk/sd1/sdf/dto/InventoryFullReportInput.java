package com.maersk.sd1.sdf.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;

@UtilityClass
public class InventoryFullReportInput {

    @Data
    public static class Input {

        @JsonProperty("pf_usuario_id")
        @NotNull
        private Integer userId;

        @JsonProperty("pf_sub_unidad_negocio_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("pf_idioma_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("pd_equipment_number")
        private String equipmentNumber;

        @JsonProperty("pagina")
        @NotNull
        private Integer page;

        @JsonProperty("cantidad")
        @NotNull
        private Integer size;

        @JsonProperty("date_filter")
        @PastOrPresent
        private LocalDateTime dateFilter;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDF")
        private Prefix prefix;
    }
}
