package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.controller.dto.SignatureGetActiveOutput;
import com.maersk.sd1.common.repository.UserSignatureRepository;
import com.maersk.sd1.common.repository.PersonSignatureRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
public class SignatureGetActiveService {

    private static final Logger logger = LogManager.getLogger(SignatureGetActiveService.class);

    private final UserSignatureRepository userSignatureRepository;

    private final PersonSignatureRepository personSignatureRepository;

    @Transactional(readOnly = true)
    public SignatureGetActiveOutput getActiveSignature(Integer userId, Integer personId) {
        SignatureGetActiveOutput output = new SignatureGetActiveOutput();
        try {
            if (userId == null || personId == null) {
                return output;
            }

            List<String> userSignatureUids = userSignatureRepository.findActiveUserSignatureUids(userId);
            output.setIds(userSignatureUids);

            List<String> personSignatureUids = personSignatureRepository.findActivePersonSignatureUids(personId);
            output.setIds(personSignatureUids);


        } catch (Exception e) {
            logger.error("An error occurred while fetching active signatures.", e);
        }
        return output;
    }
}