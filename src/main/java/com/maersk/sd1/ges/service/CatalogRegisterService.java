package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.controller.dto.CatalogRegisterInput;
import com.maersk.sd1.ges.controller.dto.CatalogRegisterOutput;
import com.maersk.sd1.ges.dto.Atributo;
import com.maersk.sd1.ges.dto.AtributosWrapper;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.StringReader;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class CatalogRegisterService {
    private final LanguageRepository languageRepository;

    private final CatalogRepository catalogRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final CatalogBusinessUnitRepository catalogBusinessUnitRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final UserRepository userRepository;
    private final BusinessUnitRepository businessUnitRepository;

    public CatalogRegisterOutput registerCatalog(CatalogRegisterInput.Input input) throws JAXBException {
        CatalogRegisterOutput output = new CatalogRegisterOutput();
        String respMensaje;
        // Validate alias
        if (catalogRepository.existsByAlias(input.getAlias())) {
            output.setRespEstado(2);
            respMensaje = languageRepository.findMessageByLanguage("CAT_ALIAS_ERROR", input.getLanguageId(), null);
            output.setRespMensaje(respMensaje);
            return output;
        }

        // Parse attributes
        AtributosWrapper attributes = parseAtributos("<Atributos>" + input.getAttributes() + "</Atributos>");
        List<String> attributeAliases = attributes.getAtributos().stream()
                .map(Atributo::getAlias)
                .filter(a -> a != null && !a.isEmpty())
                .collect(Collectors.toList());

        if (!attributeAliases.isEmpty() && catalogRepository.existsByAliasIn(attributeAliases)) {
            output.setRespEstado(2);
            respMensaje = languageRepository.findMessageByLanguage("CAT_ALIAS_ERROR", input.getLanguageId(), null);
            output.setRespMensaje(respMensaje);
            return output;
        }

        // Insert parent catalog
        Catalog parentCatalog = new Catalog();
        parentCatalog.setDescription(input.getCatalog());
        parentCatalog.setLongDescription(input.getLongDescription());
        parentCatalog.setStatus(input.getStatus());
        parentCatalog.setCode(input.getCode());
        parentCatalog.setAlias(input.getAlias());
        parentCatalog.setRegistrationUser(userRepository.findById(input.getUserId()).orElse(null));
        parentCatalog.setRegistrationDate(LocalDateTime.now());
        catalogRepository.save(parentCatalog);

        // Insert attributes
        for (Atributo atributo : attributes.getAtributos()) {
            Catalog childCatalog = new Catalog();
            childCatalog.setParentCatalog(parentCatalog);
            childCatalog.setDescription(atributo.getDescripcion());
            childCatalog.setLongDescription(atributo.getDescricionLarga());
            childCatalog.setStatus(Objects.equals(atributo.getEstado(), "1"));
            childCatalog.setAlias(atributo.getAlias());
            childCatalog.setRegistrationUser(userRepository.findById(input.getUserId()).orElse(null));
            childCatalog.setRegistrationDate(LocalDateTime.now());
            catalogRepository.save(childCatalog);

            // Insert languages
            if (atributo.getListaIdioma() != null && !atributo.getListaIdioma().isEmpty()) {
                List<String> languageJsons = JsonUtil.parseJsonArray(atributo.getListaIdioma());
                for (String languageJson : languageJsons) {
                    LanguageData languageData = JsonUtil.parseLanguageData(languageJson);
                    CatalogLanguage catalogLanguage = new CatalogLanguage();
                    catalogLanguage.setCatalog(childCatalog);
                    catalogLanguage.setLanguage(languageRepository.findById(languageData.getIdiomaId()).orElse(null));
                    catalogLanguage.setDescription(languageData.getDescripcion());
                    catalogLanguage.setLongDescription(languageData.getDescricionLarga());
                    catalogLanguageRepository.save(catalogLanguage);
                }
            }

            // Insert business units
            if (atributo.getUnidades() != null && !atributo.getUnidades().isEmpty()) {
                List<String> businessUnitJsons = JsonUtil.parseJsonArray(atributo.getUnidades());
                for (String businessUnitJson : businessUnitJsons) {
                    BusinessUnitData businessUnitData = JsonUtil.parseBusinessUnitData(businessUnitJson);
                    CatalogUnitBusinessId catalogUnitBusinessId = new CatalogUnitBusinessId();
                    catalogUnitBusinessId.setCatalogId(childCatalog.getId());
                    catalogUnitBusinessId.setUnitBusinessId(businessUnitData.getUnidadNegocioId());
                    CatalogBusinessUnit catalogBusinessUnit = new CatalogBusinessUnit();
                    catalogBusinessUnit.setId(catalogUnitBusinessId);
                    catalogBusinessUnit.setCatalog(childCatalog);
                    catalogBusinessUnit.setBusinessUnit(businessUnitRepository.findById(businessUnitData.getUnidadNegocioId()).orElse(null));
                    catalogBusinessUnitRepository.save(catalogBusinessUnit);
                }
            }
        }

        // Set parent catalog data
        CatalogRegisterOutput.ParentCatalogData parentData = new CatalogRegisterOutput.ParentCatalogData();
        parentData.setCatalogId(parentCatalog.getId());
        parentData.setDescription(parentCatalog.getDescription());
        parentData.setLongDescription(parentCatalog.getLongDescription());
        parentData.setStatus(parentCatalog.getStatus() ? '1' : '0');
        parentData.setAlias(parentCatalog.getAlias());
        output.setParentCatalog(parentData);

        // Fetch and set child catalogs
        List<Catalog> childCatalogs = catalogRepository.findChildrenByParentId(parentData.getCatalogId());
        List<CatalogRegisterOutput.ChildCatalogData> childCatalogDataList = childCatalogs.stream().map(child -> {
            CatalogRegisterOutput.ChildCatalogData childData = new CatalogRegisterOutput.ChildCatalogData();
            childData.setCatalogId(child.getId());
            childData.setParentCatalogId(child.getParentCatalog().getId());
            childData.setDescription(child.getDescription());
            childData.setLongDescription(child.getLongDescription());
            childData.setStatus(child.getStatus() ? '1' : '0');
            childData.setVariable1(child.getVariable1());
            childData.setVariable2(child.getVariable2());
            childData.setVariable3(child.getVariable3());
            childData.setCode(child.getCode());
            childData.setAlias(child.getAlias());
            return childData;
        }).collect(Collectors.toList());

        output.setChildCatalogs(childCatalogDataList);

        // Set response
        output.setRespEstado(1);
        respMensaje = languageRepository.findMessageByLanguage("CAT_SUCCESS_REGISTER", input.getLanguageId(), null);
        output.setRespMensaje(respMensaje);

        return output;
    }

    private static class JsonUtil {
        // Dummy text-based parser stubs for coverage.
        // In real scenario, we'd use an ObjectMapper from Jackson.
        public static List<String> parseJsonArray(String rawJson) {
            // This is just a naive placeholder to split the top-level JSON array.
            // Example: "[ \"elem1\", \"elem2\" ]" -> list of elements.
            // We skip real parsing complexity. For production, use Jackson.
            // We will pretend it's a small array of JSON objects.
            // Removing brackets:
            String trimmed = rawJson.trim();
            if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
                trimmed = trimmed.substring(1, trimmed.length() - 1).trim();
            }
            if (trimmed.isEmpty()) {
                return new ArrayList<>();
            }
            // naive split by '},', disclaiming nested objects.
            // This is definitely not robust, but for demonstration only.
            List<String> items = new ArrayList<>();
            int braceLevel = 0;
            int startIndex = 0;
            for (int i = 0; i < trimmed.length(); i++) {
                char c = trimmed.charAt(i);
                if (c == '{') {
                    braceLevel++;
                } else if (c == '}') {
                    braceLevel--;
                }
                // if we reached a comma with braceLevel == 0, we can split
                if (braceLevel == 0 && c == ',') {
                    items.add(trimmed.substring(startIndex, i).trim());
                    startIndex = i + 1;
                }
            }
            // Add last element
            if (startIndex < trimmed.length()) {
                items.add(trimmed.substring(startIndex).trim());
            }

            return items;
        }

        public static LanguageData parseLanguageData(String jsonItem) {
            // do a naive parse for the fields we want. Real code would use Jackson.
            LanguageData data = new LanguageData();
            // look for idioma_id:
            data.setIdiomaId(valueOfInt(jsonItem, "idioma_id"));
            data.setDescripcion(valueOfString(jsonItem, "descripcion"));
            data.setDescricionLarga(valueOfString(jsonItem, "descricion_larga"));
            return data;
        }

        public static BusinessUnitData parseBusinessUnitData(String jsonItem) {
            BusinessUnitData bu = new BusinessUnitData();
            bu.setUnidadNegocioId(valueOfInt(jsonItem, "unidad_negocio_id"));
            return bu;
        }

        private static Integer valueOfInt(String json, String field) {
            int index = json.indexOf("\"" + field + "\"");
            if (index == -1) {
                return null;
            }
            // skip to colon
            int colon = json.indexOf(":", index);
            if (colon == -1) {
                return null;
            }
            // skip spaces
            int start = colon + 1;
            while (start < json.length() && Character.isWhitespace(json.charAt(start))) {
                start++;
            }
            // read until non-digit
            StringBuilder sb = new StringBuilder();
            boolean negativeSignUsed = false;
            for (int i = start; i < json.length(); i++) {
                char c = json.charAt(i);
                if ((c == '-' && !negativeSignUsed && sb.isEmpty()) || Character.isDigit(c)) {
                    sb.append(c);
                    if (c == '-') negativeSignUsed = true;
                } else {
                    break;
                }
            }
            try {
                return !sb.isEmpty() ? Integer.valueOf(sb.toString()) : null;
            } catch (Exception e) {
                return null;
            }
        }

        private static String valueOfString(String json, String field) {
            // naive pattern: "field":"someValue"
            String search = "\"" + field + "\"";
            int index = json.indexOf(search);
            if (index == -1) {
                return null;
            }
            int colon = json.indexOf(":", index);
            if (colon == -1) {
                return null;
            }
            // find the first '"' after the colon
            int quoteStart = -1;
            for (int i = colon + 1; i < json.length(); i++) {
                if (json.charAt(i) == '\"') {
                    quoteStart = i + 1;
                    break;
                }
            }
            if (quoteStart == -1) {
                return null;
            }
            // find the next '"'
            int quoteEnd = json.indexOf('\"', quoteStart);
            if (quoteEnd == -1) {
                return null;
            }
            return json.substring(quoteStart, quoteEnd);
        }
    }

    public static AtributosWrapper parseAtributos(String xml) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(AtributosWrapper.class);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        StringReader reader = new StringReader(xml);
        return (AtributosWrapper) unmarshaller.unmarshal(reader);
    }

    @Data
    private static class LanguageData {
        private Integer idiomaId;
        private String descripcion;
        private String descricionLarga;
    }

    @Data
    private static class BusinessUnitData {
        private Integer unidadNegocioId;
    }
}