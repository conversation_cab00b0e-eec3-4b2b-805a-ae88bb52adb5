package com.maersk.sd1.ges.service;


import com.maersk.sd1.ges.dto.ReportEditInput;
import com.maersk.sd1.ges.dto.ReportEditOutput;
import com.maersk.sd1.common.repository.ReportRepository;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.Report;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.MenuRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import jakarta.transaction.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.maersk.sd1.ges.dto.Role;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ReportEditService {

    private static final Logger logger = LogManager.getLogger(ReportEditService.class);

    private final ReportRepository reportRepository;
    private final MenuRepository menuRepository;
    private final UserRepository userRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public ReportEditService(ReportRepository reportRepository, MenuRepository menuRepository, UserRepository userRepository, MessageLanguageRepository messageLanguageRepository) {
        this.reportRepository = reportRepository;
        this.menuRepository = menuRepository;
        this.userRepository = userRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public ReportEditOutput updateReport(ReportEditInput.Input input) {
        ReportEditOutput output = new ReportEditOutput();
        try {
            long duplicates = reportRepository.countById1AndIdNot(input.getId(), input.getReporteId());
            if (duplicates > 0) {
                output.setRespEstado(2);
                String aliasError = messageLanguageRepository.fnTranslatedMessage("REP_ALIAS_ERROR", 0, input.getIdiomaId());
                output.setRespMensaje(aliasError);
                return output;
            }

            Optional<Report> optReport = reportRepository.findById(input.getReporteId());
            if (optReport.isEmpty()) {
                output.setRespEstado(0);
                String notFoundMsg = "Report not found";
                output.setRespMensaje(notFoundMsg);
                return output;
            }

            Report report = optReport.get();

            Menu menu = menuRepository.findById(input.getMenuId())
                    .orElseThrow(() -> new RuntimeException("Menu not found"));
            report.setMenu(menu);

            report.setName(input.getNombre());
            report.setDescription(input.getDescripcion());
            report.setNameStore(input.getNombreStore());
            report.setParameters(input.getParametros());
            report.setColumns(input.getColumns());
            report.setId1(input.getId());
            report.setStatus(input.getEstado());

            if (input.getUsuarioId() != null) {
                User modificationUser = userRepository.findById(input.getUsuarioId())
                        .orElseThrow(() -> new RuntimeException("User (modifier) not found"));
                report.setModificationUser(modificationUser);
                report.setModificationDate(LocalDateTime.now());
            }

            reportRepository.save(report);

            reportRepository.deleteByReportId1(input.getReporteId());

            List<Role> rolesId = input.getRolesId();
            JSONArray jsonArray = new JSONArray();
            for (Role role : rolesId) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("rol_id", role.getRolId());
                jsonArray.put(jsonObject);
            }
            String rolesIdJson = jsonArray.toString();
            reportRepository.insertReportRoles(report.getId(), rolesIdJson);

            output.setRespEstado(1);
            String successMsg = messageLanguageRepository.fnTranslatedMessage("REP_SUCCESS_UPDATE", 0, input.getIdiomaId());
            output.setRespMensaje(successMsg);

        } catch (Exception ex) {
            logger.error("Error updating report", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
        }
        return output;
    }
}
