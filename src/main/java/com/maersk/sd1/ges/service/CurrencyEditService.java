package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.CurrencyRepository;
import com.maersk.sd1.ges.dto.CurrencyUpdateInput;
import com.maersk.sd1.ges.dto.CurrencyUpdateOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class CurrencyEditService {

    private static final Logger logger = LogManager.getLogger(CurrencyEditService.class);

    private final CurrencyRepository currencyRepository;

    @Autowired
    public CurrencyEditService(CurrencyRepository currencyRepository) {
        this.currencyRepository = currencyRepository;
    }

    @Transactional
    public CurrencyUpdateOutput updateCurrency(CurrencyUpdateInput.Input input) {
        CurrencyUpdateOutput output = new CurrencyUpdateOutput();
        try {
            currencyRepository.updateCurrency(
                    input.getMonedaId(),
                    input.getNombre(),
                    input.getAbreviatura(),
                    input.getSimbolo(),
                    input.getEstado(),
                    input.getUsuarioModificacionId(),
                    input.getSeparadorMiles(),
                    input.getSeparadorDecimales(),
                    input.getPrecisionVal(),
                    input.getIcu(),
                    LocalDateTime.now()
            );

            output.setRespEstado(1);
            output.setRespMensaje("Registro editado correctamente");
        } catch (Exception e) {
            logger.error("Error while updating Currency record", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}

