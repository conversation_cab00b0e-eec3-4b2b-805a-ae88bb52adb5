package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.EmailTemplateEditOutput;
import com.maersk.sd1.ges.dto.EmailTemplateEditInput;
import com.maersk.sd1.common.repository.EmailTemplateRepository;
import com.maersk.sd1.common.repository.EmailAttributeRepository;
import com.maersk.sd1.common.repository.EmailTemplateRoleRepository;
import com.maersk.sd1.common.repository.RoleRepository;
import com.maersk.sd1.common.model.EmailTemplate;
import com.maersk.sd1.common.model.EmailAttribute;
import com.maersk.sd1.common.model.EmailTemplateRole;
import com.maersk.sd1.common.model.Role;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Transactional
public class EmailTemplateEditService {

    private static final Logger logger = LogManager.getLogger(EmailTemplateEditService.class);

    private final EmailTemplateRepository emailTemplateRepository;
    private final EmailAttributeRepository emailAttributeRepository;
    private final EmailTemplateRoleRepository emailTemplateRoleRepository;
    private final RoleRepository roleRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public EmailTemplateEditOutput editEmailTemplate(EmailTemplateEditInput.Input input) {
        EmailTemplateEditOutput output = new EmailTemplateEditOutput();

        try {
            EmailTemplate template = emailTemplateRepository.findById(input.getEmailPlantillaId())
                    .orElseThrow(() -> new IllegalArgumentException("Email Template not found with id: " + input.getEmailPlantillaId()));

            if (input.getEmailPlantillaPadreId() != null) {
                EmailTemplate parent = emailTemplateRepository.findById(input.getEmailPlantillaPadreId()).orElse(null);
                template.setParentEmailTemplate(parent);
            }

            template.setContent(input.getContent());
            template.setRecipient(input.getRecipient());
            template.setCopy(input.getCopy());
            template.setCopyHidden(input.getCopyHidden());
            template.setTitle(input.getTitle());
            template.setStatus(input.getStatus());

            if (input.getUserModificationId() != null) {
                User modificationUser = new User();
                modificationUser.setId(input.getUserModificationId());
                template.setModificationUser(modificationUser);
            }

            template.setDescription(input.getDescription());

            if (input.getMenuProjectId() != null) {
                Menu menu = new Menu();
                menu.setId(input.getMenuProjectId());
                template.setProjectMenu(menu);
            }

            template.setModificationDate(LocalDateTime.now());
            template.setId1(input.getUniqueId());
            template.setEventAfterSend(input.getEventAfterSend());
            template.setSender(input.getSender());

            emailTemplateRepository.save(template);

            emailAttributeRepository.deleteByEmailTemplate(template);

            if (input.getEmailAtribute() != null) {
                for (EmailTemplateEditInput.EmailAttributeItem attributeItem : input.getEmailAtribute()) {
                    EmailAttribute newAttr = new EmailAttribute();
                    newAttr.setEmailTemplate(template);
                    newAttr.setField(attributeItem.getField());
                    emailAttributeRepository.save(newAttr);
                }
            }

            emailTemplateRoleRepository.deleteByEmailTemplate(template);

            if (input.getEmailTemplateRole() != null) {
                for (EmailTemplateEditInput.EmailTemplateRoleItem roleItem : input.getEmailTemplateRole()) {
                    Role role = roleRepository.findById(roleItem.getId())
                            .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + roleItem.getId()));

                    EmailTemplateRole newRole = new EmailTemplateRole();
                    newRole.setEmailTemplate(template);
                    newRole.setRole(role);
                    emailTemplateRoleRepository.save(newRole);
                }
            }

            output.setRespEstado(1);
            String translatedMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, input.getLanguageId());
            output.setRespMensaje(translatedMessage);

        } catch (Exception ex) {
            logger.error("Error in editEmailTemplate service.", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
        }
        return output;
    }
}
