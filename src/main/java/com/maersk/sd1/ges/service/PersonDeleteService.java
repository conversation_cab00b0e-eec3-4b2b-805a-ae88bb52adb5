package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.dto.PersonDeleteOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class PersonDeleteService {

    private static final Logger logger = LogManager.getLogger(PersonDeleteService.class.getName());

    private PersonRepository personRepository;
    private PersonRoleRepository personRoleRepository;
    private CompanyPersonRepository companyPersonRepository;
    private UserRepository userRepository;

    @Autowired
    public PersonDeleteService(PersonRepository personRepository,
                               PersonRoleRepository personRoleRepository,
                               CompanyPersonRepository companyPersonRepository,
                               UserRepository userRepository) {
        this.personRepository = personRepository;
        this.personRoleRepository = personRoleRepository;
        this.companyPersonRepository = companyPersonRepository;
        this.userRepository = userRepository;
    }

    @Transactional
    public PersonDeleteOutput deletePerson(Integer personId,
                                           Long companyId,
                                           Long businessUnitId,
                                           Long userModificationId) {
        PersonDeleteOutput output = new PersonDeleteOutput();

        try {
            List<PersonRole> personRoles = personRoleRepository.findByIdPersonId(personId);
            if (!personRoles.isEmpty()) {
                User modUser = null;
                if (userModificationId != null) {
                    modUser = userRepository.findById(Math.toIntExact(userModificationId))
                            .orElse(null);
                }
                for (PersonRole role : personRoles) {
                    role.setActive(false);
                    role.setModificationUser(modUser);
                    role.setModificationDate(LocalDateTime.now());
                }
                personRoleRepository.saveAll(personRoles);
            }

            CompanyPersonId cpId = new CompanyPersonId();
            cpId.setCompanyId(Math.toIntExact(companyId));
            cpId.setPersonId(personId);
            CompanyPerson cp = companyPersonRepository.findById(cpId).orElse(null);
            if (cp != null) {
                User modUser = null;
                if (userModificationId != null) {
                    modUser = userRepository.findById(Math.toIntExact(userModificationId))
                            .orElse(null);
                }
                cp.setActive(false);
                cp.setModificationUser(modUser);
                cp.setModificationDate(LocalDateTime.now());
                companyPersonRepository.save(cp);
            }

            Person person = personRepository.findById(personId).orElse(null);
            if (person != null) {
                User modUser = null;
                if (userModificationId != null) {
                    modUser = userRepository.findById(Math.toIntExact(userModificationId))
                            .orElse(null);
                }
                person.setActive(false);
                person.setModificationUser(modUser);
                person.setModificationDate(LocalDateTime.now());
                personRepository.save(person);
            }

            output.setRespEstado(1);
            output.setRespMensaje("Registro eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error while deleting Person.", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}
