package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.controller.dto.SftpConfigListInput;
import com.maersk.sd1.ges.controller.dto.SftpConfigListOutput;
import com.maersk.sd1.common.repository.SftpConfigRepository;
import com.maersk.sd1.common.model.SftpConfig;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class SftpConfigListService {

    private static final Logger logger = LogManager.getLogger(SftpConfigListService.class);

    private final SftpConfigRepository sftpConfigRepository;

    @Transactional(readOnly = true)
    public SftpConfigListOutput listSftpConfigs(SftpConfigListInput.Input input) {
        logger.info("Listing SFTP configs with input: {}", input);

        int page = (input.getPage() == null || input.getPage() < 1) ? 1 : input.getPage();
        int size = (input.getSize() == null || input.getSize() < 1) ? Integer.MAX_VALUE : input.getSize();

        var pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "id"));

        Page<SftpConfig> resultPage = sftpConfigRepository.findSftpConfigs(input, pageable);
        SftpConfigListOutput outputDto = new SftpConfigListOutput();
        outputDto.setTotalRegistros(resultPage.getTotalElements());

        outputDto.setRegistros(resultPage.getContent().stream().map(sftp -> {
            SftpConfigListOutput.SftpConfigData data = new SftpConfigListOutput.SftpConfigData();
            data.setSftpConfigId(sftp.getId());
            data.setIsFtp(sftp.getIsFtp());
            data.setAlias(sftp.getAlias());
            data.setSftpHost(sftp.getSftpHost());
            data.setSftpName(sftp.getSftpName());
            data.setSftpPass(sftp.getSftpPass());
            data.setSftpPort(sftp.getSftpPort());
            data.setSftpPath(sftp.getSftpPath());
            data.setEventAfterUpload(sftp.getEventAfterUpload());
            data.setStatus(sftp.getStatus());
            data.setRegistrationDate(sftp.getRegistrationDate());
            data.setModificationDate(sftp.getModificationDate());

            if (sftp.getRegistrationUser() != null) {
                data.setUserRegistrationId(sftp.getRegistrationUser().getId());
                String firstName = sftp.getRegistrationUser().getNames() != null ? sftp.getRegistrationUser().getNames() : "";
                String lastNamePart1 = sftp.getRegistrationUser().getFirstLastName() != null ? sftp.getRegistrationUser().getFirstLastName() : "";
                String lastNamePart2 = sftp.getRegistrationUser().getSecondLastName() != null ? sftp.getRegistrationUser().getSecondLastName() : "";
                data.setUserRegistrationNames(firstName);
                data.setUserRegistrationLastNames(lastNamePart1 + (lastNamePart2.isEmpty() ? "" : (" " + lastNamePart2)));
            }

            data.setRequiresPrivateKey(sftp.getRequiresPrivateKey());

            return data;
        }).toList());

        return outputDto;
    }
}