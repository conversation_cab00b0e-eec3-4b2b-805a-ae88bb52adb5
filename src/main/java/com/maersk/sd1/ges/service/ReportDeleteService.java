package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.ReportDeleteOutput;
import com.maersk.sd1.common.repository.ReportRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Service
public class ReportDeleteService {

    private static final Logger logger = LogManager.getLogger(ReportDeleteService.class);

    private final ReportRepository reportRepository;

    @Autowired
    public ReportDeleteService(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Transactional
    public ReportDeleteOutput deleteReport(Integer reportId) {
        ReportDeleteOutput output = new ReportDeleteOutput();
        try {
            logger.info("Attempting to delete Report and ReportRol entries for reporte_id: {}", reportId);

            reportRepository.deleteByReportId1(reportId);

            reportRepository.deleteByReportId(reportId);

            output.setRespEstado(1);
            output.setRespMensaje("Reporte eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error deleting report with reporte_id {}: {}", reportId, e.getMessage(), e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}

