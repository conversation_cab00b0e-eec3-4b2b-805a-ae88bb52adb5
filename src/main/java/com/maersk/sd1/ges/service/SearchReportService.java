package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.dto.SearchReportInput;
import com.maersk.sd1.ges.dto.SearchReportOutput;
import com.maersk.sd1.ges.exception.BusquedaReportException; // Import the custom exception
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
//ges.reporte_busqueda_listar
public class SearchReportService {

    private final CompanyRoleRepository companyRoleRepository;
    private final CompanyRepository companyRepository;
    private final CurrencyRepository currencyRepository;
    private final UserRepository userRepository;
    private final CatalogRepository catalogRepository;
    private final ShippingLineRepository shippingLineRepository;

    public List<SearchReportOutput> searchReporte(@NotNull SearchReportInput.Input input) {
        // Validate input not null, else throw an exception
        List<SearchReportOutput> result = new ArrayList<>();
        String tipoBusqueda = input.getTipoBusqueda();
        String buscar = input.getBuscar();
        Integer unidadNegocioId = input.getUnidadNegocioId();

        // Basic logging
        log.info("Executing search with tipoBusqueda={} buscar={} unidadNegocioId={} ", tipoBusqueda, buscar, unidadNegocioId);

        try {
            switch (tipoBusqueda) {
                case "GES.EMPRESA_CLIENTE_PERU": {
                    result = companyRoleRepository.findEmpresaClientePeru(buscar);
                    break;
                }
                case "GES.EMPRESA_CLIENTE", "OPL.EMPRESA_CLIENTE": {
                    result = companyRoleRepository.findEmpresaClienteByUnitId(unidadNegocioId, buscar);
                    break;
                }
                case "GES.EMPRESA_PROVEEDOR", "OPL.EMPRESA_PROVEEDOR": {
                    result = companyRoleRepository.findEmpresaProveedorByUnitId(unidadNegocioId, buscar);
                    break;
                }
                case "TCO.CLIENTE": {
                    result = companyRepository.findTcoClient(buscar);
                    break;
                }
                case "FAU.LISTA_CLIENTE": {
                    result = companyRoleRepository.findFauListaCliente(unidadNegocioId, buscar);
                    break;
                }
                case "GES.MONEDA": {
                    result = currencyRepository.findMoneda(buscar);
                    break;
                }
                case "SEG.USUARIO": {
                    result = userRepository.findUsuario(buscar);
                    break;
                }
                case "OPL.TIPO_PRODUCTO": {
                    result = catalogRepository.findOplTipoProducto(buscar);
                    break;
                }
                case "SDS.SHIPPING_LINE_ALL": {
                    result = shippingLineRepository.findSdsShippingLineAll(buscar);
                    break;
                }
                default:
                    // No matching case, return empty
                    log.warn("No match for tipoBusqueda: {}", tipoBusqueda);
                    break;
            }
        } catch (Exception ex) {
            log.error("Error in searchReporte", ex);
            throw new BusquedaReportException("Error searching report with tipoBusqueda: " + tipoBusqueda, ex);
        }

        return result;
    }
}