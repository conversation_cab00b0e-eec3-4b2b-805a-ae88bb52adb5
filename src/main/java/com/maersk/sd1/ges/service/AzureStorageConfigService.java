package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.AzureStorageConfig;
import com.maersk.sd1.common.repository.AzureStorageConfigRepository;
import com.maersk.sd1.ges.controller.dto.AzureStorageConfigGetOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class AzureStorageConfigService {

    private static final Logger logger = LogManager.getLogger(AzureStorageConfigService.class);

    private final AzureStorageConfigRepository azureStorageConfigRepository;

    public AzureStorageConfigService(AzureStorageConfigRepository azureStorageConfigRepository) {
        this.azureStorageConfigRepository = azureStorageConfigRepository;
    }

    @Transactional(readOnly = true)
    public AzureStorageConfigGetOutput getAzureStorageConfig(Integer azureStorageConfigId) {
        logger.info("Fetching AzureStorageConfig with ID: {}", azureStorageConfigId);
        if (azureStorageConfigId == null) {
            logger.error("The provided azureStorageConfigId is null.");
            throw new IllegalArgumentException("azureStorageConfigId cannot be null");
        }
        
        Optional<AzureStorageConfig> configOptional = azureStorageConfigRepository.findById(azureStorageConfigId);
        if (configOptional.isEmpty()) {
            logger.error("AzureStorageConfig not found for ID: {}", azureStorageConfigId);
            throw new AzureStorageConfigNotFoundException("AzureStorageConfig not found for ID: " + azureStorageConfigId);
        }

        AzureStorageConfig config = configOptional.get();

        AzureStorageConfigGetOutput output = new AzureStorageConfigGetOutput();

        output.setAzureStorageConfigId(config.getId());
        output.setId1(config.getId1());
        output.setAzureContainer(config.getAzureContainer());
        output.setAzurePath(config.getAzurePath());
        output.setAzureStorageName(config.getAzureStorageName());
        output.setAzureStorageKey(config.getAzureStorageKey());
        output.setReference01(config.getReference01());
        output.setStatus(config.getStatus());
        output.setRegistrationDate(config.getRegistrationDate());
        output.setModificationDate(config.getModificationDate());

        return output;
    }

    public static class AzureStorageConfigNotFoundException extends RuntimeException {
        public AzureStorageConfigNotFoundException(String message) {
            super(message);
        }
    }
}