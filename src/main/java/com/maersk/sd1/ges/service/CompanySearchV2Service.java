package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.CompanyRoleRepository;
import com.maersk.sd1.ges.dto.CompanySearchV2Input;
import com.maersk.sd1.ges.dto.CompanySearchV2Output;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class CompanySearchV2Service {

    private static final Logger logger = LogManager.getLogger(CompanySearchV2Service.class);

    private final CompanyRoleRepository companyRoleRepository;

    @Transactional(readOnly = true)
    public List<CompanySearchV2Output> searchCompanies(CompanySearchV2Input.Input input) {
        logger.info("Executing searchCompanies with input: {}", input);

        boolean companyIdsIsNotNull = (input.getCompanyIds() != null);
        boolean companyIdsIsNull = (input.getCompanyIds() == null);
        Boolean flagNull = Objects.isNull(input.getFlagNull()) ? null : Objects.equals(input.getFlagNull(), "1");

        List<String> roles = input.getCompanyRoles() == null ? java.util.Collections.emptyList() : input.getCompanyRoles();
        List<Integer> ids = input.getCompanyIds() == null ? java.util.Collections.emptyList() : input.getCompanyIds();

        // We fetch only top 10 (similar to TOP 10 in the stored procedure)
        var pageable = PageRequest.of(0, 10);

        var page = companyRoleRepository.searchCompanies(
                companyIdsIsNotNull, companyIdsIsNull, ids,
                flagNull, input.getBusinessUnitId(), roles,
                input.getCompanyName(),
                pageable
        );
       return page.getContent();
    }
}