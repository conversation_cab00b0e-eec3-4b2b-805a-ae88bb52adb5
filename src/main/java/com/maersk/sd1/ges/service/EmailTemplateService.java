package com.maersk.sd1.ges.service;


import com.maersk.sd1.common.model.EmailTemplate;
import com.maersk.sd1.common.repository.EmailTemplateRepository;
import com.maersk.sd1.ges.dto.EmailTemplateListInput;
import com.maersk.sd1.ges.dto.EmailTemplateListOutput;
import com.maersk.sd1.ges.dto.EmailTemplateListOutput.EmailTemplateItem;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class EmailTemplateService {

    private static final Logger logger = LogManager.getLogger(EmailTemplateService.class);

    private final EmailTemplateRepository emailTemplateRepository;

    /**
     * Replicates the logic of the stored procedure [ges].[email_plantilla_listar],
     * applying filter conditions and pagination.
     *
     * @param input the input parameters from the request
     * @return a DTO containing total records and the resulting EmailTemplate list
     */
    @Transactional(readOnly = true)
    public EmailTemplateListOutput listEmailTemplates(@Valid EmailTemplateListInput.Input input) {
        EmailTemplateListOutput output = new EmailTemplateListOutput();
        try {
            // Build a sort by ID desc (like ORDER BY 1 DESC in the stored procedure)
            Sort sort = Sort.by(Sort.Direction.DESC, "id");

            // If page or size is null, we will handle them as in stored procedure:
            // page default = 1, size default = total. For JPA, we can do something similar.
            // We'll do a two-step approach if size is not provided.
            int pageNumber = Optional.ofNullable(input.getPage()).orElse(1);
            int pageSize = Optional.ofNullable(input.getSize()).orElse(Integer.MAX_VALUE);

            Pageable pageable = PageRequest.of(pageNumber - 1, pageSize, sort);

            // Build specification with dynamic filters
            Specification<EmailTemplate> specification = buildSpecification(input);

            // Query with pagination
            Page<EmailTemplate> pageResult = emailTemplateRepository.findAll(specification, pageable);

            long totalRecords = pageResult.getTotalElements();

            // Fill the output object
            output.setTotalRegistros(totalRecords);
            output.setRespEstado(1);
            output.setRespMensaje("SUCCESS");

            List<EmailTemplateItem> items = new ArrayList<>();
            for (EmailTemplate e : pageResult.getContent()) {
                EmailTemplateItem item = new EmailTemplateItem();
                item.setEmailPlantillaId(e.getId());
                item.setEmailPlantillaPadreId(e.getParentEmailTemplate() != null ? e.getParentEmailTemplate().getId() : null);
                item.setContent(e.getContent());
                item.setParentContent(
                        e.getParentEmailTemplate() != null ? e.getParentEmailTemplate().getContent() : null
                );
                item.setRecipient(e.getRecipient());
                item.setCopy(e.getCopy());
                item.setCopyHidden(e.getCopyHidden());
                item.setTitle(e.getTitle());
                item.setStatus(e.getStatus());
                item.setRegistrationDate(e.getRegistrationDate());
                item.setModificationDate(e.getModificationDate());
                item.setDescription(e.getDescription());
                item.setSentUser(e.getSentUser());
                item.setIndicatorEnabled(e.getIndicatorEnabled());
                item.setMenuProyectoId(
                        e.getProjectMenu() != null ? e.getProjectMenu().getId() : null
                );
                item.setMenaTitle(
                        (e.getProjectMenu() != null) ? e.getProjectMenu().getTitle() : null
                );
                item.setRegistrationUserId(
                        e.getRegistrationUser() != null ? e.getRegistrationUser().getId() : null
                );
                item.setRegistrationUserNames(
                        e.getRegistrationUser() != null ? e.getRegistrationUser().getNames() : null
                );
                String userLastNames = null;
                if (e.getRegistrationUser() != null) {
                    String firstLastName = e.getRegistrationUser().getFirstLastName() != null ? e.getRegistrationUser().getFirstLastName() : "";
                    String secondLastName = e.getRegistrationUser().getSecondLastName() != null ? e.getRegistrationUser().getSecondLastName() : "";
                    userLastNames = (firstLastName + " " + secondLastName).trim();
                }
                item.setRegistrationUserLastnames(userLastNames);
                item.setId1(e.getId1());
                items.add(item);
            }

            output.setTemplates(items);
        } catch (Exception ex) {
            logger.error("Error listing EmailTemplates", ex);
            output.setRespEstado(0);
            output.setRespMensaje("ERROR: " + ex.getMessage());
            output.setTotalRegistros(0L);
            output.setTemplates(new ArrayList<>());
        }
        return output;
    }

    /**
     * Builds a {@link Specification} that replicates the stored procedure filtering behavior.
     */
    public Specification<EmailTemplate> buildSpecification(EmailTemplateListInput.Input input) {

        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>(); // Change here

            // email_plantilla_id -> e.id = :id
            if (input.getEmailPlantillaId() != null) {
                predicates.add(cb.equal(root.get("id"), input.getEmailPlantillaId().intValue()));
            }

            // email_plantilla_padre_id -> e.parentEmailTemplate.id = :id
            if (input.getEmailPlantillaPadreId() != null) {
                predicates.add(cb.equal(
                        root.join("parentEmailTemplate").get("id"),
                        input.getEmailPlantillaPadreId().intValue()
                ));
            }

            // contenido -> like %:content%
            if (input.getContent() != null && !input.getContent().isEmpty()) {
                predicates.add(cb.like(root.get("content"), "%" + input.getContent() + "%"));
            }

            // destinatario -> like %:recipient%
            if (input.getRecipient() != null && !input.getRecipient().isEmpty()) {
                predicates.add(cb.like(root.get("recipient"), "%" + input.getRecipient() + "%"));
            }

            // copia -> like %:copy%
            if (input.getCopy() != null && !input.getCopy().isEmpty()) {
                predicates.add(cb.like(root.get("copy"), "%" + input.getCopy() + "%"));
            }

            // copia_oculta -> like %:copyHidden%
            if (input.getCopyHidden() != null && !input.getCopyHidden().isEmpty()) {
                predicates.add(cb.like(root.get("copyHidden"), "%" + input.getCopyHidden() + "%"));
            }

            // titulo -> like %:title%
            if (input.getTitle() != null && !input.getTitle().isEmpty()) {
                predicates.add(cb.like(root.get("title"), "%" + input.getTitle() + "%"));
            }

            // estado -> e.status = :status
            if (input.getStatus() != null) {
                predicates.add(cb.equal(root.get("status"), input.getStatus()));
            }

            // fecha_registro range
            // if both min & max are non-null, then e.registrationDate >= min and e.registrationDate < max+1 day
            // if both null, skip
            LocalDate regMin = input.getRegistrationDateMin();
            LocalDate regMax = input.getRegistrationDateMax();
            if (regMin != null && regMax != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("registrationDate"), regMin.atStartOfDay()));
                predicates.add(cb.lessThan(root.get("registrationDate"), regMax.plusDays(1).atStartOfDay()));
            }

            // fecha_modificacion range
            LocalDate modMin = input.getModificationDateMin();
            LocalDate modMax = input.getModificationDateMax();
            if (modMin != null && modMax != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("modificationDate"), modMin.atStartOfDay()));
                predicates.add(cb.lessThan(root.get("modificationDate"), modMax.plusDays(1).atStartOfDay()));
            }

            // descripcion -> like %:description%
            if (input.getDescription() != null && !input.getDescription().isEmpty()) {
                predicates.add(cb.like(root.get("description"), "%" + input.getDescription() + "%"));
            }

            // envio_usuario -> e.sentUser = :sentUser
            if (input.getSentUser() != null) {
                predicates.add(cb.equal(root.get("sentUser"), input.getSentUser()));
            }

            // indicador_habilitado -> e.indicatorEnabled = :indicatorEnabled
            if (input.getIndicatorEnabled() != null) {
                predicates.add(cb.equal(root.get("indicatorEnabled"), input.getIndicatorEnabled()));
            }

            // menu_proyecto_id -> e.projectMenu.id = :menuProyectoId
            if (input.getMenuProyectoId() != null) {
                predicates.add(cb.equal(root.join("projectMenu").get("id"), input.getMenuProyectoId().intValue()));
            }

            // mena_titulo -> e.projectMenu.title like %title%
            if (input.getMenaTitulo() != null && !input.getMenaTitulo().isEmpty()) {
                predicates.add(
                        cb.like(root.join("projectMenu").get("title"), "%" + input.getMenaTitulo() + "%")
                );
            }

            // plantilla_padre -> if it's true => e.parentEmailTemplate is null
            if (input.getPlantillaPadre() != null && input.getPlantillaPadre()) {
                predicates.add(cb.isNull(root.get("parentEmailTemplate")));
            }

            // id -> e.id1 like %:id1%
            if (input.getId1() != null && !input.getId1().isEmpty()) {
                predicates.add(cb.like(root.get("id1"), "%" + input.getId1() + "%"));
            }

            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0])); // Change here
        };
    }
}
