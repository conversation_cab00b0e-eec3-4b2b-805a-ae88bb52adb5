package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.EmailPendingProcessDetail;
import com.maersk.sd1.ges.dto.EmailPendingProcessOutput;
import com.maersk.sd1.common.repository.PendingSendEmailRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class EmailPendingProcessService {

    private static final Logger logger = LogManager.getLogger(EmailPendingProcessService.class);

    private final PendingSendEmailRepository emailPendingProcessRepository;

    public EmailPendingProcessService(PendingSendEmailRepository emailPendingProcessRepository) {
        this.emailPendingProcessRepository = emailPendingProcessRepository;
    }

    @Transactional(readOnly = true)
    public EmailPendingProcessOutput getPendingEmails() {
        logger.info("Fetching pending emails (sentIndicator='0')...");
        EmailPendingProcessOutput output = new EmailPendingProcessOutput();
        try {
            List<EmailPendingProcessDetail> details = emailPendingProcessRepository.findPendingEmails();
            if (details == null || details.isEmpty()) {
                output.setRespEstado(0);
                output.setRespMensaje("No pending emails found.");
                output.setEmails(details);
            } else {
                output.setRespEstado(1);
                output.setRespMensaje("Success");
                output.setEmails(details);
            }
        } catch (Exception e) {
            logger.error("An error occurred while fetching pending emails.", e);
            output.setRespEstado(0);
            output.setRespMensaje("An error occurred: " + e.getMessage());
            output.setEmails(null);
        }
        return output;
    }
}

