package com.maersk.sd1.ges.repository;

import com.maersk.sd1.common.model.AzureStorageConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface AzureeStorageConfigRepository extends JpaRepository<AzureStorageConfig, Integer>, JpaSpecificationExecutor<AzureStorageConfig> {
}

