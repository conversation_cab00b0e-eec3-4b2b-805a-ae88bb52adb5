package com.maersk.sd1.ges.repository;

import com.maersk.sd1.common.model.Currency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.Optional;

public interface CurrencyObtainReposiory extends JpaRepository<Currency, Integer> {

    interface MonedaObtenerProjection {
        Integer getId();
        String getName();
        String getAbbreviation();
        String getSymbol();
        Integer getStatus();
        LocalDateTime getRegistrationDate();
        LocalDateTime getModificationDate();
        String getSeparatorMiles();
        String getSeparatorDecimals();
        String getPrecision();
        String getIcu();
    }

    @Query("SELECT c.id AS id, c.name AS name, c.abbreviation AS abbreviation, c.symbol AS symbol, " +
            "c.status AS status, c.registrationDate AS registrationDate, c.modificationDate AS modificationDate, " +
            "c.separatorMiles AS separatorMiles, c.separatorDecimals AS separatorDecimals, c.precision AS precision, " +
            "c.icu AS icu " +
            "FROM Currency c " +
            "WHERE c.id = :monedaId")
    Optional<MonedaObtenerProjection> findMonedaByMonedaId(@Param("monedaId") Integer monedaId);
}