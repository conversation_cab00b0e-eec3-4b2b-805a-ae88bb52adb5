package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.ReportObtainInput;
import com.maersk.sd1.ges.dto.ReportObtainOutput;
import com.maersk.sd1.ges.service.ReportObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReporteServiceImp")
public class ReportObtainController {

    private static final Logger logger = LogManager.getLogger(ReportObtainController.class);

    private final ReportObtainService reportObtainService;

    public ReportObtainController(ReportObtainService reportObtainService) {
        this.reportObtainService = reportObtainService;
    }

    @PostMapping("/gesreporteObtener")
    public ResponseEntity<ResponseController<ReportObtainOutput>> sdgReportObtain(@Valid @RequestBody ReportObtainInput.Root request) {
        try {
            Integer reportId = request.getPrefix().getInput().getReportId();
            ReportObtainOutput output = reportObtainService.getReport(reportId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ReportObtainOutput output = new ReportObtainOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}