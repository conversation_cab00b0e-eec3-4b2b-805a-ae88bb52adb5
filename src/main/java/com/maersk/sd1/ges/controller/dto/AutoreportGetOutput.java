package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AutoreportGetOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("automatic_report_id")
    private Integer automaticReportId;

    @JsonProperty("sub_business_unit_id")
    private Integer subBusinessUnitId;

    @JsonProperty("report_start_date_converted")
    private String reportStartDateConverted;

    @JsonProperty("report_end_date_converted")
    private String reportEndDateConverted;

    @JsonProperty("recurrency")
    private Integer recurrency;

    @JsonProperty("columns")
    private String columns;

    @JsonProperty("filter_params")
    private String filterParams;

    @JsonProperty("alias")
    private String alias;

    @JsonProperty("email_template_id")
    private Integer emailTemplateId;

    @JsonProperty("report_id")
    private Integer reportId;

    @JsonProperty("depot_id")
    private Integer depotId;

    @JsonProperty("azure_storage_config_id")
    private Integer azureStorageConfigId;

    @JsonProperty("id")
    private String azureStorageConfigUniqueId;

    @JsonProperty("azure_path")
    private String azurePath;

    @JsonProperty("active")
    private Boolean active;

    @JsonProperty("email_subject")
    private String emailSubject;
}