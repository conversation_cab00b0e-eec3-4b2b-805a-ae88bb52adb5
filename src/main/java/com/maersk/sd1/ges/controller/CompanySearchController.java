package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.CompanySearchInput;
import com.maersk.sd1.ges.dto.CompanySearchOutput;
import com.maersk.sd1.ges.service.CompanySearchService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMEmpresaServiceImp")
@RequiredArgsConstructor
public class CompanySearchController {

    private static final Logger logger = LogManager.getLogger(CompanySearchController.class);

    private final CompanySearchService companySearchService;

    @PostMapping("/gescompanySearch")
    public ResponseEntity<ResponseController<List<CompanySearchOutput>>> companySearch(@RequestBody @Valid CompanySearchInput.Root request){
        try{
            CompanySearchInput.Input input = request.getPrefix().getInput();
            List<CompanySearchOutput> output = companySearchService.searchCompanies(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch(Exception e){
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(Collections.emptyList()));
        }
    }
}
