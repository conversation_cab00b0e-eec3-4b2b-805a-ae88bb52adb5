package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.ReportRegisterInput;
import com.maersk.sd1.ges.dto.ReportRegisterOutput;
import com.maersk.sd1.ges.service.ReportRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReporteServiceImp")
public class ReportRegisterController {

    private static final Logger logger = LogManager.getLogger(ReportRegisterController.class.getName());

    private final ReportRegisterService reportRegisterService;

    @Autowired
    public ReportRegisterController(ReportRegisterService reportRegisterService) {
        this.reportRegisterService = reportRegisterService;
    }

    @PostMapping("/gesReporteRegistrar")
    public ResponseEntity<ResponseController<ReportRegisterOutput>> registerReport(
            @RequestBody @Valid ReportRegisterInput.Root request) {
        try {
            ReportRegisterInput.Input input = request.getPrefix().getInput();
            ReportRegisterOutput output = reportRegisterService.registerReport(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the report registration request.", e);
            ReportRegisterOutput output = new ReportRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}