package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReportListOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("reports")
    private List<ReportData> reports;

    @JsonProperty("total_records")
    private Long totalRecords;

    @Data
    public static class ReportData {
        @JsonProperty("report_id")
        private Integer reportId;

        @JsonProperty("id")
        private String id1;

        @JsonProperty("proyecto")
        private String project;

        @JsonProperty("plantilla")
        private String template;

        @JsonProperty("nombre")
        private String name;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("nombre_store")
        private String nameStore;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("parametros")
        private String parameters;

        @JsonProperty("roles")
        private String roles;

    }
}