package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.dto.SystemRuleEditInput;
import com.maersk.sd1.ges.dto.SystemRuleEditOutput;
import com.maersk.sd1.ges.service.SystemRuleEditService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReglaSistemaServiceImp")
public class SystemRuleEditController {

    private static final Logger logger = LogManager.getLogger(SystemRuleEditController.class);

    private final SystemRuleEditService systemRuleEditService;

    @Autowired
    public SystemRuleEditController(SystemRuleEditService systemRuleEditService) {
        this.systemRuleEditService = systemRuleEditService;
    }

    @PostMapping("/gesreglaSistemaEditar")
    public ResponseEntity<ResponseController<SystemRuleEditOutput>> editSystemRule(@Valid @RequestBody SystemRuleEditInput.Root request) {
        try {
            SystemRuleEditInput.Input input = request.getPrefix().getInput();
            SystemRuleEditOutput output = systemRuleEditService.updateSystemRule(
                    input.getSystemRuleId(),
                    input.getAlias(),
                    input.getSystemId(),
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getDescription(),
                    input.getRule(),
                    input.getActive(),
                    input.getModificationUserId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing editSystemRule request.", e);
            SystemRuleEditOutput output = new SystemRuleEditOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
