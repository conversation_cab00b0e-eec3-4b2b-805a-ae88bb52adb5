package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.CurrencyDeleteInput;
import com.maersk.sd1.ges.dto.CurrencyDeleteOutput;
import com.maersk.sd1.ges.service.CurrencyDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMMonedaServiceImp")
public class CurrencyDeleteController {

    private static final Logger logger = LogManager.getLogger(CurrencyDeleteController.class);

    private final CurrencyDeleteService monedaDeleteService;

    @Autowired
    public CurrencyDeleteController(CurrencyDeleteService monedaDeleteService) {
        this.monedaDeleteService = monedaDeleteService;
    }

    @PostMapping("/gesmonedaEliminar")
    public ResponseEntity<ResponseController<CurrencyDeleteOutput>> sdgMonedaDelete(@RequestBody @Valid CurrencyDeleteInput.Root request) {
        try {
            Integer monedaId = request.getPrefix().getInput().getMonedaId();
            CurrencyDeleteOutput output = monedaDeleteService.deleteMoneda(monedaId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CurrencyDeleteOutput output = new CurrencyDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}