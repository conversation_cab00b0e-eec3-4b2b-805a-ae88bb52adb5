package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.CatalogEditInputDTO;
import com.maersk.sd1.ges.controller.dto.CatalogEditOutputDTO;
import com.maersk.sd1.ges.service.CatalogEditService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ModuleADM/ADM/module/adm/ADMCatalogoServiceImp")
public class CatalogEditController {

    private static final Logger logger = LogManager.getLogger(CatalogEditController.class);

    @Autowired
    private CatalogEditService catalogEditService;

    @PostMapping("/gescatalogoEditar")
    public ResponseEntity<ResponseController<CatalogEditOutputDTO>> editCatalog(@RequestBody @Valid CatalogEditInputDTO.Root request) {
        try {
            CatalogEditInputDTO.Input input = request.getPrefix().getInput();
            CatalogEditOutputDTO output = catalogEditService.editCatalog(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the editCatalog request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.toString()));
        }
    }
}
