package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.CatalogLanguageListInput;
import com.maersk.sd1.ges.dto.CatalogLanguageListOutput;
import com.maersk.sd1.ges.service.CatalogLanguageListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMCatalogoIdiomaServiceImp")
public class CatalogLanguageListController {

    private static final Logger logger = LogManager.getLogger(CatalogLanguageListController.class);

    private final CatalogLanguageListService catalogLanguageListService;

    @Autowired
    public CatalogLanguageListController(CatalogLanguageListService catalogLanguageListService) {
        this.catalogLanguageListService = catalogLanguageListService;
    }

    @PostMapping("/gescatalogoIdiomaListar")
    public ResponseEntity<ResponseController<CatalogLanguageListOutput>> catalogLanguageList(@RequestBody @Valid CatalogLanguageListInput.Root request) {
        try {
            CatalogLanguageListInput.Input input = request.getPrefix().getInput();
            CatalogLanguageListOutput serviceOutput = catalogLanguageListService.listCatalogLanguages(input);
            return ResponseEntity.ok(new ResponseController<>(serviceOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CatalogLanguageListOutput errorOutput = new CatalogLanguageListOutput();
            errorOutput.setTotalRecords(0L);
            errorOutput.setCatalogLanguages(null);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}