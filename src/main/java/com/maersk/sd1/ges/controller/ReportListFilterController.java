package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.ReportListFilterInput;
import com.maersk.sd1.ges.dto.ReportListFilterOutput;
import com.maersk.sd1.ges.service.ReportListFilterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("Inlandnet/REP/gestion/ReporteConsultaServiceImp")
public class ReportListFilterController {

    private static final Logger logger = LogManager.getLogger(ReportListFilterController.class.getName());

    private ReportListFilterService reportListFilterService;

    @Autowired
    public ReportListFilterController(ReportListFilterService reportListFilterService) {
        this.reportListFilterService = reportListFilterService;
    }

    @PostMapping("/gesreporteListarFiltrar")
    public ResponseEntity<ResponseController<ReportListFilterOutput>> getFilterReportList(@RequestBody @Valid ReportListFilterInput.Root request) {
        try {
            ReportListFilterInput.Input input = request.getPrefix().getInput();
            ReportListFilterOutput result = reportListFilterService.getFilterReportList(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ReportListFilterOutput errorOutput = new ReportListFilterOutput();
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));

        }

    }
}
