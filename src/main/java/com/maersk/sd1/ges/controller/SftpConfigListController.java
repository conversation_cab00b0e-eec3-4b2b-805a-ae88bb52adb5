package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.SftpConfigListInput;
import com.maersk.sd1.ges.controller.dto.SftpConfigListOutput;
import com.maersk.sd1.ges.service.SftpConfigListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMSftpConfigServiceImp")
public class SftpConfigListController {

    private static final Logger logger = LogManager.getLogger(SftpConfigListController.class);

    private final SftpConfigListService sftpConfigListService;

    public SftpConfigListController(SftpConfigListService sftpConfigListService) {
        this.sftpConfigListService = sftpConfigListService;
    }

    @PostMapping("/gessftpConfigListar")
    public ResponseEntity<ResponseController<SftpConfigListOutput>> sftpConfigList(@RequestBody @Valid SftpConfigListInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            logger.info("Request received sftpConfigListar: {}", request);
            SftpConfigListInput.Input input = request.getPrefix().getInput();
            SftpConfigListOutput response = sftpConfigListService.listSftpConfigs(input);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.getMessage()));
        }
    }
}