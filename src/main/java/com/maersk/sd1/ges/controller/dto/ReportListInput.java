package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ReportListInput {

    @Data
    public static class Input {

        @JsonProperty("estado")
        private String status;

        @JsonProperty("nombre")
        private String names;

        @JsonProperty("nombre_store")
        private String namesStore;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        @NotNull
        @Min(1)
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        @NotNull
        private Prefix prefix;
    }

    private ReportListInput(){
        // Private constructor to prevent instantiation
    }
}