package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.service.EmailTemplateEditService;
import com.maersk.sd1.ges.dto.EmailTemplateEditInput;
import com.maersk.sd1.ges.dto.EmailTemplateEditOutput;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMEmailPlantillaServiceImp")
public class EmailTemplateEditController {

    private static final Logger logger = LogManager.getLogger(EmailTemplateEditController.class);

    private final EmailTemplateEditService emailTemplateEditService;

    @PostMapping("/gesemailPlantillaEditar")
    public ResponseEntity<ResponseController<EmailTemplateEditOutput>> editEmailTemplate(@RequestBody @Valid EmailTemplateEditInput.Root request) {
        try {

            if(request.getPrefix().getInput().getEmailPlantillaId() == null){
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(new EmailTemplateEditOutput()));
            }

            logger.info("Request received for editEmailTemplate: {}", request);
            EmailTemplateEditInput.Input input = request.getPrefix().getInput();
            EmailTemplateEditOutput output = emailTemplateEditService.editEmailTemplate(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            EmailTemplateEditOutput output = new EmailTemplateEditOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}