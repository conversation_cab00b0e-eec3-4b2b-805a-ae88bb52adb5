package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.dto.CurrencyObtainInput;
import com.maersk.sd1.ges.dto.CurrencyObtainOutput;
import com.maersk.sd1.ges.service.CurrencyObtainService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMMonedaServiceImp")
public class CurrencyObtainController {

    private static final Logger logger = LogManager.getLogger(CurrencyObtainController.class);

    private final CurrencyObtainService monedaObtenerService;

    @Autowired
    public CurrencyObtainController(CurrencyObtainService monedaObtenerService) {
        this.monedaObtenerService = monedaObtenerService;
    }

    @PostMapping("/gesmonedaObtener")
    public ResponseEntity<ResponseController<CurrencyObtainOutput>> sdgMonedaObtener(@RequestBody @Valid CurrencyObtainInput.Root request) {
        try {
            CurrencyObtainOutput output = monedaObtenerService.obtenerMoneda(request);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CurrencyObtainOutput output = new CurrencyObtainOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}