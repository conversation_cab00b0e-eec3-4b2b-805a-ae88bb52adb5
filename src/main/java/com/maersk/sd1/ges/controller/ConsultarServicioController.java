package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.dto.ConsultarServicioInputDTO;
import com.maersk.sd1.ges.dto.ConsultarServicioOutputDTO;
import com.maersk.sd1.ges.service.ConsultarServicioService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("Inlandnet/REP/gestion/ReporteConsultaServiceImp")
public class ConsultarServicioController {

    private final ConsultarServicioService consultarServicioService;

    public ConsultarServicioController(ConsultarServicioService consultarServicioService) {
        this.consultarServicioService = consultarServicioService;
    }

    @PostMapping("/consultarServicio")
    public ResponseEntity<ConsultarServicioOutputDTO> consultarServicio(@RequestBody ConsultarServicioInputDTO.Root reportDto, HttpServletRequest request) {
        ConsultarServicioOutputDTO response = consultarServicioService.executeServicio(reportDto);
        return ResponseEntity.ok(response);
    }
}