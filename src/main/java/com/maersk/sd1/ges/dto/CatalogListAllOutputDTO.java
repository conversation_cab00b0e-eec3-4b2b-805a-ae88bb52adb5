package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class CatalogListAllOutputDTO {

    @JsonProperty("catalogo_id")
    private Integer catalogoId;

    @JsonProperty("catalogo_padre_id")
    private Integer catalogoPadreId;

    @JsonProperty("unidad_negocio_id")
    private Integer unidadNegocioId;

    @JsonProperty("descripcion")
    private String descripcion;

    @JsonProperty("descricion_larga")
    private String descricionLarga;

    @JsonProperty("estado")
    private Boolean estado;

    @JsonProperty("variable_1")
    private String variable1;

    @JsonProperty("variable_2")
    private String variable2;

    @JsonProperty("variable_3")
    private Integer variable3;

    @JsonProperty("codigo")
    private String codigo;

    // Detailed language info for this Catalog
    @JsonProperty("idioma")
    private List<LanguageInfo> idioma;

    // List of associated business units
    @JsonProperty("unidades")
    private List<Integer> unidades;

    @JsonProperty("catalog_parent_alias")
    private String catalogParentAlias;

    @JsonProperty("alias")
    private String alias;

    // Nested DTO to hold the language data
    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class LanguageInfo {
        @JsonProperty("codigo")
        private String codigo;
        @JsonProperty("descripcion")
        private String descripcion;
        @JsonProperty("descricion_larga")
        private String descricionLarga;
    }
}
