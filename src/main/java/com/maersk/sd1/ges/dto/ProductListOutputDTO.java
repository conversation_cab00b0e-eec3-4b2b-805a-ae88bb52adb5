package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maersk.sd1.sds.dto.ProductListOutput;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ProductListOutputDTO {

    private  List<List<Long>> totalRecords;

    private List<ProductListOutput.ProductDetailOutput> products;
}