package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class InitializeOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("projects")
    private List<ProjectDto> projects;

    @JsonProperty("business_units")
    private List<BusinessUnitDto> businessUnits;

    @JsonProperty("configurations")
    private List<ConfigurationDto> configurations;

    @Data
    public static class ProjectDto {
        @JsonProperty("menu_id")
        private Integer menuId;

        @JsonProperty("title")
        private String title;
    }

    @Data
    public static class BusinessUnitDto {
        @JsonProperty("unidad_negocio_id")
        private Integer id;

        @JsonProperty("nombre")
        private String name;

        @JsonProperty("estado")
        private Boolean status;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ConfigurationDto {
        @JsonProperty("menu_id")
        private Integer menuId;

        @JsonProperty("id")
        private String template;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("valor")
        private String value;
    }
}
