package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class CompanySearchOutput {

    private Integer id;
    private String document;
    private String companyInfo;
}