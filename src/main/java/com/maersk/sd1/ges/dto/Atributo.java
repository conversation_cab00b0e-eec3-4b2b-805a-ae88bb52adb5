package com.maersk.sd1.ges.dto;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

@XmlType(propOrder = {"catalogoId", "unidadNegocioId", "descripcion", "descricionLarga", "variable1", "variable2", "variable3", "estado", "codigo", "alias", "listaIdioma", "unidades"})
public class Atributo {

    private String catalogoId;
    private String unidadNegocioId;
    private String descripcion;
    private String descricionLarga;
    private String variable1;
    private String variable2;
    private String variable3;
    private String estado;
    private String codigo;
    private String alias;
    private String listaIdioma;
    private String unidades;

    @XmlElement(name = "catalogo_id")
    public String getCatalogoId() {
        return catalogoId;
    }

    public void setCatalogoId(String catalogoId) {
        this.catalogoId = catalogoId;
    }

    @XmlElement(name = "unidad_negocio_id")
    public String getUnidadNegocioId() {
        return unidadNegocioId;
    }

    public void setUnidadNegocioId(String unidadNegocioId) {
        this.unidadNegocioId = unidadNegocioId;
    }

    @XmlElement(name = "descripcion")
    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    @XmlElement(name = "descricion_larga")
    public String getDescricionLarga() {
        return descricionLarga;
    }

    public void setDescricionLarga(String descricionLarga) {
        this.descricionLarga = descricionLarga;
    }

    @XmlElement(name = "variable1")
    public String getVariable1() {
        return variable1;
    }

    public void setVariable1(String variable1) {
        this.variable1 = variable1;
    }

    @XmlElement(name = "variable2")
    public String getVariable2() {
        return variable2;
    }

    public void setVariable2(String variable2) {
        this.variable2 = variable2;
    }

    @XmlElement(name = "variable3")
    public String getVariable3() {
        return variable3;
    }

    public void setVariable3(String variable3) {
        this.variable3 = variable3;
    }

    @XmlElement(name = "estado")
    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    @XmlElement(name = "codigo")
    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    @XmlElement(name = "alias")
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    @XmlElement(name = "listaIdioma")
    public String getListaIdioma() {
        return listaIdioma;
    }

    public void setListaIdioma(String listaIdioma) {
        this.listaIdioma = listaIdioma;
    }

    @XmlElement(name = "unidades")
    public String getUnidades() {
        return unidades;
    }

    public void setUnidades(String unidades) {
        this.unidades = unidades;
    }
}
