package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class CompanySearchV2Input {

    @Data
    public static class Input {
        @JsonProperty("business_unit_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("company_ids")
        private List<Integer> companyIds;

        @JsonProperty("company_roles")
        private List<String> companyRoles;

        @JsonProperty("flag_null")
        private String flagNull;

        @JsonProperty("company_name")
        @Size(max = 100)
        private String companyName;

        @JsonSetter("company_roles")
        public void setCompanyRoles(String companyRolesJson) throws JsonProcessingException {
            ObjectMapper objectMapper = new ObjectMapper();
            this.companyRoles = objectMapper.readValue(companyRolesJson, List.class);
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        private Prefix prefix;
    }
}

