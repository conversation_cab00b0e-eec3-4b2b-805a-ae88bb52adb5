package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class SystemRuleEditInput {

    @Data
    public static class Input {

        @JsonProperty("regla_sistema_id")
        @NotNull
        private Integer systemRuleId;

        @JsonProperty("id")
        @NotNull
        @Size(max = 50)
        private String alias;

        @JsonProperty("sistema_id")
        @NotNull
        private Integer systemId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("descripcion")
        @NotNull
        @Size(max = 100)
        private String description;

        @JsonProperty("regla")
        @NotNull
        private String rule;

        @JsonProperty("activo")
        @NotNull
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer modificationUserId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}

