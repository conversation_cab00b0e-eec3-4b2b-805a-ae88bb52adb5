package com.maersk.sd1.ges.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Input DTO mirroring the stored procedure parameters for empresa_listar.
 * Contains a nested structure: Root -> Prefix -> Input.
 */
public class CompanyListInputDTO {

    private CompanyListInputDTO() {}

    @Data
    public static class Input {

        @JsonProperty("empresa_id")
        private Integer companyId;

        @JsonProperty("documento")
        @Size(max = 20)
        private String document;

        @JsonProperty("razon_social")
        @Size(max = 100)
        private String legalName;

        @JsonProperty("razon_comercial")
        @Size(max = 100)
        private String commercialName;

        @JsonProperty("direccion")
        @Size(max = 200)
        private String address;

        @JsonProperty("longitud")
        @Size(max = 20)
        private String longitude;

        @JsonProperty("latitud")
        @Size(max = 20)
        private String latitude;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("fecha_registro_min")
        private LocalDateTime minRegistrationDate;

        @JsonProperty("fecha_registro_max")
        private LocalDateTime maxRegistrationDate;

        @JsonProperty("fecha_modificacion_min")
        private LocalDateTime minModificationDate;

        @JsonProperty("fecha_modificacion_max")
        private LocalDateTime maxModificationDate;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("tipo_documento")
        private Integer documentType;

        @JsonProperty("suspendido")
        private Boolean suspended = false;

        @JsonProperty("telefono")
        @Size(max = 200)
        private String phone;

        @JsonProperty("correo")
        @Size(max = 200)
        private String mail;

        @JsonProperty("abreviatura")
        @Size(max = 10)
        private String abbreviation;

        /**
         * A comma-separated list of role IDs.
         */
        @JsonProperty("roles")
        private String roles;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        private Prefix prefix;
    }
}
