package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class CountryListOutputDto {

//    @JsonProperty("resp_estado")
//    private Integer respEstado;
//
//    @JsonProperty("resp_mensaje")
//    private String respMensaje;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("total_registros")
    private List<List<Integer>> totalRegistros;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("countries")
    private List<CountryDataDto> countries;

    @Data
    @NoArgsConstructor
    public static class CountryDataDto {

        @JsonProperty("pais_id")
        private Integer paisId;

        @JsonProperty("pais")
        private String pais;

        @JsonProperty("nombre")
        private String nombre;

        @JsonProperty("cat_continente_id")
        private Integer catContinenteId;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro")
        private LocalDateTime fechaRegistro;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime fechaModificacion;

        public CountryDataDto(Integer paisId, String pais, String nombre, Integer catContinenteId, Boolean activo, LocalDateTime fechaRegistro, LocalDateTime fechaModificacion) {
            this.paisId = paisId;
            this.pais = pais;
            this.nombre = nombre;
            this.catContinenteId = catContinenteId;
            this.activo = activo;
            this.fechaRegistro = fechaRegistro;
            this.fechaModificacion = fechaModificacion;
        }
    }
}
