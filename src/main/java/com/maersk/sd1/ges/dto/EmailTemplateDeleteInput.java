package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class EmailTemplateDeleteInput {

    private EmailTemplateDeleteInput() {}

    @Data
    public static class Input {
        @JsonProperty("email_plantilla_id")
        @NotNull(message = "emailPlantillaId must not be null")
        private Integer emailPlantillaId;

        @JsonProperty("language_id")
        @NotNull(message = "languageId must not be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}