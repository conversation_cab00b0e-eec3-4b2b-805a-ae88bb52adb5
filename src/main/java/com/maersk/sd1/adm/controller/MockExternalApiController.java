package com.maersk.sd1.adm.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/ServiceTestConnection")
public class MockExternalApiController {

    @PostMapping("/ftpGetConnection")
    public ResponseEntity<Map<String, Object>> ftpGetConnection(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("ftpGetConnection"));
    }

    @PostMapping("/ftpGetListDirectory")
    public ResponseEntity<Map<String, Object>> ftpGetListDirectory(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("ftpGetListDirectory"));
    }

    @PostMapping("/ftpGetFile")
    public ResponseEntity<Map<String, Object>> ftpGetFile(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("ftpGetFile"));
    }

    @PostMapping("/ftpSetFile")
    public ResponseEntity<Map<String, Object>> ftpSetFile(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("ftpSetFile"));
    }

    @PostMapping("/sftpGetConnection")
    public ResponseEntity<Map<String, Object>> sftpGetConnection(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("sftpGetConnection"));
    }

    @PostMapping("/sftpGetListDirectory")
    public ResponseEntity<Map<String, Object>> sftpGetListDirectory(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("sftpGetListDirectory"));
    }

    @PostMapping("/sftpGetFile")
    public ResponseEntity<Map<String, Object>> sftpGetFile(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("sftpGetFile"));
    }

    @PostMapping("/sftpSetFile")
    public ResponseEntity<Map<String, Object>> sftpSetFile(@RequestBody Map<String, Object> request) {
        return ResponseEntity.ok(createMockResponse("sftpSetFile"));
    }

    private Map<String, Object> createMockResponse(String method) {
        Map<String, Object> response = new HashMap<>();
        response.put("AppServiceIP", "1");
        response.put("ConnectionType", method.startsWith("sftp") ? "SFTP" : "FTP");
        response.put("ConnectionID", "12345");
        response.put("MethodCalled", method);
        response.put("Success", true);
        return response;
    }
}


