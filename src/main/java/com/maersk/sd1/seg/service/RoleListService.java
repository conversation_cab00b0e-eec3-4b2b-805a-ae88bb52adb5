package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.RoleBusinessUnitRepository;
import com.maersk.sd1.seg.dto.RoleListInputDTO;
import com.maersk.sd1.seg.dto.RoleListOutputDTO;
import com.maersk.sd1.seg.dto.RoleListOutputDTO.RoleDTO;
import com.maersk.sd1.seg.dto.RoleListOutputDTO.UnidadDTO;
import com.maersk.sd1.common.model.Role;
import com.maersk.sd1.common.model.RoleBusinessUnit;
import com.maersk.sd1.common.repository.RoleRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
public class RoleListService {

    private static final Logger logger = LogManager.getLogger(RoleListService.class);

    private final RoleRepository roleRepository;
    private final RoleBusinessUnitRepository roleBusinessUnitRepository;

    /**
     * Translates the logic of the stored procedure seg.rol_listar into Java.
     * 1. If page/size are null, set default (1, 1000).
     * 2. If unidades is provided, parse it into a list of IDs.
     * 3. If menusDefecto is provided, parse the JSON array.
     * 4. Do a count and listing of roles that satisfy the filters.
     * 5. Return them in a DTO.
     */
    @Transactional(readOnly = true)
    public RoleListOutputDTO listarRoles(RoleListInputDTO.Root request) {
        RoleListOutputDTO output = new RoleListOutputDTO();

        try {
            // Extracting input filters
            String nombre = request.getPrefix().getInput().getNombre();
            String id = request.getPrefix().getInput().getId();
            String unidades = request.getPrefix().getInput().getUnidades();
            String menusDefecto = request.getPrefix().getInput().getMenusDefecto();
            Integer page = request.getPrefix().getInput().getPage();
            Integer size = request.getPrefix().getInput().getSize();

            if (page == null || size == null) {
                page = 1;
                size = 1000;
            }

            // Parsing unitIds (unidades)
            List<Integer> unitIds = null;
            if (unidades != null && !unidades.trim().isEmpty()) {
                unitIds = new ArrayList<>();
                String[] parts = unidades.split(",");
                for (String part : parts) {
                    unitIds.add(Integer.valueOf(part.trim()));
                }
            }

            // Parsing menuIds (menusDefecto)
            List<Integer> menuIds = null;
            if (menusDefecto != null && !menusDefecto.trim().isEmpty()) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    menuIds = mapper.readValue(menusDefecto, new TypeReference<List<Integer>>() {});
                } catch (Exception e) {
                    logger.error("Error parsing menusDefecto", e);
                    menuIds = null; // in case of error, set it to null
                }
            }

            // Query roles with applied filters
            PageRequest pageable = PageRequest.of(page - 1, size);
            Page<Role> rolesPage = roleRepository.findByFilters(
                    (nombre != null && !nombre.isBlank()) ? nombre : null,
                    (id != null && !id.isBlank()) ? id : null,
                    unitIds,
                    menuIds, // Now we're passing menuIds correctly
                    pageable
            );

            // Prepare output
            output.setRespEstado(1);
            output.setRespMensaje("Success");
            output.setTotalRegistros(rolesPage.getTotalElements());

            List<RoleDTO> roleDTOList = new ArrayList<>();
            for (Role role : rolesPage.getContent()) {
                RoleDTO roleDTO = new RoleDTO();
                roleDTO.setRolId(role.getId());
                roleDTO.setNombre(role.getName());
                roleDTO.setEstado(role.getStatus());
                roleDTO.setId(role.getId1());

                // Setting the default project menu (if available)
                if (role.getDefaultProjectMenu() != null) {
                    roleDTO.setMenuTitulo(role.getDefaultProjectMenu().getTitle());
                    roleDTO.setMenuIcono(role.getDefaultProjectMenu().getIcon());
                }

                // Step 1: Query RoleBusinessUnit for the business units associated with the role
                List<UnidadDTO> unidadDTOList = new ArrayList<>();
                List<RoleBusinessUnit> roleBusinessUnits = roleBusinessUnitRepository.findByRoleId(role.getId());
                for (RoleBusinessUnit rbu : roleBusinessUnits) {
                    UnidadDTO unidadDTO = new UnidadDTO();
                    unidadDTO.setUnidadNegocioId(rbu.getBusinessUnit().getId());
                    unidadDTO.setNombre(rbu.getBusinessUnit().getName());
                    unidadDTOList.add(unidadDTO);
                }

                roleDTO.setUnidades(unidadDTOList);

                roleDTOList.add(roleDTO);
            }

            output.setRoles(roleDTOList);

        } catch (Exception e) {
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setTotalRegistros(0L);
            output.setRoles(Collections.emptyList());
        }

        return output;
    }
}

