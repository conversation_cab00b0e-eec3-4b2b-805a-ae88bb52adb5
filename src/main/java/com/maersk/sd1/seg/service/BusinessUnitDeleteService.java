package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.dto.BusinessUnitDeleteOutput;
import com.maersk.sd1.common.repository.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class BusinessUnitDeleteService {

    private static final Logger logger = LogManager.getLogger(BusinessUnitDeleteService.class.getName());

    private final RoleBusinessUnitRepository roleBusinessUnitRepository;
    private final UserRoleBusinessUnitRepository userRoleBusinessUnitRepository;
    private final CatalogRepository catalogRepository;
    private final UserConfigRepository userConfigRepository;
    private final CurrencyRepository currencyRepository;
    private final TermRepository termRepository;
    private final FaqRepository faqRepository;
    private final BusinessUnitConfigRepository businessUnitConfigRepository;
    private final BusinessUnitCurrencyRepository businessUnitCurrencyRepository;
    private final BusinessUnitRepository businessUnitRepository;

    public BusinessUnitDeleteOutput deleteBusinessUnit(Integer unidadNegocioId, Integer usuarioId) {
        BusinessUnitDeleteOutput output = new BusinessUnitDeleteOutput();
        try {
            logger.info("Deleting Business Unit with id: {} by user: {}", unidadNegocioId, usuarioId);
            performDeletion(unidadNegocioId);
            output.setRespEstado(1);
            output.setRespMensaje("Business unit deleted successfully");
        } catch (Exception e) {
            logger.error("Error occurred while deleting business unit {}", unidadNegocioId, e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }

    private void performDeletion(Integer unidadNegocioId) {

        roleBusinessUnitRepository.deleteRoleBusinessUnitByBusinessUnit(unidadNegocioId);
        userRoleBusinessUnitRepository.deleteUserRolBusinessUnitByBusinessUnit(unidadNegocioId);
        catalogRepository.deleteCatalogByBusinessUnit(unidadNegocioId);
        userConfigRepository.deleteUserConfigByBusinessUnit(unidadNegocioId);
        currencyRepository.deleteCompanyByBusinessUnit(unidadNegocioId);
        termRepository.deleteTermByBusinessUnit(unidadNegocioId);
        faqRepository.deleteFaqByBusinessUnit(unidadNegocioId);
        businessUnitConfigRepository.deleteBusinessUnitConfigByBusinessUnit(unidadNegocioId);
        businessUnitCurrencyRepository.deleteBusinessUnitCurrencyByBusinessUnit(unidadNegocioId);
        businessUnitRepository.deleteBusinessUnit(unidadNegocioId);
    }
}