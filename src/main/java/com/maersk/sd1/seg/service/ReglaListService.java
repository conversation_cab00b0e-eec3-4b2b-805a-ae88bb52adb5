package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.dto.ReglaListInputDTO;
import com.maersk.sd1.seg.dto.ReglaListOutputDTO;
import com.maersk.sd1.common.repository.RuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReglaListService {

    private final RuleRepository ruleRepository;

    @Autowired
    public ReglaListService(RuleRepository ruleRepository) {
        this.ruleRepository = ruleRepository;
    }

    public ReglaListOutputDTO.Output getReglaList(ReglaListInputDTO.Input input) {
        List<Object[]> ruleData = ruleRepository.findReglas(Long.valueOf(input.getSistemaId()));

        List<ReglaListOutputDTO.ListResult> listResult = ruleData.stream()
                .map(row -> {
                    ReglaListOutputDTO.ListResult result = new ReglaListOutputDTO.ListResult();
                    result.setReglaId((Integer) row[0]);   // regla_id
                    result.setParametros((String) row[1]); // parametros
                    result.setId((String) row[2]);         // id (from catalogo)
                    return result;
                })
                .toList();

        ReglaListOutputDTO.Output output = new ReglaListOutputDTO.Output();
        output.setListResult(listResult);
        return output;
    }
}
