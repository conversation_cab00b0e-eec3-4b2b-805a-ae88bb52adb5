package com.maersk.sd1.seg.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.repository.NotificationRepository;
import com.maersk.sd1.common.repository.NotificationRoleRepository;
import com.maersk.sd1.common.repository.NotificationUserRepository;
import com.maersk.sd1.seg.controller.dto.NotificationRegisterInput;
import com.maersk.sd1.seg.controller.dto.NotificationRegisterOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class NotificationRegisterService {

    private int notificationId;

    private final NotificationRepository notificationRepository;

    private final NotificationRoleRepository notificationRoleRepository;

    private final NotificationUserRepository notificationUserRepository;

    private static final Logger logger = LogManager.getLogger(NotificationRegisterService.class.getName());

    @Transactional
    public NotificationRegisterOutput notificationRegister(NotificationRegisterInput.Root request) throws IOException, ParserConfigurationException, SAXException {

        NotificationRegisterOutput output = new NotificationRegisterOutput();
        NotificationRegisterInput.Input input = request.getPrefix().getInput();

        List<Integer> roleIds = new ArrayList<>();
        List<Integer> userIds = new ArrayList<>();

        String users = input.getUsers();
        String roles = input.getRoles();

        try {

            if (users != null && !users.trim().isEmpty()) {
                String usersJson = users;
                if (usersJson != null && !usersJson.trim().isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Map<String, Object>> parsedUsers = objectMapper.readValue(usersJson, new TypeReference<>() {
                    });
                    for (Map<String, Object> user : parsedUsers) {
                        Integer usuarioId = (Integer) user.get("usuario_id");
                        if (usuarioId != null) {
                            userIds.add(usuarioId);
                        }
                    }
                }
            }

            if (roles != null && !roles.trim().isEmpty()) {
                String rolesXml = roles;
                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

                factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
                factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
                factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
                factory.setXIncludeAware(false);
                factory.setExpandEntityReferences(false);

                DocumentBuilder builder = factory.newDocumentBuilder();
                InputSource is = new InputSource(new StringReader(rolesXml));
                Document doc = builder.parse(is);

                NodeList rolNodes = doc.getElementsByTagName("Rol");
                for (int i = 0; i < rolNodes.getLength(); i++) {
                    Node rolNode = rolNodes.item(i);
                    NodeList childNodes = rolNode.getChildNodes();
                    for (int j = 0; j < childNodes.getLength(); j++) {
                        Node childNode = childNodes.item(j);
                        if ("rol_id".equals(childNode.getNodeName())) {
                            roleIds.add(Integer.parseInt(childNode.getTextContent()));
                        }
                    }
                }
            }

            if(input!= null ){
                notificationId = notificationRepository.saveNotification(
                        input.getTitle(),
                        input.getSubtitle(),
                        input.getDescription(),
                        input.getIcon(),
                        input.getLevel(),
                        input.getOnce(),
                        input.getSingleAlert(),
                        input.getExpirationDate(),
                        input.getNotificationStatus(),
                        input.getUserId(),
                        input.getBusinessUnitId()
                );
            }

            if (roles != null && !roles.trim().isEmpty()) {
                notificationRoleRepository.insertNotificationRoles(notificationId, roleIds);
                notificationUserRepository.insertNotificationUsers(notificationId, roleIds);
            }

            if (users != null && !users.trim().isEmpty()) {
                notificationUserRepository.insertNotificationUsers(notificationId, roleIds);
            }
            output.setStatus(1);
            output.setMessage("Notification registered successfully");

        } catch (JsonProcessingException e) {
            output.setStatus(0);
            output.setMessage("Error parsing JSON for users: " + e.getMessage());
            logger.error("Error parsing JSON for users", e);
        } catch (ParserConfigurationException | SAXException e) {
            output.setStatus(0);
            output.setMessage("Error parsing XML for roles: " + e.getMessage());
            logger.error("Error parsing XML for roles", e);
        } catch (IOException e) {
            output.setStatus(0);
            output.setMessage("I/O error occurred while processing roles or users: " + e.getMessage());
            logger.error("I/O error occurred while processing roles or users", e);
        } catch (Exception e) {
            output.setStatus(0);
            output.setMessage("An unexpected error occurred: " + e.getMessage());
            logger.error("An unexpected error occurred", e);
        }
        return output;
    }
}