package com.maersk.sd1.seg.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.dto.EditRoleInput;
import com.maersk.sd1.seg.dto.EditRoleOutput;
import com.maersk.sd1.seg.dto.ServiceData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.*;
import java.io.IOException;
import java.io.StringReader;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class EditRoleService {

    private final RoleRepository roleRepository;
    private final RoleMenuRepository roleMenuRepository;
    private final RoleMenuActionRepository roleMenuActionRepository;
    private final RoleBusinessUnitRepository roleBusinessUnitRepository;
    private final RoleServiceRepository roleServiceRepository;

    @Autowired
    public EditRoleService(RoleRepository roleRepository, RoleMenuRepository roleMenuRepository,
                           RoleMenuActionRepository roleMenuActionRepository,
                           RoleBusinessUnitRepository roleBusinessUnitRepository,
                           RoleServiceRepository roleServiceRepository) {
        this.roleRepository = roleRepository;
        this.roleMenuRepository = roleMenuRepository;
        this.roleMenuActionRepository = roleMenuActionRepository;
        this.roleBusinessUnitRepository = roleBusinessUnitRepository;
        this.roleServiceRepository = roleServiceRepository;
    }

    @Transactional
    public EditRoleOutput editRole(EditRoleInput.Root inputRoot) {
        String statusRole = inputRoot.getInput().getStatusRole();
        String id = inputRoot.getInput().getId();
        String serviceRole = inputRoot.getInput().getRoleServices();
        Integer roleId = inputRoot.getInput().getRoleId();
        String name = inputRoot.getInput().getName();
        String menuIds = inputRoot.getInput().getMenuIds();
        String businessUnitIds = inputRoot.getInput().getBusinessUnitIds();
        Integer menuProjectDefaultId = inputRoot.getInput().getMenuProjectDefaultId();
        Integer userId = inputRoot.getInput().getUserId();
        try {
            if (roleRepository.existsById1AndIdNot(id, roleId)) {
                return EditRoleOutput.builder().statusCode(0).message("The Id entered already exists, enter another").build();
            }

            Role role = roleRepository.findById(roleId).orElseThrow(() -> new IllegalArgumentException("Role not found"));
            role.setName(name);
            role.setStatus(statusRole == null ? null : statusRole.equals("1"));
            role.setId1(id);
            role.setDefaultProjectMenu(Menu.builder().id(menuProjectDefaultId).build());
            role.setModificationUser(User.builder().id(userId).build());
            role.setModificationDate(LocalDateTime.now());
            roleRepository.save(role);

            roleMenuActionRepository.deleteByRoleId(roleId);
            roleMenuRepository.deleteByRoleId(roleId);

            List<RoleMenu> roleMenus = parseRoleMenus(menuIds, roleId);
            roleMenuRepository.saveAll(roleMenus);

            List<RoleMenuAction> roleMenuActions = parseRoleMenuActions(menuIds, roleId);
            roleMenuActionRepository.saveAll(roleMenuActions);

            roleBusinessUnitRepository.deleteByRoleId(roleId);

            List<RoleBusinessUnit> roleBusinessUnits = parseRoleBusinessUnits(businessUnitIds, roleId);
            roleBusinessUnitRepository.saveAll(roleBusinessUnits);

            if (StringUtils.isNotBlank(serviceRole)) {
                roleServiceRepository.deleteByRoleId(roleId);
                List<RoleService> roleServices = parseRoleServices(serviceRole, roleId);
                roleServiceRepository.saveAll(roleServices);
            }

        } catch (Exception e) {
            return EditRoleOutput.builder().statusCode(0).message(e.getMessage()).build();
        }

        return EditRoleOutput.builder().statusCode(1).message("Successfully edited role").build();
    }

    public List<RoleMenu> parseRoleMenus(String xmlData, Integer roleId) throws IOException, SAXException, ParserConfigurationException, XPathExpressionException {
        if (StringUtils.isBlank(xmlData)) {
            return Collections.emptyList();
        }
        Set<String> uniqueKeys = new HashSet<>();
        List<RoleMenu> roleMenus = new ArrayList<>();
        NodeList menuIdNodes = getNodeList(xmlData, "//Menu/Menu_id/text()");
        for (int i = 0; i < menuIdNodes.getLength(); i++) {
            int menuId = Integer.parseInt(menuIdNodes.item(i).getNodeValue());
            String uniqueKey = roleId + "-" + menuId;
            if (uniqueKeys.add(uniqueKey)) {
                roleMenus.add(RoleMenu.builder()
                        .id(RoleMenuId.builder().roleId(roleId).menuId(menuId).build())
                        .menu(Menu.builder().id(menuId).build())
                        .role(Role.builder().id(roleId).build())
                        .build());
            }
        }
        return roleMenus;
    }

    private List<RoleMenuAction> parseRoleMenuActions(String xmlData, Integer roleId) throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
        if (StringUtils.isBlank(xmlData)) {
            return Collections.emptyList();
        }
        List<RoleMenuAction> roleMenuActions = new ArrayList<>();
        Set<String> uniqueKeys = new HashSet<>();
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        factory.setXIncludeAware(false);
        factory.setExpandEntityReferences(false);

        DocumentBuilder builder = factory.newDocumentBuilder();
        String xmlWithRoot = "<root>" + xmlData + "</root>";
        Document document = builder.parse(new InputSource(new StringReader(xmlWithRoot)));

        XPathFactory xpathFactory = XPathFactory.newInstance();
        XPath xpath = xpathFactory.newXPath();
        NodeList menuNodes = (NodeList) xpath.evaluate("//Menu", document, XPathConstants.NODESET);

        for (int i = 0; i < menuNodes.getLength(); i++) {
            Element menuElement = (Element) menuNodes.item(i);

            XPathExpression menuIdExpr = xpath.compile("./Menu_id/text()");
            String menuIdStr = (String) menuIdExpr.evaluate(menuElement, XPathConstants.STRING);
            Integer menuId = Integer.parseInt(menuIdStr);

            NodeList accionIdNodes = (NodeList) xpath.compile("./Accion_id/text()").evaluate(menuElement, XPathConstants.NODESET);
            if (accionIdNodes.getLength() > 0) {
                String actionIdStr = accionIdNodes.item(0).getNodeValue();
                Integer actionId = Integer.parseInt(actionIdStr);

                String uniqueKey = roleId + "-" + menuId + "-" + actionId;
                if (uniqueKeys.add(uniqueKey)) {
                    roleMenuActions.add(
                            RoleMenuAction.builder()
                                    .id(RoleMenuActionId.builder()
                                            .roleId(roleId)
                                            .menuId(menuId)
                                            .typeActionId(actionId)
                                            .build())
                                    .catActionType(Catalog.builder().id(actionId).build())
                                    .build());
                }
            }
        }

        return roleMenuActions;
    }

    private List<RoleBusinessUnit> parseRoleBusinessUnits(String xmlData, Integer roleId) throws ParserConfigurationException, IOException, SAXException, XPathExpressionException {
        if (StringUtils.isBlank(xmlData)) {
            return Collections.emptyList();
        }
        Set<String> uniqueKeys = new HashSet<>();
        List<RoleBusinessUnit> roleBusinessUnits = new ArrayList<>();
        NodeList nodeList = getNodeList(xmlData, "//unidad_negocio/unidad_negocio_id/text()");
        for (int i = 0; i < nodeList.getLength(); i++) {
            int businessUnitId = Integer.parseInt(nodeList.item(i).getNodeValue());
            String uniqueKey = roleId + "-" + businessUnitId;
            if (uniqueKeys.add(uniqueKey)) {
                roleBusinessUnits.add(
                        RoleBusinessUnit.builder()
                                .id(RoleUnitBusinessId.builder()
                                        .unitBusinessId(businessUnitId)
                                        .roleId(roleId)
                                        .build())
                                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                                .role(Role.builder().id(roleId).build())
                                .build()
                );
            }
        }
        return roleBusinessUnits;
    }

    private NodeList getNodeList(String xmlData, String tagName) throws ParserConfigurationException, SAXException, IOException, XPathExpressionException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        DocumentBuilder builder = factory.newDocumentBuilder();
        String xmlWithRoot = "<root>" + xmlData + "</root>";
        Document document = builder.parse(new InputSource(new StringReader(xmlWithRoot)));
        XPathFactory xpathFactory = XPathFactory.newInstance();
        XPath xpath = xpathFactory.newXPath();
        XPathExpression expr = xpath.compile(tagName);
        return (NodeList) expr.evaluate(document, XPathConstants.NODESET);

    }

    private List<RoleService> parseRoleServices(String jsonData, Integer roleId) throws JsonProcessingException {
        if (StringUtils.isBlank(jsonData)) {
            return Collections.emptyList();
        }
        List<RoleService> roleServices = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        List<ServiceData> services = objectMapper.readValue(jsonData, new TypeReference<>() {
        });
        Set<String> uniqueKeys = new HashSet<>();
        for (ServiceData service : services) {
            Integer serviceId = service.getServiceId();
            String uniqueKey = roleId + "-" + serviceId;
            if (uniqueKeys.add(uniqueKey)) {
                roleServices.add(
                        RoleService.builder()
                                .id(RoleServiceId.builder().roleId(roleId).serviceId(serviceId).build())
                                .role(Role.builder().id(roleId).build())
                                .service(com.maersk.sd1.common.model.Service.builder().id(serviceId).build())
                                .build()
                );
            }
        }
        return roleServices;
    }
}
