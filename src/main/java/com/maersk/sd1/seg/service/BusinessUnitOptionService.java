package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.BusinessUnitOptionOutput;
import com.maersk.sd1.seg.dto.CatalogOptionDto;
import com.maersk.sd1.seg.dto.CurrencyOptionDto;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.CurrencyRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BusinessUnitOptionService {

    private static final Integer CATALOG_PARENT_ID = 182;
    private static final Integer CURRENCY_STATUS = 20253;

    private static final Logger logger = LogManager.getLogger(BusinessUnitOptionService.class);

    private final CatalogRepository catalogRepository;
    private final CurrencyRepository currencyRepository;

    public BusinessUnitOptionService(CatalogRepository catalogRepository,
                                     CurrencyRepository currencyRepository) {
        this.catalogRepository = catalogRepository;
        this.currencyRepository = currencyRepository;
    }

    public BusinessUnitOptionOutput getBusinessUnitOptions() {
        logger.info("Starting retrieval of business unit options");
        BusinessUnitOptionOutput output = new BusinessUnitOptionOutput();
        try {
            List<CatalogOptionDto> catalogOptions = catalogRepository.findCatalogOptionsByParentCatalogId(CATALOG_PARENT_ID);
            List<CurrencyOptionDto> currencyOptions = currencyRepository.findCurrencyOptionsByStatus(CURRENCY_STATUS);

            output.setCatalogOptions(catalogOptions);
            output.setCurrencyOptions(currencyOptions);
            output.setRespStatus(1);
            output.setRespMessage("Data fetched successfully.");
            logger.info("Retrieval of business unit options completed successfully");

        } catch (Exception e) {
            logger.error("Error fetching business unit options: {}", e.getMessage(), e);
            output.setRespStatus(0);
            output.setRespMessage("An error occurred while fetching the business unit options.");
        }
        return output;
    }
}