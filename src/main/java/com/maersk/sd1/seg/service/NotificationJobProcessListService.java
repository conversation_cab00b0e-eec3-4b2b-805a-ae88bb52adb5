package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.NotificationJob;
import com.maersk.sd1.common.repository.NotificationJobRepository;
import com.maersk.sd1.seg.controller.dto.NotificationJobProcessListOutput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class NotificationJobProcessListService {


    private final NotificationJobRepository notificationJobRepository;

    public List<NotificationJobProcessListOutput> processNotificationJobList() {
            List<NotificationJob> jobs = notificationJobRepository.findNotificationJobs(Parameter.ACTIVE_STATUS);
            LocalDateTime now = LocalDateTime.now();

            return jobs.stream()
                    .filter(job -> job.getLastExecutionDate() == null ||
                            now.isAfter(job.getLastExecutionDate().plusSeconds(job.getPeriod())))
                    .map(this::convertToOutput)
                    .toList();
    }

    private NotificationJobProcessListOutput convertToOutput(NotificationJob job) {
        NotificationJobProcessListOutput notificationJobProcessListOutput = new NotificationJobProcessListOutput();
        notificationJobProcessListOutput.setNotificationJobId(job.getId());
        notificationJobProcessListOutput.setPushNotificationProcedure(job.getPushNotificationProcedure());
        notificationJobProcessListOutput.setWebNotificationProcedure(job.getWebNotificationProcedure());
        notificationJobProcessListOutput.setEmailProcedure(job.getEmailProcedure());
        return notificationJobProcessListOutput;
    }

}
