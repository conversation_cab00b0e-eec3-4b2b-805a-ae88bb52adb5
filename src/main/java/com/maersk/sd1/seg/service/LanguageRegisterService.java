package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.LanguageRepository;
import com.maersk.sd1.seg.dto.LanguageRegisterInput;
import com.maersk.sd1.seg.dto.LanguageRegisterOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;

@Service
@RequiredArgsConstructor
public class LanguageRegisterService {

    private static final Logger logger = LogManager.getLogger(LanguageRegisterService.class.getName());

    private final LanguageRepository languageRepository;

    @Transactional
    public ResponseEntity<ResponseController<LanguageRegisterOutput>> languageRegisterService(LanguageRegisterInput.Root request) {

        LanguageRegisterOutput output=new LanguageRegisterOutput();

        if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            logger.error("Invalid input: Missing required fields.");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>("Invalid input: Missing required fields."));
        }

        LanguageRegisterInput.Input input=request.getPrefix().getInput();

        String code=input.getCode();
        String name=input.getName();
        Character isActive=input.getIsActive();
        Integer userRegistrationId=input.getRegistrationUserId();

        if (code == null || name == null || isActive == null || userRegistrationId == null) {
            logger.error("Missing required fields: Code, Name, Active, or User Registration ID.");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>("Missing required fields: Code, Name, Active, or User Registration ID."));
        }

        try{

            Integer id=languageRepository.insertLanguageAndGetId(code, name, isActive, userRegistrationId);

            output.setResultId(id);
            output.setResultStatus(1);
            output.setResultMessage("Language registered successfully.");

        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            output.setResultStatus(0);
            output.setResultMessage("An error occurred while processing the request: " + e.getMessage());
            output.setResultId(0);
        }
        return ResponseEntity.ok(new ResponseController<>(output));
    }
}
