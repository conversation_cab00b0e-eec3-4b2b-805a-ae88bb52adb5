package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.SystemLanguageRepository;
import com.maersk.sd1.seg.dto.LanguageSystemListDTO;
import com.maersk.sd1.seg.dto.LanguageSystemListInput;
import com.maersk.sd1.seg.dto.LanguageSystemListOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class LanguageSystemListService {

    private static final Logger logger = LogManager.getLogger(LanguageSystemListService.class.getName());

    private final SystemLanguageRepository systemLanguageRepository;


    @Transactional
    public ResponseEntity<ResponseController<LanguageSystemListOutput>> languageSystemListService(LanguageSystemListInput.Root request) {

        LanguageSystemListOutput output=new LanguageSystemListOutput();

        if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            logger.error("Invalid input: Missing required fields.");
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input: Missing required fields."));
        }

        LanguageSystemListInput.Input input = request.getPrefix().getInput();

        Integer systemId=input.getSystemId();
        Integer languageId=input.getLanguageId();
        Integer page=input.getPage();
        Integer size=input.getSize();

        Integer count=systemLanguageRepository.countBySystemIdAndLanguageId(systemId,languageId);

        output.setTotalCount(count);

        if(count==0){
            output.setData(Collections.emptyList());
            return ResponseEntity.ok(new ResponseController<>(output));
        }

        List<LanguageSystemListDTO> resultList=systemLanguageRepository.findSystemAndLanguageIds(systemId,languageId);

        int start = (page - 1) * size;
        int end = Math.min(start + size, resultList.size());

        List<LanguageSystemListDTO> paginatedList = resultList.subList(start, end);


        List<LanguageSystemListOutput.DataItem> dataItems = paginatedList.stream().map(dto -> {
            LanguageSystemListOutput.DataItem dataItem = new LanguageSystemListOutput.DataItem();
            dataItem.setSystemId(dto.getSystemId());
            dataItem.setLanguageId(dto.getLanguageId());
            return dataItem;
        }).toList();

        output.setData(dataItems);

        return ResponseEntity.ok(new ResponseController<>(output));
    }
}
