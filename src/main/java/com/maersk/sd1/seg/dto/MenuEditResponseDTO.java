package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class MenuEditResponseDTO {

    @Data
    public static class Output {

        @JsonProperty("status")
        private Integer status;

        @JsonProperty("message")
        private String message;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Output output;
    }

    @Data
    public static class Root {
        @JsonProperty("MenuEdit")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setOutput(new Output());
        }
    }
}
