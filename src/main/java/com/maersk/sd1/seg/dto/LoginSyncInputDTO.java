package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class LoginSyncInputDTO {

    @Data
    public static class Input {

        @JsonProperty("usuario_id")
        @NotNull
        private Integer userId;

        @JsonProperty("sistema_id")
        @NotNull
        private Integer systemId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}

