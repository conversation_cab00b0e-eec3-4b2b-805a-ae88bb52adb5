package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDate;

public class RuleListFullInput {

    @Data
    public static class Input {

        @JsonProperty("regla_id")
        private Integer reglaId;

        @JsonProperty("sistema_id")
        private Long sistemaId;

        @JsonProperty("parametros")
        private String parametros;

        @JsonProperty("estado")
        private Character estado;

        @JsonProperty("fecha_registro_min")
        private LocalDate fechaRegistroMin;

        @JsonProperty("fecha_registro_max")
        private LocalDate fechaRegistroMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDate fechaModificacionMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDate fechaModificacionMax;

        @JsonProperty("cat_regla_id")
        private Long catReglaId;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setInput(new Input());
        }
    }
}
