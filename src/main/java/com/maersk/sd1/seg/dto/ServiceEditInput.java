package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ServiceEditInput {

    @Data
    public static class Input {
        @JsonProperty("service_id")
        @NotNull
        private Integer serviceId;

        @JsonProperty("service_name")
        @NotNull
        @Size(max = 200)
        private String serviceName;

        @JsonProperty("service_protected")
        @NotNull
        private Character serviceProtected;

        @JsonProperty("service_active")
        @NotNull
        private Character serviceActive;

        @JsonProperty("user_modification_id")
        @NotNull
        private Integer userModificationId;

        @JsonProperty("service_roles")
        private String serviceRoles;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}
