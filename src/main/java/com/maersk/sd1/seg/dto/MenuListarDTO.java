package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

public class MenuListarDTO {

    @Data
    public static class Rol {
        @JsonProperty("nombre")
        private String nombre;
    }

    @Data
    public static class Accion {
        @JsonProperty("defecto_activo")
        private String defectoActivo;
    }

    @Data
    public static class Menu {
        @JsonProperty("menu_id")
        private Integer menuId;

        @JsonProperty("menu_padre_id")
        private Integer menuPadreId;

        @JsonProperty("titulo")
        private String titulo;

        @JsonProperty("descripcion")
        private String descripcion;

        @JsonProperty("plantilla")
        private String plantilla;

        @JsonProperty("icono")
        private String icono;

        @JsonProperty("orden")
        private Integer orden;

        @JsonProperty("estado")
        private Boolean estado;

        @JsonProperty("tiene_id")
        private Boolean tieneId;

        @JsonProperty("menu_base_id")
        private Integer menuBaseId;

        @JsonProperty("roles")
        private Rol roles;

        @JsonProperty("acciones")
        private Accion acciones;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;

        @Data
        public static class Prefix {
            @JsonProperty("menu")
            private List<Menu> menuList;

            public void setMenuList(List<Menu> menuList) {
                this.menuList = menuList;
            }
        }
    }

    @Data
    public static class Output {
        @JsonProperty("menus")
        private List<Menu> menus;
    }
}