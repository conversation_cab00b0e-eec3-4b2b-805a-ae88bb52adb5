package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

public class RoleRegisterInput {

    @Data
    public static class Input {
        @JsonProperty("role_name")
        @NotNull(message = "Role name cannot be null")
        private String roleName;

        @JsonProperty("role_id")
        @NotNull(message = "Role id cannot be null")
        private String roleId;

        @JsonProperty("role_state")
        @NotNull(message = "Role state cannot be null")
        private Boolean roleState;

        @JsonProperty("menu_project_default_id")
        private Integer menuProjectDefaultId;

        @JsonProperty("role_services")
        private List<ServiceDTO> roleServices;

        @JsonProperty("user_registration_id")
        @NotNull(message = "User registration id cannot be null")
        private Integer userRegistrationId;

        @JsonProperty("menus")
        private List<MenuDTO> menus;

        @JsonProperty("business_units")
        private List<BusinessUnitDTO> businessUnits;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "Prefix input cannot be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        @NotNull(message = "Root SDG cannot be null")
        private Prefix prefix;
    }
}

