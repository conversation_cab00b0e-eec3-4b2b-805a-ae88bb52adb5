package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

/**
 * Input DTO replicating the parameters of the stored procedure.
 * It follows the same structure as the example Input DTO: Root -> Prefix -> Input.
 */
public class BusinessUnitListInput {

    @Data
    public static class Input {
        @JsonProperty("parentUnit") // Parent unit
        @PositiveOrZero(message = "parentUnit must be zero or positive")
        private Integer parentUnit;

        @JsonProperty("unitType") // Type of the unit
        private Integer unitType;

        /**
         * indicador_padres may be '1' or some other single character (or possibly blank).
         * We add a simple @Pattern check if needed. Otherwise, we leave it open.
         */
        @JsonProperty("parentIndicator") // Parent indicator
        private String parentIndicator;

        @JsonProperty("page") // Page number
        @PositiveOrZero(message = "page must be zero or positive")
        private Integer page;

        @JsonProperty("size") // Size of the page
        @PositiveOrZero(message = "size must be zero or positive")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "Input prefix cannot be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        @NotNull(message = "Root SDG cannot be null")
        private Prefix prefix;
    }
}