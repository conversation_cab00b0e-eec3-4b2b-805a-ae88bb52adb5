package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Input DTO for listing Languages.
 * Follows the same structure with Root -> Prefix -> Input,
 * replicating the approach of EirSignatureRegisterInput.
 */
public class LanguageListInput {

    @Data
    public static class Input {
        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        @JsonProperty("active")
        private Character active;

        @JsonProperty("registration_date_min")
        private LocalDateTime registrationDateMin;

        @JsonProperty("registration_date_max")
        private LocalDateTime registrationDateMax;

        @JsonProperty("modification_date_min")
        private LocalDateTime modificationDateMin;

        @JsonProperty("modification_date_max")
        private LocalDateTime modificationDateMax;

        /**
         * Page number for pagination.
         * Optional, but must be >= 1 if provided.
         */
        @JsonProperty("page")
        @Min(value = 1, message = "The page number must be >= 1.")
        private Integer page;

        /**
         * Page size for pagination.
         * Optional, but must be >= 1 if provided.
         */
        @JsonProperty("size")
        @Min(value = 1, message = "The page size must be >= 1.")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}

