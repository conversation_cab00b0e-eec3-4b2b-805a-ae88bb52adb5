package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
public class ServiceGetServiceDto {

    @JsonProperty("servicio_id")
    private Integer servicioId;

    @JsonProperty("nombre")
    private String nombre;

    @JsonProperty("indicador_protegido")
    private Character indicadorProtegido;

    @JsonProperty("estado")
    private Boolean estado;

    @JsonProperty("usuario_registro_id")
    private Integer usuarioRegistroId;

    @JsonProperty("fecha_registro")
    private LocalDateTime fechaRegistro;

    @JsonProperty("usuario_modificacion_id")
    private Integer usuarioModificacionId;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime fechaModificacion;
}