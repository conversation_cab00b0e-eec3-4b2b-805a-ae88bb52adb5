package com.maersk.sd1.seg.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RoleOutputDTO {
    @JsonProperty("nombre")
    private String nombre;

    @JsonProperty("estado")
    private Boolean estado;

    public RoleOutputDTO() {
    }

    public RoleOutputDTO(String nombre, Boolean estado) {
        this.nombre = nombre;
        this.estado = estado;
    }
}
