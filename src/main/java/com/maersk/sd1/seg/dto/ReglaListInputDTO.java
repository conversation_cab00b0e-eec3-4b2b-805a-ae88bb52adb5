package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class ReglaListInputDTO {

    @Data
    public static class Input {

        @JsonProperty("sistema_id")
        private Integer sistemaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setInput(new Input());
        }
    }
}
