package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.NotificationRegisterInput;
import com.maersk.sd1.seg.controller.dto.NotificationRegisterOutput;
import com.maersk.sd1.seg.service.NotificationRegisterService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/dsinland")
public class NotificationRegisterController {

    private static final Logger logger = LogManager.getLogger(NotificationRegisterController.class.getName());

    private final NotificationRegisterService notificationRegisterService;

    @PostMapping("/segnotificacionRegistrar")
    public ResponseEntity<ResponseController<NotificationRegisterOutput>> notificationRegister(@RequestBody NotificationRegisterInput.Root request) {

        try {
            NotificationRegisterOutput notificationRegisterOutput = notificationRegisterService.notificationRegister(request);
            return ResponseEntity.ok(new ResponseController<>(notificationRegisterOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            NotificationRegisterOutput notificationRegisterOutput = new NotificationRegisterOutput();
            notificationRegisterOutput.setStatus(0);
            notificationRegisterOutput.setMessage("An error occurred while processing the request: " + e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(notificationRegisterOutput));
        }
    }
}