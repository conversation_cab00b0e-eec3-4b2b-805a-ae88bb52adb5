package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class BusinessUnitDeleteValidationInput {

    @Data
    public static class Input {
        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer businessUnitId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }

    private BusinessUnitDeleteValidationInput() {
        // private constructor to hide the implicit public one
    }
}