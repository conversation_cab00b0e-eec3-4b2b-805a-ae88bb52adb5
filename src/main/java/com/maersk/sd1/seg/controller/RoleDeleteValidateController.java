package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.RoleDeleteValidateInput;
import com.maersk.sd1.seg.controller.dto.RoleDeleteValidateOutput;
import com.maersk.sd1.seg.service.RoleDeleteValidateService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolServiceImp")
public class RoleDeleteValidateController {

    private static final Logger logger = LogManager.getLogger(RoleDeleteValidateController.class);

    private final RoleDeleteValidateService roleDeleteValidateService;

    @PostMapping("/segrolEliminarValidar")
    public ResponseEntity<ResponseController<RoleDeleteValidateOutput>> validateRole(@RequestBody @Valid RoleDeleteValidateInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            Integer roleId = request.getPrefix().getInput().getRoleId();
            if(roleId == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Role Id cannot be null."));
            }
            RoleDeleteValidateOutput output = roleDeleteValidateService.validateRoleDelete(roleId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing RoleDeleteValidate.", e);
            RoleDeleteValidateOutput output = new RoleDeleteValidateOutput();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}