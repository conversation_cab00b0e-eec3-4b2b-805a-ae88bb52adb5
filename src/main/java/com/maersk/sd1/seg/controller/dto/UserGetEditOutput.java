package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class UserGetEditOutput {

    @JsonProperty("usuario_id")
    private Integer userId;

    @JsonProperty("user_alias")
    private String userAlias;

    @JsonProperty("correo")
    private String correo;

    @JsonProperty("nombres")
    private String names;

    @JsonProperty("apellido_paterno")
    private String apellidoPaterno;

    @JsonProperty("apellido_materno")
    private String apellidoMaterno;

    @JsonProperty("empresa_id")
    private Integer empresaId;

    @JsonProperty("adjunto_foto_id")
    private Integer adjuntoFotoId;

    @JsonProperty("estado")
    private Character status;

    @JsonProperty("caducidad_clave")
    private Long caducidadClave;

    @JsonProperty("persona_id")
    private Integer personId;

    @JsonProperty("empresas_asociadas")
    private List<AssociatedCompanyDTO> empresasAsociadas;

    @JsonProperty("usuario_email_plantillas")
    private List<UserEmailTemplateDTO> userEmailTemplateDTO;

    @JsonProperty("email_plantillas")
    private List<EmailTemplateDTO> emailTemplateDTOList;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class AssociatedCompanyDTO {
        @JsonProperty("empresa_id")
        private Integer empressId;
        
        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("razon_social")
        private String razonSocial;

        @JsonProperty("documento")
        private String document;

        @JsonProperty("descripcion")
        private String descriptionTypeDoc;

        @JsonProperty("unidad_negocio_nombre")
        private String businessUnitName;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class UserEmailTemplateDTO {
        @JsonProperty("email_plantilla_id")
        private Integer emailPlantillaId;
        
        @JsonProperty("estado")
        private Character status;

        @JsonProperty("habilitado")
        private Character habilitado;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmailTemplateDTO {
        @JsonProperty("email_plantilla_id")
        private Integer emailPlantillaId;

        @JsonProperty("titulo")
        private String title;
    }
}