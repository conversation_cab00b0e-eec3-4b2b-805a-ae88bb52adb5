package com.maersk.sd1.seg.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.RoleObtainInputDTO;
import com.maersk.sd1.seg.dto.RoleObtainOutputDTO;
import com.maersk.sd1.seg.service.RoleObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller class that handles the role data obtain process,
 * mimicking the stored procedure seg.rol_obtener.
 */
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolService")
public class RoleObtainController {

    private static final Logger logger = LogManager.getLogger(RoleObtainController.class);

    private final RoleObtainService roleObtainService;

    @Autowired
    public RoleObtainController(RoleObtainService roleObtainService) {
        this.roleObtainService = roleObtainService;
    }

    /**
     * Endpoint to fetch the role data referencing the logic of seg.rol_obtener.
     * @param request The input request containing rol_id.
     * @return A ResponseEntity containing the aggregated data.
     */
    @PostMapping("/segrolObtener")
    public ResponseEntity<ResponseController<RoleObtainOutputDTO>> getRoleData(@RequestBody @Valid RoleObtainInputDTO.Root request) {
        try {
            // Null field validation handled by @Valid on request.
            Integer rolId = request.getPrefix().getInput().getRolId();

            RoleObtainOutputDTO output = roleObtainService.getRoleData(rolId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception ex) {
            logger.error("An error occurred while getting role data.", ex);
            RoleObtainOutputDTO errorOutput = new RoleObtainOutputDTO();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(ex.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
