package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.NotificationReadInput;
import com.maersk.sd1.seg.controller.dto.NotificationReadOutputDTO;
import com.maersk.sd1.seg.service.NotificationReadService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/security/SistemaServiceImp")
public class NotificationReadController {

    private static final Logger logger = LogManager.getLogger(NotificationReadController.class.getName());

    private final NotificationReadService notificationReadService;

    @PostMapping("/segnotificacionLeer")
    public ResponseEntity<ResponseController<NotificationReadOutputDTO>> processNotificationJobRegistrar(
            @RequestBody NotificationReadInput.Root input){
        try{
            NotificationReadOutputDTO response = notificationReadService.processNotificationRead(input);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request."));
        }
    }
}