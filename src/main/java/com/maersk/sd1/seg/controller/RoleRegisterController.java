package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.RoleRegisterInput;
import com.maersk.sd1.seg.dto.RoleRegisterOutput;
import com.maersk.sd1.seg.service.RoleRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolServiceImp")
public class RoleRegisterController {

    private static final Logger logger = LogManager.getLogger(RoleRegisterController.class);

    private final RoleRegisterService roleRegisterService;

    @Autowired
    public RoleRegisterController(RoleRegisterService roleRegisterService) {
        this.roleRegisterService = roleRegisterService;
    }

    @PostMapping("/segrolRegistrar")
    public ResponseEntity<ResponseController<RoleRegisterOutput>> registerRole(@RequestBody @Valid RoleRegisterInput.Root request) {
        try {
            RoleRegisterInput.Input input = request.getPrefix().getInput();
            RoleRegisterOutput output = roleRegisterService.registerRole(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while registering the Role.", e);
            RoleRegisterOutput output = new RoleRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}

