package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.criteria.CriteriaBuilder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SystemValidationOutput {

    @JsonProperty("version_details")
    private VersionDetails versionDetails;

    @JsonProperty("result")
    private Output output;

    @Data
    public static class VersionDetails {
        @JsonProperty("version")
        private String version;

        @JsonProperty("nivel")
        private Character level;

        @JsonProperty("fecha_despliegue")
        private LocalDateTime deploymentDate;
    }

    @Data
    public static class Output {
        private Integer notificationId;
        private int type;
        private String icon;
        private String title;
        private String subtitle;
        private String description;
        private Character level;
        private Character onlyAlert;
        private Character onlyTime;
        private LocalDateTime expirationDate;
        private LocalDateTime registrationDate;
        private String urlReference;
        private String template;
        private Integer read;
    }
}