package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.EditRoleInput;
import com.maersk.sd1.seg.dto.EditRoleOutput;
import com.maersk.sd1.seg.service.EditRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolServiceImp")
@RequiredArgsConstructor
public class RoleController {


    private final EditRoleService editRoleService;

    @PostMapping("/segrolEditar")
    public ResponseEntity<ResponseController<Object[]>> editRole(@RequestBody EditRoleInput.Root input){
        EditRoleOutput response = editRoleService.editRole(input);
        ResponseController<Object[]> controllerResponse = new ResponseController<>(response.toResultArray());
        return ResponseEntity.ok(controllerResponse);
    }
}
