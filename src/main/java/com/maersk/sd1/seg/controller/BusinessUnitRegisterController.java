package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.BusinessUnitRegisterInput;
import com.maersk.sd1.seg.dto.BusinessUnitRegisterOutput;
import com.maersk.sd1.seg.service.BusinessUnitRegisterService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUnidadNegocioServiceImp")
@Log4j2
public class BusinessUnitRegisterController {

    private final BusinessUnitRegisterService businessUnitRegisterService;

    @PostMapping("/segunidadNegocioRegistrar")
    public ResponseEntity<ResponseController<BusinessUnitRegisterOutput>> registerBusinessUnit(@RequestBody @Valid BusinessUnitRegisterInput.Root request) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                log.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            log.info("Request received registerBusinessUnit: {}", request);
            BusinessUnitRegisterInput.Input input = request.getPrefix().getInput();

            BusinessUnitRegisterOutput result = businessUnitRegisterService.registerBusinessUnit(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            log.error("An error occurred while processing the request.", e);
            BusinessUnitRegisterOutput output = new BusinessUnitRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
