package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.LoginCounterUpdateRequestDTO;
import com.maersk.sd1.seg.dto.LoginCounterUpdateResponseDTO;
import com.maersk.sd1.seg.service.LoginCounterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/module/seg/login")
public class LoginCounterController {

    private final LoginCounterService loginCounterService;

    @Autowired
    public LoginCounterController(LoginCounterService loginCounterService) {
        this.loginCounterService = loginCounterService;
    }

    @PostMapping("/updateCounter")
    public ResponseEntity<LoginCounterUpdateResponseDTO.Root> updateLoginCounter(@RequestBody LoginCounterUpdateRequestDTO.Root request) {

        LoginCounterUpdateResponseDTO.Root response = loginCounterService.updateLoginCounter(request);

        if (response.getPrefix().getOutput().getAttempts() != null) {

            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {

            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        }
    }
}
