package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.RoleListInputDTO;
import com.maersk.sd1.seg.dto.RoleListOutputDTO;
import com.maersk.sd1.seg.service.RoleListService;
import com.maersk.sd1.common.controller.dto.ResponseController; // Example import if following the sample's pattern
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller to handle listing Roles analogous to the stored procedure seg.rol_listar.
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolServiceImp")
public class RoleListController {

    private static final Logger logger = LogManager.getLogger(RoleListController.class);

    private final RoleListService roleListService;

    @PostMapping("/segrolListar")
    public ResponseEntity<ResponseController<RoleListOutputDTO>> listarRoles(@RequestBody @Valid RoleListInputDTO.Root request) {
        try {
            RoleListOutputDTO outputDTO = roleListService.listarRoles(request);
            return ResponseEntity.ok(new ResponseController<>(outputDTO));
        } catch (Exception e) {
            logger.error("Error in RoleListController.listarRoles", e);
            RoleListOutputDTO errorOutput = new RoleListOutputDTO();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(e.getMessage());
            errorOutput.setTotalRegistros(0L);
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}