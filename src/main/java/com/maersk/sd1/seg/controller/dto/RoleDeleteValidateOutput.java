package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class RoleDeleteValidateOutput {

    @JsonProperty("titulo")
    private List<String> menus;

    @JsonProperty("users")
    private List<String> users;

    @JsonProperty("titulo_2")
    private List<String> notifications;

    @JsonProperty("nombre")
    private List<String> reports;

    @JsonProperty("nombre_2")
    private List<String> businessUnits;

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;
}