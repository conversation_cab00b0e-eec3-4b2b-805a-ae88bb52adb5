package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class UserValidateInput {

    @Data
    public static class Input {

        @JsonProperty("correo")
        private String email;

        @JsonProperty("id")
        private String id;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SEG")
        private Prefix prefix;

    }
}