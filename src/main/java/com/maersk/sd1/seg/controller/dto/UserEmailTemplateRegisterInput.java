package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

public class UserEmailTemplateRegisterInput {

    @Data
    public static class EmailConfig {
        @JsonProperty("email_plantilla_id")
        @NotNull
        private Integer emailTemplateId;

        @JsonProperty("habilitado")
        @NotNull
        private Character habilitado;

        @JsonProperty("estado")
        @NotNull
        private Character status;
    }

    @Data
    public static class Input {
        @JsonProperty("usuario_id")
        @NotNull
        private Integer userId;

        @JsonProperty("plantillas")
        @NotNull
        @Size(min = 1, message = "Must contain at least one email configuration.")
        private String template;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        @NotNull
        private Prefix prefix;
    }

    private UserEmailTemplateRegisterInput(){
        // Private constructor to hide the implicit public one
    }
}