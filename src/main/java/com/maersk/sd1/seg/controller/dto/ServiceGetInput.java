package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ServiceGetInput {
    @Data
    public static class Input {
        @NotNull
        @JsonProperty("servicio_id")
        private Integer serviceId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }

    private ServiceGetInput() {
        // Private constructor to hide the implicit public one
    }
}