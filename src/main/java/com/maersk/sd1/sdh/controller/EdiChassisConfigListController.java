package com.maersk.sd1.sdh.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdh.controller.dto.EdiChassisConfigListInput;
import com.maersk.sd1.sdh.controller.dto.EdiChassisConfigListOutput;
import com.maersk.sd1.sdh.service.EdiChassisConfigListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("ModuleSDH/module/sdh/SDHEdiChassisConfigServiceImp/")
public class EdiChassisConfigListController {

    private static final Logger logger = LogManager.getLogger(EdiChassisConfigListController.class);

    private final EdiChassisConfigListService ediChassisConfigListService;

    @PostMapping("/sdhediChassisConfigList")
    public ResponseEntity<ResponseController<EdiChassisConfigListOutput>> getChassisConfigList(@Valid @RequestBody EdiChassisConfigListInput.Root request) {
        try {
            EdiChassisConfigListInput.Input input = request.getPrefix().getInput();
            EdiChassisConfigListOutput response = ediChassisConfigListService.listEdiChassisConfig(input);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("An error occurred while fetching EDI chassis config list.", e);
            EdiChassisConfigListOutput output = new EdiChassisConfigListOutput();
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}