package com.maersk.sd1.sdh.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdh.dto.DocumentChassisDeleteGateInInput;
import com.maersk.sd1.sdh.dto.DocumentChassisDeleteGateInOutput;
import com.maersk.sd1.sdh.service.DocumentChassisDeleteGateInService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDH/SDH/module/sdh/SDHChassisDocumentationServiceImp")
public class DocumentChassisDeleteGateInController {

    private static final Logger logger = LogManager.getLogger(DocumentChassisDeleteGateInController.class);

    private final DocumentChassisDeleteGateInService documentChassisDeleteGateInService;

    @PostMapping("/sdhdocumentChassisDeleteGateIn")
    public ResponseEntity<ResponseController<DocumentChassisDeleteGateInOutput>> sdgDocumentChassisDeleteGateIn(
            @RequestBody @Valid DocumentChassisDeleteGateInInput.Root request) {
        DocumentChassisDeleteGateInOutput output = new DocumentChassisDeleteGateInOutput();
        try {
            DocumentChassisDeleteGateInInput.Input input = request.getPrefix().getInput();
            output = documentChassisDeleteGateInService.deleteGateIn(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing deleteGateIn.", e);
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
