package com.maersk.sd1.sdh.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdh.dto.ChassisRegisterInput;
import com.maersk.sd1.sdh.dto.ChassisRegisterOutput;
import com.maersk.sd1.sdh.service.ChassisRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDH/module/sdh/SDHChassisDocumentationService")
public class ChassisRegisterController {

    private static final Logger logger = LogManager.getLogger(ChassisRegisterController.class.getName());

    private final ChassisRegisterService chassisRegisterService;

    @PostMapping("/sdhemptyReferenceChassisRegister")
    public ResponseEntity<ResponseController<ChassisRegisterOutput>> sdgChassisRegister(@RequestBody @Valid ChassisRegisterInput.Root request) {
        try {
            logger.info("Request received sdgChassisRegister: {}", request);
            ChassisRegisterInput.Input input = request.getPrefix().getInput();
            ChassisRegisterOutput output = chassisRegisterService.registerChassisInReference(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing sdgChassisRegister.", e);
            ChassisRegisterOutput output = new ChassisRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

