package com.maersk.sd1.sdh.service;

import com.maersk.sd1.common.model.ChassisEdiSetting;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.sdh.controller.dto.EdiChassisConfigListInput;
import com.maersk.sd1.sdh.controller.dto.EdiChassisConfigListOutput;
import com.maersk.sd1.sdh.controller.dto.EdiChassisConfigListOutput.Record;
import com.maersk.sd1.common.repository.ChassisEdiSettingRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
public class EdiChassisConfigListService {

    private static final Logger logger = LogManager.getLogger(EdiChassisConfigListService.class);

    private final ChassisEdiSettingRepository chassisEdiSettingRepository;

    private final CatalogLanguageRepository catalogLanguageRepository;

    @Transactional(readOnly = true)
    public EdiChassisConfigListOutput listEdiChassisConfig(EdiChassisConfigListInput.Input input) {
        EdiChassisConfigListOutput output = new EdiChassisConfigListOutput();
        try {
            // Validate fields
            if (Objects.isNull(input.getLanguageId())) {
                throw new IllegalArgumentException("language_id cannot be null.");
            }

            // Convert date parameters to LocalDateTime
            LocalDateTime registerDateMin = null;
            if (input.getRegisterDateMin() != null) {
                registerDateMin = input.getRegisterDateMin().atStartOfDay();
            }

            LocalDateTime registerDateMax = null;
            if (input.getRegisterDateMax() != null) {
                registerDateMax = input.getRegisterDateMax().atTime(23, 59, 59);
            }

            // Normalize empty string to null for JPQL query
            String deliverySystemName = input.getDeliverySystemName();
            if (deliverySystemName != null && deliverySystemName.isEmpty()) {
                deliverySystemName = null;
            }

            // Build pagination
            int pageNum = 0; // Default page (0-indexed)
            int pageSize = Integer.MAX_VALUE; // Default size

            if (input.getPage() != null && input.getSize() != null && input.getPage() > 0 && input.getSize() > 0) {
                pageNum = input.getPage() - 1; // Convert 1-indexed page to 0-indexed
                pageSize = input.getSize();
            }

            Pageable pageable = PageRequest.of(pageNum, pageSize);

            // Execute JPQL query
            Page<ChassisEdiSetting> pageResult = chassisEdiSettingRepository.findByFilters(
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getOwnerChassisId(),
                    deliverySystemName,
                    registerDateMin,
                    registerDateMax,
                    input.getActive(),
                    pageable
            );

            List<Record> records = new ArrayList<>();

            // Calculate base index for the current page
            int baseIndex = pageNum * pageSize;

            // Map entities to output records
            int index = 0;
            for (ChassisEdiSetting setting : pageResult.getContent()) {
                Record rec = new Record();

                // Set _key field based on absolute position in the result set
                setRecordKey(rec, baseIndex + index + 1); // +1 because keys start from 1
                index++;

                rec.setChassisEdiSettingId(setting.getId());
                if (setting.getBusinessUnit() != null) {
                    rec.setBusinessUnitId(setting.getBusinessUnit().getId());
                    rec.setBusinessUnit(setting.getBusinessUnit().getName());
                }
                if (setting.getSubBusinessUnit() != null) {
                    rec.setSubBusinessUnitId(setting.getSubBusinessUnit().getId());
                    rec.setSubBusinessUnit(setting.getSubBusinessUnit().getName());
                }
                if (setting.getChassisOwnerCompany() != null) {
                    rec.setChassisOwnerCompanyId(setting.getChassisOwnerCompany().getId());
                    rec.setChassisOwner(setting.getChassisOwnerCompany().getLegalName());
                }
                rec.setDeliverySystemName(setting.getDeliverySystemName());

                if (setting.getCatChassisEdiSendMode() != null) {
                    String sendMode = catalogLanguageRepository.fnCatalogoTraducidoDesLarga(setting.getCatChassisEdiSendMode().getId(), input.getLanguageId());
                    rec.setSendMode(sendMode);
                }

                rec.setActive(setting.getActive());
                if (setting.getRegistrationUser() != null) {
                    rec.setUserRegistrationId(setting.getRegistrationUser().getId());
                    rec.setUserNameRegister(setting.getRegistrationUser().getNames());
                    // Combine last names
                    StringBuilder sb = new StringBuilder();
                    sb.append(setting.getRegistrationUser().getFirstLastName() == null ? "" : setting.getRegistrationUser().getFirstLastName());
                    if (setting.getRegistrationUser().getSecondLastName() != null) {
                        sb.append(" ").append(setting.getRegistrationUser().getSecondLastName());
                    }
                    rec.setUserLastnameRegister(sb.toString().trim());
                }
                rec.setRegistrationDate(setting.getRegistrationDate());
                if (setting.getModificationUser() != null) {
                    rec.setUserModificationId(setting.getModificationUser().getId());
                    rec.setUserNameModification(setting.getModificationUser().getNames());
                    StringBuilder sb2 = new StringBuilder();
                    sb2.append(setting.getModificationUser().getFirstLastName() == null ? "" : setting.getModificationUser().getFirstLastName());
                    if (setting.getModificationUser().getSecondLastName() != null) {
                        sb2.append(" ").append(setting.getModificationUser().getSecondLastName());
                    }
                    rec.setUserLastnameModification(sb2.toString().trim());
                }
                rec.setModificationDate(setting.getModificationDate());

                records.add(rec);
            }

            output.setRecords(records);
            output.setTotalRecords(List.of(List.of(pageResult.getTotalElements())));
        } catch (Exception e) {
            logger.error("An error occurred in listEdiChassisConfig.", e);
            output.setTotalRecords(List.of(List.of(0L)));
            output.setRecords(null);
        }
        return output;
    }

    // Helper method to set the _key field using reflection
    private void setRecordKey(Record record, int key) {
        record.setKey(key); // Assuming `setKey()` exists in `Record`
    }
}