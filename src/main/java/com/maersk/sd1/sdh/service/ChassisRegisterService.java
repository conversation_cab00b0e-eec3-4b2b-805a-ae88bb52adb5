package com.maersk.sd1.sdh.service;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.model.Chassis;
import com.maersk.sd1.common.model.ChassisDocument;
import com.maersk.sd1.common.model.ChassisDocumentDetail;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.ChassisRepository;
import com.maersk.sd1.common.repository.ChassisDocumentRepository;
import com.maersk.sd1.common.repository.ChassisDocumentDetailRepository;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sdh.dto.ChassisRegisterInput;
import com.maersk.sd1.sdh.dto.ChassisRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
// [sdh].[empty_reference_chassis_register]
public class ChassisRegisterService {

    private static final Logger logger = LogManager.getLogger(ChassisRegisterService.class);

    private final ChassisRepository chassisRepository;
    private final ChassisDocumentRepository chassisDocumentRepository;
    private final ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final CatalogRepository catalogRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public ChassisRegisterOutput registerChassisInReference(ChassisRegisterInput.Input input) {
        ChassisRegisterOutput output = new ChassisRegisterOutput();
        try {
            // 1) Get parent business unit
            Optional<Integer> parentBuOpt = businessUnitRepository.findParentBusinessUnitIdOrNull(input.getSubBusinessUnitLocalId());
            if (parentBuOpt.isEmpty()) {
                // If we can't find a parent, treat it like an error.
                String msgNotFound = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 2, input.getLanguageId());
                output.setRespEstado(2);
                output.setRespMensaje(msgNotFound);
                return output;
            }
            Integer subBusinessUnitId = parentBuOpt.get();

            // 2) Check if we have a pending reference
            //    We know the movement input alias is '43080' from sp.
            Optional<Catalog> movementCatalogOpt = catalogRepository.findByAliasOrNull("43080");
            if (movementCatalogOpt.isEmpty()) {
                // Movement catalog not found => error
                String msgMovement = messageLanguageRepository.fnTranslatedMessage("REGISTER_CHASSIS_IN_REFERENCE", 2, input.getLanguageId());
                output.setRespEstado(2);
                output.setRespMensaje(msgMovement);
                return output;
            }
            Integer movementTypeId = movementCatalogOpt.get().getId();

            // We also need the 'sd1_chassisdoc_status_pending' alias
            Optional<Catalog> pendingCatalogOpt = catalogRepository.findByAliasOrNull("sd1_chassisdoc_status_pending");
            if (pendingCatalogOpt.isEmpty()) {
                String msgPending = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 2, input.getLanguageId());
                output.setRespEstado(2);
                output.setRespMensaje(msgPending);
                return output;
            }

            // Checking if there's a pending reference for this chassis in the provided sub BU
            // We'll do a custom query that joins detail & doc & chassis & cat status, but to simplify,
            // we can search in ChassisDocumentDetail or we just replicate the logic here.
            Optional<String> possiblyPendingRef = findPendingReferenceNumber(input.getChassisNumber(), subBusinessUnitId, "sd1_chassisdoc_status_pending", "43080");
            if (possiblyPendingRef.isPresent()) {
                // Found a pending reference, so resp_state=2 and a msg with replacements.
                String rawMsg = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 4, input.getLanguageId());
                String finalMsg = rawMsg
                        .replace("{CHAX}", input.getChassisNumber())
                        .replace("{REFX}", possiblyPendingRef.get());
                output.setRespEstado(2);
                output.setRespMensaje(finalMsg);
                return output;
            }

            Optional<ChassisDocument> docChassisOpt = chassisDocumentRepository.findActiveDocumentChassis(
                    input.getReferenceNumber(),
                    subBusinessUnitId,
                    movementTypeId,
                    input.getReferenceType()
            );

            if (docChassisOpt.isEmpty()) {
                // docChassis not found => set state=2, message code=1
                String msgDocNotFound = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 1, input.getLanguageId());
                output.setRespEstado(2);
                output.setRespMensaje(msgDocNotFound);
                return output;
            }

            ChassisDocument docChassis = docChassisOpt.get();

            // 4) find or create the chassis
            Optional<Chassis> existingChassisOpt = chassisRepository.findByChassisNumber(input.getChassisNumber());

            Chassis targetChassis;
            if (existingChassisOpt.isEmpty()) {
                // Insert chassis
                // cat_unidad_medida_peso = 48727 from sp
                Optional<Catalog> catTareUnitOpt = catalogRepository.findById(48727);
                Optional<Catalog> catSourceCreationOpt = catalogRepository.findByAliasOrNull("sd1_creationsource_chassis_gate");
                if (catTareUnitOpt.isEmpty() || catSourceCreationOpt.isEmpty()) {
                    String msgCat = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 2, input.getLanguageId());
                    output.setRespEstado(2);
                    output.setRespMensaje(msgCat);
                    return output;
                }

                Catalog tareUnit = catTareUnitOpt.get();
                Catalog sourceCreation = catSourceCreationOpt.get();

                Catalog catChassisType = new Catalog(input.getCatChassisTypeId().intValue());
                BusinessUnit businessUnit = new BusinessUnit();

                targetChassis = new Chassis();
                targetChassis.setChassisNumber(input.getChassisNumber());
                targetChassis.setBusinessUnit(businessUnit);
                targetChassis.setTare(java.math.BigDecimal.ZERO);
                targetChassis.setCatTareUnit(tareUnit);
                targetChassis.setCatChassisType(catChassisType);
                targetChassis.setActive(true);
                targetChassis.setRegistrationUser(null); // We'll assume user entity is set somewhere else
                targetChassis.setRegistrationDate(LocalDateTime.now());
                targetChassis.setTraceChassis("ins_by_gral_gate_in");
                targetChassis.setCatSourceCreationChassis(sourceCreation);
                // docChassis.getOwnerCompany() might define the owner. The sp uses @owner_company_id.
                // We'll replicate the logic.
                targetChassis.setOwnerCompany(docChassis.getOwnerCompany());
                // We'll also set user_registration_id as we have no direct user object here.
                // Possibly skip storing user. In real code we might fetch the user from a userRepo.

                chassisRepository.save(targetChassis);
            } else {
                // If found, update active=1 if needed
                targetChassis = existingChassisOpt.get();
                if (!targetChassis.getActive()) {
                    targetChassis.setActive(true);
                    targetChassis.setTraceChassis("upd_by_gral_gate_in");
                    targetChassis.setModificationDate(LocalDateTime.now());
                    // same note about user_modification_id.
                }
                chassisRepository.save(targetChassis);
            }

            // 5) Insert doc chassis detail
            // We also need cat_source_creation, cat_status_chassis
            Optional<Catalog> catSourceCreationDocOpt = catalogRepository.findByAliasOrNull("sd1_creationsource_chassis_gate");
            Optional<Catalog> catStatusPendingOpt = catalogRepository.findByAliasOrNull("sd1_chassisdoc_status_Pending");

            if (catSourceCreationDocOpt.isEmpty() || catStatusPendingOpt.isEmpty()) {
                String errorMsg = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 2, input.getLanguageId());
                output.setRespEstado(2);
                output.setRespMensaje(errorMsg);
                return output;
            }

            ChassisDocumentDetail detail = new ChassisDocumentDetail();
            detail.setChassisDocument(docChassis);
            detail.setChassis(targetChassis);
            detail.setTransportCompany(null);
            detail.setDateEta(null);
            detail.setCatSourceCreation(catSourceCreationDocOpt.get());
            detail.setCatStatusChassis(catStatusPendingOpt.get());
            detail.setActive(true);
            detail.setRegistrationUser(null); // same note about user
            detail.setRegistrationDate(LocalDateTime.now());
            detail.setTraceChassisDocDetail("create_gral_gate_in");

            chassisDocumentDetailRepository.save(detail);

            // 6) Everything success => set state=1, message= code=3
            String okMsgRaw = messageLanguageRepository.fnTranslatedMessage(Constants.REGISTER_CHASSIS_IN_REFERENCE, 3, input.getLanguageId());
            output.setRespEstado(1);
            output.setRespMensaje(okMsgRaw);
        } catch (Exception e) {
            logger.error("Error in registerChassisInReference", e);
            // In the sp, we log and set state=0.
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }

    // We replicate the pending reference check from the sp, with a JPA approach.
    private Optional<String> findPendingReferenceNumber(String chassisNumber, Integer subBusinessUnitId,
                                                        String statusAlias, String movementAlias) {
        // This query basically does:
        //  FROM sdh.document_chassis (cd)
        //  JOIN sdh.document_chassis_detail (dd) ON dd.document_chassis_id = cd.document_chassis_id
        //  JOIN sdh.chassis (c) ON c.chassis_id = dd.chassis_id
        //  JOIN ges.catalogo cat ON cat.catalogo_id = dd.cat_status_chassis_id
        //  WHERE c.chassis_number = chassisNumber
        //    AND cd.sub_business_unit_id = subBusinessUnitId
        //    AND cd.cat_movement_type_id = (SELECT id FROM Catalog WHERE alias=movementAlias)
        //    AND cat.alias = 'sd1_chassisdoc_status_pending'
        //    AND cd.active = 1 and dd.active = 1

        // We'll do it programmatically for clarity.
        Optional<Catalog> catMovementOpt = catalogRepository.findByAliasOrNull(movementAlias);
        if (catMovementOpt.isEmpty()) {
            return Optional.empty();
        }
        Optional<Catalog> catPendingOpt = catalogRepository.findByAliasOrNull(statusAlias);
        if (catPendingOpt.isEmpty()) {
            return Optional.empty();
        }
        Integer movementId = catMovementOpt.get().getId();
        Integer statusId = catPendingOpt.get().getId();

        // Now we look for a chassisDocumentDetail that meets these conditions.
        // We'll do a small loop or direct matching from the repo.
        // For demonstration, we can search all docChassis with movementId, subBU, active=1, then filter.

        // This is not the best for performance, but to keep purely JPA without a complicated query,
        // we illustrate here. In a real project we'd annotate a custom query in a detail repository.

        // We'll do a direct approach with docChassisRepository findAll. We'll filter for active=1, subBU= etc.
        // Then from each doc, we check details.

        return chassisDocumentRepository.findAll().stream()
                .filter(cd -> Boolean.TRUE.equals(cd.getActive()))
                .filter(cd -> cd.getCatMovementType() != null && cd.getCatMovementType().getId().equals(movementId))
                .filter(cd -> cd.getSubBusinessUnit() != null && cd.getSubBusinessUnit().getId().equals(subBusinessUnitId))
                .flatMap(cd -> cd.getId() == null ?
                        java.util.stream.Stream.empty() :
                        chassisDocumentDetailRepository.findAll().stream()
                                .filter(dd -> dd.getChassisDocument() != null && dd.getChassisDocument().getId().equals(cd.getId()))
                                .filter(dd -> Boolean.TRUE.equals(dd.getActive()))
                                .filter(dd -> dd.getCatStatusChassis() != null && dd.getCatStatusChassis().getId().equals(statusId))
                                .filter(dd -> dd.getChassis() != null && chassisNumber.equals(dd.getChassis().getChassisNumber()))
                                .map(dd -> dd.getChassisDocument().getDocumentChassisNumber())
                )
                .findFirst();
    }
}
