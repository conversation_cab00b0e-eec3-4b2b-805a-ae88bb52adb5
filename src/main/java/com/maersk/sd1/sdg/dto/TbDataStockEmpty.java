package com.maersk.sd1.sdg.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TbDataStockEmpty {
    private Integer businessUnitId; // a.unidad_negocio_id
    private Integer subBusinessUnitId; // a.sub_unidad_negocio_id
    private Integer programacionNaveDetalleId; // a.programacion_nave_detalle_id
    private Integer contenedorId; // a.contenedor_id
    private Integer chassisId; // NULL as chassis_id (optional)
    private Integer catEmptyFullId; // a.cat_empty_full_id
    private String local; // Tlocal.nombre AS Local
    private Integer eirId; // A.eir_id
    private Date fechaIngresoCamion; // A.fecha_ingreso_camion
    private Date fechaSalidaCamion; // A.fecha_salida_camion
    private String contenedor; // cnt.numero_contenedor AS [Contenedor]
    private Integer catTamanoId; // cnt.cat_tamano_id
    private Integer catTipoContenedorId; // cnt.cat_tipo_contenedor_id
    private Integer isContainer; // @is_container
    private Integer catClaseId; // cnt.cat_clase_id
    private Integer codigoIsoId; // cnt.codigo_iso_id
    private Integer shippingLine; // cnt.linea_naviera_id AS shipping_line
    private Integer chassisOwnerCompanyId; // NULL as chassis_owner_company_id
    private Date fechaRegistro; // a.fecha_registro
    private String seals; // ISNULL(precinto_1,'') + ... AS seals
    private String observacion; // a.observacion
    private Integer catCargoDocumentTypeId; // NULL as cat_cargo_document_type_id
    private String cargoDocument; // NULL as cargo_document
    private String shipperNro; // NULL as shipper_nro
    private String shipperName; // NULL as shipper_name
    private String consigneeNro; // NULL as consignee_nro
    private String consigneeName; // NULL as consignee_name
    private Integer operationType; // NULL as operation_type // CHECK
    private Integer productoId; // NULL as producto_id
    private Integer eirChassisId; // A.eir_chassis_id
    private Integer catStructureConditionId; // sdg.fn_GetEquipmentConditionID(a.eir_id,@is_container,'S','CUR')
    private Integer catMachineryConditionId; // sdg.fn_GetEquipmentConditionID(a.eir_id,@is_container,'M','CUR')
    private Boolean flagChassisStayed; // a.flag_chassis_stayed
    private Integer filter; // 0 AS filter //CHECK
    private String inspectorComment; // NULL as inspector_comment
    private Integer potencialFoodAid; // 0 AS potencial_food_aid
    private String ginComment; // a.observacion AS gin_comment
    private Integer usdaApproved; // 0 AS USDA_approved
    private Integer catStructureConditionInspId; // sdg.fn_GetEquipmentConditionID(a.eir_id,@is_container,'S','INS')
    private Integer catMachineryConditionInspId; // sdg.fn_GetEquipmentConditionID(a.eir_id,@is_container,'M','INS')
    private String bookingPreAllocation; // NULL AS booking_pre_allocation


    public Integer getCatEquipmentCategory() {
        return isContainer;
    }

    public void setCatEquipmentCategory(Integer catClaseId) {
        this.catClaseId = catClaseId;
    }

    public Integer getContainerId() {
        return contenedorId;
    }

    public void setContainerId(Integer contenedorId) {
        this.contenedorId = contenedorId;
    }

    public Integer getId() {
        return eirId;
    }

    public void setId(Integer eirId) {
        this.eirId = eirId;
    }

    public Integer getEquipmentSizeId() {
        return catTamanoId;
    }

    public void setEquipmentSizeId(Integer catTamanoId) {
        this.catTamanoId = catTamanoId;
    }

    public Integer getEquipmentTypeId() {
        return catTipoContenedorId;
    }

    public void setEquipmentTypeId(Integer catTipoContenedorId) {
        this.catTipoContenedorId = catTipoContenedorId;
    }

    public Integer getShippingLineId() {
        return shippingLine;
    }

    public void setShippingLineId(Integer shippingLine) {
        this.shippingLine = shippingLine;
    }

    public String getEquipmentNumber() {
        return contenedor;
    }

    public void setEquipmentNumber(String contenedor) {
        this.contenedor = contenedor;
    }
}
