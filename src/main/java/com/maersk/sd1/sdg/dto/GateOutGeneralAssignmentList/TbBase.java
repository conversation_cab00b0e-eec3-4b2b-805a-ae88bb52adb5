package com.maersk.sd1.sdg.dto.GateOutGeneralAssignmentList;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbBase {
    Integer subBusinessUnitId;
    Integer subBusinessUnitLocalId;
    Integer bookingGoeId;
    String numeroBookingGoe;
    Integer catDocumentTypeGoeId;
    Integer documentoCargaGofId;
    String documentoCargaGof;
    Integer catDocumentTypeGofId;
    Integer catEstadoDocumentoCargaId;
    String vesselVoyage;
    Integer catOperacionId;
    Integer operationTypeId;
    Integer catMovimientoId;
    Integer catEmptyFullId;
    Integer catProcedenciaId;
    Integer eirId;
    String placa;
    Integer empresaTransporteId;
    LocalDateTime fechaIngresoCamion;
    Integer contenedorId;
    Integer controlAsignacionLight;
    Integer lineaNavieraId;
    Integer programacionNaveDetalleId;
    LocalDateTime fechaCutoffRetiroVacioDry;
    LocalDateTime fechaCutoffRetiroVacioReefer;
    Integer catEstadoBooking;
    String numeroDocumentoExterno;
    Integer eirChassisId;
    Integer documentChassisGoId;
    String chassisAllocationControl;
    Integer catChassisDocumentType;
    String documentChassisNumber;
    Integer catChassisOperationTypeId;
    Integer personaConductorId;
    Integer empresaEmbarcadorId;
    Integer empresaConsignatarioId;
    Boolean withContainerNumer;
    Boolean findContainer;
    Integer containerFullId;
    String containerNumber;
    String containerType;
    String isoCode;
    String grade;
    String tara;
    String payload;
    String reeferType;
    Integer valueReceivedWeight;
    Integer catReceivedWeightMeasureId;
    String seal1;
    String seal2;
    String seal3;
    String seal4;
    Boolean findChassis;
    Integer chassisId;
    String chassisNumber;
    String ownerCompany;
    String observation;
    String preAllocatedContainer;
    String remarkRule;
}
