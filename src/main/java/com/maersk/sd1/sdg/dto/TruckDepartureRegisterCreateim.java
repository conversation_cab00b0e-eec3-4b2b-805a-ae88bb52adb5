package com.maersk.sd1.sdg.dto;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Cell;
import com.maersk.sd1.common.model.Level;
import com.maersk.sd1.common.model.Yard;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TruckDepartureRegisterCreateim {
    private Yard yardId;
    private Block blockId;
    private Cell cellId;
    private Level levelId;
}
