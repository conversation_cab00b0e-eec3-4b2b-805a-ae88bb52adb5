package com.maersk.sd1.sdg.dto;

import lombok.Data;

import java.util.Date;

@Data
public class TruckArrivalRejectProcessDTO {
    private Integer subUnidadNegocioId;
    private String containerNumber;
    private String chassisNumber;
    private String typeMovement;
    private String emptyFullProcess;
    private String CatTypeMovementId;
    private String EmptyFullProcessId;
    private String truckingCompany;
    private String driverName;
    private Date dateRegistrationDesde;
    private Date dateRegistrationHasta;
    private Integer BusinessUnitId;
    private Integer pfIdiomaId;
    private Integer pagina;
    private Integer cantidad;
}
