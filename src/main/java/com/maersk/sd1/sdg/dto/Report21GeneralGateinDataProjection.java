package com.maersk.sd1.sdg.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface Report21GeneralGateinDataProjection {
    BigDecimal getBusinessUnitId();
    BigDecimal getSubBusinessUnitId();
    Integer getProgramacionNaveDetalleId();
    Integer getContainerId();
    Integer getChassisId();
    BigDecimal getCatEmptyFullId();
    String getTipoMov();
    String getLocal();
    Integer getEirContainerId();
    LocalDateTime getFechaIngresoCamion();
    LocalDateTime getFechaSalidaCamion();
    String getEquipmentNumber();
    String getEquipmentSizeType();
    BigDecimal getCatEquipmentCategory();
    Integer getIsocodeId();
    String getIsocodeNumber();
    String getOwnerPropietario();
    String getReefer();
    Integer getVehiculeId();
    String getPlateTruckNumber();
    BigDecimal getTransportCompanyId();
    String getTransportCompanyCode();
    String getTransportCompanyName();
    String getTransportCompanyScac();
    Integer getDriverId();
    String getDriverDoc();
    String getDriverName();
    BigDecimal getUserRegisterId();
    String getUserRegisterName();
    LocalDateTime getFechaRegistro();
    String getSeals();
    String getObservacion();
    BigDecimal getCatCargoDocumentTypeId();
    String getCargoDocumentNumber();
    String getShipperNro();
    String getShipperName();
    String getConsigneeNro();
    String getConsigneeName();
    BigDecimal getOperationTypeId();
    String getOperationTypeName();
    Boolean getIsShow();
    Integer getUsuarioSalidaCamion();
    Integer getProductoId();
    LocalDateTime getGateOutDate();
    Integer getEirChassisId();
    String getEirChassisNumber();
    BigDecimal getCatStructureConditionId();
    BigDecimal getCatMachineryConditionId();
    String getRefChassisNumber();
    String getOperationGroupType();
    BigDecimal getGradeId();
    Boolean getFlagChassisPickup();
    String getNumeroTwr();
}