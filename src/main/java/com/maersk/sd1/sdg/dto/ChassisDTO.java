package com.maersk.sd1.sdg.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ChassisDTO {

    private Integer chassisId;
    private Integer documentChassisDetailId;
    private Integer chassisTypeId;

    @JsonCreator
    public ChassisDTO(@JsonProperty("chassis_id") Integer chassisId,
                      @JsonProperty("document_chassis_detail_id") Integer documentChassisDetailId,
                      @JsonProperty("chassis_type_id") Integer chassisTypeId) {
        this.chassisId = chassisId;
        this.documentChassisDetailId = documentChassisDetailId;
        this.chassisTypeId = chassisTypeId;
    }
}