package com.maersk.sd1.sdg.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class ContainerBookingAvailavilityDto {
    private Integer eirId;
    private Integer bookingId;
    private Integer containerSizeId;

    private Integer containerTypeId;

    private Long reservationQuantity;
    private Long inProgressQuantity;
    private Long completedQuantity;
    private Boolean isReefer;


}
