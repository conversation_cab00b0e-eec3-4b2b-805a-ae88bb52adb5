package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class TruckDepartureRejectContainerFindInput {

    @Data
    public static class Input {
        @JsonProperty("eir_id")
        @NotNull(message = "EIR ID must not be null")
        private Integer eirId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "Input must not be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        @NotNull(message = "SDG must not be null")
        @Valid
        private Prefix sdg;

        public Input getInput() {
            return sdg != null ? sdg.getInput() : null;
        }
    }

}