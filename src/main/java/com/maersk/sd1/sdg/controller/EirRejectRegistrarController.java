package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.EirRejectRegistrarInputDTO;
import com.maersk.sd1.sdg.controller.dto.EirRejectRegistrarOutputDTO;
import com.maersk.sd1.sdg.service.EirRejectRegistrarService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDG/module/sdg/SDGEIRServiceImp")
@RequiredArgsConstructor
public class EirRejectRegistrarController {

    private static final Logger logger = LogManager.getLogger(EirRejectRegistrarController.class.getName());
    private final EirRejectRegistrarService eirRejectRegistrarService;

    @PostMapping("/sdseirRejectRegistrar")
    public ResponseEntity<ResponseController<EirRejectRegistrarOutputDTO>> processEirRejectRegistrar(
            @RequestBody EirRejectRegistrarInputDTO.Root request) {

        try {
            EirRejectRegistrarOutputDTO rejectRegistrarOutputDTO = eirRejectRegistrarService.processEirRejectRegistrar(request);
            return ResponseEntity.ok(new ResponseController<>(rejectRegistrarOutputDTO));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            EirRejectRegistrarOutputDTO rejectRegistrarOutputDTO = EirRejectRegistrarOutputDTO.builder()
                    .statusMessage(e.toString())
                    .newId(0)
                    .statusCode(0)
                    .build();
            return ResponseEntity.status(500).body(new ResponseController<>(rejectRegistrarOutputDTO));
        }
    }
}
