package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class SdgTruckDepartureGetInput {

    @Data
    public static class Input {
        @JsonProperty("eir")
        private int eirId;

        @JsonProperty("size")
        private int size;

        @JsonProperty("page")
        private int page;

        @JsonProperty("language")
        private int languageId;

        @JsonProperty("sub_business_unit_local_id")
        private int subBusinessUnitLocalId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix sdg;
    }

}