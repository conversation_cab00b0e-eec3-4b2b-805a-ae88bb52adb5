package com.maersk.sd1.sdg.controller.dto;

import lombok.Data;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Data
public class GateoutGeneralAssigmentListDtoOutput {

    @Data
    public class ResultList {
        private Integer eirNumber;
        private Integer eirChassis;
        private String status;
        private String moveType;
        private Integer catProcedenceId;
        private String truckInDate;
        private String plateNumber;
        private String documentContainer;
        private String documentChassis;
        private String shippingLine;
        private String vesselPlanDetail;
        private String cutoff;
        private String driverName;
        private String operationType;
        private String shipperName;
        private String consigneeName;
        private String containerAvailabilityType;
        private Integer findContainer;
        private Integer findChassis;
        private String chassisOperationType;
        private String chassisAvailabilityType;
        private Integer containerId;
        private String containerNumber;
        private String containerType;
        private String isoCode;
        private String grade;
        private String tara;
        private String payload;
        private String reeferType;
        private String documentChassisId;
        private String documentoCargaDetalleId;
        private String receivedWeight;
        private String planningDetailId;
        private String seal1;
        private String seal2;
        private String seal3;
        private String seal4;
        private String chassisId;
        private Object containerSizeTypeJson;
        private String documentContainerNumber;
        private String mercaderia;
        private String chassisNumber;
        private String ownerCompany;
        private String observation;
        private String preAllocatedContainer;

        public List<Object> toResultList() {
            List<Object> result = new ArrayList<>();
            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    result.add(field.get(this));
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            return result;
        }
    }

    private List<ResultList> data;
    private List<List<Integer>> total;

    public Object[] convertToResultFormat() {
        List<List<Object>> dataResult = new ArrayList<>();
        List<List<Object>> totalResult = new ArrayList<>();

        if (data != null) {
            for (ResultList item : data) {
                dataResult.add(item.toResultList());
            }
        }

        if (total != null) {
            for (List<Integer> sublist : total) {
                totalResult.add(new ArrayList<>(sublist));
            }
        }
        return new Object[]{dataResult,totalResult};
    }


}
