package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.Report22GeneralGateoutInput;
import com.maersk.sd1.sdg.controller.dto.Report22GeneralGateoutOutput;
import com.maersk.sd1.sdg.service.Report22GeneralGateoutService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/ModuleSDG/module/sdg/ReportConsultServiceImp")
public class Report22GeneralGateoutController {

    private static final Logger logger = LogManager.getLogger(Report22GeneralGateoutController.class.getName());

    private final Report22GeneralGateoutService service;

    @Autowired
    public Report22GeneralGateoutController(Report22GeneralGateoutService service) {
        this.service = service;
    }

    @PostMapping("/consultReport22")
    public ResponseEntity<ResponseController<Report22GeneralGateoutOutput>> report22GeneralGateoutService(@RequestBody Report22GeneralGateoutInput.Root request) {
        try {
            return service.report22GeneralGateoutService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
