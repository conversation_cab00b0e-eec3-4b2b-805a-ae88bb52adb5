package com.maersk.sd1.sdg.controller.dto;
// TABLE(RowID INT IDENTITY(1,1), eir_id INT, eir_notification_id INT, ticket_content NVARCHAR(MAX), azure_storage_config_id INT)

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ResponseTruckDepartureRegisterBeforeYard {
    private int respResult;
    private String respMessage;
    private Boolean respFlagCreateworkorder;
    private String respContainerNumber;
    private char respEmptyFullAlias;


}
