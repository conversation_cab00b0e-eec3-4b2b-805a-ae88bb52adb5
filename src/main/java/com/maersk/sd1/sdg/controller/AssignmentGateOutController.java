package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.service.BusinessUnitConfigService;
import com.maersk.sd1.common.service.UtilsService;
import com.maersk.sd1.sdg.controller.dto.*;
import com.maersk.sd1.sdg.service.AssignmentGateOutDeleteService;
import com.maersk.sd1.sdg.service.GateoutGeneralAssignmentContainerFindService;
import com.maersk.sd1.sdg.service.GateOutGeneralAssignmentListService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;


@RestController
@RequestMapping("/ModuleSDG/module/sdg/SDGAssignmentGateOutServiceImp")
@RequiredArgsConstructor
public class AssignmentGateOutController {


    private final GateoutGeneralAssignmentContainerFindService gateoutGeneralAssignmentContainerFindService;

    private final GateOutGeneralAssignmentListService gateoutGeneralAssignmentListService;

    private final BusinessUnitConfigService businessUnitConfigService;

    private final AssignmentGateOutDeleteService assignmentGateOutDeleteService;

    private final UtilsService utilsService;

    @PostMapping("/sdggateoutGeneralAssignmentContainerFind")
    public ResponseEntity<ResponseController<SdggateoutGeneralAssignmentContainerFindOutput>> sdggateoutGeneralAssignmentContainerFind(@RequestBody SdggateoutGeneralAssignmentContainerFindInput.Root input) {
        return ResponseEntity.ok(new ResponseController<>(gateoutGeneralAssignmentContainerFindService.execute(input)));
    }

    @PostMapping("/test")
    public ResponseEntity<String> test(@RequestBody SdggateoutGeneralAssignmentContainerFindInput.Root input) {

        Integer subSubinessUnitId = 86;

        // Instanciar una variable con la fecha y hora actual
        LocalDateTime now = LocalDateTime.now();
        System.out.println("Fecha y hora actual: " + now);

        LocalDateTime after = businessUnitConfigService.getDateTime(subSubinessUnitId, now);
        System.out.println("Fecha y hora actual: " + after);

        return ResponseEntity.ok("Ok");

    }

    @PostMapping("/sdggateoutGeneralAssignmentList")
    public ResponseEntity<ResponseController<Object>>  sdgGateoutGeneralAssignmentList(@RequestBody SdgGateoutGeneralAssignmentListInput.Root input) {
        GateoutGeneralAssigmentListDtoOutput result = gateoutGeneralAssignmentListService.gateoutGeneralAssigmentListv2(input);
        return ResponseEntity.ok(new ResponseController<>(result.convertToResultFormat()));
    }

    @PostMapping("/sdgassignmentGateoutDelete")
    public ResponseEntity<ResponseController<Object[]>>  sdgassignmentGateoutDelete(@RequestBody SdgAssignmentGateoutDeleteInput.Root input){
        SdgAssignmentGateoutDeleteOutput response = assignmentGateOutDeleteService.deleteAssignmentGateOut(input);
        ResponseController<Object[]> controllerResponse = new ResponseController<>(response.toResultArray());
        return ResponseEntity.ok(controllerResponse);
    }

}