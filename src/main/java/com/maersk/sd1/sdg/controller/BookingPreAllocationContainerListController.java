package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListInput;
import com.maersk.sd1.sdg.controller.dto.BookingResponse;
import com.maersk.sd1.sdg.service.BookingPreAllocationContainerListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/ModuleSDG/module/sdg/SDGGateOutServiceImp")
public class BookingPreAllocationContainerListController {
    private static final Logger logger = LogManager.getLogger(BookingPreAllocationContainerListController.class.getName());

    @Autowired
    public BookingPreAllocationContainerListController(BookingPreAllocationContainerListService service) {
        this.service = service;
    }

    private final BookingPreAllocationContainerListService service;

    @PostMapping("/sdgbookingPreAllocationContainerList")
    public ResponseEntity<ResponseController<BookingResponse>> bookingPreAllocationContainerList(@RequestBody BookingPreAllocationContainerListInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
            }
            return service.bookingPreAllocationContainerListService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request."));
        }
    }
}
