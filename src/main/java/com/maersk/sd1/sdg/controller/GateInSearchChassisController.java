package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.GateInSearchChassisInput;
import com.maersk.sd1.sdg.controller.dto.GateInSearchChassisOutput;
import com.maersk.sd1.sdg.service.GateInSearchChassisService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/SDGGateInServiceImp")
public class GateInSearchChassisController {

    private final GateInSearchChassisService service;

    @PostMapping("/sdggateInSearchChassis")
    public ResponseEntity<ResponseController<GateInSearchChassisOutput.Output>> gateInSearchChassis(@RequestBody GateInSearchChassisInput.Root request) {
        if (request != null && request.getPrefix() != null && request.getPrefix().getInput() != null
        && request.getPrefix().getInput().getBusinessUnitId() != null && request.getPrefix().getInput().getSubBusinessUnitId() != null
        && request.getPrefix().getInput().getChassis() != null && request.getPrefix().getInput().getRegistrationUserId() != null) {
            return ResponseEntity.ok(new ResponseController<>(service.gateInSearchChassis(request)));
        } else {
            return ResponseEntity.badRequest().build();
        }
    }
}
