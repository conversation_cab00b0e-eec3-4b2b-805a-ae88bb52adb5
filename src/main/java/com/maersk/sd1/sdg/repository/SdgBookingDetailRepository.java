package com.maersk.sd1.sdg.repository;

import java.util.List;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.sdg.dto.GateoutGeneralAssignmentContainerFindBooking;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SdgBookingDetailRepository extends BookingDetailRepository {

    @Query("SELECT new com.maersk.sd1.sdg.dto.GateoutGeneralAssignmentContainerFindBooking(bd.id, b.shippingLine.id, bd.catSize.id, bd.catContainerType.id, bd.maximumLoadRequired, " +
            "bd.reservationQuantity, COALESCE(bd.remarkRulesName, '')) " +
            "FROM BookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE b.id = :bookingId " +
            "AND b.active = true " +
            "AND bd.active = true")
    List<GateoutGeneralAssignmentContainerFindBooking> findBookingDetailsByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT COUNT(p.id) FROM ContainerPreassignment p WHERE p.active = true AND p.bookingDetail.id = :bookingDetailId")
    Integer countActiveContainerPreassignments(@Param("bookingDetailId") Integer bookingDetailId);

}
