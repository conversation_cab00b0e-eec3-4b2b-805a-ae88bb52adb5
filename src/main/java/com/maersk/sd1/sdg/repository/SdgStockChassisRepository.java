package com.maersk.sd1.sdg.repository;

import com.maersk.sd1.common.repository.StockChassisRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SdgStockChassisRepository extends StockChassisRepository {


    @Query(value = "SELECT SC.eirChassisGateout.id FROM StockChassis SC " +
            "WHERE SC.eirChassisGatein.id = :eirChassisId " +
            "AND SC.active = TRUE ORDER BY SC.id DESC")
    Integer findEirChassisGateOutIdByEirChassisId(Integer eirChassisId);


    @Query(value = "SELECT SC.eirChassisGatein.id FROM StockChassis SC " +
            "WHERE SC.eirChassisGatein.id = :eirChassisId " +
            "AND SC.active = TRUE ORDER BY SC.id DESC")
    Integer findEirChassisGateIntIdByEirChassisId(Integer eirChassisId);
}
