package com.maersk.sd1.sdg.service;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListInput;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListOutput;
import com.maersk.sd1.sdg.controller.dto.BookingResponse;
import com.maersk.sd1.sdg.dto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class BookingPreAllocationContainerListService {
    private static final Logger logger = LogManager.getLogger(BookingPreAllocationContainerListService.class);

    private final BusinessUnitRepository businessUnitRepository;
    private final CatalogRepository catalogRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final StockEmptyRepository stockEmptyRepository;
    private final EirRepository eirRepository;
    private final ContainerRepository containerRepository;
    private final FgisInspectionRepository fgisInspectionRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final ContainerPreassignmentRepository containerPreassignmentRepository;
    private final StockFullRepository stockFullRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final BookingRepository bookingRepository;
    private final BookingDetailRepository bookingDetailRepository;
    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final CompanyRepository companyRepository;
    private final ProductRepository productRepository;
    private final IsoCodeRepository isoCodeRepository;

    @Autowired
    public BookingPreAllocationContainerListService(
            BusinessUnitRepository businessUnitRepository,
            CatalogRepository catalogRepository,
            CargoDocumentRepository cargoDocumentRepository,
            StockEmptyRepository stockEmptyRepository,
            EirRepository eirRepository,
            ContainerRepository containerRepository,
            FgisInspectionRepository fgisInspectionRepository,
            EirActivityZoneRepository eirActivityZoneRepository,
            ContainerPreassignmentRepository containerPreassignmentRepository,
            StockFullRepository stockFullRepository,
            ContainerRestrictionRepository containerRestrictionRepository,
            CatalogLanguageRepository catalogLanguageRepository,
            CargoDocumentDetailRepository cargoDocumentDetailRepository,
            BookingRepository bookingRepository,
            BookingDetailRepository bookingDetailRepository,
            EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository,
            CompanyRepository companyRepository,
            ProductRepository productRepository,
            IsoCodeRepository isoCodeRepository
    ) {
        this.businessUnitRepository = businessUnitRepository;
        this.catalogRepository = catalogRepository;
        this.cargoDocumentRepository = cargoDocumentRepository;
        this.stockEmptyRepository = stockEmptyRepository;
        this.eirRepository = eirRepository;
        this.containerRepository = containerRepository;
        this.fgisInspectionRepository = fgisInspectionRepository;
        this.eirActivityZoneRepository = eirActivityZoneRepository;
        this.containerPreassignmentRepository = containerPreassignmentRepository;
        this.stockFullRepository = stockFullRepository;
        this.containerRestrictionRepository = containerRestrictionRepository;
        this.catalogLanguageRepository = catalogLanguageRepository;
        this.cargoDocumentDetailRepository = cargoDocumentDetailRepository;
        this.bookingRepository = bookingRepository;
        this.bookingDetailRepository = bookingDetailRepository;
        this.eirDocumentCargoDetailRepository = eirDocumentCargoDetailRepository;
        this.companyRepository = companyRepository;
        this.productRepository = productRepository;
        this.isoCodeRepository = isoCodeRepository;
    }

    public ResponseEntity<ResponseController<BookingResponse>>bookingPreAllocationContainerListService(BookingPreAllocationContainerListInput.Root input) {
        Integer localSubBusinessUnitId = input.getPrefix().getInput().getSubBusinessUnitLocalId();
        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(localSubBusinessUnitId);
        Integer emptyFullId = input.getPrefix().getInput().getEmptyFullId();
        Integer languageId = input.getPrefix().getInput().getLanguageId();
        Integer containerSizeId = input.getPrefix().getInput().getContainerSizeId();
        Integer containerTypeId = input.getPrefix().getInput().getContainerTypeId();
        Integer containerGradeId = input.getPrefix().getInput().getContainerGradeId();
        Integer containerShippingLineId = input.getPrefix().getInput().getContainerShippingLineId();
        Integer isGateOut = catalogRepository.findIdByAliasName("43081");
        Integer isFull = catalogRepository.findIdByAliasName("43084");
        Integer isEmptyy = catalogRepository.findIdByAliasName("43083");
        logger.info("isEmpty: {}", isEmptyy);
        Integer isChassis = catalogRepository.findIdByAliasName("sd1_equipment_category_chassis");
        Integer isContainer = catalogRepository.findIdByAliasName("sd1_equipment_category_container");
        Integer isMtyActivityInsp = catalogRepository.findIdByAliasName("cat_43161_box_inspection");

        // Fetch contenedor_id for 'NO-CNT'
        Integer equipmentPendingId = containerRepository.findContenedorIdByNumeroContenedor("NO-CNT");

        // Fetch contenedor_id for 'NOT APPLICA'
        Integer equipmentNotApplicableId = containerRepository.findContenedorIdByNumeroContenedor("NOT APPLICA");

        List<TbDataStockEmpty> tbDataList = processAndUpdateTbData(subBusinessUnitId, emptyFullId, containerSizeId, containerTypeId,
                containerGradeId, containerShippingLineId, isContainer, isMtyActivityInsp, equipmentPendingId, equipmentNotApplicableId);

        logger.info("tbDataList actual count: {}", tbDataList.size());


        List<tbFgisBookingPreAllocation> tbFGIS = getFgInspectionData(tbDataList, isEmptyy, isContainer);
        logger.info("tbFGIS actual count: {}", tbFGIS.size());


        updateUsdaApprovalStatus(tbDataList, tbFGIS, isEmptyy, isContainer);
        logger.info("tbDataList updated usda count: {}", tbDataList.size());


        List<TbPreallocationDTO> tbPreallocationList = processPreallocation(tbDataList, isEmptyy, isContainer, isGateOut);
        logger.info("tbPreallocationList actual count: {}", tbPreallocationList.size());

        List<TbDataStockEmpty> tbDataListUpdated = updateTbDataBookingPreallocation(tbPreallocationList, tbDataList);
        logger.info("tbDataListUpdated actual count: {}", tbDataListUpdated.size());

        // Update Equipment Details
        List<TbDataEmpty> item = stockFullRepository.updateEquipmentDetails(
                isContainer, subBusinessUnitId, emptyFullId, containerSizeId,
                containerTypeId, containerGradeId, containerShippingLineId,
                equipmentPendingId, equipmentNotApplicableId
        );

        for (TbDataEmpty row : item) {
            TbDataStockEmpty dto = new TbDataStockEmpty();

            // Set properties for dto
            dto.setBusinessUnitId(row.getBusinessUnitId() != null ? row.getBusinessUnitId().intValue() : null);
            dto.setSubBusinessUnitId(row.getSubBusinessUnitId() != null ? row.getSubBusinessUnitId().intValue() : null);
            dto.setProgramacionNaveDetalleId(row.getProgramacionNaveDetalleId());
            dto.setContenedorId(row.getContenedorId());
            dto.setChassisId(row.getChassisId());
            dto.setCatEmptyFullId(row.getCatEmptyFullId() != null ? row.getCatEmptyFullId().intValue() : null);
            dto.setLocal(row.getLocal());
            dto.setEirId(row.getEirId());
            dto.setFechaIngresoCamion(row.getFechaIngresoCamion());
            dto.setFechaSalidaCamion(row.getFechaSalidaCamion());
            dto.setContenedor(row.getContenedor());
            dto.setCatTamanoId(row.getCatTamanoId() != null ? row.getCatTamanoId().intValue() : null);
            dto.setCatTipoContenedorId(row.getCatTipoContenedorId() != null ? row.getCatTipoContenedorId().intValue() : null);
            dto.setIsContainer(row.getIsContainer());
            dto.setCatClaseId(row.getCatClaseId() != null ? row.getCatClaseId().intValue() : null);
            dto.setCodigoIsoId(row.getCodigoIsoId());
            dto.setShippingLine(row.getShippingLine());
            dto.setChassisOwnerCompanyId(row.getChassisOwnerCompanyId());
            dto.setFechaRegistro(row.getFechaRegistro());
            dto.setSeals(row.getSeals());
            dto.setObservacion(row.getObservacion());
            dto.setCatCargoDocumentTypeId(row.getCatCargoDocumentTypeId());
            dto.setCargoDocument(row.getCargoDocument());
            dto.setShipperNro(row.getShipperNro());
            dto.setShipperName(row.getShipperName());
            dto.setConsigneeNro(row.getConsigneeNro());
            dto.setConsigneeName(row.getConsigneeName());
            dto.setOperationType(row.getOperationType());
            dto.setProductoId(row.getProductoId());
            dto.setEirChassisId(row.getEirChassisId());
            dto.setCatStructureConditionId(row.getCatStructureConditionId() != null ? row.getCatStructureConditionId().intValue() : null);
            dto.setCatMachineryConditionId(row.getCatMachineryConditionId() != null ? row.getCatMachineryConditionId().intValue() : null);
            dto.setFlagChassisStayed(row.getFlagChassisStayed());
            dto.setFilter(row.getFilter());
            dto.setInspectorComment(row.getInspectorComment());
            dto.setPotencialFoodAid(row.getPotencialFoodAid());
            dto.setGinComment(row.getGinComment());
            dto.setUsdaApproved(row.getUsdaApproved());
            dto.setCatStructureConditionInspId(row.getCatStructureConditionInspId() != null ? row.getCatStructureConditionInspId().intValue() : null);
            dto.setCatMachineryConditionInspId(row.getCatMachineryConditionInspId() != null ? row.getCatMachineryConditionInspId().intValue() : null);
            dto.setBookingPreAllocation(row.getBookingPreAllocation());

            // Add dto to updated list
            tbDataListUpdated.add(dto);
        }

        logger.info("tbDataListUpdated actual stockfull count: {}", tbDataListUpdated.size());

        tbDataListUpdated = tbDataListUpdated.stream()
                .filter(data -> data.getBookingPreAllocation() != null)  // Include only if bookingPreAllocation is not null
                .collect(Collectors.toList());


        logger.info("tbDataListUpdated actual stockfull count after filter: {}", tbDataListUpdated.size());





        tbDataListUpdated = updateShipperDetails(tbDataListUpdated);
        logger.info("tbDataListUpdated actual stockfull count after updateShipperDetails: {}", tbDataListUpdated.size());


        List<TbRestrictionBookingPreAllocation> tbRestrictionList = getTbRestrictionBookingPreAllocations();
        logger.info("restrictions: {}", tbRestrictionList.size());

        List<BookingPreAllocationContainerListOutput> output = processTbDataList(tbDataListUpdated,languageId, isContainer, isChassis, isEmptyy,isFull, tbRestrictionList);
        logger.info("output: {}", output.size());


        Integer total = output.isEmpty() ? 0 : output.getFirst().getTotal();

        // Create the TotalOutputDTO
        TotalOutputDTO totalOutputDTO = new TotalOutputDTO();
        totalOutputDTO.setTotal(total);

        // Wrap the response in BookingResponse (contains both data and total)
        BookingResponse bookingResponse = new BookingResponse(output, totalOutputDTO);

        return ResponseEntity.ok(new ResponseController<>(bookingResponse));


    }

        public List<TbRestrictionBookingPreAllocation> getTbRestrictionBookingPreAllocations() {

            List<TbRestriction> row = containerRestrictionRepository.getRestriccionContenedores();
            logger.info("Fetched {} container restrictions.", row.size());

            List<TbRestrictionBookingPreAllocation> tbRestrictionList = new ArrayList<>();

            // Loop through each fetched restriction
            for (TbRestriction r : row) {

                TbRestrictionBookingPreAllocation tbRestrictionBookingPreAllocation = new TbRestrictionBookingPreAllocation();
                tbRestrictionBookingPreAllocation.setCatEmptyFullId(r.getCatEmptyFullId()); //REFERENCE
                tbRestrictionBookingPreAllocation.setContainerId(r.getContainerId());
                tbRestrictionBookingPreAllocation.setRestrictionRemark(r.getRestrictionRemark());
                tbRestrictionBookingPreAllocation.setNumeroBooking(r.getNumeroBooking());
                tbRestrictionList.add(tbRestrictionBookingPreAllocation);
            }

            logger.info("Mapped {} restrictions to TbRestrictionBookingPreAllocation.", tbRestrictionList.size());
            return tbRestrictionList;
        }


    private List<TbDataStockEmpty> processAndUpdateTbData(Integer subBusinessUnitId, Integer emptyFullId, Integer containerSizeId,
                                                          Integer containerTypeId, Integer containerGradeId, Integer containerShippingLineId,
                                                          Integer isContainer, Integer isMtyActivityInsp, Integer equipmentPendingId, Integer equipmentNotApplicableId) {
        // 1. Fetch data (INSERT operation) from stock_vacio
        List<TbDataEmpty> item = stockEmptyRepository.getTbDataList(subBusinessUnitId, emptyFullId, containerSizeId,
                containerTypeId, containerGradeId, containerShippingLineId, equipmentPendingId, equipmentNotApplicableId, isContainer);
        List<TbDataStockEmpty> tbDataList = new ArrayList<>();
        for (TbDataEmpty row : item) {
            TbDataStockEmpty dto = new TbDataStockEmpty();
            dto.setBusinessUnitId(row.getBusinessUnitId() != null ? row.getBusinessUnitId().intValue() : null);
            dto.setSubBusinessUnitId(row.getSubBusinessUnitId() != null ? row.getSubBusinessUnitId().intValue() : null);
            dto.setProgramacionNaveDetalleId(row.getProgramacionNaveDetalleId());
            dto.setContenedorId(row.getContenedorId());
            dto.setChassisId(row.getChassisId());
            dto.setCatEmptyFullId(row.getCatEmptyFullId() != null ? row.getCatEmptyFullId().intValue() : null);
            dto.setLocal(row.getLocal());
            dto.setEirId(row.getEirId());
            dto.setFechaIngresoCamion(row.getFechaIngresoCamion());
            dto.setFechaSalidaCamion(row.getFechaSalidaCamion());
            dto.setContenedor(row.getContenedor());
            dto.setCatTamanoId(row.getCatTamanoId() != null ? row.getCatTamanoId().intValue() : null);
            dto.setCatTipoContenedorId(row.getCatTipoContenedorId() != null ? row.getCatTipoContenedorId().intValue() : null);
            dto.setIsContainer(row.getIsContainer());
            dto.setCatClaseId(row.getCatClaseId() != null ? row.getCatClaseId().intValue() : null);
            dto.setCodigoIsoId(row.getCodigoIsoId());
            dto.setShippingLine(row.getShippingLine());
            dto.setChassisOwnerCompanyId(row.getChassisOwnerCompanyId());
            dto.setFechaRegistro(row.getFechaRegistro());
            dto.setSeals(row.getSeals());
            dto.setObservacion(row.getObservacion());
            dto.setCatCargoDocumentTypeId(row.getCatCargoDocumentTypeId());
            dto.setCargoDocument(row.getCargoDocument());
            dto.setShipperNro(row.getShipperNro());
            dto.setShipperName(row.getShipperName());
            dto.setConsigneeNro(row.getConsigneeNro());
            dto.setConsigneeName(row.getConsigneeName());
            dto.setOperationType(row.getOperationType());
            dto.setProductoId(row.getProductoId());
            dto.setEirChassisId(row.getEirChassisId());
            dto.setCatStructureConditionId(row.getCatStructureConditionId() != null ? row.getCatStructureConditionId().intValue() : null);
            dto.setCatMachineryConditionId(row.getCatMachineryConditionId() != null ? row.getCatMachineryConditionId().intValue() : null);
            dto.setFlagChassisStayed(row.getFlagChassisStayed());
            dto.setFilter(row.getFilter());
            dto.setInspectorComment(row.getInspectorComment());
            dto.setPotencialFoodAid(row.getPotencialFoodAid());
            dto.setGinComment(row.getGinComment());
            dto.setUsdaApproved(row.getUsdaApproved());
            dto.setCatStructureConditionInspId(row.getCatStructureConditionInspId() != null ? row.getCatStructureConditionInspId().intValue() : null);
            dto.setCatMachineryConditionInspId(row.getCatMachineryConditionInspId() != null ? row.getCatMachineryConditionInspId().intValue() : null);
            dto.setBookingPreAllocation(row.getBookingPreAllocation());
            tbDataList.add(dto);

        }
        logger.info("count tbdatalist: {}", tbDataList.size());


        // 2. Fetch inspection data
        logger.info("Fetching inspection data...");
        List<MtyStructureInspectionDTO> inspectionDataList = eirActivityZoneRepository.findMtyStructureInspection(isMtyActivityInsp);
        logger.info("{} inspection data fetched.", inspectionDataList.size());
        logger.info("Inspection data fetched.");

        // 3. Update TbDataStockEmpty with matching inspection data
        for (TbDataStockEmpty tbData : tbDataList) {
            // Find the corresponding inspection data based on eirId and other conditions
            if (tbData.getCatEmptyFullId().equals(emptyFullId) && tbData.getCatEquipmentCategory().equals(isContainer)) {
                // Default values if no match is found
                String inspectorComment = "";
                Integer potentialFoodAid = 0;

                // Search through inspection data and find the matching eirId
                for (MtyStructureInspectionDTO inspectionData : inspectionDataList) {
                    if (inspectionData.getEirId().equals(tbData.getEirId())) {
                        // If match found, update the values
                        inspectorComment = inspectionData.getInspectorComment();
                        potentialFoodAid = inspectionData.getPotentialFoodAid();
                        break; // Exit loop after first match
                    }
                }

                // Update TbDataStockEmpty with the values
                tbData.setInspectorComment(inspectorComment);
                tbData.setPotencialFoodAid(potentialFoodAid);

            }
        }
        return tbDataList;
    }

    public List<tbFgisBookingPreAllocation> getFgInspectionData(List<TbDataStockEmpty> tbDataList, Integer isEmptyy, Integer isContainer) {
        // Initialize result and inspections lists
        List<tbFgisBookingPreAllocation> resultList = new ArrayList<>();
        List<tbFgisBookingPreAllocation> inspections = new ArrayList<>();

        // Fetching the most recent fgis_inspection data (already filtered and aggregated in the repository query)
        List<tbFgisBooking> row = fgisInspectionRepository.fetchFgInspectionData();
        logger.info("Number of records fetched from DB: {}", row.size()); // Log the size of the fetched data

        // Log the fetched data outside loop for initial validation
        if (!row.isEmpty()) {
            logger.info("First fetched inspection data entry: gateInEirId = {}, containerId = {}, fgisDate = {}",
                    row.getFirst().getGateInEirId(), row.getFirst().getContainerId(), row.getFirst().getFgisDate());
        }

        // Loop through the fetched inspection data and populate the inspections list
        for (tbFgisBooking r : row) {
            tbFgisBookingPreAllocation tbFGIS = new tbFgisBookingPreAllocation();
            tbFGIS.setGateInEirId(r.getGateInEirId());
            tbFGIS.setFgisDate(r.getFgisDate());
            tbFGIS.setContainerId(r.getContainerId());
            inspections.add(tbFGIS);
        }

        // Check if tbDataList is empty
        logger.info("Number of records in tbDataList: {}", tbDataList.size());
        if (tbDataList.isEmpty()) {
            logger.warn("tbDataList is empty, no records to process.");
        }

        // Loop through tbDataList to filter relevant records based on cat_empty_full_id and cat_equipment_category
        for (TbDataStockEmpty tbData : tbDataList) {
            Integer catEmptyFullId = tbData.getCatEmptyFullId();
            Integer catEquipmentCategory = tbData.getCatEquipmentCategory();

            // Log the filtering conditions for each tbData
            if (Objects.equals(catEmptyFullId, isEmptyy) && Objects.equals(catEquipmentCategory, isContainer)) {
                for (tbFgisBookingPreAllocation tbFGIS : inspections) {
                    Integer gateInEirId = tbFGIS.getGateInEirId();

                    // Only log when a match is found to avoid excessive logging
                    if (tbData.getEirId().equals(gateInEirId)) {
                        resultList.add(tbFGIS);
                    }
                }
            }
        }

        logger.info("Total number of results added to resultList: {}", resultList.size());
        return resultList;
    }



    void updateUsdaApprovalStatus(List<TbDataStockEmpty> tbDataList, List<tbFgisBookingPreAllocation> tbFGIS, Integer isEmptyy, Integer isContainer) {
        // Get all gateInEirId and fgisDate combinations from tbFGIS
        Set<Integer> gateInEirIds = new HashSet<>();
        Set<LocalDateTime> fgisDates = new HashSet<>();
        for (tbFgisBookingPreAllocation tbFgis : tbFGIS) {
            if (tbFgis.getGateInEirId() != null && tbFgis.getFgisDate() != null) {
                // Collect all the gateInEirId and fgisDate combinations
                gateInEirIds.add(tbFgis.getGateInEirId());
                LocalDateTime fgisDate = tbFgis.getFgisDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                fgisDates.add(fgisDate);
            }
        }

        // Query the database to get all FgisInspection records for the relevant gateInEirIds and fgisDates
        List<FgisInspection> fgisInspections = fgisInspectionRepository.findByGateInEirIdInAndFgisDateIn(gateInEirIds, fgisDates);

        // Create a map to store inspections by gateInEirId and fgisDate for fast lookup
        Map<String, FgisInspection> inspectionsMap = new HashMap<>();
        for (FgisInspection fgisInspection : fgisInspections) {
            String key = fgisInspection.getGateInEir().getId() + "_" + fgisInspection.getFgisDate();
            inspectionsMap.put(key, fgisInspection);
        }

        // Now loop through tbDataList and update USDA approval status
        for (TbDataStockEmpty tbData : tbDataList) {
            if (Objects.equals(tbData.getCatEmptyFullId(), isEmptyy) && Objects.equals(tbData.getCatEquipmentCategory(), isContainer)) {
                for (tbFgisBookingPreAllocation tbFgis : tbFGIS) {
                    if (tbFgis.getGateInEirId() != null && tbFgis.getFgisDate() != null) {
                        // Build the key for lookup
                        String key = tbFgis.getGateInEirId() + "_" + tbFgis.getFgisDate().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();

                        // Check if the key exists in the map
                        FgisInspection fgisInspection = inspectionsMap.get(key);
                        if (fgisInspection != null && tbData.getContainerId().equals(fgisInspection.getContainer().getId())) {
                            // Matching entry found, update the USDA approval status
                            Boolean myBoolean = fgisInspection.getUsdaApproved();
                            Integer usdaApproved = myBoolean != null && myBoolean ? 1 : 0;

                            switch (usdaApproved) {
                                case 0:
                                    tbData.setUsdaApproved(0); // If USDA approved is null, set to 0
                                    break;
                                case 1:
                                    tbData.setUsdaApproved(1); // Set to 1 if USDA approved
                                    break;
                                default:
                                    tbData.setUsdaApproved(2); // Set to 2 for other values
                                    break;
                            }

                            break; // Exit loop once matching entry is found
                        }
                    }
                }
            }
        }
    }


    public List<TbPreallocationDTO> processPreallocation(List<TbDataStockEmpty> tbDataList, Integer isEmptyy, Integer isContainer, Integer isGateOut) {
        List<TbPreallocationDTO> tbPreallocationList = new ArrayList<>();

        logger.info("Processing TbDataList with size: {}", tbDataList.size());

        // Fetch all required data at once to avoid repeated database calls
        List<Integer> containerIds = tbDataList.stream()
                .filter(tbData -> Objects.equals(tbData.getCatEmptyFullId(), isEmptyy) && Objects.equals(tbData.getCatEquipmentCategory(), isContainer))
                .map(TbDataStockEmpty::getContainerId)
                .distinct()
                .toList();

        // Fetch all related preallocation containers for the containerIds in one query
        Map<Integer, List<ContainerPreassignment>> preallocationContainersMap = containerPreassignmentRepository
                .findByContainerIds(containerIds).stream()
                .collect(Collectors.groupingBy(p -> p.getContainer().getId()));

        // Loop through the filtered tbDataList
        for (TbDataStockEmpty tbData : tbDataList) {
            if (Objects.equals(tbData.getCatEmptyFullId(), isEmptyy) && Objects.equals(tbData.getCatEquipmentCategory(), isContainer)) {
                TbPreallocationDTO.TbPreallocationDTOBuilder tbPreallocationBuilder = TbPreallocationDTO.builder();
                // Get preallocation containers for the current containerId
                List<ContainerPreassignment> preallocationContainers = preallocationContainersMap.get(tbData.getContainerId());

                if (preallocationContainers != null && !preallocationContainers.isEmpty()) {
                    for (ContainerPreassignment preallocationContainer : preallocationContainers) {
                        // Fetch the CargoDocumentDetail and Booking in one go to avoid multiple DB hits
                        CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailRepository
                                .findByBookingDetailIdAndContainerId(preallocationContainer.getBookingDetail().getId(), preallocationContainer.getContainer().getId());

                        if (cargoDocumentDetail != null) {
                            BookingDetail bookingDetail = bookingDetailRepository.findByBookingDetailId(preallocationContainer.getBookingDetail().getId());

                            if (bookingDetail != null) {
                                Booking booking = bookingRepository.findByBookingIdAndBusinessUnitId(bookingDetail.getBooking().getId(), tbData.getSubBusinessUnitId());

                                if (booking != null) {
                                    tbPreallocationBuilder
                                            .id(tbData.getId())
                                            .containerId(tbData.getContainerId())
                                            .documentoCargaDetalleId(cargoDocumentDetail.getId())
                                            .isDispatched(false)
                                            .numeroBooking(booking.getBookingNumber());

                                    tbPreallocationList.add(tbPreallocationBuilder.build());
                                } else {
                                    logger.warn("Booking not found for BookingDetail ID: {} and BusinessUnitId: {}", bookingDetail.getId(), tbData.getSubBusinessUnitId());
                                }
                            } else {
                                logger.warn("BookingDetail not found for BookingDetailId: {}", preallocationContainer.getBookingDetail().getId());
                            }
                        } else {
                            logger.warn("CargoDocumentDetail not found for BookingDetailId: {} and ContainerId: {}", preallocationContainer.getBookingDetail().getId(), preallocationContainer.getContainer().getId());
                        }
                    }
                } else {
                    logger.warn("No preallocation containers found for containerId: {}", tbData.getContainerId());
                }
            } else {
                logger.info("Skipping TbData with containerId: {} as it doesn't match the filter criteria", tbData.getContainerId());
            }
        }

        logger.info("Populated {} TbPreallocationDTO objects", tbPreallocationList.size());

        // Update dispatch status for all preallocations
        updateDispatchStatus(tbPreallocationList, isGateOut, isEmptyy);

        logger.info("Updating dispatch status for {} preallocation entries", tbPreallocationList.size());

        // Remove preallocated records with dispatched status
        tbPreallocationList.removeIf(TbPreallocationDTO::isDispatched);

        logger.info("Removed dispatched preallocation entries. Final size of tbPreallocationList: {}", tbPreallocationList.size());

        return tbPreallocationList;
    }

    private void updateDispatchStatus(List<TbPreallocationDTO> tbPreallocationList, int isGateOut, int isEmptyy) {
        logger.info("Starting dispatch status update for {} preallocation entries", tbPreallocationList.size());

        // Fetch EirDocumentCargoDetail entries in bulk for all documentoCargaDetalleIds
        List<Integer> documentoCargaDetalleIds = tbPreallocationList.stream()
                .map(TbPreallocationDTO::getDocumentoCargaDetalleId)
                .distinct()
                .toList();

        Map<Integer, List<EirDocumentCargoDetail>> eirDocumentDetailsMap = eirDocumentCargoDetailRepository
                .findByDocumentoCargaDetalleIdsAndActivo(documentoCargaDetalleIds).stream()
                .collect(Collectors.groupingBy(e -> e.getCargoDocumentDetail().getId()));

        // Loop through tbPreallocation and check dispatch status
        for (TbPreallocationDTO preallocation : tbPreallocationList) {
            List<EirDocumentCargoDetail> eirDocumentDetails = eirDocumentDetailsMap.get(preallocation.getDocumentoCargaDetalleId());

            if (eirDocumentDetails != null && !eirDocumentDetails.isEmpty()) {
                for (EirDocumentCargoDetail eirDocumentDetail : eirDocumentDetails) {
                    Eir eir = eirRepository.findEirIds(eirDocumentDetail.getEir().getId());

                    if (eir != null && eir.getCatMovement().getId() == isGateOut
                            && eir.getCatEmptyFull().getId() == isEmptyy && eir.getActive()) {

                        // Mark preallocation as dispatched
                        preallocation.setIsDispatched(true);
                    }
                }
            } else {
                logger.warn("No active EirDocumentCargoDetail found for DocumentoCargaDetalleId: {}", preallocation.getDocumentoCargaDetalleId());
            }
        }

        logger.info("Finished updating dispatch status.");
    }



    private List<TbDataStockEmpty>updateTbDataBookingPreallocation(List<TbPreallocationDTO> tbPreallocationList, List<TbDataStockEmpty> tbDataList) {
        for (TbDataStockEmpty tbData : tbDataList) {
            // For each TbData, find a matching TbPreallocationDTO by _id
            for (TbPreallocationDTO preallocation : tbPreallocationList) {
                // If the _id matches, update the bookingPreAllocation field
                if (tbData.getId().equals(preallocation.getId())) {
                    tbData.setBookingPreAllocation(preallocation.getNumeroBooking());
                    break;  // Exit the inner loop once the match is found
                }
            }
        }
        return tbDataList;
    }

    public List<TbDataStockEmpty> updateShipperDetails(List<TbDataStockEmpty> tbDataList) {
        for (TbDataStockEmpty data : tbDataList) {
            // Fetch the related EirDocumentoCargaDetalle based on eir_id
            EirDocumentCargoDetail eirDoc = eirDocumentCargoDetailRepository.findOneByEirId(data.getEirId());
            if (eirDoc != null) {
                // Fetch the related DocumentoCargaDetalle based on documento_carga_detalle_id
                CargoDocumentDetail dodx = cargoDocumentDetailRepository.findByCargoDetailId(eirDoc.getCargoDocumentDetail().getId());
                if (dodx != null) {
                    // Fetch the related DocumentoCarga based on documento_carga_id
                    CargoDocument doc = cargoDocumentRepository.findcargoDoc(dodx.getCargoDocument().getId());
                    if (doc != null) {
                        // Fetch the related Empresa (shipper) based on empresa_embarcador_id
                        Company shipper = null;
                        if (doc.getShipperCompany() != null) {
                            shipper = companyRepository.findByCompanyId(doc.getShipperCompany().getId());
                        }
                        if (shipper != null) {
                            // Update the TbData fields based on the joins
                            data.setShipperNro(shipper.getDocument());
                            data.setShipperName(shipper.getLegalName());
                            data.setProductoId(dodx.getProduct().getId());
                        } else {
                            // Handle case where the shipper was not found
                            data.setShipperNro(null);
                            data.setShipperName(null);
                        }
                    } else {
                        // Handle case where CargoDocument was not found
                        data.setShipperNro(null);
                        data.setShipperName(null);
                    }
                } else {
                    // Handle case where CargoDocumentDetail was not found
                    data.setShipperNro(null);
                    data.setShipperName(null);
                }
            } else {
                // Handle case where EirDocumentCargoDetail was not found
                data.setShipperNro(null);
                data.setShipperName(null);
            }
        }
        return tbDataList;
    }
    public List<BookingPreAllocationContainerListOutput> processTbDataList(List<TbDataStockEmpty> dataList, Integer languageId, Integer isContainer, Integer isChassis, Integer isEmptyy, Integer isFull, List<TbRestrictionBookingPreAllocation> restriction) {
        List<BookingPreAllocationContainerListOutput> result = new ArrayList<>();

        // Loop through each TbData object and apply the necessary transformations
        for (TbDataStockEmpty data : dataList) {
            // Apply the 'emptyFull' logic from SQL
            String emptyFull = (data.getCatEquipmentCategory() != null && data.getCatEquipmentCategory().equals(isContainer)) ?
                    catalogLanguageRepository.fnCatalogTranslationDescLong(data.getCatEmptyFullId(), languageId) : "";

            // Apply the 'equipmentSizeType' logic from SQL (using catalog descriptions)
            String equipmentSizeType = "";
            if (data.getCatEquipmentCategory() != null && data.getCatEquipmentCategory().equals(isContainer)) {
                equipmentSizeType = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getEquipmentSizeId(), languageId)
                        + " " + catalogLanguageRepository.fnCatalogTranslationDescLong(data.getEquipmentTypeId(), languageId);
            } else if (data.getCatEquipmentCategory() != null && data.getCatEquipmentCategory().equals(isChassis)) {
                equipmentSizeType = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getEquipmentTypeId(), languageId);
            }

            // Fetch other catalog descriptions based on the SQL logic
            String equipmentCategory = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getCatEquipmentCategory(), languageId);
            String isoCode = isoCodeRepository.findCodeByIsoCodeId(data.getCodigoIsoId());
            String shippingLine = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getShippingLineId(), languageId);
            String structureConditionCurrent = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getCatStructureConditionId(), languageId);
            String machineryConditionCurrent = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getCatMachineryConditionId(), languageId);
            String structureConditionInsp = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getCatStructureConditionInspId(), languageId);
            String machineryConditionInsp = catalogLanguageRepository.fnCatalogTranslationDescLong(data.getCatMachineryConditionInspId(), languageId);
            String commodity = productRepository.findProductName(data.getProductoId());

            // Handle the 'equipmentRestriction' logic (using restrictions from the repository)
            String equipmentRestriction = getEquipmentRestriction(data.getContainerId(), data.getCatEquipmentCategory(), restriction, isContainer, isEmptyy, isFull);

            // Only add to result if equipmentRestriction is empty after trimming whitespace
            if (equipmentRestriction.trim().isEmpty()) {
                Integer total = countEmptyFullRestrictions(dataList, restriction, isContainer, isEmptyy, isFull);

                // Set the transformed data into a new TbData object
                BookingPreAllocationContainerListOutput transformedData = new BookingPreAllocationContainerListOutput();
                transformedData.setDepot(data.getLocal());
                transformedData.setEmptyFull(emptyFull);
                transformedData.setEquipmentNumber(data.getEquipmentNumber());
                transformedData.setEquipmentCategory(equipmentCategory);
                transformedData.setEquipmentSizeType(equipmentSizeType);
                transformedData.setEquipmentGrade(catalogRepository.findDescriptionById(data.getCatClaseId()));
                transformedData.setIsoCode(isoCode);
                transformedData.setShippingLine(shippingLine);
                transformedData.setShipperName(data.getShipperName());
                transformedData.setCommodity(commodity);
                transformedData.setGateInDate(getGateInDate(data.getFechaIngresoCamion()));
                transformedData.setDwellTime(calculateDwellTime(data.getFechaIngresoCamion()));
                transformedData.setStructureConditionCurrent(structureConditionCurrent);
                transformedData.setMachineryConditionCurrent(machineryConditionCurrent);
                transformedData.setEquipmentRestriction(equipmentRestriction);
                transformedData.setUsdaApproved(getUsdaApproved(data.getUsdaApproved()));
                transformedData.setGateIn(data.getEirId().toString());
                transformedData.setMachineryConditionInspection(machineryConditionInsp);
                transformedData.setStructureConditionInspection(structureConditionInsp);
                transformedData.setGateInComment(data.getGinComment());
                transformedData.setInspectorComment(data.getInspectorComment());
                transformedData.setBookingPreAllocation(data.getBookingPreAllocation());
                transformedData.setPotentialFgis(String.valueOf(data.getPotencialFoodAid()));
                transformedData.setEquipmentSizeId(data.getEquipmentSizeId());
                transformedData.setEquipmentTypeId(data.getEquipmentTypeId());
                transformedData.setContainerId(data.getContainerId());
                transformedData.setTotal(total);
                result.add(transformedData);
            } else {
                logger.info("Skipping data due to non-empty equipmentRestriction.");
            }
        }

        // Final log before returning the result
        logger.info("Processed {} items", result.size());

        return result;
    }

    public int countEmptyFullRestrictions(List<TbDataStockEmpty> tbDataList, List<TbRestrictionBookingPreAllocation> tbRestrictionList, Integer isContainer, Integer isEmptyy, Integer isFull) {
        int count = 0;

        // Iterate through tbDataList to apply the restriction logic
        for (TbDataStockEmpty tbData : tbDataList) {
            // Get the restriction remarks for this container
            String equipmentRestriction = getEquipmentRestriction(tbData.getContainerId(), tbData.getCatEquipmentCategory(), tbRestrictionList, isContainer, isEmptyy, isFull);

            // Check if the restriction remark is empty after trimming whitespace
            if (equipmentRestriction.trim().isEmpty()) {
                count++;
            }
        }

        return count;
    }

    // Helper method to get equipment restrictions
    public String getEquipmentRestriction(Integer tb, Integer catEquip, List<TbRestrictionBookingPreAllocation> restrictions, Integer isContainer, Integer isEmptyy, Integer isFull) {
        StringBuilder restrictionRemark = new StringBuilder();

        // Loop through each TbDataStockEmpty object
            // Check if the equipment category is the same as 'isContainer'
            if (Objects.equals(catEquip, isContainer)) {
                // Check for 'Empty' restrictions
                for (TbRestrictionBookingPreAllocation restriction : restrictions) {
                    if (Objects.equals(restriction.getContainerId(), tb) && Objects.equals(restriction.getCatEmptyFullId(), isEmptyy)) {
                        // Append the '[Empty]' remark with restriction details
                        restrictionRemark.append("[Empty]")
                                .append(Optional.ofNullable(restriction.getRestrictionRemark()).orElse("")) // restriction remark or empty if null
                                .append(": ")
                                .append(Optional.ofNullable(restriction.getNumeroBooking()).orElse("")) // booking number or empty if null
                                .append(" ");
                    }
                }

                // Check for 'Full' restrictions
                for (TbRestrictionBookingPreAllocation restriction : restrictions) {
                    if (Objects.equals(restriction.getContainerId(), tb) && Objects.equals(restriction.getCatEmptyFullId(), isFull)) {
                        // Append the '[Full]' remark with restriction details
                        restrictionRemark.append("[Full]")
                                .append(Optional.ofNullable(restriction.getRestrictionRemark()).orElse("")) // restriction remark or empty if null
                                .append(": ")
                                .append(Optional.ofNullable(restriction.getNumeroBooking()).orElse("")) // booking number or empty if null
                                .append(" ");
                    }
                }
            }

        // Trim leading and trailing whitespace before returning the string
        return restrictionRemark.toString().trim();
    }


    // Helper method to calculate dwell time
    String calculateDwellTime(Date gateInDate) {
        if (gateInDate != null) {
            // Get the current date
            Date currentDate = new Date();

            // Calculate the difference in milliseconds
            long diffInMillis = currentDate.getTime() - gateInDate.getTime();

            // Convert milliseconds to days
            long dwellTime = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS) + 1;

            return String.valueOf(dwellTime); // Return the dwell time as a string
        } else {
            return "0"; // Return "0" if the input date is null
        }
    }

    // Helper method to fetch USDA approved status
    String getUsdaApproved(Integer usdaApproved) {
        switch (usdaApproved) {
            case 1:
                return "Yes";
            case 2:
                return "No";
            default:
                return "";
        }
    }

    // Helper method to get gate-in date (formatting as needed)
    String getGateInDate(Date fechaIngresoCamion) {
        // Check if the Date object is not null
        if (fechaIngresoCamion != null) {
            // Define a date format, e.g., "yyyy-MM-dd"
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(fechaIngresoCamion); // Format the Date and return it as a string
        } else {
            return ""; // Return empty string if the input date is null
        }
    }


}