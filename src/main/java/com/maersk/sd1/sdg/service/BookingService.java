package com.maersk.sd1.sdg.service;

import java.util.List;
import com.maersk.sd1.sdg.dto.GateoutGeneralAssignmentContainerFindBooking;
import com.maersk.sd1.sdg.repository.SdgBookingDetailRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BookingService {

    private final SdgBookingDetailRepository sdgBookingDetailRepository;

    public List<GateoutGeneralAssignmentContainerFindBooking> findBookingDetailsByBookingId(Integer bookingId){

        List<GateoutGeneralAssignmentContainerFindBooking> bookingsDetail = sdgBookingDetailRepository.findBookingDetailsByBookingId(bookingId);

        for (GateoutGeneralAssignmentContainerFindBooking detail : bookingsDetail) {
            Integer preassignmentCount = sdgBookingDetailRepository.countActiveContainerPreassignments(detail.getBookingDetailId());
            detail.setQuantityAssigned(preassignmentCount);
        }

        return bookingsDetail;

    }

}
