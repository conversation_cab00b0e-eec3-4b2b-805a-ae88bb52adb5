package com.maersk.sd1.sdg.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.repository.GesReglaSistemaRepository;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralEquipmentAppointmentValidateInput;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralEquipmentAppointmentValidateOutput;
import com.maersk.sd1.seg.repository.SegUnidadNegocioRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
@RequiredArgsConstructor
public class GateinGeneralEquipmentAppointmentValidateService {


    private final SegUnidadNegocioRepository segUnidadNegocioRepository;

    private final GesReglaSistemaRepository gesReglaSistemaRepository;

    public ResponseEntity<ResponseController<GateInGeneralEquipmentAppointmentValidateOutput>> gateInGeneralEquipmentAppointmentValidateService(GateInGeneralEquipmentAppointmentValidateInput.Root input) {
        Integer subBusinessUnitLocalId = input.getPrefix().getInput().getSubBusinessUnitLocalId();

        String subBusinessUnitLocalAlias = segUnidadNegocioRepository.findAliasByUnidadNegocioId(subBusinessUnitLocalId);
        String reglaJson = gesReglaSistemaRepository.findReglaById(Parameter.SD1_RULE_INETRATION_APS);

        ObjectMapper objectMapper = new ObjectMapper();
        List<Map<String, String>> reglaList = null;
        try {
            reglaList = objectMapper.readValue(reglaJson, new TypeReference<List<Map<String, String>>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        for (Map<String, String> dict : reglaList) {
            if (subBusinessUnitLocalAlias != null && dict != null && subBusinessUnitLocalAlias.equals(dict.get(Parameter.SUB_BUSINESS_UNIT_LOCAL_ALIAS))) {
                GateInGeneralEquipmentAppointmentValidateOutput output = new GateInGeneralEquipmentAppointmentValidateOutput();
                output.setSubBusinessUnitLocalAlias(subBusinessUnitLocalAlias);
                output.setTypeProductIntegration(dict.get("type_product_integration"));
                return ResponseEntity.ok(new ResponseController<>(output));
            }
        }

        return ResponseEntity.ok(new ResponseController<>(Constants.NO_DATA_FOUND));
    }
}
