package com.maersk.sd1.sdg.service;


import com.maersk.sd1.sde.service.EirNotificationService;
import com.maersk.sd1.ges.service.GESCatalogService;

import com.maersk.sd1.common.repository.AzureStorageConfigRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TruckDepartureTicketListService {

    private static final String PENDING_ALIAS = "sd1_eir_notification_pending";
    private static final String PROCESSING_ALIAS = "sd1_eir_notification_processing";
    private static final String AZURE_SDG_TRUCK_DEPARTURE_TICKET = "azure_sdg_truck_departure_ticket";

    private static final Integer USER_REGISTER_ID = 1;


    private final EirNotificationService eirNotificationService;


    private final GESCatalogService catalogService;


    private final AzureStorageConfigRepository azureStorageConfigRepository;

    
}
