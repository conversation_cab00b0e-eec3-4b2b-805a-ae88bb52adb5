package com.maersk.sd1.sdg.service;

import java.util.*;
import java.util.stream.Collectors;

import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureGetInput;
import com.maersk.sd1.sdg.dto.ContainerBookingAvailavilityDto;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureGetDto;
import com.maersk.sd1.sdg.repository.SdgEirRepository;


@Service
@NoArgsConstructor(force = true)
@RequiredArgsConstructor
public class TruckDepartureGetService {


    private final CatalogLanguageRepository catLangRepository;

    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    private final ContainerRepository containerRepository;

    private final CatalogRepository catalogRepository;

    private final SdgEirRepository eirRepository;


    public List<ResponseTruckDepartureGetDto> getTruckDeparture(SdgTruckDepartureGetInput.Root ppo) {

        if (ppo == null) {
            return null;
        }

        List<ResponseTruckDepartureGetDto> resultList = new ArrayList<>();

        Integer containerNoCntId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NO_CNT).getId();
        Integer containerNotApplicableId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NOT_APPLICA).getId();

        Integer isContainerDry = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS);
        Integer isGateIn = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
        Integer isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer isFullId = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Integer isChassis = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CHASSIS);
        Integer isContainer = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CONTAINER);

        SdgTruckDepartureGetInput.Input input = ppo.getSdg().getInput();


        Integer eirId = input.getEirId();
        Integer languageId = input.getLanguageId();

        List<EirDocumentCargoDetail> eirDocumentCargoDetailList = eirDocumentCargoDetailRepository.findByEirIdAndActiveIsTrue(eirId);

        Integer eirCatEmptyFullId;
        Integer catMovementId;

        Eir eir = eirRepository.findOneById(eirId);

        if (eir == null) {
            return resultList;
        }

        if (eirDocumentCargoDetailList.isEmpty()) {
            EirDocumentCargoDetail defaultDetail = new EirDocumentCargoDetail();
            eirDocumentCargoDetailList = List.of(defaultDetail);
            defaultDetail.setEir(eir);
        }

        eirCatEmptyFullId = (eir.getCatEmptyFull() != null) ? eir.getCatEmptyFull().getId() : null;
        catMovementId = (eir.getCatMovement() != null) ? eir.getCatMovement().getId() : null;


        Integer bookingIdGoutLight = null;
        Integer documentoCargaGofId = null;
        Integer withContainerNumber = null;

        boolean findContainer = false;

        if (eir.getContainer() != null
            && (Objects.equals(eir.getContainer().getId(), containerNoCntId)
                || Objects.equals(eir.getContainer().getId(), containerNotApplicableId))
            && eir.getCatMovement() != null
            && Objects.equals(eir.getCatMovement().getId(), isGateOut)
            && (eir.getControlAssignment() != null && (eir.getControlAssignment() == 0 || eir.getControlAssignment() == 2))
            && Boolean.TRUE.equals(eir.getActive())) {

            bookingIdGoutLight = eir.getBookingGout().getId();
            documentoCargaGofId = eir.getDocumentCargoGof().getId();
            withContainerNumber = 0;
        }

        if (Objects.equals(eirCatEmptyFullId, isFullId)) {
            Integer loadDocumentId = eirRepository.getDocumentoCargaIdByEirIdAndContainerIdAndIsGateOutAndIsFull(eir.getId(), containerNoCntId, isGateOut, isFullId);
            if (loadDocumentId != null) {
                bookingIdGoutLight = null;
                documentoCargaGofId = loadDocumentId;
                withContainerNumber = 1;
            }
        }

        if (bookingIdGoutLight != null && withContainerNumber == 0) {
            findContainer = true;
        }

        if (documentoCargaGofId != null && withContainerNumber == 0) {
            findContainer = true;
        }

        Integer eirChasisId = null;
        String chassisAllocationControl = null;
        Integer chassisId = null;


        boolean findChassis = false;
        if (eir.getEirChassis() != null) {
            eirChasisId = eir.getEirChassis().getId();
            chassisAllocationControl = eir.getEirChassis().getChassisAllocationControl();
            chassisId = eir.getEirChassis().getChassis().getId();
        }


        if (Objects.equals(catMovementId, isGateIn) && eirChasisId != null && chassisId == null) {
            findChassis = true;
        }

        if (Objects.equals(catMovementId, isGateOut) && eirChasisId != null && chassisId == null && Objects.equals(chassisAllocationControl, "0")) {
            findChassis = true;
        }


        Boolean finalFindChassis = findChassis;
        Boolean finalFindContainer = findContainer;

        List<Integer> eirList = new ArrayList<>();
        eirList.add(eir.getId());

        List<ContainerBookingAvailavilityDto> containerBookingAvailavilityDtos =
                eirRepository.getContainerBookingQuantity(eirList, containerNoCntId, isContainerDry);


        String resultContainerAvailavility = containerBookingAvailavilityDtos.stream()
                .map(dto -> String.format("Size%s - Type%s (%d)",
                        catLangRepository.fnCatalogTranslationDesc(dto.getContainerSizeId(), languageId),
                        catLangRepository.fnCatalogTranslationDesc(dto.getContainerTypeId(), languageId),
                        dto.getReservationQuantity()))
                .collect(Collectors.joining(", "));

        eirDocumentCargoDetailList.forEach(eirDocumentCargoDetail -> {
            ResponseTruckDepartureGetDto resp = new ResponseTruckDepartureGetDto();

            Eir e = eirDocumentCargoDetail.getEir();

            //GENERAL INFO
            resp.setEir(e.getId().toString());
            resp.setTruckCompanyId(e.getTransportCompany().getId());
            resp.setTruckComanyName(e.getTransportCompany().getCommercialName());

            resp.setTruckInDate(e.getTruckArrivalDate().toString());
            resp.setMovementId(e.getCatMovement() != null ? e.getCatMovement().getId().toString() : null);

            StringBuilder movement = new StringBuilder();
            if (e.getCatMovement() != null) {
                movement.append(catLangRepository.fnCatalogTranslationDescLong(e.getCatMovement().getId(), languageId));
            }
            if (e.getCatOrigin() != null) {
                movement.append(" - ");
                movement.append(catLangRepository.fnCatalogTranslationDescLong(e.getCatOrigin().getId(), languageId));
            }
            resp.setMovement(movement.toString());

            resp.setTruckLicensePlate(e.getTruck().getPlate());
            resp.setDriverLicense(e.getDriverPerson().getDriversLicense());

            resp.setDriverName(e.getDriverPerson().getNames() + " " + eir.getDriverPerson().getFirstLastName());

            //EQUIPMENT
            resp.setEquipmentNumber(e.getContainer().getContainerNumber());
            resp.setEquipmentType(catLangRepository.fnCatalogTranslationDescLong(e.getContainer().getCatContainerType().getId(), languageId));

            StringBuilder equipmentReferenceNumber = new StringBuilder();

            String cargoDocument = Optional.ofNullable(eir.getDocumentCargoGof())
                    .map(CargoDocument::getCargoDocument)
                    .orElse(Optional.ofNullable(eirDocumentCargoDetail.getCargoDocumentDetail())
                            .map(CargoDocumentDetail::getCargoDocument)
                            .map(CargoDocument::getCargoDocument)
                            .orElse(Optional.ofNullable(eir.getBookingGout())
                                    .map(Booking::getBookingNumber).orElse(null)));

            equipmentReferenceNumber.append(Optional.ofNullable(eirDocumentCargoDetail.getCargoDocumentDetail())
                    .map(CargoDocumentDetail::getCargoDocument)
                    .map(CargoDocument::getCatCargoDocumentType)
                    .map(Catalog::getId)
                    .map(id -> catLangRepository.fnCatalogTranslationDesc(id, languageId))
                    .orElse(""));

            equipmentReferenceNumber.append(" - ");
            equipmentReferenceNumber.append(cargoDocument);
            resp.setEquipmentReferenceNumber(equipmentReferenceNumber.toString());

            ShippingLine shippingLine = null;

            shippingLine = Optional.of(eir)
                    .map(Eir::getDocumentCargoGof)
                    .map(CargoDocument::getShippingLine)
                    .orElse(Optional.of(eirDocumentCargoDetail)
                            .map(EirDocumentCargoDetail::getCargoDocumentDetail)
                            .map(CargoDocumentDetail::getCargoDocument)
                            .map(CargoDocument::getShippingLine)
                            .orElse(Optional.of(eir)
                                    .map(Eir::getBookingGout)
                                    .map(Booking::getShippingLine)
                                    .orElse(null)
                            )
                    );


            if (shippingLine != null) {
                resp.setShippingLineName(shippingLine.getName());
                resp.setShippingLineId(shippingLine.getId());
            }

            Company shippingCompany = null;

            shippingCompany = Optional.of(eir)
                    .map(Eir::getDocumentCargoGof)
                    .map(CargoDocument::getShipperCompany)
                    .orElseGet(() -> Optional.of(eirDocumentCargoDetail)
                            .map(EirDocumentCargoDetail::getCargoDocumentDetail)
                            .map(CargoDocumentDetail::getCargoDocument)
                            .map(CargoDocument::getShipperCompany)
                            .orElseGet(() -> Optional.of(eir)
                                    .map(Eir::getBookingGout)
                                    .map(Booking::getShipperCompany)
                                    .orElse(null)));


            if (shippingCompany != null) {
                resp.setShipperId(shippingCompany.getId());
                resp.setShipperName(shippingCompany.getLegalName());
            }


            Company consCompany = null;

            consCompany = Optional.of(eir)
                    .map(Eir::getDocumentCargoGof)
                    .map(CargoDocument::getConsigneeCompany)
                    .orElseGet(() -> Optional.of(eirDocumentCargoDetail)
                            .map(EirDocumentCargoDetail::getCargoDocumentDetail)
                            .map(CargoDocumentDetail::getCargoDocument)
                            .map(CargoDocument::getConsigneeCompany)
                            .orElseGet(() -> Optional.of(eir)
                                    .map(Eir::getBookingGout)
                                    .map(Booking::getClientCompany)
                                    .orElse(null)));

            if (consCompany != null) {
                resp.setConsignee(consCompany.getLegalName());
                resp.setConsigneeId(consCompany.getId());
            }

            // chassis
            resp.setEirChassisId(Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getId).orElse(null));

            resp.setChassisNumber(Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassis)
                    .map(Chassis::getChassisNumber).orElse(null));

            resp.setChassisDocumentReferenceId(Optional.of(eir).map(Eir::getEirChassis).map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisDocument)
                    .map(ChassisDocument::getCatReferenceType)
                    .map(Catalog::getId).orElse(null));


            Integer catChassisReferencetype = Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentGo)
                    .map(ChassisDocument::getCatReferenceType)
                    .map(Catalog::getId)
                    .orElseGet(() -> Optional.of(eir)
                            .map(Eir::getEirChassis)
                            .map(EirChassis::getChassisDocumentDetail)
                            .map(ChassisDocumentDetail::getChassisDocument)
                            .map(ChassisDocument::getCatReferenceType)
                            .map(Catalog::getId)
                            .orElse(null));

            StringBuilder chassisDocument = new StringBuilder();
            if (catChassisReferencetype != null) {
                chassisDocument.append(catLangRepository.fnCatalogTranslationDesc(catChassisReferencetype, languageId));
                chassisDocument.append(" - ");
            }
            String documentChassisNumber = Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisDocument)
                    .map(ChassisDocument::getDocumentChassisNumber)
                    .orElse(null);

            if (documentChassisNumber != null) {
                chassisDocument.append(documentChassisNumber);
            }

            resp.setChassisDocumentNumber(chassisDocument.toString());

            Integer chassisReferenceId = Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisDocument)
                    .map(ChassisDocument::getCatReferenceType)
                    .map(Catalog::getId)
                    .orElse(null);
            String chassisReferenceName = null;
            if (chassisReferenceId != null) {
                chassisReferenceName = catLangRepository.fnCatalogTranslationDescLong(chassisReferenceId, languageId);
            }
            resp.setChassisDocumentReferenceName(chassisReferenceName);


            Integer chassisTypeId = Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassis)
                    .map(Chassis::getCatChassisType)
                    .map(Catalog::getId)
                    .orElse(null);

            resp.setChassisTypeName(chassisTypeId != null ? catLangRepository.fnCatalogTranslationDescLong(chassisTypeId, languageId) : null);


            resp.setChassisTypeId(Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassis)
                    .map(Chassis::getCatChassisType)
                    .map(Catalog::getId)
                    .orElse(null));


            resp.setOwnerId(Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassis)
                    .map(Chassis::getOwnerCompany)
                    .map(Company::getId)
                    .orElse(null));

            resp.setOwnerName(Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassis)
                    .map(Chassis::getOwnerCompany)
                    .map(Company::getLegalName)
                    .orElse(null));

            Integer equipmentGradeId = Optional.of(eir)
                    .map(Eir::getContainer)
                    .map(Container::getCatGrade)
                    .map(Catalog::getId)
                    .orElse(null);

            resp.setEquipmentGrade(catLangRepository.fnCatalogTranslationDesc(equipmentGradeId, languageId));

            resp.setStructureCondition(
                    catLangRepository.fnCatalogTranslationDescLong(
                            eirRepository.fn_GetEquipmentConditionID(e.getId(), isContainer, "S", "CUR"),
                            languageId));

            resp.setMachineryCondition(
                    catLangRepository.fnCatalogTranslationDescLong(
                            eirRepository.fn_GetEquipmentConditionID(e.getId(), isContainer, "M", "CUR"),
                            languageId));

            resp.setStructureConditionChassis(
                    catLangRepository.fnCatalogTranslationDescLong(
                            eirRepository.fn_GetEquipmentConditionID(e.getId(),
                                    isChassis, "S", "CUR"),
                            languageId));

            resp.setIsDummy(eir.getContainer() != null && Objects.equals(eir.getContainer().getId(), containerNotApplicableId) ? 1 : 0);
            resp.setIsReefer(eir.getCatContainerType() != null ? eir.getCatContainerType().getCode() : null);

            Integer containerSizeId = eir.getCatSizeCnt() != null ? eir.getCatSizeCnt().getId() : null;
            resp.setEquipmentSize(containerSizeId != null ? catLangRepository.fnCatalogTranslationDesc(containerSizeId, languageId) : null);

            resp.setSeal1(eir.getSeal1());
            resp.setSeal2(eir.getSeal2());
            resp.setSeal3(eir.getSeal3());
            resp.setSeal4(eir.getSeal4());

            StringBuilder movementLarge = new StringBuilder();
            movementLarge.append(eir.getCatMovement() != null
                ? catLangRepository.fnCatalogTranslationDescLong(eir.getCatMovement().getId(), languageId)
                : "");
            movementLarge.append("  ");
            movementLarge.append(eir.getCatEmptyFull() != null
                ? catLangRepository.fnCatalogTranslationDescLong(eir.getCatEmptyFull().getId(), languageId)
                : "");

            if (eir.getCatOrigin() != null) {
                movementLarge.append(" - ");
                movementLarge.append(catLangRepository.fnCatalogTranslationDescLong(eir.getCatOrigin().getId(), languageId));
            }

            resp.setMovementLarge(movementLarge.toString());

            resp.setInputChassisNumber(eir.getChassisNumber());

            resp.setFindChassis(finalFindChassis);
            resp.setFindContainer(finalFindContainer);

            String equipmentSizeType = catLangRepository.fnCatalogTranslationDesc(eir.getCatSizeCnt().getId(), languageId) +
                    " - " +
                    catLangRepository.fnCatalogTranslationDescLong(eir.getCatContainerType().getId(), languageId);


            resp.setEquipmentSizeType(equipmentSizeType);

            resp.setDocumentChassisId(String.valueOf(Optional.of(eir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisDocument)
                    .map(ChassisDocument::getId)
                    .orElse(null)));

            resp.setCatProcedenciaId(eir.getCatOrigin() != null ? eir.getCatOrigin().getId() : null);


            Integer chassisQuantity = Optional.of(eirDocumentCargoDetail)
                    .map(EirDocumentCargoDetail::getEir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisBookingDocument)
                    .map(ChassisBookingDocument::getQuanty).orElse(0);

            Integer chassisAttendedQuantity = Optional.of(eirDocumentCargoDetail)
                    .map(EirDocumentCargoDetail::getEir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisBookingDocument)
                    .map(ChassisBookingDocument::getQuanty)
                    .orElse(0);

            String chaType = Optional.of(eirDocumentCargoDetail)
                    .map(EirDocumentCargoDetail::getEir)
                    .map(Eir::getEirChassis)
                    .map(EirChassis::getChassisDocumentDetail)
                    .map(ChassisDocumentDetail::getChassisBookingDocument)
                    .map(ChassisBookingDocument::getCatChassisType)
                    .map(Catalog::getDescription)
                    .orElse(null);

            resp.setChassisAvailabilityType(chaType != null ? chaType + "(" + (chassisAttendedQuantity - chassisQuantity) + ")" : null);// TODO review logic


            resp.setContainerAvailabilityType(resultContainerAvailavility);// TODO review logic

            resp.setAccelerateProgramNumber(eir.getAccelerateProgramNumber());


            resultList.add(resp);
        });


        return resultList;

    }


}
