package com.maersk.sd1.sdg.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maersk.sd1.common.dto.SystemRuleDTO;
import com.maersk.sd1.common.exception.SD1Exception;
import com.maersk.sd1.common.integration.*;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.*;
import lombok.RequiredArgsConstructor;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.maersk.sd1.common.Constants.*;
import static com.maersk.sd1.common.Parameter.*;

@Service
@RequiredArgsConstructor
public class GateOutGeneralRegisterService {


    private   GESCatalogService catalogService;

    private   MessageLanguageService messageLanguageService;

    private   ChassisDocumentRepository chassisDocumentRepository;

    private   EirChassisRepository eirChassisRepository;

    private   BusinessUnitRepository businessUnitRepository;

    private   ContainerRepository containerRepository;

    private   CargoDocumentRepository cargoDocumentRepository;

    private   SystemRuleRepository systemRuleRepository;

    private   ShippingLineRepository shippingLineRepository;

    private   IsoCodeRepository isoCodeRepository;

    private   BookingRepository bookingRepository;

    private   EirRepository eirRepository;

    private   BookingDetailRepository bookingDetailRepository;

    private   VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    private   EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    private   CargoDocumentDetailRepository cargoDocumentDetailRepository;

    private   TransportPlanningDetailRepository transportPlanningDetailRepository;

    private   AttachmentRepository attachmentRepository;

    private   EirGateDriverPhotoRepository eirGateDriverPhotoRepository;

    private   VesselRepository vesselRepository;

    private   VesselProgrammingRepository vesselProgrammingRepository;

    private  VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    @Autowired
    private Environment env;
    @Autowired
    private SD1IntegrationConnectionFactory connectionFactory;

    private GateInGeneralRegisterV2Service gateInGeneralRegisterService;
    @Autowired
    public void setGateInGeneralRegisterService(@Lazy GateInGeneralRegisterV2Service service) {
        this.gateInGeneralRegisterService = service;
    }

    public GateInGeneralRegisterV2Service getGateInGeneralRegisterService() {
        return gateInGeneralRegisterService;
    }

    private List<String> catalogsAliases = Arrays.asList(
            CATALOG_TYPE_GATE_IS_GATEIN_ALIAS,
            CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS,
            CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
            CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
            CATALOG_CONTAINER_MOVEMENT_POSITIONING_ALIAS,
            CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS,
            CATALOG_MEASURE_WEIGHT_KG_ALIAS,
            CATALOG_TRANSPORT_PLANNING_STATUS_SCHEDULED_ALIAS,
            CATALOG_TYPE_CREATION_SOURCE_GENERAL_GATEOUT_ALIAS,
            CATALOG_CHASSIS_IN_PROGRESS,
            CATALOG_TRK_PLAN_IN_PROCESS,
            CATALOG_MEANS_TRANSPORT,
            CATALOG_NO_GRADE,
            IS_CREATION_GO_GENERAL,
            CATALOG_TYPE_CONTAINER_DRY_ALIAS,
            IS_TYPE_BOOKING,
            IS_ISO_CODE_DUMMY,
            CATALOG_CHASSIS_IN_PENDING_ALIAS,
            CATALOG_DEPOT_OPER_MTY_LOAD_ALIAS,
            CATALOG_SIZE_CONTAINER_20_ALIAS,
            CATALOG_SIZE_CONTAINER_40_ALIAS,
            CATALOG_CNT_FAMILY_DRY_ALIAS,
            CATALOG_CNT_TYPE_FLAT_ALIAS,
            CATALOG_CNT_TYPE_FLAT_RACK_ALIAS,
            CATALOG_CNT_TYPE_PLATFORM_ALIAS,
            CATALOG_DOC_TYPE_BL_ALIAS,
            CATALOG_CHASSIS_OPETYPE_CUSDELIVERY_ALIAS,
            CATALOG_DEPOT_OPER_MTY_DISCHARGE
    );

    private HashMap<String, Integer> catalogIds;

    @Transactional
    public GateOutGeneralRegisterOutput gateOutGeneralRegisterTransactional(GateOutGeneralRegisterInput.Root request) throws Exception {
        return gateOutGeneralRegister(request.getPrefix().getInput());
    }

    public GateOutGeneralRegisterOutput gateOutGeneralRegisterNonTransactional(GateOutGeneralRegisterInput.Root request) throws Exception {
        return gateOutGeneralRegister(request.getPrefix().getInput());
    }

    private GateOutGeneralRegisterOutput gateOutGeneralRegister(GateOutGeneralRegisterInput.Input gateOutInput) throws Exception {
        gateOutInput.setSystemRuleId("sd1_rule_integration_sdy");
        gateOutInput.setTypeProcess("gateout");
        HashMap<String, Object> multipleResults = new HashMap<>();
        GateOutGeneralRegisterOutput gateOutOutput = new GateOutGeneralRegisterOutput();
        final GateOutGeneralRegisterOutput gateOutErrorOutput = new GateOutGeneralRegisterOutput();
        String chassisJson = gateOutInput.getChassis();

        if (!gateOutInput.getFlagChassisPickup() && chassisJson != null) { //IMPLEMENT DUMMY GATEIN FOR THE CHASSIS
            //1. Register GateIn
            GateInGeneralRegisterV2Input.Root gateInRequest = GateInGeneralRegisterV2Input.Root.builder()
                    .prefix(GateInGeneralRegisterV2Input.Prefix.builder()
                            .input(GateInGeneralRegisterV2Input.Input.generateDummyGateInInput(gateOutInput))
                            .build())
                    .build();

            GateInGeneralRegisterV2Output.Output gateInOutput = gateInGeneralRegisterService.registerGateInNonTransactional(gateInRequest);
            if (gateInOutput != null) {
                if (gateInOutput.getResult().getResultState().equals(1)) {
                    multipleResults = registerData(gateOutInput);
                    gateOutOutput = (GateOutGeneralRegisterOutput) multipleResults.get(GATEOUT_RESULT);
                } else {
                    return GateOutGeneralRegisterOutput.builder()
                            .resultNewId(0)
                            .resultState(gateInOutput.getResult().getResultState())
                            .resultMessage(gateInOutput.getResult().getResultMessage())
                            .build();
                }
            }
        } else {
            multipleResults = registerData(gateOutInput);
            gateOutOutput = (GateOutGeneralRegisterOutput) multipleResults.get(GATEOUT_RESULT);
        }


        multipleResults.forEach((key, result) -> {
            if (result == null) {
                gateOutErrorOutput.setResultNewId(0);
                gateOutErrorOutput.setResultState(2);
                gateOutErrorOutput.setResultMessage("Error: " + key);
            } else if (key.equals(GATEOUT_RESULT)) {
                GateOutGeneralRegisterOutput output = (GateOutGeneralRegisterOutput) result;
                if (!output.getResultState().equals(1)) {
                    gateOutErrorOutput.setResultNewId(0);
                    gateOutErrorOutput.setResultState(output.getResultState());
                    gateOutErrorOutput.setResultMessage(output.getResultMessage());
                }
            } else {
                HashMap<String, Object> resultMap = (HashMap<String, Object>) result;
                if (resultMap.isEmpty() || resultMap.values().stream().anyMatch(rm -> rm.toString().equals("0"))) {
                    gateOutErrorOutput.setResultNewId(0);
                    gateOutErrorOutput.setResultState(2);
                    gateOutErrorOutput.setResultMessage(resultMap.get("result_message").toString());
                }
            }
        });

        if (gateOutErrorOutput.getResultState() != null) {
            //ROLLBACK
            throw new SD1Exception(this.getClass().getName(), gateOutErrorOutput.getResultMessage());
        }
        return gateOutOutput;
    }

    private HashMap<String, Object> registerData(GateOutGeneralRegisterInput.Input gateOutInput) {
        Integer chassisId = null;
        HashMap<String, Object> validateChassisResult;
        HashMap<String, Object> chassisAssignmentResult;
        GateOutGeneralRegisterOutput gateOutOutput = null;
        HashMap<String, Object> results = new HashMap<>();

        if (!gateOutInput.getFlagChassisPickup() && gateOutInput.getChassis() != null) {
            //2. Register Document Chassis Gate Out Generated v2
            validateChassisResult = chassisDocumentRepository.documentChassisGateOutGenerated(gateOutInput.getSubBusinessUnitLocalId(), gateOutInput.getChassis(), gateOutInput.getUserRegistrationId(), gateOutInput.getLanguageId());
            results.put("validateChassisResult", validateChassisResult);
            if (validateChassisResult != null && !validateChassisResult.isEmpty() && validateChassisResult.get(RESULT_STATE) != null && validateChassisResult.get(RESULT_STATE).equals(1)) {
                gateOutInput.setDocumentChassisId(Integer.valueOf(validateChassisResult.get("result_doc_gateout_id").toString()));
                chassisId = Integer.valueOf(validateChassisResult.get("result_chassis_id").toString());
            }
        }

        //3. Register Gate Out
        gateOutOutput = execute(gateOutInput);
        results.put(GATEOUT_RESULT, gateOutOutput);
        if (gateOutOutput == null || gateOutOutput.getResultState() == null || !gateOutOutput.getResultState().equals(1)) {
            return results;
        } else {
            if (gateOutOutput.getResultEirListId() != null) {
                Type listType = new TypeToken<List<HashMap<String, String>>>() {
                }.getType();
                List<HashMap<String, String>> eirList = new Gson().fromJson(gateOutOutput.getResultEirListId(), listType);
                int pivot = 0;

                for (HashMap<String, String> eir : eirList) {
                    if (eir.get("cat_movimiento_id").equals(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)) && pivot == 0) {
                        chassisAssignmentResult = gateoutGeneralAssignmentChassisRegisterV3(gateOutInput, Integer.valueOf(eir.get(EIR_ID)), gateOutInput.getFlagChassisPickup(), chassisId);
                        if (chassisAssignmentResult != null && !chassisAssignmentResult.isEmpty()) {
                            results.put("chassisAssignmentResult", chassisAssignmentResult);
                            return results;
                        }
                    }
                    pivot++;
                }
            } else {
                //ONLY FOR CHASSIS
                chassisAssignmentResult = gateoutGeneralAssignmentChassisRegisterV3(gateOutInput, gateOutOutput.getResultNewId(), gateOutInput.getFlagChassisPickup(), chassisId);
                if (chassisAssignmentResult != null && !chassisAssignmentResult.isEmpty()) {
                    results.put("chassisAssignmentResult", chassisAssignmentResult);
                    return results;
                }
            }
        }

        return results;
    }

    private HashMap<String, Object> gateoutGeneralAssignmentChassisRegisterV3(GateOutGeneralRegisterInput.Input gateOutInput, Integer eirId, Boolean flagChassisPickup, Integer chassisId) {
        if (!flagChassisPickup && chassisId != null) {
            //4. Register Gate Out General Assignment Chassis Register v2
            HashMap<String, Object> chassisAssignmentResult = eirChassisRepository.gateoutGeneralAssignmentChassisRegister(eirId, chassisId, gateOutInput.getUserRegistrationId(), gateOutInput.getLanguageId());
            Integer resultChassisAssignmentState = Integer.valueOf(chassisAssignmentResult.get(RESULT_STATE).toString());
            if (resultChassisAssignmentState.equals(2)) {
                chassisAssignmentResult.clear();
                chassisAssignmentResult.put(RESULT_STATE, 0);
                chassisAssignmentResult.put("result_message", "Error : sdggateoutGeneralAssignmentChassisRegisterV3");
                return chassisAssignmentResult;
            }
        }
        return new HashMap<>();
    }

    private GateOutGeneralRegisterOutput execute(GateOutGeneralRegisterInput.Input input) {
        //CATALOGS
        this.catalogIds = catalogService.findIdsByAliases(catalogsAliases);

        //VARIABLES
        Integer equipmentNotApplicableId = null;
        Integer catMarcaMotorId = null;
        Integer reeferTypeId = null;
        Integer shippinglineBl = null;
        Integer consigneeId = null;
        Integer catCargoDocumentTypeId = null;
        Integer eirChassisId = null;
        Integer depotOperationId;
        Integer vesselId;
        Integer programacionNaveId;
        Integer eirId = null;
        Integer catMovimientoId = catalogIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer catEmptyFullId = input.getEquipmentCatEmptyFullId();
        Integer inspectorId = null;
        Integer catReceivedWeightMeasureId = null;
        Integer equipmentTypeId = null;
        Integer equipmentSizeId = null;
        Integer equipmentFamilyId;
        Integer codigoIsoId = null;
        Integer tare = null;
        Integer catEquipMeasureTareId = null;
        Integer payLoad = null;
        Integer catEquipMeasurePayloadId = null;
        Integer gradeId = null;
        Integer containerDummyId = null;
        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId());
        Integer businessUnitId = businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId);
        Integer isEcuador = businessUnitRepository.findByBusinesUnitAlias("ECU").getId();
        String documentoCargaReferencia = null;
        String equipmentNotApplicable = "NOT APPLICA";
        String isIsocodeDummy20dryCode = "2000";
        String equipmentNumber = null;
        String isReefer;
        String containerDummy = "NO-CNT";
        String shippingLineOthers = "OTHER";
        String vessel = "VARIOUS";

        Boolean isBkSpecialType = false;
        Boolean boxWithDamage = false;
        Boolean boxDamaged = false;
        Boolean machineryWithDamage = false;
        Boolean machineryDamaged = false;
        Boolean isBkSize20 = false;


        Short reviewControl = null;
        Short allocationControlLight = null;
        Short chassisAllocationControl = null;

        BigDecimal netWeight = BigDecimal.ZERO;

        LocalDateTime truckInDate = LocalDateTime.now();



        LocalDateTime manufacture = null;

        //UTIL
        Gson gson = new Gson();
        JSONArray eirRegistered = new JSONArray();
        Pageable pageable = PageRequest.of(0, 1);

        GateOutGeneralRegisterOutput output = new GateOutGeneralRegisterOutput();
        output.setResultNewId(0);
        output.setResultState(2);
        output.setResultMessage("");
        output.setResultEirChassisId(0);

        Optional<ShippingLine> maerskShippingLine = shippingLineRepository.findByShippingLine("Maersk Line");
        Optional<IsoCode> isoCodeDummy = isoCodeRepository.findByIsoCode("4000");

        input.setSeal1(input.getSeal1() != null ? input.getSeal1().trim().toUpperCase() : null);
        input.setSeal2(input.getSeal2() != null ? input.getSeal2().trim().toUpperCase() : null);
        input.setSeal3(input.getSeal3() != null ? input.getSeal3().trim().toUpperCase() : null);
        input.setSeal4(input.getSeal4() != null ? input.getSeal4().trim().toUpperCase() : null);

        if (input.getInputChassisNumber() != null) {
            input.setFlagChassisPickup(false);
        }

        //VALIDATION
        if (businessUnitId == null || subBusinessUnitId == null || input.getSubBusinessUnitLocalId() == null || input.getDriverId() == null || input.getVehicleId() == null || input.getTruckCompanyId() == null
                || (input.getDocumentChassisId() == null && input.getDocumentoCargaId() == null)
                || (catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) && input.getMoveTypeId() == null)
                || (catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) && input.getBookingId() == null)
                || catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId) && input.getEquipmentId() != null && input.getDocumentoCargaDetalleId() == null) {
            output.setResultMessage(messageLanguageService.getMessage("GENERAL", 21, input.getLanguageId()));
            return output;
        }

        if (input.getEquipmentCatEmptyFullId() != null && input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)) && businessUnitId.equals(isEcuador) && input.getMoveTypeId().equals(catalogIds.get(CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS)) && input.getApsId() == null) {
            output.setResultMessage(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY, 13, input.getLanguageId()));
            return output;
        }

        if (catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) || (catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId) && input.getEquipmentId() == null) || input.getDocumentChassisId() != null) {
            String validationMessage = cargoDocumentRepository.validateDocumentAvailability(businessUnitId, catEmptyFullId, input.getBookingId(), input.getDocumentoCargaId(), input.getDocumentChassisId(), input.getLanguageId());
            if (validationMessage != null && !validationMessage.isBlank()) {
                output.setResultMessage(validationMessage);
                return output;
            }
        }

        //SDY INTEGRATION
        if ((catalogIds.containsKey(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS) && catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) ||
                catalogIds.containsKey(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS) && catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId)) &&
                input.getSystemRuleId() != null && input.getDocumentoCargaId() != null) {
            SystemRule systemRule = systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId());
            Type integrationSystemRuleListType = new TypeToken<ArrayList<SystemRuleDTO>>() {
            }.getType();
            List<SystemRuleDTO> integrationSystemRuleList = gson.fromJson(systemRule.getRule(), integrationSystemRuleListType);
            Optional<BusinessUnit> optionalLocalBusinessUnit = businessUnitRepository.findById(input.getSubBusinessUnitLocalId());
            if (optionalLocalBusinessUnit.isPresent()) {
                BusinessUnit localBusinessUnit = optionalLocalBusinessUnit.get();
                output.setResultTypeIntegration(integrationSystemRuleList.stream().filter(sr -> sr.getSubBusinessUnitLocalAlias().equals(localBusinessUnit.getBusinesUnitAlias()) && sr.getTypeProcess().equals(input.getTypeProcess())).findFirst().map(SystemRuleDTO::getTypeProductIntegration).orElse(null));
            }
            output.setResultContainerContentType(catEmptyFullId.equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)) ? "F" : "E");
        }

        if (catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) && input.getEquipmentId() == null) {
            String availabilityCheck = containerRepository.fnCheckContainerGateoutAvailability(subBusinessUnitId, input.getSubBusinessUnitLocalId(), input.getContainerSizeId(), input.getContainerTypeId(), input.getContainerGradeId(), input.getBookingId(), input.getQuantity(), output.getResultTypeIntegration().equals("sdy") ? Short.valueOf("1") : Short.valueOf("0"));
            if (availabilityCheck == null || !availabilityCheck.equals("OK")) {
                output.setResultMessage(availabilityCheck);
                return output;
            }
        }

        if ((catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId) || catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId)) && input.getEquipmentId() == null) { //DUMMY CONTAINER
            Container dummyContainer = containerRepository.findByContainerNumber(containerDummy);
            if (dummyContainer == null) {
                dummyContainer = containerRepository.save(Container.builder()
                        .containerNumber(containerDummy)
                        .catFamily(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS)).build())
                        .catSize(Catalog.builder().id(catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS)).build())
                        .catContainerType(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS)).build())
                        .shippingLine(maerskShippingLine.orElse(null))
                        .containerTare(3500)
                        .maximunPayload(28250)
                        .isoCode(isoCodeDummy.orElse(null))
                        .catGrade(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                        .manufactureDate(LocalDate.parse("1961-01-01").atStartOfDay())
                        .shipperOwn(false)
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .traceContainer("ins_go_gral")
                        .forSale(false)
                        .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                        .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                        .build());
            } else if (!dummyContainer.getActive()) {
                dummyContainer.setActive(true);
                dummyContainer.setTraceContainer("RecupCntDummyNO");
                containerRepository.save(dummyContainer);
            }

            containerDummyId = dummyContainer.getId();
            equipmentSizeId = Optional.ofNullable(dummyContainer.getCatSize()).map(Catalog::getId).orElse(null);
            equipmentTypeId = Optional.ofNullable(dummyContainer.getCatContainerType()).map(Catalog::getId).orElse(null);
            tare = dummyContainer.getContainerTare();
            catEquipMeasureTareId = Optional.ofNullable(dummyContainer.getCatEquipMeasureTare()).map(Catalog::getId).orElse(null);
            payLoad = dummyContainer.getMaximunPayload();
            catEquipMeasurePayloadId = Optional.ofNullable(dummyContainer.getCatEquipMeasurePayload()).map(Catalog::getId).orElse(null);
            codigoIsoId = Optional.ofNullable(dummyContainer.getIsoCode()).map(IsoCode::getId).orElse(null);
            gradeId = Optional.ofNullable(dummyContainer.getCatGrade()).map(Catalog::getId).orElse(null);
            manufacture = dummyContainer.getManufactureDate();
        }

        List<Eir> redundantEirs = eirRepository.findByLocalBusinessUnitIdVehicleIdMovementIdAndActiveWithNoTruckDepartureDate(input.getSubBusinessUnitLocalId(), input.getVehicleId(), catMovimientoId, true); //QEirs
        if (catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS).equals(catEmptyFullId)) {
            Optional<Booking> booking = bookingRepository.findByBookingIdAndActive(input.getBookingId(), true);
            consigneeId = booking.map(Booking::getClientCompany).map(Company::getId).orElse(null);
            shippinglineBl = booking.map(Booking::getShippingLine).map(ShippingLine::getId).orElse(null);
            catCargoDocumentTypeId = catalogIds.get(IS_TYPE_BOOKING);

            if (!redundantEirs.isEmpty()) {
                if (bookingDetailRepository.findPendingBookingDetailByBookingIdCatSizeIdAndActiveTrue(input.getBookingId(), catalogIds.get(CATALOG_SIZE_CONTAINER_20_ALIAS), true).size() > 0) {
                    isBkSize20 = true;
                }
                if (bookingDetailRepository.findPendingBookingDetailByBookingIdCatTypeIdsAndActiveTrue(input.getBookingId(), Arrays.asList(catalogIds.get(CATALOG_CNT_TYPE_FLAT_ALIAS), catalogIds.get(CATALOG_CNT_TYPE_FLAT_RACK_ALIAS), catalogIds.get(CATALOG_CNT_TYPE_PLATFORM_ALIAS)), true).size() > 0) {
                    isBkSpecialType = true;
                }

                if ((redundantEirs.size() != 1 || !isBkSize20) && (redundantEirs.isEmpty() || redundantEirs.size() > 3 || !isBkSpecialType)) {
                    output.setResultMessage(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY, 12, input.getLanguageId()));
                    return output;
                }
            }
        }

        if (catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS).equals(catEmptyFullId)) {
            if (input.getEquipmentId() != null) {
                allocationControlLight = 1;

                Optional<Container> equipmentContainer = containerRepository.findById(input.getEquipmentId());
                equipmentTypeId = equipmentContainer.map(Container::getCatContainerType).map(Catalog::getId).orElse(null);
                equipmentSizeId = equipmentContainer.map(Container::getCatSize).map(Catalog::getId).orElse(null);
                tare = equipmentContainer.map(Container::getContainerTare).orElse(null);
                catEquipMeasureTareId = equipmentContainer.map(Container::getCatEquipMeasureTare).map(Catalog::getId).orElse(null);
                payLoad = equipmentContainer.map(Container::getMaximunPayload).orElse(null);
                catEquipMeasurePayloadId = equipmentContainer.map(Container::getCatEquipMeasurePayload).map(Catalog::getId).orElse(null);
                gradeId = equipmentContainer.map(Container::getCatGrade).map(Catalog::getId).orElse(null);
                manufacture = equipmentContainer.map(Container::getManufactureDate).orElse(null);
                catMarcaMotorId = equipmentContainer.map(Container::getCatEngineBrand).map(Catalog::getId).orElse(null);
                equipmentNumber = equipmentContainer.map(Container::getContainerNumber).orElse(null);
                codigoIsoId = equipmentContainer.map(Container::getIsoCode).map(IsoCode::getId).orElse(null);
                isReefer = equipmentContainer.map(Container::getCatContainerType).map(Catalog::getCode).orElse(null);

                if (isReefer == null) {
                    reeferTypeId = null;
                    catMarcaMotorId = null;
                } else {
                    reeferTypeId = containerRepository.fnGetDefaultReeferType(equipmentNumber);
                }

                if (gradeId == null) {
                    gradeId = catalogIds.get(CATALOG_NO_GRADE);
                }

                Optional<CargoDocumentDetail> cargoDocumentDetail = vesselProgrammingContainerRepository.findActiveVesselProgrammingContainerByContainerIdAndProgrammingDetailId(input.getEquipmentId(), input.getProgramacionNaveDetalleId());
                consigneeId = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getConsigneeCompany).map(Company::getId).orElse(null);
                shippinglineBl = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getShippingLine).map(ShippingLine::getId).orElse(null);
                documentoCargaReferencia = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCargoDocument).orElse(null);
                netWeight = cargoDocumentDetail.map(CargoDocumentDetail::getReceivedWeight).orElse(null);
                catReceivedWeightMeasureId = cargoDocumentDetail.map(CargoDocumentDetail::getCatReceivedWeightMeasure).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
                catCargoDocumentTypeId = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCatCargoDocumentType).map(Catalog::getId).orElse(null);

                output.setResultMessage(cargoDocumentDetailRepository.validateContainerStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS), input.getEquipmentId(), equipmentNumber, 1, catalogIds.get(CATALOG_TYPE_CREATION_SOURCE_GENERAL_GATEOUT_ALIAS), "adjust_stock_ggof", input.getLanguageId()));
            } else {
                allocationControlLight = 0;
                Optional<CargoDocument> cargoDocument = cargoDocumentRepository.findByIdAndActive(input.getDocumentoCargaId(), true);
                consigneeId = cargoDocument.map(CargoDocument::getConsigneeCompany).map(Company::getId).orElse(null);
                shippinglineBl = cargoDocument.map(CargoDocument::getShippingLine).map(ShippingLine::getId).orElse(null);
                catCargoDocumentTypeId = cargoDocument.map(CargoDocument::getCatCargoDocumentType).map(Catalog::getId).orElse(null);

                if (!redundantEirs.isEmpty()) {
                    if (!cargoDocumentRepository.findActiveByIdContainerSizeWithPositiveBalance(input.getDocumentoCargaId(), catalogIds.get(CATALOG_SIZE_CONTAINER_20_ALIAS)).isEmpty()) {
                        isBkSize20 = true;
                    }

                    if (redundantEirs.size() != 1 || !isBkSize20) {
                        output.setResultMessage(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY, 12, input.getLanguageId()));
                    }
                }
            }
            if (catCargoDocumentTypeId == null) {
                catCargoDocumentTypeId = catalogIds.get(CATALOG_DOC_TYPE_BL_ALIAS);
            }
        } else {
            equipmentTypeId = input.getContainerTypeId();
            equipmentSizeId = input.getContainerSizeId();
            gradeId = input.getContainerGradeId();
        }

        if (!output.getResultMessage().isBlank()) {
            return output;
        }

        Type pictureListType = new TypeToken<ArrayList<com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterPicture>>() {
        }.getType();
        ArrayList<GateOutGeneralRegisterPicture> driverPictures = gson.fromJson(input.getDriverPictures(), pictureListType);

        if (input.getEquipmentCatEmptyFullId() != null) {
            if (input.getEquipmentId() == null) {
                input.setEquipmentId(containerDummyId);
                equipmentNumber = containerDummy;
            }

            if (input.getQuantity() == null) {
                Eir newEir = eirRepository.save(Eir.builder()
                        .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                        .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                        .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                        .catMovement(Optional.ofNullable(catMovimientoId).map(cm -> Catalog.builder().id(cm).build()).orElse(null))
                        .catEmptyFull(Optional.ofNullable(input.getEquipmentCatEmptyFullId()).map(ef -> Catalog.builder().id(ef).build()).orElse(null))
                        .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                        .truckArrivalDate(truckInDate)
                        .vesselProgrammingDetail(Optional.ofNullable(input.getProgramacionNaveDetalleId()).map(vpd -> VesselProgrammingDetail.builder().id(vpd).build()).orElse(null))
                        .container(Optional.ofNullable(input.getEquipmentId()).map(ei -> Container.builder().id(ei).build()).orElse(null))
                        .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                        .truck(Truck.builder().id(input.getVehicleId()).build())
                        .driverPerson(Person.builder().id(input.getDriverId()).build())
                        .shippingLine(Optional.ofNullable(shippinglineBl).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                        .taraCnt(tare)
                        .cargoMaximumCnt(payLoad)
                        .isoCode(Optional.ofNullable(codigoIsoId).map(ci -> IsoCode.builder().id(ci).build()).orElse(null))
                        .catContainerType(Optional.ofNullable(equipmentTypeId).map(et -> Catalog.builder().id(et).build()).orElse(null))
                        .catSizeCnt(Optional.ofNullable(equipmentSizeId).map(es -> Catalog.builder().id(es).build()).orElse(null))
                        .catClassCnt(Optional.ofNullable(gradeId).map(gi -> Catalog.builder().id(gi).build()).orElse(null))
                        .catTypeReefer(Optional.ofNullable(reeferTypeId).map(rt -> Catalog.builder().id(rt).build()).orElse(null))
                        .catEngineBrand(Optional.ofNullable(catMarcaMotorId).map(mm -> Catalog.builder().id(mm).build()).orElse(null))
                        .dateManufacture(manufacture)
                        .observation(input.getComments())
                        .structureWithDamage(boxWithDamage)
                        .structureDamaged(boxDamaged)
                        .machineryWithDamage(machineryWithDamage)
                        .machineryDamaged(machineryDamaged)
                        .inspectorPerson(Optional.ofNullable(inspectorId).map(ii -> Person.builder().id(ii).build()).orElse(null))
                        .truckMultipleLoad(false)
                        .truckRetreatWithLoad(input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)))
                        .active(true)
                        .seal1(input.getSeal1())
                        .seal2(input.getSeal2())
                        .seal3(input.getSeal3())
                        .seal4(input.getSeal4())
                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CREATION_SOURCE_GENERAL_GATEOUT_ALIAS)).build())
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .clientCompany(Optional.ofNullable(consigneeId).map(ci -> Company.builder().id(ci).build()).orElse(null))
                        .controlAssignmentLight(allocationControlLight)
                        .controlRevision(reviewControl)
                        .traceEir(INS_GO_GENERAL)
                        .bookingGout(Optional.ofNullable(input.getBookingId()).map(bi -> Booking.builder().id(bi).build()).orElse(null))
                        .externalDocumentNumber(Optional.ofNullable(input.getApsId()).map(ai -> ai.toString()).orElse(null))
                        .externalDocumentNumberRef(Optional.ofNullable(input.getExternalCode()).map(dn -> dn).orElse(null))
                        .weightGoods(netWeight)
                        .catMeasureWeight(Optional.ofNullable(catReceivedWeightMeasureId).map(rwm -> Catalog.builder().id(rwm).build()).orElse(null))
                        .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                        .catMeasureTare(Optional.ofNullable(catEquipMeasureTareId).map(emt -> Catalog.builder().id(emt).build()).orElse(null))
                        .catMeasurePayload(Optional.ofNullable(catEquipMeasurePayloadId).map(emp -> Catalog.builder().id(emp).build()).orElse(null))
                        .transportPlanningDetailFull(Optional.ofNullable(input.getPlanningDetailId()).map(pd -> TransportPlanningDetail.builder().id(pd).build()).orElse(null))
                        .documentCargoGof(input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)) && input.getEquipmentId().equals(containerDummyId) ? CargoDocument.builder().id(input.getDocumentoCargaId()).build() : null)
                        .flagChassisPickup(input.getFlagChassisPickup())
                        .chassisNumber(input.getInputChassisNumber())
                        .catGradeGateOut(Optional.ofNullable(input.getContainerGradeId()).map(cg -> Catalog.builder().id(cg).build()).orElse(null))
                        .preAllocatedContainer(Optional.ofNullable(input.getPreAllocatedContainerId()).map(pc -> Container.builder().id(pc).build()).orElse(null))
                        .numberTwr(input.getTwrNumber())
                        .accelerateProgramNumber(input.getAccelerateProgramNumber())
                        .build());

                eirId = newEir.getId();
            } else {
                for (int i = 1; i <= input.getQuantity(); i++) {
                    Eir newEir = eirRepository.save(Eir.builder()
                            .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                            .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                            .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                            .catMovement(Optional.ofNullable(catMovimientoId).map(cm -> Catalog.builder().id(cm).build()).orElse(null))
                            .catEmptyFull(Optional.ofNullable(input.getEquipmentCatEmptyFullId()).map(ef -> Catalog.builder().id(ef).build()).orElse(null))
                            .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                            .truckArrivalDate(truckInDate)
                            .vesselProgrammingDetail(Optional.ofNullable(input.getProgramacionNaveDetalleId()).map(vpd -> VesselProgrammingDetail.builder().id(vpd).build()).orElse(null))
                            .container(Optional.ofNullable(input.getEquipmentId()).map(ei -> Container.builder().id(ei).build()).orElse(null))
                            .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                            .truck(Truck.builder().id(input.getVehicleId()).build())
                            .driverPerson(Person.builder().id(input.getDriverId()).build())
                            .shippingLine(Optional.ofNullable(shippinglineBl).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                            .taraCnt(tare)
                            .cargoMaximumCnt(payLoad)
                            .isoCode(Optional.ofNullable(codigoIsoId).map(ci -> IsoCode.builder().id(ci).build()).orElse(null))
                            .catContainerType(Optional.ofNullable(equipmentTypeId).map(et -> Catalog.builder().id(et).build()).orElse(null))
                            .catSizeCnt(Optional.ofNullable(equipmentSizeId).map(es -> Catalog.builder().id(es).build()).orElse(null))
                            .catClassCnt(Optional.ofNullable(gradeId).map(gi -> Catalog.builder().id(gi).build()).orElse(null))
                            .catTypeReefer(Optional.ofNullable(reeferTypeId).map(rt -> Catalog.builder().id(rt).build()).orElse(null))
                            .catEngineBrand(Optional.ofNullable(catMarcaMotorId).map(mm -> Catalog.builder().id(mm).build()).orElse(null))
                            .dateManufacture(manufacture)
                            .observation(input.getComments())
                            .structureWithDamage(boxWithDamage)
                            .structureDamaged(boxDamaged)
                            .machineryWithDamage(machineryWithDamage)
                            .machineryDamaged(machineryDamaged)
                            .inspectorPerson(Optional.ofNullable(inspectorId).map(ii -> Person.builder().id(ii).build()).orElse(null))
                            .truckMultipleLoad(false)
                            .truckRetreatWithLoad(input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)))
                            .active(true)
                            .seal1(input.getSeal1())
                            .seal2(input.getSeal2())
                            .seal3(input.getSeal3())
                            .seal4(input.getSeal4())
                            .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CREATION_SOURCE_GENERAL_GATEOUT_ALIAS)).build())
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .clientCompany(Optional.ofNullable(consigneeId).map(ci -> Company.builder().id(ci).build()).orElse(null))
                            .controlAssignmentLight(allocationControlLight)
                            .controlRevision(reviewControl)
                            .traceEir(INS_GO_GENERAL)
                            .bookingGout(Optional.ofNullable(input.getBookingId()).map(bi -> Booking.builder().id(bi).build()).orElse(null))
                            .externalDocumentNumber(Optional.ofNullable(input.getApsId()).map(ai -> ai.toString()).orElse(null))
                            .externalDocumentNumberRef(input.getExternalCode())
                            .weightGoods(netWeight)
                            .catMeasureWeight(Optional.ofNullable(catReceivedWeightMeasureId).map(rwm -> Catalog.builder().id(rwm).build()).orElse(null))
                            .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                            .catMeasureTare(Optional.ofNullable(catEquipMeasureTareId).map(emt -> Catalog.builder().id(emt).build()).orElse(null))
                            .catMeasurePayload(Optional.ofNullable(catEquipMeasurePayloadId).map(emp -> Catalog.builder().id(emp).build()).orElse(null))
                            .transportPlanningDetailFull(Optional.ofNullable(input.getPlanningDetailId()).map(pd -> TransportPlanningDetail.builder().id(pd).build()).orElse(null))
                            .documentCargoGof(input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)) && input.getEquipmentId().equals(containerDummyId) ? CargoDocument.builder().id(input.getDocumentoCargaId()).build() : null)
                            .flagChassisPickup(input.getFlagChassisPickup())
                            .chassisNumber(input.getInputChassisNumber())
                            .catGradeGateOut(Optional.ofNullable(input.getContainerGradeId()).map(cg -> Catalog.builder().id(cg).build()).orElse(null))
                            .preAllocatedContainer(Optional.ofNullable(input.getPreAllocatedContainerId()).map(pc -> Container.builder().id(pc).build()).orElse(null))
                            .numberTwr(input.getTwrNumber())
                            .accelerateProgramNumber(input.getAccelerateProgramNumber())
                            .build());

                    JSONObject eirJson = new JSONObject();
                    eirJson.put("id", i);
                    eirJson.put(EIR_ID, newEir.getId());
                    eirJson.put("cat_movimiento_id", catMovimientoId);
                    eirRegistered.put(eirJson);
                }
            }

            if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))
                    && !input.getEquipmentId().equals(containerDummyId)) {
            if (eirRegistered.isEmpty()) {
                    eirDocumentCargoDetailRepository.save(EirDocumentCargoDetail.builder()
                            .eir(Eir.builder().id(eirId).build())
                            .cargoDocumentDetail(Optional.ofNullable(input.getDocumentoCargaDetalleId()).map(dcc -> CargoDocumentDetail.builder().id(input.getDocumentoCargaDetalleId()).build()).orElse(null))
                            .active(true)
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .documentCargoReference(documentoCargaReferencia)
                            .catCargoDocumentType(Optional.ofNullable(catCargoDocumentTypeId).map(cdt -> Catalog.builder().id(cdt).build()).orElse(null))
                            .build());
                } else {
                    final String documentoCargaReferenciaClone = documentoCargaReferencia;
                    final Integer catCargoDocumentTypeIdClone = catCargoDocumentTypeId;
                    eirRegistered.forEach(
                            it -> {
                                JSONObject eir = new JSONObject(it);
                                eirDocumentCargoDetailRepository.save(EirDocumentCargoDetail.builder()
                                        .eir(Eir.builder().id(eir.getInt(EIR_ID)).build())
                                        .cargoDocumentDetail(Optional.ofNullable(input.getDocumentoCargaDetalleId()).map(dcc -> CargoDocumentDetail.builder().id(dcc).build()).orElse(null))
                                        .active(true)
                                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                                        .registrationDate(LocalDateTime.now())
                                        .documentCargoReference(documentoCargaReferenciaClone)
                                        .catCargoDocumentType(Optional.ofNullable(catCargoDocumentTypeIdClone).map(cdt -> Catalog.builder().id(cdt).build()).orElse(null))
                                        .build());
                            }
                    );
                }

                CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailRepository.findById(input.getDocumentoCargaDetalleId()).orElseThrow();
                cargoDocumentDetail.setDispatchedQuantity(BigDecimal.ONE);
                cargoDocumentDetail.setBalanceQuantity(BigDecimal.ZERO);
                cargoDocumentDetail.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                cargoDocumentDetail.setTraceCargoDocumentDetail(INS_GO_GENERAL);
                cargoDocumentDetailRepository.save(cargoDocumentDetail);

                if (input.getPlanningDetailId() != null) {
                    TransportPlanningDetail transportPlanningDetail = transportPlanningDetailRepository.findById(input.getPlanningDetailId()).orElseThrow();
                    transportPlanningDetail.setCatTrkPlanningState(Catalog.builder().id(catalogIds.get(CATALOG_TRK_PLAN_IN_PROCESS)).build());
                    transportPlanningDetail.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                    transportPlanningDetail.setModificationDate(LocalDateTime.now());
                    transportPlanningDetailRepository.save(transportPlanningDetail);
                }

                if (driverPictures.stream().anyMatch(dp -> dp.getPhotoType().equals("D"))) {
                    List<Attachment> attachments = new ArrayList<>();
                    for (GateOutGeneralRegisterPicture picture : driverPictures.stream().filter(dp -> dp.getPhotoType().equals("D")).distinct().toList()) {
                        attachments.add(attachmentRepository.save(Attachment.builder()
                                .name(picture.getName())
                                .format(picture.getFormat())
                                .weight(Integer.parseInt(picture.getWeight()))
                                .location(picture.getLocation())
                                .status(true)
                                .id1(picture.getId())
                                .catAttachmentType(Catalog.builder().id(picture.getAttachmentType()).build())
                                .url(picture.getUrl())
                                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                                .registrationDate(LocalDateTime.now())
                                .build()));
                    }
                    if (!attachments.isEmpty()) {
                        for (Attachment attachment : attachments) {
                            eirGateDriverPhotoRepository.save(EirGateDriverPhoto.builder()
                                    .eir(Eir.builder().id(eirId).build())
                                    .attachment(attachment)
                                    .active(true)
                                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                                    .registrationDate(LocalDateTime.now())
                                    .build());
                        }
                    }
                }
            }
        }

        if (input.getEquipmentCatEmptyFullId() == null && input.getDocumentChassisId() != null) { //DUMMY GATEOUT
            input.setEquipmentId(null);
            input.setProgramacionNaveDetalleId(null);
            input.setEquipmentCatEmptyFullId(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS));
            input.setMoveTypeId(catalogIds.get(CATALOG_CONTAINER_MOVEMENT_POSITIONING_ALIAS));
            Optional<ShippingLine> dummyShippingLine = shippingLineRepository.findByShippingLine(shippingLineOthers);
            shippinglineBl = dummyShippingLine.map(ShippingLine::getId).orElse(null);
            gradeId = catalogIds.get(CATALOG_NO_GRADE);
            depotOperationId = catalogIds.get(CATALOG_DEPOT_OPER_MTY_LOAD_ALIAS);

            Container notApplicableContainer = containerRepository.findByContainerNumber(equipmentNotApplicable);
            if (notApplicableContainer == null) {
                Optional<IsoCode> dummyIsoCode = isoCodeRepository.findByIsoCode(isIsocodeDummy20dryCode);
                equipmentSizeId = dummyIsoCode.map(IsoCode::getCatSize).map(Catalog::getId).orElse(null);
                equipmentTypeId = dummyIsoCode.map(IsoCode::getCatContainerType).map(Catalog::getId).orElse(null);
                equipmentFamilyId = dummyIsoCode.map(IsoCode::getCatContainerType).map(Catalog::getVariable3).orElse(null);

                notApplicableContainer = containerRepository.save(Container.builder()
                        .containerNumber(CONTAINER_NOT_APPLICA)
                        .catFamily(Optional.ofNullable(equipmentFamilyId).map(ef -> Catalog.builder().id(ef).build()).orElse(null))
                        .catSize(Optional.ofNullable(equipmentSizeId).map(es -> Catalog.builder().id(es).build()).orElse(null))
                        .catContainerType(Optional.ofNullable(equipmentTypeId).map(et -> Catalog.builder().id(et).build()).orElse(null))
                        .shippingLine(dummyShippingLine.orElse(null))
                        .containerTare(0)
                        .maximunPayload(0)
                        .isoCode(dummyIsoCode.orElse(null))
                        .catGrade(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                        .manufactureDate(LocalDate.parse("1961-01-01").atStartOfDay())
                        .shipperOwn(false)
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .traceContainer("ins_go_gral_chassis")
                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(IS_CREATION_GO_GENERAL)).build())
                        .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                        .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                        .build());
            }
            input.setEquipmentId(notApplicableContainer.getId());
            tare = notApplicableContainer.getContainerTare();
            catEquipMeasureTareId = Optional.ofNullable(notApplicableContainer).map(Container::getCatEquipMeasureTare).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
            payLoad = notApplicableContainer.getMaximunPayload();
            catEquipMeasurePayloadId = Optional.ofNullable(notApplicableContainer).map(Container::getCatEquipMeasurePayload).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
            equipmentNumber = notApplicableContainer.getContainerNumber();
            codigoIsoId = Optional.ofNullable(notApplicableContainer).map(Container::getIsoCode).map(IsoCode::getId).orElse(null);
            equipmentSizeId = Optional.ofNullable(notApplicableContainer).map(Container::getCatSize).map(Catalog::getId).orElse(null);
            equipmentTypeId = Optional.ofNullable(notApplicableContainer).map(Container::getCatContainerType).map(Catalog::getId).orElse(null);

            Vessel dummyVessel = vesselRepository.findByShip(vessel).orElse(null);
            if (dummyVessel == null) {
                dummyVessel = vesselRepository.save(Vessel.builder()
                        .ship(vessel)
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .name(vessel)
                        .catVesselCreationOrigin(Catalog.builder().id(catalogIds.get(IS_CREATION_GO_GENERAL)).build())
                        .build());
            } else if (!dummyVessel.getActive()) {
                dummyVessel.setActive(true);
                dummyVessel.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                dummyVessel.setModificationDate(LocalDateTime.now());
                dummyVessel.setCatVesselCreationOrigin(Catalog.builder().id(catalogIds.get(IS_CREATION_GO_GENERAL)).build());
                dummyVessel.setName(vessel);
                dummyVessel = vesselRepository.save(dummyVessel);
            }
            vesselId = dummyVessel.getId();

            VesselProgramming dummyVesselProgramming = vesselProgrammingRepository.getLastByVesselVoyageAndBusinessUnitId(pageable, vesselId, vessel, subBusinessUnitId).stream().findFirst().orElse(null);
            if (dummyVesselProgramming == null) {
                dummyVesselProgramming = vesselProgrammingRepository.save(VesselProgramming.builder()
                        .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                        .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                        .vessel(dummyVessel)
                        .voyage(vessel)
                        .etaDate(LocalDateTime.now())
                        .etdDate(LocalDateTime.now())
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(IS_CREATION_GO_GENERAL)).build())
                        .build());
            }
            programacionNaveId = dummyVesselProgramming.getId();

            VesselProgrammingDetail dummyVesselProgrammingDetail = vesselProgrammingDetailRepository.getProgrammingDetailByProgrammingVesselAndDepotOperation(programacionNaveId, depotOperationId).orElse(null);
            if (dummyVesselProgrammingDetail == null) {
                dummyVesselProgrammingDetail = vesselProgrammingDetailRepository.save(VesselProgrammingDetail.builder()
                        .vesselProgramming(dummyVesselProgramming)
                        .catOperation(Catalog.builder().id(depotOperationId).build())
                        .manifestYear(String.valueOf(LocalDateTime.now().getYear()))
                        .manifestNumber(null)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(IS_CREATION_GO_GENERAL)).build())
                        .active(true)
                        .build());
            }
            input.setProgramacionNaveDetalleId(dummyVesselProgrammingDetail.getId());

            Integer chassisOperationTypeId = chassisDocumentRepository.findActiveChassisDocumentByIdAndSubBusinessUnit(input.getDocumentChassisId(), subBusinessUnitId).map(ChassisDocument::getCatChassisOperationType).map(Catalog::getId).orElse(null);
            if (input.getEquipmentId().equals(equipmentNotApplicableId) && chassisOperationTypeId.equals(catalogIds.get(CATALOG_CHASSIS_OPETYPE_CUSDELIVERY_ALIAS))) {
                input.setMoveTypeId(catalogIds.get(CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS));
            }

            Eir dummyEir = eirRepository.save(Eir.builder()
                    .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                    .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                    .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                    .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).build())
                    .catEmptyFull(Optional.ofNullable(input.getEquipmentCatEmptyFullId()).map(ef -> Catalog.builder().id(ef).build()).orElse(null))
                    .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .truckArrivalDate(truckInDate)
                    .vesselProgrammingDetail(Optional.ofNullable(input.getProgramacionNaveDetalleId()).map(pd -> VesselProgrammingDetail.builder().id(pd).build()).orElse(null))
                    .container(Container.builder().id(input.getEquipmentId()).build())
                    .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                    .truck(Truck.builder().id(input.getVehicleId()).build())
                    .driverPerson(Person.builder().id(input.getDriverId()).build())
                    .shippingLine(Optional.ofNullable(shippinglineBl).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                    .taraCnt(tare)
                    .cargoMaximumCnt(payLoad)
                    .isoCode(Optional.ofNullable(codigoIsoId).map(ci -> IsoCode.builder().id(ci).build()).orElse(null))
                    .catContainerType(Optional.ofNullable(equipmentTypeId).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .catSizeCnt(Optional.ofNullable(equipmentSizeId).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .catClassCnt(Optional.ofNullable(gradeId).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .observation(input.getComments())

                    .structureWithDamage(boxWithDamage)
                    .structureDamaged(boxDamaged)
                    .machineryWithDamage(machineryWithDamage)
                    .machineryDamaged(machineryDamaged)

                    .truckMultipleLoad(false)
                    .truckRetreatWithLoad(false)
                    .active(true)
                    .catCreationOrigin(Catalog.builder().id(catalogIds.get(IS_CREATION_GO_GENERAL)).build())
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .registrationDate(LocalDateTime.now())
                    .clientCompany(null)
                    .traceEir("ins_go_gral_dummy")

                    .weightGoods(BigDecimal.ZERO)
                    .catMeasureWeight(null)
                    .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                    .catMeasureTare(Optional.ofNullable(catEquipMeasureTareId).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .catMeasurePayload(Optional.ofNullable(catEquipMeasurePayloadId).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .controlAssignmentLight(Short.valueOf("0"))
                    .flagChassisPickup(input.getFlagChassisPickup())
                    .chassisNumber(input.getInputChassisNumber())
                    .catGradeGateOut(Optional.ofNullable(input.getContainerGradeId()).map(cat -> Catalog.builder().id(cat).build()).orElse(null))
                    .numberTwr(input.getTwrNumber())
                    .accelerateProgramNumber(input.getAccelerateProgramNumber())
                    .build());

            eirId = dummyEir.getId();
        }

        if (input.getDocumentChassisId() != null) {
            chassisAllocationControl = 0;
            EirChassis eirChassis = eirChassisRepository.save(EirChassis.builder()
                    .chassisDocumentDetail(null)
                    .chassis(null)
                    .active(true)
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .registrationDate(LocalDateTime.now())
                    .chassisAllocationControl(chassisAllocationControl.toString())
                    .chassisDocumentGo(ChassisDocument.builder().id(input.getDocumentChassisId()).build())
                    .build());

            eirChassisId = eirChassis.getId();
        }

        if (eirChassisId != null) {
            if (eirRegistered.isEmpty()) {
                Optional<Eir> optionalCreatedEir = eirRepository.findById(eirId);
                if (optionalCreatedEir.isPresent()) {
                    Eir createdEir = optionalCreatedEir.get();
                    createdEir.setEirChassis(EirChassis.builder().id(eirChassisId).build());
                    createdEir.setTraceEir("upd_go_eir_chassis1");
                    createdEir.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                    createdEir.setModificationDate(LocalDateTime.now());
                    eirRepository.saveAndFlush(createdEir);
                }
            } else {
                final Integer eirChassisIdClone = eirChassisId;
                eirRegistered.forEach(it -> {
                    JSONObject eir = (JSONObject) it;
                    Eir createdEir = eirRepository.findById(eir.getInt(EIR_ID)).get();
                    createdEir.setEirChassis(EirChassis.builder().id(eirChassisIdClone).build());
                    createdEir.setTraceEir("upd_go_eir_chassis2");
                    createdEir.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                    createdEir.setModificationDate(LocalDateTime.now());
                    eirRepository.saveAndFlush(createdEir);
                });
            }
        }

        output.setResultNewId(eirId);
        output.setResultState(1);
        output.setResultEirChassisId(eirChassisId);
        output.setResultEirListId(eirRegistered.toString());

        if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))) {
            output.setResultIsEmpty(1);
            if (eirRegistered.isEmpty()) {
                output.setResultMessage(messageLanguageService.getMessage(PRC_GO_GENERAL, 1, input.getLanguageId(), Map.of(REPLACE_STRING, eirId != null ? eirId.toString() : "0")));
            } else {
                output.setResultMessage("");
                eirRegistered.forEach(it -> {
                    JSONObject eir = (JSONObject) it;
                    output.setResultMessage(messageLanguageService.getMessage(PRC_GO_GENERAL, 1, input.getLanguageId(), Map.of(REPLACE_STRING, eir != null ? String.valueOf(eir.getInt(EIR_ID)) : "0")) + " | ");
                });
            }
        } else {
            output.setResultIsEmpty(0);
            if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))) {
                output.setResultMessage(messageLanguageService.getMessage("PRC_FULL_GI_GO", 8, input.getLanguageId(), Map.of(REPLACE_STRING, eirId != null ? eirId.toString() : "0")));
            } else {
                output.setResultMessage(messageLanguageService.getMessage(PRC_GO_GENERAL, 2, input.getLanguageId(), Map.of(REPLACE_STRING, eirChassisId != null ? eirChassisId.toString() : "0")));
            }
        }

        if ("sdy".equals(output.getResultTypeIntegration())) {
            output.setResultContainerNumber(equipmentNumber != null ? equipmentNumber : "DUMMY");
        }

        return output;
    }

    public void handleIntegrations(GateOutGeneralRegisterInput.Input input, GateOutGeneralRegisterOutput output) throws Exception {
        if (input.getApsId() != null && !input.getApsId().toString().isBlank()) {
            APSIntegrationConnection apsIntegrationConnection = new APSIntegrationConnection();
            apsIntegrationConnection.attendAppointmentAPS(input.getUserRegistrationId().toString(), "[{\"aps_id\":\"" + input.getApsId() + "\"}]");
        }

        if (output.getResultTypeIntegration() != null && output.getResultTypeIntegration().equals("sdy")) {
            gateOutYardIntegration(input, output);
        }
    }

    private void gateOutYardIntegration(GateOutGeneralRegisterInput.Input input, GateOutGeneralRegisterOutput output) throws Exception {
        JSONArray integrationResponseJson = new JSONArray();

        SD1IntegrationConnection sdyConnection = connectionFactory.createConnection(
                env.getProperty("msk.api.apiUserLogin.sdy.loginUrl"),
                env.getProperty("msk.api.apiUserLogin.sdy.user"),
                env.getProperty("msk.api.apiUserLogin.sdy.password"),
                env.getProperty("msk.api.apiUserLogin.sdy.system")
        );

        List<YardContainer> yardContainers = new ArrayList<>();
        if (output.getResultEirListId() != null && !output.getResultEirListId().isBlank()) {
            JSONArray eirArray = new JSONArray(output.getResultEirListId());
            eirArray.forEach(eirJson -> {
                JSONObject eir = new JSONObject(eirJson.toString());
                yardContainers.add(new YardContainer(output.getResultContainerNumber(), eir.getInt(EIR_ID)));
            });
        } else {
            yardContainers.add(new YardContainer(output.getResultContainerNumber(), output.getResultNewId()));
        }

        YardGateOutInput yardGateOutInput = new YardGateOutInput();
        yardGateOutInput.setUserId(input.getUserRegistrationId().toString());
        yardGateOutInput.setBusinessUnitId(input.getSubBusinessUnitLocalId());
        yardGateOutInput.setOperationType(input.getMoveTypeCode());
        yardGateOutInput.setContainers(yardContainers);
        yardGateOutInput.setCatContainerContentTypeId(yardGateOutInput.getCatContainerContentTypeId());
        yardGateOutInput.setLocalSubBusinessUnitId(input.getSubBusinessUnitLocalId());

        if (output.getResultContainerNumber() != null && output.getResultContainerNumber().equals("NO-CNT")) {
            yardGateOutInput.setContainersAssignment(true);
            yardGateOutInput.setContainersRelated(false);
            yardGateOutInput.setContainerContentType(output.getResultContainerContentType());
            if (output.getResultContainerContentType() != null && output.getResultContainerContentType().equals("E")) {
                yardGateOutInput.setBookingId(input.getBookingId());
                yardGateOutInput.setCatContainerSizeId(input.getContainerSizeId());
                yardGateOutInput.setCatContainerTypeId(input.getContainerTypeId());
                yardGateOutInput.setCatContainerClassId(input.getContainerGradeId());
                yardGateOutInput.setPickUpQuantity(input.getQuantity());
            } else {
                yardGateOutInput.setPickUpQuantity(1);
                yardGateOutInput.setCargoDocumentId(input.getDocumentoCargaId());
            }
        } else {
            yardGateOutInput.setContainersAssignment(false);
            yardGateOutInput.setContainersRelated(true);
        }

        HashMap<String, Object> integrationResponse = sdyConnection.post(env.getProperty("msk.api.apiUserLogin.sdy.gateOutUrl"), yardGateOutInput.toJSON("SDY"));
        if (integrationResponse != null) {
            JSONObject jsonResponse = new JSONObject(integrationResponse);
            if (jsonResponse.get("result") instanceof JSONObject) {
                JSONObject jsonResult = jsonResponse.getJSONObject("result");
                if (jsonResult.has("contenedores")) {
                    JSONArray containerArray = jsonResult.getJSONArray("contenedores");
                    for (int i = 0; i < containerArray.length(); i++) {
                        JSONObject container = containerArray.getJSONObject(i);
                        String equipmentNumber = container.getString("numero_contenedor");
                        String newYardLocation = container.getString("newYardLocation");
                        JSONObject modifiedIntegrationResult = new JSONObject();
                        modifiedIntegrationResult.put("container_number", equipmentNumber);
                        modifiedIntegrationResult.put("final_location", newYardLocation);
                        integrationResponseJson.put(modifiedIntegrationResult);
                    }
                } else if (jsonResult.has("activity_log_id") && jsonResult.has("error_message")) {
                    integrationResponseJson.put(jsonResult);
                }
            }
        }

        if (integrationResponseJson != null && !integrationResponseJson.isEmpty()) {
            output.setResultDestinyLocation(integrationResponseJson.toString());
        }
    }
}
