package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EirRejectRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sdg.controller.dto.EirRejectRegistrarInputDTO;
import com.maersk.sd1.sdg.controller.dto.EirRejectRegistrarOutputDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class EirRejectRegistrarService {

    private final CatalogRepository catalogRepository;
    private final EirRejectRepository eirRejectRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public EirRejectRegistrarOutputDTO processEirRejectRegistrar(EirRejectRegistrarInputDTO.Root request) {

        EirRejectRegistrarInputDTO.Input input = request.getInput();

        try {
            Integer catTypeMovementId = null;
            List<Integer> catalogIds = catalogRepository.findCatalogIdByDescription(input.getCatTypeMovementAlias());
            if(!CollectionUtils.isEmpty(catalogIds)) {
                catTypeMovementId = catalogIds.getFirst();
            }

            EirReject eirReject = EirReject.builder()
                    .businessUnit(getBusinessUnit(input.getBusinessUnitId()))
                    .localSubBusinessUnit(getBusinessUnit(input.getSubBusinessUnitLocalId()))
                    .containerNumber(request.getContainerNumber())
                    .containerBooking(input.getContainerBooking())
                    .catEmptyFull(getCatalog(input.getCatEmptyFullId()))
                    .chassisNumber(request.getChassisNumber())
                    .chassisBooking(input.getChassisBooking())
                    .catTypeReject(getCatalog(input.getCatTypeRejectId()))
                    .catTypeMovement(getCatalog(catTypeMovementId))
                    .truck(getTruck(input.getVehicleId()))
                    .personDriver(getPerson(input.getPersonDriverId()))
                    .companyTransport(getCompany(input.getCompanyTransportId()))
                    .comment(input.getComment())
                    .registrationUser(getUser(input.getUserRegistrationId()))
                    .registrationDate(LocalDateTime.now())
                    .build();

            eirReject = eirRejectRepository.save(eirReject);
            return EirRejectRegistrarOutputDTO.builder()
                    .newId(eirReject.getId())
                    .statusCode(1)
                    .statusMessage(messageLanguageRepository.findMensaje("EIR", 3, input.getLanguageId()))
                    .build();

        } catch (Exception e) {
            return EirRejectRegistrarOutputDTO.builder()
                    .newId(0)
                    .statusCode(0)
                    .statusMessage(e.getMessage())
                    .build();
        }
    }

    private BusinessUnit getBusinessUnit(Integer id) {
        return id != null ? BusinessUnit.builder().id(id).build() : null;
    }

    private Catalog getCatalog(Integer id) {
        return id != null ? Catalog.builder().id(id).build() : null;
    }

    private Truck getTruck(Integer id) {
        return id != null ? Truck.builder().id(id).build() : null;
    }

    private Person getPerson(Integer id) {
        return id != null ? Person.builder().id(id).build() : null;
    }

    private Company getCompany(Integer id) {
        return id != null ? Company.builder().id(id).build() : null;
    }

    private User getUser(Integer id) {
        return id != null ? User.builder().id(id).build() : null;
    }

}
