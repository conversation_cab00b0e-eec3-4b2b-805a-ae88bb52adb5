package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.GateTransmissionEmptyActivitySetting;
import com.maersk.sd1.common.model.GateTransmissionLocalSetting;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.repository.GateTransmissionEmptyActivitySettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionLocalSettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import com.maersk.sd1.sde.dto.SetContainerEDIExchangeOutput;
import com.maersk.sd1.sde.dto.SetContainerEDIExchangeOutput.ActivityEmptyDTO;
import com.maersk.sd1.sde.dto.SetContainerEDIExchangeOutput.SubUnidadesLocalDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;


import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class SetContainerEDIExchangeService {

    private static final Logger logger = LogManager.getLogger(SetContainerEDIExchangeService.class);

    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;
    private final GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;
    private final GateTransmissionEmptyActivitySettingRepository gateTransmissionEmptyActivitySettingRepository;
    ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    public SetContainerEDIExchangeService(GateTransmissionSettingRepository gateTransmissionSettingRepository,
                                          GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository,
                                          GateTransmissionEmptyActivitySettingRepository gateTransmissionEmptyActivitySettingRepository) {
        this.gateTransmissionSettingRepository = gateTransmissionSettingRepository;
        this.gateTransmissionLocalSettingRepository = gateTransmissionLocalSettingRepository;
        this.gateTransmissionEmptyActivitySettingRepository = gateTransmissionEmptyActivitySettingRepository;
    }

    private static final Integer GATE_IN_MOVEMENT_ID = 43080;
    private static final Integer GATE_OUT_MOVEMENT_ID = 43081;

    @Transactional(readOnly = true)
    public SetContainerEDIExchangeOutput getSeteoEdiCodecoData(Integer seteoEdiCodecoId) {
        SetContainerEDIExchangeOutput output = new SetContainerEDIExchangeOutput();
        SetContainerEDIExchangeOutput.ResponseData response = new SetContainerEDIExchangeOutput.ResponseData();
        try {
            // 1) Retrieve main entity
            Optional<GateTransmissionSetting> optionalSetting = gateTransmissionSettingRepository.findById(seteoEdiCodecoId);
            if (optionalSetting.isEmpty()) {
                throw new IllegalArgumentException("No GateTransmissionSetting found for ID: " + seteoEdiCodecoId);
            }
            GateTransmissionSetting setting = optionalSetting.get();

            // 2) Retrieve subUnidadesLocal
            List<GateTransmissionLocalSetting> localSettings = gateTransmissionLocalSettingRepository
                    .findByGateTransmissionSetting_IdAndActive(seteoEdiCodecoId, true);

            // 3) Retrieve procedenciaGateIn
            List<GateTransmissionEmptyActivitySetting> gateInList = gateTransmissionEmptyActivitySettingRepository
                    .findByGateTransmissionSetting_IdAndCatMovement_IdAndActive(seteoEdiCodecoId, GATE_IN_MOVEMENT_ID, true);

            // 4) Retrieve procedenciaGateOut
            List<GateTransmissionEmptyActivitySetting> gateOutList = gateTransmissionEmptyActivitySettingRepository
                    .findByGateTransmissionSetting_IdAndCatMovement_IdAndActive(seteoEdiCodecoId, GATE_OUT_MOVEMENT_ID, true);

            // Fill main record fields onto response
            response.setSeteoEdiCodecoId(setting.getId());
            response.setUnidadNegocioId(setting.getBusinessUnit() != null ? setting.getBusinessUnit().getId() : null);
            response.setSubUnidadNegocioId(setting.getSubBusinessUnit() != null ? setting.getSubBusinessUnit().getId() : null);
            response.setLineaNavieraId(setting.getShippingLine() != null ? setting.getShippingLine().getId() : null);
            response.setSistemaEntrega(setting.getSystemDelivery());
            response.setInfoSistemaEntrega(setting.getInfoSystemDelivery());
            response.setIdentificadorReceptor(setting.getReceptorIdentifier());
            response.setEnviarGateInEmpty(setting.getSendGateInEmpty());
            response.setEnviarGateOutEmpty(setting.getSendGateOutEmpty());
            response.setEnviarGateInFull(setting.getSendGateInFull());
            response.setEnviarGateOutFull(setting.getSendGateOutFull());
            response.setEnviarStatusActivity(setting.getSendStatusActivity());
            response.setCatFormatoGateOutEmpty(
                    setting.getCatGateOutEmptyFormat() != null ? setting.getCatGateOutEmptyFormat().getId() : null
            );
            response.setCatFormatoGateInFull(
                    setting.getCatGateInFullFormat() != null ? setting.getCatGateInFullFormat().getId() : null
            );
            response.setCatFormatoGateOutFull(
                    setting.getCatGateOutFullFormat() != null ? setting.getCatGateOutFullFormat().getId() : null
            );
            response.setCatFormatoGateInEmpty(
                    setting.getCatGateInEmptyFormat() != null ? setting.getCatGateInEmptyFormat().getId() : null
            );
            response.setCatFormatoStatusActivity(
                    setting.getCatStatusActivityFormat() != null ? setting.getCatStatusActivityFormat().getId() : null
            );
            response.setCatCanalEnvioId(
                    setting.getCatDeliveryChannel() != null ? setting.getCatDeliveryChannel().getId() : null
            );
            response.setCatModoGenerarArchivoId(
                    setting.getCatGenerateFileMode() != null ? setting.getCatGenerateFileMode().getId() : null
            );
            response.setCorreoCodecoDestino(setting.getGateTransmissionDestinationMail());
            response.setCorreoTelexDestino(setting.getTelexDestinationMail());
            response.setParametro1(setting.getParameter1());
            response.setParametro2(setting.getParameter2());
            response.setParametro3(setting.getParameter3());
            response.setParametro4(setting.getParameter4());
            response.setEsHistorico(setting.getIsHistorical());
            response.setFechaDebaja(setting.getDeactivationDate() == null ? null : setting.getDeactivationDate().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            response.setMotivoDebaja(setting.getDeactivationReason());
            response.setActivo(setting.getActive());
            response.setFechaRegistro(setting.getRegistrationDate() == null ? null : setting.getRegistrationDate().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            response.setFechaModificacion(setting.getModificationDate() == null ? null : setting.getModificationDate().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            response.setParametro5(setting.getParameter5());
            response.setParametro6(setting.getParameter6());
            response.setAzureIdCodeco(setting.getAzureIdGateTransmission());
            response.setAzureIdTelex(setting.getAzureIdTelex());
            response.setSftpId(setting.getSftpId());
            response.setExtensionArchivoEnviar(setting.getExtensionFileSend());
            response.setMinutosTrancurridos(setting.getElapsedMinutes());

            // Convert local settings to SubUnidadesLocalDTO
            List<SubUnidadesLocalDTO> subUnitDTOs = new ArrayList<>();
            for (GateTransmissionLocalSetting ls : localSettings) {
                SubUnidadesLocalDTO dto = new SubUnidadesLocalDTO();
                dto.setSeleccionado(1); // as per stored procedure result
                dto.setUnidadNegocioId(ls.getLocalSubBusinessUnit() != null ? ls.getLocalSubBusinessUnit().getId() : null);
                dto.setSeteoEdiCodecoLocalId(ls.getId());
                dto.setActivo(ls.getActive());
                dto.setIdentificadorEmisor(ls.getIdentifierEmitter());
                dto.setLocacionActividad(ls.getLocacionActivity());
                subUnitDTOs.add(dto);
            }

            String subInJson = objectMapper.writeValueAsString(subUnitDTOs);
            output.setSubUnidadesLocal(List.of(List.of(subInJson)));

            // Convert gateInList to ActivityEmptyDTO
            List<ActivityEmptyDTO> gateInDTOs = new ArrayList<>();
            for (GateTransmissionEmptyActivitySetting gi : gateInList) {
                ActivityEmptyDTO dto = new ActivityEmptyDTO();
                dto.setSeleccionado(1); // as per stored procedure
                dto.setCatalogoId(gi.getCatOrigin() != null ? gi.getCatOrigin().getId() : null);
                dto.setSeteoEdiCodecoActividadId(gi.getId());
                dto.setActivo(gi.getActive());
                gateInDTOs.add(dto);
            }
            String gateInJson = objectMapper.writeValueAsString(gateInDTOs);
            output.setProcedenciaGateIn(List.of(List.of(gateInJson)));

            // Convert gateOutList to ActivityEmptyDTO
            List<ActivityEmptyDTO> gateOutDTOs = new ArrayList<>();
            for (GateTransmissionEmptyActivitySetting go : gateOutList) {
                ActivityEmptyDTO dto = new ActivityEmptyDTO();
                dto.setSeleccionado(1);
                dto.setCatalogoId(go.getCatOrigin() != null ? go.getCatOrigin().getId() : null);
                dto.setSeteoEdiCodecoActividadId(go.getId());
                dto.setActivo(go.getActive());
                gateOutDTOs.add(dto);
            }
            String gateOutJson = objectMapper.writeValueAsString(gateOutDTOs);
            output.setProcedenciaGateOut(List.of(List.of(gateOutJson)));

            output.setResponse(List.of(response));

            // If everything was successful
//            response.setRespEstado(1);
//            response.setRespMensaje("Success");

        } catch (Exception e) {
            logger.error("Error in getSeteoEdiCodecoData", e);
//            response.setRespEstado(0);
//            response.setRespMensaje(e.getMessage());
        }
        return output;
    }
}

