package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.GateTransmissionLocalSetting;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.dto.CodeConfiguredOutput;
import com.maersk.sd1.common.repository.GateTransmissionLocalSettingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * Service class that replicates the stored procedure logic in pure Java.
 */
@RequiredArgsConstructor
@Service
@Log4j2
//sde.servicio_codeco_configurado
public class CodeConfiguredService {

    private final GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;
    private final CatalogRepository catalogRepository;

    // Aliases from the stored procedure.
    private static final String ALIAS_GATE_IN = "43080";
    private static final String ALIAS_GATE_OUT = "43081";
    private static final String ALIAS_EMPTY = "43083";
    private static final String ALIAS_FULL = "43084";
    private static final String ALIAS_FORMAT_TELEX = "sd1_messagetype_gatetrans_telex";
    private static final String ALIAS_FORMAT_322 = "sd1_messagetype_gatetrans_322";

    /**
     * Main method that replicates the stored procedure logic.
     */
    public CodeConfiguredOutput findAllCodecoConfigured() {
        log.info("Starting findAllCodecoConfigured...");

        // Fetch catalogs
        Catalog gateInCatalog = getCatalogByAlias(ALIAS_GATE_IN);
        Catalog gateOutCatalog = getCatalogByAlias(ALIAS_GATE_OUT);
        Catalog emptyCatalog = getCatalogByAlias(ALIAS_EMPTY);
        Catalog fullCatalog = getCatalogByAlias(ALIAS_FULL);

        List<CodeConfiguredOutput.CodeConfiguredItemDto> finalList = new ArrayList<>();

        // Collect data for each type
        finalList.addAll(collectGateInEmptyData(gateInCatalog, emptyCatalog));
        finalList.addAll(collectStatusActivityData(gateInCatalog, emptyCatalog));
        finalList.addAll(collectGateOutEmptyData(gateOutCatalog, emptyCatalog));
        finalList.addAll(collectGateInFullData(gateInCatalog, fullCatalog));
        finalList.addAll(collectGateOutFullData(gateOutCatalog, fullCatalog));

        // Sort the results
        sortCodeConfiguredList(finalList);

        CodeConfiguredOutput output = new CodeConfiguredOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
        output.setItems(finalList);

        log.info("Completed findAllCodecoConfigured with {} records.", finalList.size());
        return output;
    }

    /**
     * Fetch catalog by alias.
     */
    public Catalog getCatalogByAlias(String alias) {
        if (alias == null) {
            return null;
        }
        Optional<Catalog> catalogOpt = catalogRepository.findByAliasOptional(alias);
        return catalogOpt.orElse(null);
    }

    /**
     * Helper function to map shared properties of DTO.
     */
    public CodeConfiguredOutput.CodeConfiguredItemDto mapCommonFields(
            GateTransmissionLocalSetting localSetting, Catalog movementCatalog, Catalog emptyFullCatalog, int eventId, String eventName) {

        CodeConfiguredOutput.CodeConfiguredItemDto dto = new CodeConfiguredOutput.CodeConfiguredItemDto();
        dto.setRowId(null); // _id is an identity in T-SQL table variable, not in actual DB, so we keep it null.
        dto.setGateTransmissionSettingId(localSetting.getGateTransmissionSetting().getId());
        dto.setSubBusinessUnitId(getSubBusinessUnitId(localSetting));
        dto.setLocalSubBusinessUnitId(getLocalSubBusinessUnitId(localSetting));
        dto.setEventId(eventId);
        dto.setEventName(eventName);

        // Shipping line
        dto.setShippingLineId(localSetting.getGateTransmissionSetting().getShippingLine().getId());
        dto.setShippingLineCompany(localSetting.getGateTransmissionSetting().getShippingLine().getShippingLineCompany());

        // Movement Catalog
        dto.setMovementCatId(movementCatalog == null ? null : movementCatalog.getId());
        dto.setMovementDescription(movementCatalog == null ? null : movementCatalog.getDescription());

        // Empty/Full Catalog
        dto.setEmptyFullCatId(emptyFullCatalog == null ? null : emptyFullCatalog.getId());
        dto.setEmptyFullDescription(emptyFullCatalog == null ? null : emptyFullCatalog.getDescription());

        // Common fields
        dto.setTransactionType("O");
        dto.setSystemDelivery(localSetting.getGateTransmissionSetting().getSystemDelivery());
        dto.setReceptorIdentifier(localSetting.getGateTransmissionSetting().getReceptorIdentifier());
        dto.setEmitterIdentifier(localSetting.getIdentifierEmitter());
        dto.setActivityLocation(localSetting.getLocacionActivity());
        dto.setDeliveryChannelCatId(getCatalogId(localSetting.getGateTransmissionSetting().getCatDeliveryChannel()));
        dto.setDeliveryChannelDescription(getCatalogDescription(localSetting.getGateTransmissionSetting().getCatDeliveryChannel()));
        dto.setGenerateFileModeCatId(getCatalogId(localSetting.getGateTransmissionSetting().getCatGenerateFileMode()));
        dto.setExtensionFileSend(localSetting.getGateTransmissionSetting().getExtensionFileSend());

        return dto;
    }

    /**
     * Helper function to extract SubBusinessUnitId from GateTransmissionLocalSetting.
     */
    public Long getSubBusinessUnitId(GateTransmissionLocalSetting localSetting) {
        if (localSetting == null || localSetting.getGateTransmissionSetting() == null || localSetting.getGateTransmissionSetting().getSubBusinessUnit() == null) {
            return null;
        }
        Integer subBusinessUnitId = localSetting.getGateTransmissionSetting().getSubBusinessUnit().getId();
        if (subBusinessUnitId == null) {
            return null;
        }
        return subBusinessUnitId.longValue();
    }


    /**
     * Helper function to extract LocalSubBusinessUnitId from GateTransmissionLocalSetting.
     */
    public Long getLocalSubBusinessUnitId(GateTransmissionLocalSetting localSetting) {
        return localSetting.getLocalSubBusinessUnit().getId() == null ? null
                : localSetting.getLocalSubBusinessUnit().getId().longValue();
    }

    /**
     * Helper function to extract catalog ID.
     */
    public Integer getCatalogId(Catalog catalog) {
        return catalog == null ? null : catalog.getId();
    }

    /**
     * Helper function to extract catalog description.
     */
    public String getCatalogDescription(Catalog catalog) {
        return catalog == null ? null : catalog.getDescription();
    }

    /**
     * Collect Gate In Empty data and map to DTO.
     */
    public List<CodeConfiguredOutput.CodeConfiguredItemDto> collectGateInEmptyData(Catalog gateInCatalog, Catalog emptyCatalog) {
        List<GateTransmissionLocalSetting> gateInEmpty = gateTransmissionLocalSettingRepository.findGateInEmpty();
        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = new ArrayList<>();
        for (GateTransmissionLocalSetting localSetting : gateInEmpty) {
            CodeConfiguredOutput.CodeConfiguredItemDto dto = mapCommonFields(localSetting, gateInCatalog, emptyCatalog, 1, "GATE_IN_EMPTY");
            result.add(dto);
        }
        return result;
    }

    /**
     * Collect Status Activity data and map to DTO.
     */
    public List<CodeConfiguredOutput.CodeConfiguredItemDto> collectStatusActivityData(Catalog gateInCatalog, Catalog emptyCatalog) {
        List<GateTransmissionLocalSetting> statusActivity = gateTransmissionLocalSettingRepository.findStatusActivity();
        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = new ArrayList<>();
        for (GateTransmissionLocalSetting localSetting : statusActivity) {
            int eventId = determineEventId(localSetting);
            CodeConfiguredOutput.CodeConfiguredItemDto dto = mapCommonFields(localSetting, gateInCatalog, emptyCatalog, eventId, "STATUS_ACTIVITY");
            result.add(dto);
        }
        return result;
    }

    /**
     * Collect Gate Out Empty data and map to DTO.
     */
    public List<CodeConfiguredOutput.CodeConfiguredItemDto> collectGateOutEmptyData(Catalog gateOutCatalog, Catalog emptyCatalog) {
        List<GateTransmissionLocalSetting> gateOutEmpty = gateTransmissionLocalSettingRepository.findGateOutEmpty();
        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = new ArrayList<>();
        for (GateTransmissionLocalSetting localSetting : gateOutEmpty) {
            CodeConfiguredOutput.CodeConfiguredItemDto dto = mapCommonFields(localSetting, gateOutCatalog, emptyCatalog, 4, "GATE_OUT_EMPTY");
            result.add(dto);
        }
        return result;
    }

    /**
     * Collect Gate In Full data and map to DTO.
     */
    public List<CodeConfiguredOutput.CodeConfiguredItemDto> collectGateInFullData(Catalog gateInCatalog, Catalog fullCatalog) {
        List<GateTransmissionLocalSetting> gateInFull = gateTransmissionLocalSettingRepository.findGateInFull();
        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = new ArrayList<>();
        for (GateTransmissionLocalSetting localSetting : gateInFull) {
            CodeConfiguredOutput.CodeConfiguredItemDto dto = mapCommonFields(localSetting, gateInCatalog, fullCatalog, 5, "GATE_IN_FULL");
            result.add(dto);
        }
        return result;
    }

    /**
     * Collect Gate Out Full data and map to DTO.
     */
    public List<CodeConfiguredOutput.CodeConfiguredItemDto> collectGateOutFullData(Catalog gateOutCatalog, Catalog fullCatalog) {
        List<GateTransmissionLocalSetting> gateOutFull = gateTransmissionLocalSettingRepository.findGateOutFull();
        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = new ArrayList<>();
        for (GateTransmissionLocalSetting localSetting : gateOutFull) {
            CodeConfiguredOutput.CodeConfiguredItemDto dto = mapCommonFields(localSetting, gateOutCatalog, fullCatalog, 6, "GATE_OUT_FULL");
            result.add(dto);
        }
        return result;
    }

    /**
     * Determine event ID based on format.
     */
    public int determineEventId(GateTransmissionLocalSetting localSetting) {
        Catalog format = localSetting.getGateTransmissionSetting().getCatStatusActivityFormat();
        return (format != null && ALIAS_FORMAT_TELEX.equalsIgnoreCase(format.getAlias())) ? 2 : 3;
    }

    /**
     * Sort the CodeConfiguredItems by defined criteria.
     */
    public void sortCodeConfiguredList(List<CodeConfiguredOutput.CodeConfiguredItemDto> finalList) {
        finalList.sort(
                Comparator.comparing(CodeConfiguredOutput.CodeConfiguredItemDto::getShippingLineCompany, Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(CodeConfiguredOutput.CodeConfiguredItemDto::getLocalSubBusinessUnitId)
                        .thenComparing(CodeConfiguredOutput.CodeConfiguredItemDto::getGateTransmissionSettingId)
                        .thenComparing(CodeConfiguredOutput.CodeConfiguredItemDto::getEventId)
        );
    }
}
