package com.maersk.sd1.sde.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.GateTransmissionEmptyActivitySetting;
import com.maersk.sd1.common.model.GateTransmissionLocalSetting;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.GateTransmissionEmptyActivitySettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionLocalSettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterInput;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class SetupContainerGateinRegisterService {

    private static final Logger logger = LogManager.getLogger(SetupContainerGateinRegisterService.class.getName());
    private static final String CATALOGO_ID = "catalogo_id";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};

    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;

    private final GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;

    private final GateTransmissionEmptyActivitySettingRepository gateTransmissionEmptyActivitySettingRepository;



    @Autowired
    public SetupContainerGateinRegisterService(GateTransmissionSettingRepository gateTransmissionSettingRepository,
                                               GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository,
                                               GateTransmissionEmptyActivitySettingRepository gateTransmissionEmptyActivitySettingRepository) {
        this.gateTransmissionSettingRepository = gateTransmissionSettingRepository;
        this.gateTransmissionLocalSettingRepository = gateTransmissionLocalSettingRepository;
        this.gateTransmissionEmptyActivitySettingRepository = gateTransmissionEmptyActivitySettingRepository;
    }

    /**
     * Utility method to parse JSON string to List<Map<String, Object>>
     */
    private List<Map<String, Object>> parseJsonString(String jsonString, String fieldName) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            logger.debug("JSON string is null or empty for field: {}", fieldName);
            return new ArrayList<>();
        }

        try {
            logger.debug("Parsing JSON string for field {}: {}", fieldName, jsonString);
            List<Map<String, Object>> result = objectMapper.readValue(jsonString, typeRef);
            logger.debug("Successfully parsed {} items for field: {}", result.size(), fieldName);
            return result;
        } catch (JsonProcessingException e) {
            logger.error("Failed to parse JSON string for field {}: {}", fieldName, jsonString, e);
            throw new IllegalArgumentException("Invalid JSON format for field " + fieldName + ": " + e.getMessage(), e);
        }
    }

    @Transactional
    public SetupContainerGateinRegisterOutput registerCodecoSetting(SetupContainerGateinRegisterInput.Input input) {
        SetupContainerGateinRegisterOutput output = new SetupContainerGateinRegisterOutput();
        try {
            logger.info("Checking if a GateTransmissionSetting with the same keys already exists...");

            Optional<GateTransmissionSetting> existing = gateTransmissionSettingRepository.findByUniqueKeys(
                    input.getUnidadNegocioId(),
                    input.getSubUnidadNegocioId(),
                    input.getLineaNavieraId(),
                    input.getSistemaEntrega()
            );

            if (existing.isPresent()) {
                logger.info("Record already exists with same keys.");
                output.setRespEstado(2);
                output.setRespMensaje("There is another codeco with the same key (Business Unit, Sub Business Unit, Shipping Line and Delivery System)");
                output.setRespNewId(null);
                return output;
            }

            GateTransmissionSetting setting = new GateTransmissionSetting();
            BusinessUnit bu = new BusinessUnit();
            bu.setId(input.getUnidadNegocioId().intValue());

            BusinessUnit sbu = new BusinessUnit();
            sbu.setId(input.getSubUnidadNegocioId().intValue());

            com.maersk.sd1.common.model.ShippingLine shippingLine = new com.maersk.sd1.common.model.ShippingLine();
            shippingLine.setId(input.getLineaNavieraId());

            setting.setBusinessUnit(bu);
            setting.setSubBusinessUnit(sbu);
            setting.setShippingLine(shippingLine);
            setting.setSystemDelivery(input.getSistemaEntrega());
            setting.setInfoSystemDelivery(input.getInfoSistemaEntrega());
            setting.setReceptorIdentifier(input.getIdentificadorReceptor());
            setting.setSendGateInEmpty(input.getEnviarGateInEmpty());
            setting.setSendGateOutEmpty(input.getEnviarGateOutEmpty());
            setting.setSendGateInFull(input.getEnviarGateInFull());
            setting.setSendGateOutFull(input.getEnviarGateOutFull());
            setting.setSendStatusActivity(input.getEnviarStatusActivity());

            if (input.getCatFormatoGateOutEmpty() != null) {
                Catalog catOutEmpty = new Catalog();
                catOutEmpty.setId(input.getCatFormatoGateOutEmpty().intValue());
                setting.setCatGateOutEmptyFormat(catOutEmpty);
            }
            if (input.getCatFormatoGateInFull() != null) {
                Catalog catInFull = new Catalog();
                catInFull.setId(input.getCatFormatoGateInFull().intValue());
                setting.setCatGateInFullFormat(catInFull);
            }
            if (input.getCatFormatoGateOutFull() != null) {
                Catalog catOutFull = new Catalog();
                catOutFull.setId(input.getCatFormatoGateOutFull().intValue());
                setting.setCatGateOutFullFormat(catOutFull);
            }
            if (input.getCatFormatoGateInEmpty() != null) {
                Catalog catInEmpty = new Catalog();
                catInEmpty.setId(input.getCatFormatoGateInEmpty().intValue());
                setting.setCatGateInEmptyFormat(catInEmpty);
            }
            if (input.getCatFormatoStatusActivity() != null) {
                Catalog catStat = new Catalog();
                catStat.setId(input.getCatFormatoStatusActivity().intValue());
                setting.setCatStatusActivityFormat(catStat);
            }

            Catalog catCanalEnvio = new Catalog();
            if (input.getCatCanalEnvioId() != null) {
                catCanalEnvio.setId(input.getCatCanalEnvioId().intValue());
            }
            setting.setCatDeliveryChannel(catCanalEnvio);

            Catalog catModoGenFile = new Catalog();
            if (input.getCatModoGenerarArchivoId() != null) {
                catModoGenFile.setId(input.getCatModoGenerarArchivoId().intValue());
            }
            setting.setCatGenerateFileMode(catModoGenFile);

            setting.setGateTransmissionDestinationMail(input.getCorreoCodecoDestino());
            setting.setTelexDestinationMail(input.getCorreoTelexDestino());
            setting.setParameter1(input.getParametro1());
            setting.setParameter2(input.getParametro2());
            setting.setParameter3(input.getParametro3());
            setting.setParameter4(input.getParametro4());
            setting.setIsHistorical(input.getEsHistorico());

            if (input.getFechaDebaja() != null && !input.getFechaDebaja().isBlank()) {
                LocalDate localDate = LocalDate.parse(input.getFechaDebaja());
                setting.setDeactivationDate(localDate.atStartOfDay());
            }
            setting.setDeactivationReason(input.getMotivoDebaja());
            setting.setActive(input.getActivo());

            User regUser = new User();
            if (input.getUsuarioRegistroId() != null) {
                regUser.setId(input.getUsuarioRegistroId().intValue());
            }
            setting.setRegistrationUser(regUser);

            setting.setRegistrationDate(LocalDateTime.now());

            setting.setParameter5(input.getParametro5());
            setting.setParameter6(input.getParametro6());
            setting.setSftpId(input.getSftpId());
            setting.setElapsedMinutes(input.getMinutosTranscurridos());
            setting.setExtensionFileSend(input.getExtensionArchivoEnviar());
            setting.setAzureIdGateTransmission(input.getAzureIdCodeco());
            setting.setAzureIdTelex(input.getAzureIdTelex());

            gateTransmissionSettingRepository.save(setting);

            Integer newId = setting.getId();
            logger.info("New GateTransmissionSetting ID: {}", newId);

            if (newId != null) {
                getSubUnidadesJson(input, setting, regUser);
                getGateInEmptyProcedenciaJson(input, setting, regUser);
                getGateOutEmptyProcedenciaJson(input, setting, regUser);
                output.setRespEstado(1);
                output.setRespMensaje("Successfully register");
                output.setRespNewId(newId);
            } else {
                output.setRespEstado(0);
                output.setRespMensaje("Failed to get new ID after saving GateTransmissionSetting.");
                output.setRespNewId(null);
            }
        } catch (Exception ex) {
            logger.error("Error in registerCodecoSetting", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
            output.setRespNewId(null);
        }
        return output;
    }
    public void getSubUnidadesJson(SetupContainerGateinRegisterInput.Input input, GateTransmissionSetting setting, User regUser) {
        try {
            List<Map<String, Object>> subUnidadesList = parseJsonString(input.getSubUnidadesJson(), "sub_unidades_json");

            if (subUnidadesList != null && !subUnidadesList.isEmpty()) {
                logger.info("Processing {} sub unidades from JSON", subUnidadesList.size());

                for (Map<String, Object> item : subUnidadesList) {
                    GateTransmissionLocalSetting localSetting = new GateTransmissionLocalSetting();
                    localSetting.setGateTransmissionSetting(setting);

                    // Get identificador_emisor from JSON
                    String identifierEmitter = item.get("identificador_emisor") != null ? item.get("identificador_emisor").toString() : null;
                    if (identifierEmitter != null && identifierEmitter.length() > 10) {
                        identifierEmitter = identifierEmitter.substring(0, 10);
                    }
                    localSetting.setIdentifierEmitter(identifierEmitter);
                    logger.debug("Set identifierEmitter: {}", identifierEmitter);

                    // Get locacion_actividad from JSON
                    String locacionActivity = item.get("locacion_actividad") != null ? item.get("locacion_actividad").toString() : null;
                    if (locacionActivity != null && locacionActivity.length() > 10) {
                        locacionActivity = locacionActivity.substring(0, 10);
                    }
                    localSetting.setLocacionActivity(locacionActivity);
                    logger.debug("Set locacionActivity: {}", locacionActivity);

                    // Get unidad_negocio_id from JSON item, not from input
                    BusinessUnit localSubBusinessUnit = new BusinessUnit();
                    if (item.get("unidad_negocio_id") != null) {
                        Number unidadNegocioId = (Number) item.get("unidad_negocio_id");
                        localSubBusinessUnit.setId(unidadNegocioId.intValue());
                        logger.debug("Set unidad_negocio_id from JSON: {}", unidadNegocioId);
                    } else {
                        // Fallback to input value if not present in JSON
                        localSubBusinessUnit.setId(input.getSubUnidadNegocioId().intValue());
                        logger.debug("Set unidad_negocio_id from input fallback: {}", input.getSubUnidadNegocioId());
                    }
                    localSetting.setLocalSubBusinessUnit(localSubBusinessUnit);

                    localSetting.setActive(setting.getActive());
                    localSetting.setRegistrationUser(regUser);
                    localSetting.setRegistrationDate(LocalDateTime.now());

                    gateTransmissionLocalSettingRepository.save(localSetting);
                    logger.debug("Saved GateTransmissionLocalSetting with ID: {}", localSetting.getId());
                }
            } else {
                logger.info("No sub unidades to process");
            }
        } catch (Exception e) {
            logger.error("Error processing sub_unidades_json", e);
            throw new RuntimeException("Failed to process sub_unidades_json: " + e.getMessage(), e);
        }
    }

    public void getGateInEmptyProcedenciaJson (SetupContainerGateinRegisterInput.Input input, GateTransmissionSetting setting, User regUser) {
        try {
            List<Map<String, Object>> procedenciaList = parseJsonString(input.getGateInEmptyProcedenciaJson(), "gate_in_empty_procedencia_json");

            if (procedenciaList != null && !procedenciaList.isEmpty()) {
                logger.info("Processing {} gate in empty procedencia items from JSON", procedenciaList.size());

                for (Map<String, Object> item : procedenciaList) {
                    GateTransmissionEmptyActivitySetting emptySetting = new GateTransmissionEmptyActivitySetting();
                    emptySetting.setGateTransmissionSetting(setting);

                    if (input.getGateInEmptyMovimientoId() != null) {
                        Catalog catMov = new Catalog();
                        catMov.setId(input.getGateInEmptyMovimientoId().intValue());
                        emptySetting.setCatMovement(catMov);
                        logger.debug("Set gate in empty movement ID: {}", input.getGateInEmptyMovimientoId());
                    }

                    if (item.get(CATALOGO_ID) != null) {
                        Number catId = (Number) item.get(CATALOGO_ID);
                        Catalog originCatalog = new Catalog();
                        originCatalog.setId(catId.intValue());
                        emptySetting.setCatOrigin(originCatalog);
                        logger.debug("Set catalogo_id: {}", catId);
                    } else {
                        throw new IllegalArgumentException("catalogo_id cannot be null in gate_in_empty_procedencia_json");
                    }

                    emptySetting.setActive(setting.getActive());
                    emptySetting.setRegistrationUser(regUser);
                    emptySetting.setRegistrationDate(LocalDateTime.now());

                    gateTransmissionEmptyActivitySettingRepository.save(emptySetting);
                    logger.debug("Saved GateTransmissionEmptyActivitySetting for gate in with ID: {}", emptySetting.getId());
                }
            } else {
                logger.info("No gate in empty procedencia items to process");
            }
        } catch (Exception e) {
            logger.error("Error processing gate_in_empty_procedencia_json", e);
            throw new RuntimeException("Failed to process gate_in_empty_procedencia_json: " + e.getMessage(), e);
        }
    }

    public void getGateOutEmptyProcedenciaJson (SetupContainerGateinRegisterInput.Input input, GateTransmissionSetting setting, User regUser) {
        try {
            List<Map<String, Object>> procedenciaList = parseJsonString(input.getGateOutEmptyProcedenciaJson(), "gate_out_empty_procedencia_json");

            if (procedenciaList != null && !procedenciaList.isEmpty()) {
                logger.info("Processing {} gate out empty procedencia items from JSON", procedenciaList.size());

                for (Map<String, Object> item : procedenciaList) {
                    GateTransmissionEmptyActivitySetting emptySetting = new GateTransmissionEmptyActivitySetting();
                    emptySetting.setGateTransmissionSetting(setting);

                    if (input.getGateOutEmptyMovimientoId() != null) {
                        Catalog catMov = new Catalog();
                        catMov.setId(input.getGateOutEmptyMovimientoId().intValue());
                        emptySetting.setCatMovement(catMov);
                        logger.debug("Set gate out empty movement ID: {}", input.getGateOutEmptyMovimientoId());
                    }

                    if (item.get(CATALOGO_ID) != null) {
                        Number catId = (Number) item.get(CATALOGO_ID);
                        Catalog originCatalog = new Catalog();
                        originCatalog.setId(catId.intValue());
                        emptySetting.setCatOrigin(originCatalog);
                        logger.debug("Set catalogo_id: {}", catId);
                    } else {
                        throw new IllegalArgumentException("catalogo_id cannot be null in gate_out_empty_procedencia_json");
                    }

                    emptySetting.setActive(setting.getActive());
                    emptySetting.setRegistrationUser(regUser);
                    emptySetting.setRegistrationDate(LocalDateTime.now());

                    gateTransmissionEmptyActivitySettingRepository.save(emptySetting);
                    logger.debug("Saved GateTransmissionEmptyActivitySetting for gate out with ID: {}", emptySetting.getId());
                }
            } else {
                logger.info("No gate out empty procedencia items to process");
            }
        } catch (Exception e) {
            logger.error("Error processing gate_out_empty_procedencia_json", e);
            throw new RuntimeException("Failed to process gate_out_empty_procedencia_json: " + e.getMessage(), e);
        }
    }
}

