package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.GateTransmissionSent;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.GateTransmissionSentRepository;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import com.maersk.sd1.sde.controller.dto.CompanyMailServiceOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CompanyMailService {

    private static final Logger logger = LogManager.getLogger(CompanyMailService.class);

    private final CatalogRepository catalogRepository;

    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;

    private final GateTransmissionSentRepository gateTransmissionSentRepository;

    @Autowired
    public CompanyMailService (CatalogRepository catalogRepository,
                                                 GateTransmissionSettingRepository gateTransmissionSettingRepository,
                                                 GateTransmissionSentRepository gateTransmissionSentRepository)
    {
        this.catalogRepository = catalogRepository;
        this.gateTransmissionSentRepository = gateTransmissionSentRepository;
        this.gateTransmissionSettingRepository = gateTransmissionSettingRepository;
    }

    @Transactional(readOnly = true)
    public CompanyMailServiceOutput sendCompanyFiles(Integer seteoEdiCodecoId, Integer catStructureFormatId) {
        CompanyMailServiceOutput output = new CompanyMailServiceOutput();
        try {
            // 1) Retrieve the catalog for alias '48158' => gener "GENERADO"
            Catalog generatedFileCatalog = catalogRepository.findByAlias("48158");

            if (generatedFileCatalog == null) {
                throw new IllegalArgumentException("No Catalog found for alias 48158");
            }
                // 2) Retrieve the structure format Catalog
            Catalog catStructureFormat = catalogRepository.findById(catStructureFormatId)
                    .orElseThrow(() -> new IllegalArgumentException("No Catalog found for cat_structure_format_id: " + catStructureFormatId));

            // 3) Retrieve the GateTransmissionSetting
            GateTransmissionSetting gateTransmissionSetting = gateTransmissionSettingRepository.findById(seteoEdiCodecoId)
                    .orElseThrow(() -> new IllegalArgumentException("No GateTransmissionSetting found for seteo_edi_codeco_id: " + seteoEdiCodecoId));

            // 4) Build the subject:
            // subject = catStructureFormat.description + " - " + gateTransmissionSetting.systemDelivery +
            //           " - " + shippingLine.linea_naviera + "/" + gateTransmissionSetting.id
            ShippingLine shippingLine = gateTransmissionSetting.getShippingLine();
            String lineaNavieraValue = shippingLine != null ? shippingLine.getShippingLineCompany() : "";
            String subject = catStructureFormat.getDescription() + " - "
                    + gateTransmissionSetting.getSystemDelivery() + " - "
                    + (lineaNavieraValue == null ? "" : lineaNavieraValue)
                    + " /" + gateTransmissionSetting.getId();

            output.setAsuntoCorreo(subject);

            // 5) Query all GateTransmissionSent records that match the condition:
            //    seteo_edi_codeco_id, cat_structure_format_id, cat_estado_envio_codeco_id = generatedFileCatalog, active= true.
            List<GateTransmissionSent> foundRecords = gateTransmissionSentRepository
                    .findByGateTransmissionSettingAndCatStructureFormatAndCatGateTransmissionStatusAndActiveOrderByRegistrationDate(
                            gateTransmissionSetting,
                            catStructureFormat,
                            generatedFileCatalog,
                            true
                    );

            // 6) Build the list of IDs
            List<Integer> listaEdiCodecoEnvioId = foundRecords.stream()
                    .map(GateTransmissionSent::getId)
                            .toList();
            output.setListaEdiCodecoEnvioId(listaEdiCodecoEnvioId);

            // 7) Build the detail list
            List<CompanyMailServiceOutput.CompanyMailServiceOutputDetail> detalle = foundRecords.stream().map(rec -> {
                CompanyMailServiceOutput.CompanyMailServiceOutputDetail item = new CompanyMailServiceOutput.CompanyMailServiceOutputDetail();
                item.setEdiCodecoEnvioId(rec.getId());
                item.setNombreArchivoCodeco(rec.getGateTransmissionNameFile());
                // catStructureFormat is not null, so let's get that ID
                item.setCatStructureFormatId(
                        rec.getCatStructureFormat() != null ? rec.getCatStructureFormat().getId() : null
                );
                return item;
            }).toList();

            output.setListaPendientesEnvio(detalle);

        } catch (Exception ex) {
            logger.error("Error in sendCodecoFiles", ex);
            throw ex; // rethrow so controller can handle or handle here.
        }
        return output;
    }
}
