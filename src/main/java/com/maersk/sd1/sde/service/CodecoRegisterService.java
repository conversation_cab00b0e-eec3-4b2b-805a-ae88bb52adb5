package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.GateTransmissionRepository;
import com.maersk.sd1.common.repository.GateTransmissionSentRepository;
import com.maersk.sd1.sde.dto.CodecoRegisterInput;
import com.maersk.sd1.sde.dto.CodecoRegisterOutput;
import com.maersk.sd1.sde.exception.CodecoRegisterException;  // Import your custom exception
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class CodecoRegisterService {

    private static final Logger logger = LogManager.getLogger(CodecoRegisterService.class);

    private final CatalogRepository catalogRepository;
    private final GateTransmissionRepository gateTransmissionRepository;
    private final GateTransmissionSentRepository gateTransmissionSentRepository;

    @Autowired
    public CodecoRegisterService(CatalogRepository catalogRepository,
                                 GateTransmissionRepository gateTransmissionRepository,
                                 GateTransmissionSentRepository gateTransmissionSentRepository) {
        this.catalogRepository = catalogRepository;
        this.gateTransmissionRepository = gateTransmissionRepository;
        this.gateTransmissionSentRepository = gateTransmissionSentRepository;
    }

    @Transactional
    public CodecoRegisterOutput registerSdeCodeco(CodecoRegisterInput.Input input) {
        CodecoRegisterOutput outputDTO = new CodecoRegisterOutput();

        // Initialize out parameters
        outputDTO.setRespNewId(0);
        outputDTO.setRespEstado(0);
        outputDTO.setRespMensaje("");

        try {
            // Step 1: Retrieve catalog for alias "48158"
            Catalog generatedCatalog = catalogRepository.findByAlias("48158");
            if (generatedCatalog == null) {
                throw new CodecoRegisterException("No Catalog found for alias 48158.");
            }

            // Step 2: Insert into GateTransmissionSent (edi_codeco_envio)
            GateTransmissionSent gateTransmissionSent = new GateTransmissionSent();
            gateTransmissionSent.setCatGateTransmissionStatus(generatedCatalog);
            GateTransmissionSetting gateTransmissionSetting = new GateTransmissionSetting();
            gateTransmissionSetting.setId(input.getSeteoEdiCodecoId());
            gateTransmissionSent.setGateTransmissionSetting(gateTransmissionSetting);
            gateTransmissionSent.setGateTransmissionNameFile(input.getNombreArchivo());
            gateTransmissionSent.setGateTransmissionContent(input.getContenido());
            gateTransmissionSent.setGateTransmissionSentFileDate(null);
            gateTransmissionSent.setActive(true);
            gateTransmissionSent.setRegistrationDate(LocalDateTime.now());

            gateTransmissionSent.setRegistrationUser(new User(1));
            gateTransmissionSent.setRecipientMail(input.getCorreoDestinatario());
            gateTransmissionSent.setProcessRecords(input.getArchivosProcesar());
            gateTransmissionSent.setCatStructureFormat(new Catalog(input.getTipoEstructuraId().intValue()));

            GateTransmissionSent savedGateTransmissionSent = gateTransmissionSentRepository.save(gateTransmissionSent);
            Integer newEnvioId = savedGateTransmissionSent.getId();
            outputDTO.setRespNewId(newEnvioId);

            // Step 3: Update GateTransmission for each ID in the list
            List<Integer> codecoIds = new ArrayList<>();
            input.getListaEdiCodecoId().forEach(item -> codecoIds.add(item.getEdiCodecoId()));

            List<GateTransmission> transmissions = gateTransmissionRepository.findAllById(codecoIds);
            for (GateTransmission gt : transmissions) {
                gt.setGateTransmissionSent(savedGateTransmissionSent);
                gt.setCatSendStatusGateTransmission(generatedCatalog);

                String currentTrace = gt.getTraceGateTransmission() != null ? gt.getTraceGateTransmission() : "";
                String newTrace = (currentTrace + "-> start");
                if (newTrace.length() > 50) {
                    newTrace = newTrace.substring(0, 50);
                }
                gt.setTraceGateTransmission(newTrace);
            }
            gateTransmissionRepository.saveAll(transmissions);

            // Step 4: Set response for successful transaction
            outputDTO.setRespEstado(1);
            outputDTO.setRespMensaje("Transaction completed successfully.");

        } catch (CodecoRegisterException e) {
            // Handle custom exception
            logger.error("Error registering SDE CODECO: {}", e.getMessage());
            outputDTO.setRespEstado(0);
            outputDTO.setRespMensaje(e.getMessage());
        } catch (Exception e) {
            // Catch any other unexpected exceptions
            logger.error("Unexpected error during SDE CODECO registration", e);
            outputDTO.setRespEstado(0);
            outputDTO.setRespMensaje("An unexpected error occurred.");
        }

        return outputDTO;
    }
}
