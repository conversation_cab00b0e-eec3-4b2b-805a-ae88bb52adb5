package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.EirActivityZoneRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
class EirActivityZoneService {
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final com.maersk.sd1.common.repository.EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final com.maersk.sd1.common.repository.CargoDocumentRepository cargoDocumentRepository;
    private final com.maersk.sd1.common.repository.CompanyRepository companyRepository;

    /**
     * Return true if activity cat_actividad_zona_id => cat= 'INSP' or 'PTI', concluded=1.
     */
    public boolean isActivityConcluded(Integer eirId, String zoneCode) {
        if (eirId == null) return false;
        return eirActivityZoneRepository.existsByEirIdAndCatZoneActivity_DescriptionAndConcluded(eirId, zoneCode, true);
    }

    /**
     * Return the last endDate if cat='PTI','LAV','INSP', etc.
     */
    public LocalDateTime findLastActivityEndDate(Integer eirId, String zoneCode) {
        if (eirId == null) return null;
        return eirActivityZoneRepository.findLastEndDateForActivity(eirId, zoneCode);
    }

    /**
     * Return true if there's an activity with blockContainer=1 for cat='INSP'.
     */
    public boolean isContainerBlocked(Integer eirId) {
        if (eirId == null) return false;
        return eirActivityZoneRepository.existsByEirIdAndCatZoneActivity_DescriptionAndBlockContainer(eirId, "INSP", true);
    }

    /**
     * If there's an activity PTI => con_sensor=1 => returns true.
     */
    public Boolean findSensorInPTI(Integer eirId) {
        return eirActivityZoneRepository.findSensorInPTI(eirId);
    }

    /**
     * Return a comma-separated list of cargo_document from eir_documento_carga_detalle.
     * The stored procedure does similar logic.
     */
    public String findCargoDocuments(Integer eirId) {
        List<String> docs = eirDocumentCargoDetailRepository.findCargoDocuments(eirId);
        if (docs == null || docs.isEmpty()) return "";
        return docs.stream().collect(Collectors.joining(", "));
    }

    /**
     * Return top 1 consignee name.
     */
    public String findConsignee(Integer eirId) {
        // The sp does a subselect from sds.documento_carga => empresa_consignatario_id => EMP.razon_social.
        // We'll replicate a simpler approach in a repository query or we do it here.
        return eirDocumentCargoDetailRepository.findTopConsignee(eirId);

    }

    /**
     * Return top 1 shipper.
     */
    public String findShipper(Integer eirId) {
        return eirDocumentCargoDetailRepository.findTopShipper(eirId);

    }
}
