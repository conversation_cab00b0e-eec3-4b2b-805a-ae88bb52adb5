package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.CodecoEnviarArchivosSftpInput;
import com.maersk.sd1.sde.dto.CodecoEnviarArchivosSftpOutput;
import com.maersk.sd1.common.repository.GateTransmissionSentRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CodecoEnviarArchivosSftpService {

    private static final Logger logger = LogManager.getLogger(CodecoEnviarArchivosSftpService.class);

    private final GateTransmissionSentRepository gateTransmissionSentRepository;

    @Autowired
    public CodecoEnviarArchivosSftpService(GateTransmissionSentRepository gateTransmissionSentRepository) {
        this.gateTransmissionSentRepository = gateTransmissionSentRepository;
    }

    @Transactional(readOnly = true)
    public CodecoEnviarArchivosSftpOutput fetchPendingRecords(CodecoEnviarArchivosSftpInput.Input input) {
        CodecoEnviarArchivosSftpOutput response = new CodecoEnviarArchivosSftpOutput();
        try {
            if (input == null || input.getSeteoEdiCodecoId() == null) {
                throw new IllegalArgumentException("seteo_edi_codeco_id is required.");
            }

            Integer seteoEdiCodecoId = input.getSeteoEdiCodecoId();
            List<CodecoEnviarArchivosSftpOutput.Record> results = gateTransmissionSentRepository.findAllPendingRecords(seteoEdiCodecoId);

            response.setRespEstado(1);
            response.setRespMensaje("Success");
            response.setRecords(results);
        } catch (Exception e) {
            logger.error("Error in fetchPendingRecords:", e);
            response.setRespEstado(0);
            response.setRespMensaje(e.getMessage());
        }
        return response;
    }
}
