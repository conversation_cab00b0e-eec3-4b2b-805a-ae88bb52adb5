package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.controller.dto.*;
import com.maersk.sd1.common.Parameter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@NoArgsConstructor(force = true)
@RequiredArgsConstructor
public class InspectionBoxGetService {


    private final EirActivityZoneRepository eirActivityZoneRepository;

    private final EirRepository eirRepository;

    private final EirObservationInspectorRepository eirObservationInspectorRepository;

    private final EstimateEmrRepository estimateEmrRepository;

    private final EstimateEmrEirPhotoRepository estimateEmrEirPhotoRepository;

    private final EstimateEmrDetailPhotoRepository estimateEmrDetailPhotoRepository;

    private final CatalogRepository catalogRepository;

    private final MessageLanguageRepository messageLanguageRepository;

    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Transactional(readOnly = true)
    public InspectionBoxGetOutputDTO getInspectionBox(InspectionBoxGetInputDTO.Input input) {

        Integer eirActivityZoneId = null;
        String msjFracaso = "";
        String inspectorComment = null;
        String observationsComment = null;
        Integer isActivityInspection = catalogRepository.findIdByAlias(Parameter.CATALOG_BOX_INSPECTION_ALIAS);
        InspectionBoxGetOutputDTO output = new InspectionBoxGetOutputDTO();
        Integer statusInspectionStarted = catalogRepository.findIdByAlias(Parameter.INSPECTION_GRAL_STATUS_STARTED);
        Integer statusInspectionPending = catalogRepository.findIdByAlias(Parameter.INSPECTION_GRAL_STATUS_PENDING);
        // Step 1) Retrieve EirActivityZone with concluded=0, cat_actividad_zona_id = isActivityInspection
        Optional<EirActivityZone> optEirActivityZone = eirActivityZoneRepository
            .findByEirIdAndCatZoneActivityIdAndActiveTrueAndConcludedFalse(
                input.getEirId(), isActivityInspection);
        if (optEirActivityZone.isPresent()) {
            eirActivityZoneId = optEirActivityZone.get().getId();
            inspectorComment = optEirActivityZone.get().getInspectorComment();
            observationsComment = optEirActivityZone.get().getInspectionObservationComment();
        } else {
            msjFracaso = messageLanguageRepository.fnTranslatedMessage("LIS_STRUCTURA_INSP", 1, input.getIdiomaId());
        }

        Eir eir = eirRepository.getReferenceById(input.getEirId());
        Integer shippingProgrammingDetailId = Optional.ofNullable(eir.getVesselProgrammingDetail())
                .map(VesselProgrammingDetail::getId)
                .orElse(null);

        Integer containerId = Optional.ofNullable(eir.getContainer())
                .map(Container::getId)
                .orElse(null);

        if (containerId == null || shippingProgrammingDetailId == null) {
            throw new IllegalArgumentException("containerId or shippingProgrammingDetailId not found for Eir id: " + input.getEirId());
        }

        List<Boolean> dangerousIndicator = cargoDocumentDetailRepository.findDangerousCargoByContainerIdAndProgramacionNaveDetalleId(containerId, shippingProgrammingDetailId);

        Integer emrNumber = eirActivityZoneRepository.findEstimadoEmrIdByActividadZonaIdAndEstimateStatus(isActivityInspection, Parameter.ESTIMATE_STATUS_FINALIZED, Parameter.ESTIMATE_STATUS_CREATED);

        if (emrNumber == null || emrNumber == 0) {
            emrNumber = estimateEmrRepository.findTopEstimateIdByEirIdAndTypeAndStatus(input.getEirId(), Parameter.ESTIMATE_TYPE_STRUCTURE, List.of(Parameter.ESTIMATE_STATUS_FINALIZED, Parameter.ESTIMATE_STATUS_CREATED));
        }

        boolean isDangerousCargo = dangerousIndicator.contains(true);

        String exitoFracaso = msjFracaso.isEmpty() ? "1" : "0";
        output.setSuccessFailure(exitoFracaso);
        output.setFailureMessage(msjFracaso);

        List<Object[]> eirActivityZoneDetails = eirActivityZoneRepository.findEirActivityZoneDetails(eirActivityZoneId,
                isDangerousCargo, inspectorComment, observationsComment, input.getEirId(), isActivityInspection, statusInspectionStarted, statusInspectionPending, input.getIdiomaId());
        output.setInspectionBoxDTO(mapToInspectionBoxDTO(eirActivityZoneDetails));
        List<Object[]> boxZoneWithoutDamage = eirActivityZoneRepository.callObtenerSiguienteZona(input.getEirId(), "INSP", 0);
        output.setNextZoneWithoutDamageDTO(mapFirstToNextZoneDTO(boxZoneWithoutDamage));
        List<Object[]> boxZoneWithDamage = eirActivityZoneRepository.callObtenerSiguienteZona(input.getEirId(), "INSP", 1);
        output.setNextZoneWithDamageDTO(mapFirstToNextZoneDTO(boxZoneWithDamage));
        List<Object[]> estimateEmrDetails = estimateEmrRepository.findEstimateEmrDetails(emrNumber, input.getIdiomaId());
        output.setEstimatedEmrDetailDTO(mapToEstimatedEmrDetailDTO(estimateEmrDetails));
        output.setEstimatedEmrPhotoHeaderDTO(estimateEmrEirPhotoRepository.findEstimateEmrPhotoHeaders(emrNumber));
        output.setEstimatedEmrDetailPhotoDTO(estimateEmrDetailPhotoRepository.findEstimateEmrDetailPhotos(emrNumber));
        List<Object[]> observationStructure = eirObservationInspectorRepository.findObservationsForEir(input.getEirId(), input.getIdiomaId(), false);
        List<Object[]> observationMachinery = eirObservationInspectorRepository.findObservationsForEir(input.getEirId(), input.getIdiomaId(), true);
        output.setObservationStructureDTOList(mapToObservationDTO(observationStructure));
        output.setObservationMachineryDTOList(mapToObservationDTO(observationMachinery));
        return output;
    }

    public List<InspectionBoxDTO> mapToInspectionBoxDTO(List<Object[]> eirActivityZoneDetails) {
        List<InspectionBoxDTO> dtoList = new ArrayList<>();

        if (eirActivityZoneDetails != null && !eirActivityZoneDetails.isEmpty()) {
            for (Object[] details : eirActivityZoneDetails) {
                InspectionBoxDTO inspectionBoxDTO = new InspectionBoxDTO();
                inspectionBoxDTO.setEntryDate(details[0] != null ? ((Timestamp) details[0]).toLocalDateTime() : null);
                inspectionBoxDTO.setEirId(details[1] != null ? ((Integer) details[1]) : null);
                inspectionBoxDTO.setContainerNumber((String) details[2]);
                inspectionBoxDTO.setContainerSize(details[3] != null ? (BigDecimal) details[3] : null);
                inspectionBoxDTO.setContainerType(details[4] != null ? (BigDecimal) details[4] : null);
                inspectionBoxDTO.setContainerClass((String) details[5]);
                inspectionBoxDTO.setTareWeight((String) details[6]);
                inspectionBoxDTO.setMaxLoad((String) details[7]);
                inspectionBoxDTO.setIsoCodeId(details[8] != null ? ((Integer) details[8]) : null);
                inspectionBoxDTO.setIsoCode((String) details[9]);
                inspectionBoxDTO.setLineCode(details[10] != null ? ((Integer) details[10]) : null);
                inspectionBoxDTO.setLineDescription((String) details[11]);
                inspectionBoxDTO.setMovement((String) details[12]);
                inspectionBoxDTO.setClient((String) details[13]);
                inspectionBoxDTO.setNvr((String) details[14]);
                inspectionBoxDTO.setLocation((String) details[15]);
                inspectionBoxDTO.setManufactureDate(details[16] != null ? ((Timestamp) details[16]).toLocalDateTime() : null);
                inspectionBoxDTO.setReeferTypeCode(details[17] != null ? ((Integer) details[17]) : null);
                inspectionBoxDTO.setReeferType((String) details[18]);
                inspectionBoxDTO.setEngineBrandCode(details[19] != null ? ((Integer) details[19]) : null);
                inspectionBoxDTO.setEngineBrand((String) details[20]);
                inspectionBoxDTO.setDangerousLoadMessage((String) details[21]);
                inspectionBoxDTO.setZoneActivityId(details[22] != null ? ((Integer) details[22]) : null);
                inspectionBoxDTO.setInspectorComment((String) details[23]);
                inspectionBoxDTO.setPotentialFoodAid((Boolean) details[24]);
                inspectionBoxDTO.setImoChecklist((String) details[25]);
                inspectionBoxDTO.setFlagCleaningSectionInterior((Boolean) details[26]);
                inspectionBoxDTO.setFlagCleaningSectionBottom((Boolean) details[27]);
                inspectionBoxDTO.setFlagCleaningSectionRight((Boolean) details[28]);
                inspectionBoxDTO.setFlagCleaningSectionLeft((Boolean) details[29]);
                inspectionBoxDTO.setFlagCleaningSectionTop((Boolean) details[30]);
                inspectionBoxDTO.setCleaningTypeId(details[31] != null ? ((Integer) details[31]) : null);
                inspectionBoxDTO.setObservationsComment((String) details[32]);
                inspectionBoxDTO.setTruckExitDate(details[33] != null ? ((Integer) details[33]) : null);
                inspectionBoxDTO.setStatusBoolean((Boolean) details[34]);
                inspectionBoxDTO.setStatus((String) details[35]);
                inspectionBoxDTO.setStatusAlias((String) details[36]);

                dtoList.add(inspectionBoxDTO);
            }
        }

        return dtoList;
    }

    public List<NextZoneDTO> mapFirstToNextZoneDTO(List<Object[]> boxZoneWithoutDamage) {
        List<NextZoneDTO> dtoList = new ArrayList<>();

        if (boxZoneWithoutDamage != null && !boxZoneWithoutDamage.isEmpty()) {
            for (Object[] details : boxZoneWithoutDamage) {
                NextZoneDTO nextZoneDTO = new NextZoneDTO();
                nextZoneDTO.setNextZone((String) details[0]);
                nextZoneDTO.setNextZoneALT((String) details[1]);
                nextZoneDTO.setComment((String) details[2]);
                nextZoneDTO.setCommentALT((String) details[3]);
                dtoList.add(nextZoneDTO);
            }
        }

        return dtoList;
    }

    public List<EstimatedEmrDetailDTO> mapToEstimatedEmrDetailDTO(List<Object[]> estimateEmrDetails) {
        List<EstimatedEmrDetailDTO> dtoList = new ArrayList<>();

        for (Object[] details : estimateEmrDetails) {
            EstimatedEmrDetailDTO dto = new EstimatedEmrDetailDTO();
            dto.setItem(details[0] != null ? ((Number) details[0]).longValue() : null);
            dto.setEstimatedEmrDetailId(details[1] != null ? (Integer) details[1] : null);
            dto.setDamageLocationCategoryId(details[2] != null ? (BigDecimal) details[2] : null);
            dto.setDamageLocation((String) details[3]);
            dto.setDamageTypeCategoryId(details[4] != null ? (BigDecimal) details[4] : null);
            dto.setDamageType((String) details[5]);
            dto.setComponentCategoryId(details[6] != null ? (BigDecimal) details[6] : null);
            dto.setComponent((String) details[7]);
            dto.setRepairMethodCategoryId(details[8] != null ? (BigDecimal) details[8] : null);
            dto.setRepairMethod((String) details[9]);
            dto.setCedexMercCode((String) details[10]);
            dto.setCedexMercDescription((String) details[11]);
            dto.setDimensionTypeCategoryId(details[12] != null ? (BigDecimal) details[12] : null);
            dto.setDamageDimensionType((String) details[13]);
            dto.setDimensionLength(details[14] != null ? (BigDecimal) details[14] : null);
            dto.setDimensionWidth(details[15] != null ? (BigDecimal) details[15] : null);
            dto.setDimension((String) details[16]);
            dto.setCostAssumptionCategoryId(details[17] != null ? (BigDecimal) details[17] : null);
            dto.setCostAssumption((String) details[18]);
            dto.setPartsToRepair(details[19] != null ? (BigDecimal) details[19] : null);
            dto.setHoursPerPart(details[20] != null ? (BigDecimal) details[20] : null);
            dto.setLaborCostPerPart(details[21] != null ? (BigDecimal) details[21] : null);
            dto.setMaterialCostPerPart(details[22] != null ? (BigDecimal) details[22] : null);
            dto.setSparePart((BigDecimal) details[23]);
            dto.setTotalSparePartCost(details[24] != null ? (BigDecimal) details[24] : null);
            dto.setTotalItemCost(details[25] != null ? (BigDecimal) details[25] : null);
            dto.setObservation((String) details[26]);
            dto.setIsReefer(details[27] != null ? (Boolean) details[27] : null);
            dto.setShippingLineId(details[28] != null ? (Integer) details[28] : null);
            dto.setUnitMeasureCategoryId(details[29] != null ? (BigDecimal) details[29] : null);
            dto.setRepairLocationDescription((String) details[30]);
            dto.setDamageTypeDescription((String) details[31]);
            dto.setComponentDescription((String) details[32]);
            dto.setRepairMethodDescription((String) details[33]);

            dtoList.add(dto);
        }

        return dtoList;
    }

    public List<ObservationDTO> mapToObservationDTO(List<Object[]> observationMachinery) {
        List<ObservationDTO> dtoList = new ArrayList<>();

        for (Object[] details : observationMachinery) {
            ObservationDTO dto = new ObservationDTO();
            dto.setCatObservationEirId(details[0] != null ? (BigDecimal) details[0] : null);
            dto.setRow(details[1] != null ? (Long) details[1] : null);
            dto.setObservationDescriptionSecos(details[2] != null ? (String) details[2] : null);
            dtoList.add(dto);
        }

        return dtoList;
    }
}
