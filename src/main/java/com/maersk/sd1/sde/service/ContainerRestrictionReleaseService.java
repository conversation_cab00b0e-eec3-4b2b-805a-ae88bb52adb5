package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ContainerRestriction;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.ContainerRestrictionRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.dto.ContainerRestrictionReleaseInput;
import com.maersk.sd1.sde.dto.ContainerRestrictionReleaseOutput;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@Log4j2
public class ContainerRestrictionReleaseService {

    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final CatalogRepository catalogRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public ContainerRestrictionReleaseService(ContainerRestrictionRepository containerRestrictionRepository, CatalogRepository catalogRepository, MessageLanguageRepository messageLanguageRepository) {
        this.containerRestrictionRepository = containerRestrictionRepository;
        this.catalogRepository = catalogRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public ContainerRestrictionReleaseOutput releaseContainerRestriction(ContainerRestrictionReleaseInput.Input input) {
        ContainerRestrictionReleaseOutput output = new ContainerRestrictionReleaseOutput();
        Catalog catOriginRelease = catalogRepository.findByAliasAndStatusTrue(input.getCatOriginReleaseRestrictionAlias())
                .orElse(null);
        if (catOriginRelease == null) {
            throw new IllegalArgumentException("Catalog with alias " + input.getCatOriginReleaseRestrictionAlias() + " not found or inactive.");
        }
        ContainerRestriction containerRestriction = containerRestrictionRepository.findById(input.getRestrictionId())
                .orElse(null);
        if (containerRestriction == null) {
            throw new IllegalArgumentException("ContainerRestriction with ID " + input.getRestrictionId() + " not found.");
        }
        containerRestriction.setReleasedRestriction(true);
        containerRestriction.setRestrictionReleaseAnnotation(input.getObservation());
        containerRestriction.setCatRestrictionReleaseOrigin(catOriginRelease);
        containerRestriction.setRestrictionModificationDate(LocalDateTime.now());
        User userModification = new User(input.getUserModificationId());
        containerRestriction.setRestrictionModificationUser(userModification);
        containerRestrictionRepository.save(containerRestriction);

        String translatedMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, input.getLanguageId());
        output.setRespEstado(1);
        output.setRespMensaje(translatedMsg);
        return output;
    }
}

