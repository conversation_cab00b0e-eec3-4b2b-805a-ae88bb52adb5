package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.EirZone;
import com.maersk.sd1.common.repository.EirZoneRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
class EirZoneService {
    private final EirZoneRepository eirZoneRepository;

    /**
     * Return the description of the last zone. If none found, return empty.
     * If found, check if cat_zone_contenedor's description is 'SINP' => we also map it to 'INSP'.
     */
    public String findLastZoneDescription(Integer eirId) {
        if (eirId == null) {
            return "";
        }
        // We find EirZone with eir.id = eirId, order by date desc.
        List<String> last = eirZoneRepository.findLastZone(eirId);
        if (last == null || last.isEmpty()) return "";
        return last.get(0);
    }

    public Boolean findOkSecadoLavado(Integer eirId) {
        if (eirId == null) return false;
        // The sp logic searches for zone with cat='LAV' => pick ok_secado.
        // Possibly the last or any. We'll replicate the sp approach: we group by eir, zone LAV.
        List<EirZone> zones = eirZoneRepository.findAllByEirIdAndActive(eirId);
        // Filter cat_zona_contenedor => 'LAV'.
        EirZone found = zones.stream()
                .filter(z -> (z.getCatContainerZone() != null && "LAV".equalsIgnoreCase(z.getCatContainerZone().getDescription())))
                .findFirst()
                .orElse(null);
        if (found == null) return false;
        return found.getFlagDried();
    }
}
