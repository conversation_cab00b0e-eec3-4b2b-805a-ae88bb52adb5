package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.sde.dto.GateTransmissionSettingEditInput;
import com.maersk.sd1.sde.dto.GateTransmissionSettingEditOutput;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionLocalSettingRepository;
import com.maersk.sd1.common.repository.GateTransmissionEmptyActivitySettingRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class GateTransmissionSettingEditService {

    private static final Logger logger = LogManager.getLogger(GateTransmissionSettingEditService.class);

    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;
    private final GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;
    private final GateTransmissionEmptyActivitySettingRepository gateTransmissionEmptyActivitySettingRepository;
    private final UserRepository userRepository;

    @Transactional
    public GateTransmissionSettingEditOutput editGateTransmissionSetting(GateTransmissionSettingEditInput.Input input) {
        GateTransmissionSettingEditOutput output = new GateTransmissionSettingEditOutput();
        try {
            logger.info("Starting editGateTransmissionSetting with input: {}", input);

            if (isDuplicateSetting(input)) {
                output.setRespEstado(2);
                output.setRespMensaje("There is another codeco with the same key (Business Unit, Sub Business Unit, Shipping Line and Delivery System)");
                return output;
            }

            Optional<GateTransmissionSetting> optionalSetting = gateTransmissionSettingRepository.findById(input.getGateTransmissionSettingId());
            if (optionalSetting.isEmpty()) {
                output.setRespEstado(0);
                output.setRespMensaje("GateTransmissionSetting not found");
                return output;
            }

            GateTransmissionSetting setting = optionalSetting.get();
            updateSettingFields(setting, input);
            gateTransmissionSettingRepository.save(setting);

            if (setting.getId() != null) {
                updateLocalAndEmptyActivitySettings(setting, input);
            }

            output.setRespEstado(1);
            output.setRespMensaje("Successfully update");
            return output;
        } catch (Exception e) {
            logger.error("Error in editGateTransmissionSetting", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return output;
        }
    }

    private boolean isDuplicateSetting(GateTransmissionSettingEditInput.Input input) {
        Optional<GateTransmissionSetting> existingSetting = gateTransmissionSettingRepository.findSameKeyDifferentId(
                input.getBusinessUnitId(),
                input.getSubBusinessUnitId(),
                input.getShippingLineId(),
                input.getSystemDelivery(),
                input.getGateTransmissionSettingId()
        );
        return existingSetting.isPresent();
    }

    private void updateSettingFields(GateTransmissionSetting setting, GateTransmissionSettingEditInput.Input input) {
        if (setting.getBusinessUnit() == null) {
            setting.setBusinessUnit(new BusinessUnit());
        }
        setting.getBusinessUnit().setId(input.getBusinessUnitId());

        if (setting.getSubBusinessUnit() == null) {
            setting.setSubBusinessUnit(new BusinessUnit());
        }
        setting.getSubBusinessUnit().setId(input.getSubBusinessUnitId());

        if (setting.getShippingLine() == null) {
            setting.setShippingLine(new ShippingLine());
        }
        setting.getShippingLine().setId(input.getShippingLineId());

        setting.setSystemDelivery(input.getSystemDelivery());
        setting.setInfoSystemDelivery(input.getInfoSystemDelivery());
        setting.setReceptorIdentifier(input.getReceptorIdentifier());
        setting.setSendGateInEmpty(input.getSendGateInEmpty());
        setting.setSendGateOutEmpty(input.getSendGateOutEmpty());
        setting.setSendGateInFull(input.getSendGateInFull());
        setting.setSendGateOutFull(input.getSendGateOutFull());
        setting.setSendStatusActivity(input.getSendStatusActivity());
        updateCatalogFields(setting, input);
        setting.setGateTransmissionDestinationMail(input.getGateTransmissionDestinationMail());
        setting.setTelexDestinationMail(input.getTelexDestinationMail());
        setting.setParameter1(input.getParameter1());
        setting.setParameter2(input.getParameter2());
        setting.setParameter3(input.getParameter3());
        setting.setParameter4(input.getParameter4());
        setting.setIsHistorical(input.getHistorical());
        setting.setDeactivationDate(input.getDeactivationDate() != null ? input.getDeactivationDate().atStartOfDay() : null);
        setting.setDeactivationReason(input.getDeactivationReason());
        setting.setActive(input.getActive());
        updateModificationUser(setting, input);
        setting.setModificationDate(LocalDateTime.now());
        setting.setParameter5(input.getParameter5());
        setting.setParameter6(input.getParameter6());
        setting.setAzureIdGateTransmission(input.getAzureIdGateTransmission());
        setting.setAzureIdTelex(input.getAzureIdTelex());
        setting.setSftpId(input.getSftpId());
        setting.setExtensionFileSend(input.getExtensionFileSend());
        setting.setElapsedMinutes(input.getElapsedMinutes());
    }

    private void updateCatalogFields(GateTransmissionSetting setting, GateTransmissionSettingEditInput.Input input) {
        setting.setCatGateOutEmptyFormat(updateCatalog(setting.getCatGateOutEmptyFormat(), input.getCatFormatoGateOutEmptyId()));
        setting.setCatGateInFullFormat(updateCatalog(setting.getCatGateInFullFormat(), input.getCatFormatoGateInFullId()));
        setting.setCatGateOutFullFormat(updateCatalog(setting.getCatGateOutFullFormat(), input.getCatFormatoGateOutFullId()));
        setting.setCatGateInEmptyFormat(updateCatalog(setting.getCatGateInEmptyFormat(), input.getCatFormatoGateInEmptyId()));
        setting.setCatStatusActivityFormat(updateCatalog(setting.getCatStatusActivityFormat(), input.getCatFormatoStatusActivityId()));
        setting.setCatDeliveryChannel(updateCatalog(setting.getCatDeliveryChannel(), input.getCatCanalEnvioId()));
        setting.setCatGenerateFileMode(updateCatalog(setting.getCatGenerateFileMode(), input.getCatModoGenerarArchivoId()));
    }

    private Catalog updateCatalog(Catalog catalog, Integer catalogId) {
        if (catalogId != null) {
            if (catalog == null) {
                catalog = new Catalog();
            }
            catalog.setId(catalogId);
        } else {
            catalog = null;
        }
        return catalog;
    }

    private void updateModificationUser(GateTransmissionSetting setting, GateTransmissionSettingEditInput.Input input) {
//        User modUser = setting.getModificationUser();
//        if (modUser == null) {
//            modUser = new User();
//        }
        User modUser = userRepository.findById(input.getUserModificationId())
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + input.getUserModificationId()));
//        modUser.setId(input.getUserModificationId());
        setting.setModificationUser(modUser);
    }

    private void updateLocalAndEmptyActivitySettings(GateTransmissionSetting setting, GateTransmissionSettingEditInput.Input input) {
        gateTransmissionLocalSettingRepository.deleteByGateTransmissionSetting(setting);
        gateTransmissionEmptyActivitySettingRepository.deleteByGateTransmissionSetting(setting);

        if (input.getSubUnits() != null) {
            try {
                // Convert the JSON string to a List<SubUnit>
                ObjectMapper objectMapper = new ObjectMapper();
                List<GateTransmissionSettingEditInput.SubUnit> subUnits = objectMapper.readValue(
                        input.getSubUnits(),
                        new TypeReference<List<GateTransmissionSettingEditInput.SubUnit>>() {}
                );

                // Process the list
                subUnits.forEach(subUnit -> saveLocalSetting(setting, input, subUnit));
            } catch (Exception e) {
                logger.error("Error parsing subUnits JSON string", e);
                throw new RuntimeException("Invalid subUnits JSON format", e);
            }
        }


        if (input.getGateInEmptyProcedencias() != null) {
            try {
                // Parse JSON string to List<Procedencia>
                ObjectMapper objectMapper = new ObjectMapper();
                List<GateTransmissionSettingEditInput.Procedencia> gateInEmptyProcedencias = objectMapper.readValue(
                        input.getGateInEmptyProcedencias(),
                        new TypeReference<List<GateTransmissionSettingEditInput.Procedencia>>() {}
                );

                // Process the list
                gateInEmptyProcedencias.forEach(proc -> saveEmptyActivitySetting(setting, input, proc, input.getGateInEmptyMovimientoId()));
            } catch (Exception e) {
                logger.error("Error parsing GateInEmptyProcedencias JSON string", e);
                throw new RuntimeException("Invalid GateInEmptyProcedencias JSON format", e);
            }
        }

        if (input.getGateOutEmptyProcedencias() != null) {
            try {
                // Parse JSON string to List<Procedencia>
                ObjectMapper objectMapper = new ObjectMapper();
                List<GateTransmissionSettingEditInput.Procedencia> gateOutEmptyProcedencias = objectMapper.readValue(
                        input.getGateOutEmptyProcedencias(),
                        new TypeReference<List<GateTransmissionSettingEditInput.Procedencia>>() {}
                );

                // Process the list
                gateOutEmptyProcedencias.forEach(proc -> saveEmptyActivitySetting(setting, input, proc, input.getGateOutEmptyMovimientoId()));
            } catch (Exception e) {
                logger.error("Error parsing GateOutEmptyProcedencias JSON string", e);
                throw new RuntimeException("Invalid GateOutEmptyProcedencias JSON format", e);
            }
        }
    }

    private void saveLocalSetting(GateTransmissionSetting setting, GateTransmissionSettingEditInput.Input input, GateTransmissionSettingEditInput.SubUnit subUnit) {
        GateTransmissionLocalSetting localSetting = new GateTransmissionLocalSetting();
        localSetting.setId(null);
        localSetting.setGateTransmissionSetting(setting);
        BusinessUnit localBU = new BusinessUnit();
        localBU.setId(subUnit.getBusinessUnitId());
        localSetting.setLocalSubBusinessUnit(localBU);
        localSetting.setActive(input.getActive());
        User regUser = new User();
        regUser.setId(input.getUserModificationId());
        localSetting.setRegistrationUser(regUser);
        localSetting.setRegistrationDate(LocalDateTime.now());
        localSetting.setIdentifierEmitter(subUnit.getIdentifierEmitter());
        localSetting.setLocacionActivity(subUnit.getLocacionActivity());
        gateTransmissionLocalSettingRepository.save(localSetting);
    }

    private void saveEmptyActivitySetting(GateTransmissionSetting setting, GateTransmissionSettingEditInput.Input input, GateTransmissionSettingEditInput.Procedencia proc, Integer movimientoId) {
        GateTransmissionEmptyActivitySetting activitySetting = new GateTransmissionEmptyActivitySetting();
        activitySetting.setId(null);
        activitySetting.setGateTransmissionSetting(setting);
        Catalog movementCat = new Catalog();
        movementCat.setId(movimientoId);
        activitySetting.setCatMovement(movementCat);
        Catalog originCat = new Catalog();
        originCat.setId(proc.getCatalogId());
        activitySetting.setCatOrigin(originCat);
        activitySetting.setActive(input.getActive());
        User regUser = new User();
        regUser.setId(input.getUserModificationId());
        activitySetting.setRegistrationUser(regUser);
        activitySetting.setRegistrationDate(LocalDateTime.now());
        gateTransmissionEmptyActivitySettingRepository.save(activitySetting);
    }
}