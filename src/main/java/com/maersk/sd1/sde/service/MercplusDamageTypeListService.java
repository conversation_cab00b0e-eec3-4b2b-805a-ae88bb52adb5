package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.dto.MercplusDamageTypeListOutput;
import com.maersk.sd1.sde.exception.MercplusDamageTypeListException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Service class that replicates the logic of the sde.mercplus_damage_type_list stored procedure.
 * It fetches the parent Catalog by alias = 'sd1_mercplus_damage_type', then returns all active children.
 */
@Service
@Log4j2
public class MercplusDamageTypeListService {

    private final CatalogRepository catalogRepository;

    @Autowired
    public MercplusDamageTypeListService(CatalogRepository catalogRepository) {
        this.catalogRepository = catalogRepository;
    }

    /**
     * Retrieves a list of damage types.
     * This is the Java/Spring equivalent of the stored procedure logic.
     * @return a list of DamageTypeListOutput objects.
     */
    @Transactional(readOnly = true)
    public List<MercplusDamageTypeListOutput> getDamageTypes() {
        try {
            // 1. Retrieve the parent Catalog entry by alias.
            Catalog parentCatalog = catalogRepository
                    .findByAlias("sd1_mercplus_damage_type");


            // 2. Retrieve all child catalogs (active) with that parent, ordered by description and longDescription.
            List<Catalog> childCatalogs = catalogRepository
                    .findByStatusTrueAndParentCatalogIdOrderByDescriptionAscLongDescriptionAsc(parentCatalog.getId());

            // 3. Build output list.
            List<MercplusDamageTypeListOutput> result = new ArrayList<>();
            for (Catalog c : childCatalogs) {
                MercplusDamageTypeListOutput dto = new MercplusDamageTypeListOutput();
                dto.setCatDamageTypeId(c.getId());
                String desc = c.getDescription() != null ? c.getDescription() : "";
                String longDesc = c.getLongDescription() != null ? c.getLongDescription() : "";
                dto.setDamageType(desc + " - " + longDesc);
                result.add(dto);
            }

            return result;
        } catch (Exception e) {
            log.error("Error retrieving damage types", e);
            throw new MercplusDamageTypeListException("Unexpected error while retrieving damage types", e);
        }
    }
}

