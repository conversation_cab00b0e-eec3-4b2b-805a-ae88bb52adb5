package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sde.dto.BookingBlockCancellationReleaseOutput;
import com.maersk.sd1.common.repository.BookingBlockCancellationDetailRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.CargoDocumentRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BookingBlockCancellationReleaseService {
    private static final Logger logger = LogManager.getLogger(BookingBlockCancellationReleaseService.class);

    private final BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;
    private final BookingRepository bookingRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final MessageLanguageService messageLanguageService;

    @Autowired
    public BookingBlockCancellationReleaseService(BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository,
                                                  BookingRepository bookingRepository,
                                                  CargoDocumentRepository cargoDocumentRepository,
                                                  MessageLanguageService messageLanguageService) {
        this.bookingBlockCancellationDetailRepository = bookingBlockCancellationDetailRepository;
        this.bookingRepository = bookingRepository;
        this.cargoDocumentRepository = cargoDocumentRepository;
        this.messageLanguageService = messageLanguageService;
    }

    @Transactional
    public BookingBlockCancellationReleaseOutput releaseBooking(Integer cancelBlockBookingId,
                                                                String comment,
                                                                Integer reasonCategory,
                                                                Integer userId,
                                                                Integer languageId) {
        BookingBlockCancellationReleaseOutput output = new BookingBlockCancellationReleaseOutput();
        try {
            List<Object[]> detail = bookingBlockCancellationDetailRepository.findFirstDetailWithActiveBooking(cancelBlockBookingId);

            Integer bookingId = null;
            Integer cargoDocId = null;

            if (!detail.isEmpty()) {
                Object[] row = detail.get(0);
                if (row.length >= 2) {
                    bookingId = row[0] != null ? ((Number) row[0]).intValue() : null;
                    cargoDocId = row[1] != null ? ((Number) row[1]).intValue() : null;
                }
            }

            int updatedRows = bookingBlockCancellationDetailRepository.updateBookingBlockCancellationDetail(
                    cancelBlockBookingId,
                    comment,
                    reasonCategory,
                    userId
            );

            logger.info("updated_Rows: {}", updatedRows);

//            if (bookingId != null && bookingId > 0) {
//                bookingRepository.updateBookingStatusToActive(bookingId, userId);
//            }
//
//            if (cargoDocId != null && cargoDocId > 0) {
//                cargoDocumentRepository.updateCargoDocumentStatusToActive(cargoDocId, userId);
//            }

            String translatedMessage = messageLanguageService.getMessage("CNC_BLQ_BK", 2, languageId);
            output.setResponseStatus(1);
            output.setResponseMessage(translatedMessage);



            logger.info("BookingBlockCancellationReleaseService.releaseBooking -> Success");
        } catch (Exception ex) {
            logger.error("Error while releasing booking block/cancellation", ex);
            output.setResponseStatus(0);
            output.setResponseMessage(ex.getMessage());
        }

        return output;
    }
}
