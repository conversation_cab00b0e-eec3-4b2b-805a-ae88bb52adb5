package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.EirPhotosRegisterInput;
import com.maersk.sd1.sde.dto.EirPhotosRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional
public class EirPhotosRegisterService {

    private static final Logger logger = LogManager.getLogger(EirPhotosRegisterService.class);

    private final CatalogRepository catalogRepository;
    private final AttachmentRepository attachmentRepository;
    private final EirRepository eirRepository;
    private final ChassisEstimateEirPhotoRepository chassisEstimateEirPhotoRepository;
    private final EstimateEmrEirPhotoRepository estimateEmrEirPhotoRepository;
    private final SdfEirPhotoRepository sdfEirPhotoRepository;

    public EirPhotosRegisterOutput registerEirPhotos(EirPhotosRegisterInput.Input input) {
        EirPhotosRegisterOutput output = new EirPhotosRegisterOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            logger.info("Starting EIR photos register process: eirId={}, equipmentCategoryId={}", input.getEirId(), input.getEquipmentCategoryId());

            Eir eir = eirRepository.findById(input.getEirId())
                    .orElseThrow(() -> new IllegalArgumentException("EIR not found for id " + input.getEirId()));

            List<Attachment> insertedAttachments = processAttachments(input);

            if (!insertedAttachments.isEmpty()) {
                saveEirPhotos(input, eir, insertedAttachments);
            }

            output.setRespEstado(1);
            output.setRespMensaje("Photos were successfully added to current EIR");

        } catch (Exception e) {
            logger.error("Error registering photos for EIR.", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }

    private List<Attachment> processAttachments(EirPhotosRegisterInput.Input input) {
        List<Attachment> insertedAttachments = new ArrayList<>();
        if (input.getFotos() != null && !input.getFotos().isEmpty()) {
            for (EirPhotosRegisterInput.FotoDto foto : input.getFotos()) {
                if ("I".equalsIgnoreCase(foto.getTipoFoto())) {
                    Attachment attachment = createAttachment(input, foto);
                    Attachment saved = attachmentRepository.save(attachment);
                    insertedAttachments.add(saved);
                }
            }
        }
        return insertedAttachments;
    }

    private Attachment createAttachment(EirPhotosRegisterInput.Input input, EirPhotosRegisterInput.FotoDto foto) {
        Attachment attachment = new Attachment();
        attachment.setName(foto.getNombre());
        attachment.setFormat(foto.getFormato());
        attachment.setWeight(parseWeight(foto.getPeso()));
        attachment.setLocation(foto.getUbicacion());
        attachment.setStatus(true);
        attachment.setRegistrationUser(new User(input.getUsuarioRegistroId()));
        attachment.setRegistrationDate(LocalDateTime.now());
        attachment.setId1(foto.getId());
        if (foto.getTipoAdjunto() != null) {
            attachment.setCatAttachmentType(new Catalog(foto.getTipoAdjunto()));
        }
        attachment.setUrl(foto.getUrl());
        return attachment;
    }

    private int parseWeight(String peso) {
        try {
            return peso != null ? Integer.parseInt(peso) : 0;
        } catch (NumberFormatException ex) {
            return 0;
        }
    }

    private void saveEirPhotos(EirPhotosRegisterInput.Input input, Eir eir, List<Attachment> insertedAttachments) {
        Catalog isChassisCatalog = catalogRepository.findByAlias("sd1_equipment_category_chassis");
        Catalog isEmptyCatalog = catalogRepository.findByAlias("43083");

        boolean isChassis = isChassisCatalog != null && isChassisCatalog.getId().equals(input.getEquipmentCategoryId());
        boolean isEmpty = eir.getCatEmptyFull() != null && isEmptyCatalog != null
                && eir.getCatEmptyFull().getAlias() != null
                && eir.getCatEmptyFull().getAlias().equals(isEmptyCatalog.getAlias());

        for (Attachment att : insertedAttachments) {
            if (isChassis) {
                saveChassisPhoto(input, eir, att);
            } else if (isEmpty) {
                saveEmrPhoto(input, eir, att);
            } else {
                saveSdfEirPhoto(input, eir, att);
            }
        }
    }

    private void saveChassisPhoto(EirPhotosRegisterInput.Input input, Eir eir, Attachment att) {
        ChassisEstimateEirPhoto chassisPhoto = new ChassisEstimateEirPhoto();
        chassisPhoto.setChassisEstimate(null);
        chassisPhoto.setEirChassis(eir.getEirChassis());
        chassisPhoto.setAttachment(att);
        chassisPhoto.setActive(true);
        chassisPhoto.setRegistrationUser(new User(input.getUsuarioRegistroId()));
        chassisPhoto.setRegistrationDate(LocalDateTime.now());
        chassisEstimateEirPhotoRepository.save(chassisPhoto);
    }

    private void saveEmrPhoto(EirPhotosRegisterInput.Input input, Eir eir, Attachment att) {
        EstimateEmrEirPhoto emrPhoto = new EstimateEmrEirPhoto();
        emrPhoto.setEstimadoEmr(null);
        emrPhoto.setEir(eir);
        emrPhoto.setAttachment(att);
        emrPhoto.setActive(true);
        emrPhoto.setRegistrationUser(new User(input.getUsuarioRegistroId()));
        emrPhoto.setRegistrationDate(LocalDateTime.now());
        estimateEmrEirPhotoRepository.save(emrPhoto);
    }

    private void saveSdfEirPhoto(EirPhotosRegisterInput.Input input, Eir eir, Attachment att) {
        SdfEirPhoto eirPhoto = new SdfEirPhoto();
        eirPhoto.setEir(eir);
        eirPhoto.setAttached(att);
        eirPhoto.setActive(true);
        eirPhoto.setUserRegistration(new User(input.getUsuarioRegistroId()));
        eirPhoto.setRegistrationDate(LocalDateTime.now());
        sdfEirPhotoRepository.save(eirPhoto);
    }
}