package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.controller.dto.GateTransmissionSendMailRegisterOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class GateTransmissionSendMailRegisterService {

    private static final Logger logger = LogManager.getLogger(GateTransmissionSendMailRegisterService.class);


    private final GateTransmissionSentMailRepository gateTransmissionSentMailRepository;

    private final  GateTransmissionSentRepository gateTransmissionSentRepository;

    private final GateTransmissionRepository gateTransmissionRepository;

    private final UserRepository userRepository; // Hypothetical. In a real scenario, inject your user repo.

    private final CatalogRepository catalogRepository; // Hypothetical. In a real scenario, inject your catalog repo.

    public GateTransmissionSendMailRegisterService(GateTransmissionSentMailRepository gateTransmissionSentMailRepository,
                                                   GateTransmissionSentRepository gateTransmissionSentRepository,
                                                   GateTransmissionRepository gateTransmissionRepository,
                                                   UserRepository userRepository,
                                                   CatalogRepository catalogRepository)
    {
        this.gateTransmissionSentMailRepository = gateTransmissionSentMailRepository;
        this.gateTransmissionRepository = gateTransmissionRepository;
        this.gateTransmissionSentRepository = gateTransmissionSentRepository;
        this.userRepository = userRepository;
        this.catalogRepository = catalogRepository;
    }
    /**
     * Replicates the logic of the stored procedure:
     * 1) Inserts new record in GateTransmissionSentMail (edi_codeco_envio_correo)
     * 2) For each edi_codeco_envio_id in the input JSON, fetch GateTransmissionSent, parse its JSON listGateTransmissionId.
     *    For each edi_codeco_id in that nested JSON, fetch GateTransmission, update it with catSendStatusGateTransmission=48165,
     *    user_modification_id=1, modification date=now, traceGateTransmission += ' -> inqueueemail'.
     * 3) Update GateTransmissionSent with catGateTransmissionStatus=48165, user_modification_id=1, modification date=now.
     * 4) Return the new GateTransmissionSentMail ID.
     */
    @Transactional
    public GateTransmissionSendMailRegisterOutput registerSendMail(String listaEdiCodecoEnvioId) {
        GateTransmissionSendMailRegisterOutput output = new GateTransmissionSendMailRegisterOutput();
        try {
            // 1) Insert a new record in GateTransmissionSentMail
            GateTransmissionSentMail mailEntity = new GateTransmissionSentMail();
            mailEntity.setListGateTransmissionSentId(listaEdiCodecoEnvioId);
            // We save to get the new ID
            mailEntity = gateTransmissionSentMailRepository.save(mailEntity);

            // Prepare user and catalog references (for user_modification_id=1 & cat_estado_envio_codeco_id=48165)
            User defaultModificationUser = userRepository.findById(1)
                    .orElseThrow(() -> new RuntimeException("Default user with ID=1 not found"));
            Catalog catQueuedStatus = catalogRepository.findById(48165)
                    .orElseThrow(() -> new RuntimeException("Queued Status with ID=48165 not found"));

            // 2) Parse the input JSON to read all edi_codeco_envio_id values
            //    Then for each edi_codeco_envio_id, retrieve GateTransmissionSent.
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(listaEdiCodecoEnvioId);

            if (!rootNode.isArray()) {
                throw new IllegalArgumentException("lista_edi_codeco_envio_id must be a JSON array.");
            }

            for (JsonNode item : rootNode) {
                JsonNode ediCodecoEnvioIdNode = item.get("edi_codeco_envio_id");
                if (ediCodecoEnvioIdNode == null || !ediCodecoEnvioIdNode.isInt()) {
                    logger.warn("Skipping invalid object in JSON array: {}", item);
                    continue;
                }
                Integer ediCodecoEnvioId = ediCodecoEnvioIdNode.asInt();

                // fetch GateTransmissionSent (sde.edi_codeco_envio) by id
                GateTransmissionSent gateTransmissionSent = gateTransmissionSentRepository.findById(ediCodecoEnvioId)
                        .orElseThrow(() -> new RuntimeException("GateTransmissionSent with ID=" + ediCodecoEnvioId + " not found"));

                // parse the JSON inside gateTransmissionSent.getListGateTransmissionId()
                String nestedJson = gateTransmissionSent.getListGateTransmissionId();
                if (nestedJson != null) {
                    JsonNode nestedRoot = mapper.readTree(nestedJson);
                    if (nestedRoot.isArray()) {

                        for (JsonNode nestedItem : nestedRoot) {
                            JsonNode ediCodecoIdNode = nestedItem.get("edi_codeco_id");
                            if (ediCodecoIdNode == null || !ediCodecoIdNode.isInt()) {
                                logger.warn("Skipping invalid object in nested JSON array: {}", nestedItem);
                                continue;
                            }
                            Integer ediCodecoId = ediCodecoIdNode.asInt();

                            // fetch GateTransmission (sde.edi_codeco) by id
                            GateTransmission gateTransmission = gateTransmissionRepository.findById(ediCodecoId)
                                    .orElseThrow(() -> new RuntimeException("GateTransmission with ID=" + ediCodecoId + " not found"));

                            // set cat_estado_envio_codeco_id=48165 (catSendStatusGateTransmission), modification user=1, date=now
                            gateTransmission.setCatSendStatusGateTransmission(catQueuedStatus);
                            gateTransmission.setModificationUser(defaultModificationUser);
                            gateTransmission.setModificationDate(LocalDateTime.now());

                            // traceGateTransmission: append " -> inqueueemail" with max length=50
                            String oldTrace = gateTransmission.getTraceGateTransmission() != null
                                    ? gateTransmission.getTraceGateTransmission()
                                    : "";
                            String newTrace = oldTrace + " -> inqueueemail";
                            if (newTrace.length() > 50) {
                                newTrace = newTrace.substring(0, 50);
                            }
                            gateTransmission.setTraceGateTransmission(newTrace);

                            gateTransmissionRepository.save(gateTransmission);
                        }
                    }
                }

                // 3) Update GateTransmissionSent => cat_estado_envio_codeco_id=48165, modification user=1, date=now
                gateTransmissionSent.setCatGateTransmissionStatus(catQueuedStatus);
                gateTransmissionSent.setModificationUser(defaultModificationUser);
                gateTransmissionSent.setModificationDate(LocalDateTime.now());
                gateTransmissionSentRepository.save(gateTransmissionSent);
            }

            // 4) Return new ID from GateTransmissionSentMail
            output.setRespNewId(mailEntity.getId());
            output.setRespEstado(1);
            output.setRespMensaje("Success");
        } catch (Exception e) {
            logger.error("Error in registerSendMail", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
