package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterInput;
import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterOutput;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.exception.BookingBlockCancellationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
public class BookingBlockCancellationRegisterService {

    private static final String CNC_BLQ_BK = "CNC_BLQ_BK";
    private static final String GENERAL = "GENERAL";

    private final BookingRepository bookingRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final BookingBlockCancellationRepository bookingBlockCancellationRepository;
    private final BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public BookingBlockCancellationRegisterOutput registerCancelBookingBlock(BookingBlockCancellationRegisterInput.Input input){
        BookingBlockCancellationRegisterOutput output = new BookingBlockCancellationRegisterOutput();

        Integer businessUnitId = input.getBusinessUnitId();
        Integer subBusinessUnitId = input.getSubBusinessUnitId();
        Integer catType = input.getCatType();
        Integer catReason = input.getCatReason();
        String comment = input.getComment();
        Integer userRegistrationId = input.getUserRegistrationId();
        Integer cargoDocumentId = input.getDocumentCargoId();
        Integer languageId = input.getLanguageId();

        try {
            log.info("Registering cancel/block booking for cargoDocumentId: {}", cargoDocumentId);

            Booking booking = getBooking(cargoDocumentId, languageId, output);
            if (booking == null) return output;

            if (isDuplicateCancellation(cargoDocumentId, subBusinessUnitId, catType)) {
                setDuplicateCancellationMessage(languageId, output);
                return output;
            }

            BookingBlockCancellation savedBlockEntity = saveBlockCancellation(businessUnitId, subBusinessUnitId, catType, catReason, comment, userRegistrationId);
            saveBlockCancellationDetail(cargoDocumentId, savedBlockEntity);

            updateBookingStatus(booking, userRegistrationId);
            updateCargoDocumentStatus(cargoDocumentId, userRegistrationId);

            setSuccessMessage(catType, languageId, booking.getBookingNumber(), output);

        } catch (Exception e) {
            log.error("Error in registerCancelBookingBlock: ", e);
            output.setRespMensaje(e.getMessage());
            output.setRespEstado(0);
            output.setRespNewId(0);
        }
        return output;
    }

    private Booking getBooking(Integer cargoDocumentId, Integer languageId, BookingBlockCancellationRegisterOutput output) {
        List<Integer> bookingId = cargoDocumentRepository.findBookingIdByCargoDocumentId(cargoDocumentId);
        if (bookingId == null) {
            String msgNotFound = messageLanguageRepository.fnTranslatedMessage(CNC_BLQ_BK, 1, languageId);
            if (msgNotFound == null) {
                msgNotFound = "Booking does not exist or is inactive.";
            }
            output.setRespMensaje(msgNotFound);
            output.setRespEstado(0);
            output.setRespNewId(0);
            return null;
        }

        Booking booking = bookingRepository.findActiveBookingById(bookingId.getFirst()).orElse(null);
        if (booking == null) {
            String msgNotFound = messageLanguageRepository.fnTranslatedMessage(CNC_BLQ_BK, 1, languageId);
            if (msgNotFound == null) {
                msgNotFound = "Booking does not exist or is inactive.";
            }
            output.setRespMensaje(msgNotFound);
            output.setRespEstado(0);
            output.setRespNewId(0);
        }
        return booking;
    }

    private boolean isDuplicateCancellation(Integer cargoDocumentId, Integer subBusinessUnitId, Integer catType) {
        return bookingBlockCancellationRepository.findDuplicateCount(cargoDocumentId, subBusinessUnitId, catType) > 0;
    }

    private void setDuplicateCancellationMessage(Integer languageId, BookingBlockCancellationRegisterOutput output) {
        String message = messageLanguageRepository.fnTranslatedMessage(CNC_BLQ_BK, 1, languageId);
        if (message == null) {
            message = "Booking already has a cancellation/block for this deposit.";
        }
        output.setRespMensaje(message);
        output.setRespEstado(0);
        output.setRespNewId(0);
    }

    private BookingBlockCancellation saveBlockCancellation(Integer businessUnitId, Integer subBusinessUnitId, Integer catType, Integer catReason, String comment, Integer userRegistrationId) {
        BookingBlockCancellation blockEntity = new BookingBlockCancellation();
        blockEntity.setBusinessUnit(new BusinessUnit());
        blockEntity.getBusinessUnit().setId(businessUnitId);
        blockEntity.setSubBusinessUnit(new BusinessUnit());
        blockEntity.getSubBusinessUnit().setId(subBusinessUnitId);
        blockEntity.setCatType(new Catalog(catType));
        blockEntity.setBlockCancellationDate(LocalDateTime.now());
        blockEntity.setCatReason(new Catalog(catReason));
        blockEntity.setComment(comment);
        blockEntity.setRegistrationUser(new User(userRegistrationId));
        blockEntity.setRegistrationDate(LocalDateTime.now());
        blockEntity.setCatOriginCancelBlock(new Catalog(48174));
        blockEntity.setActive(true);
        return bookingBlockCancellationRepository.save(blockEntity);
    }

    private void saveBlockCancellationDetail(Integer cargoDocumentId, BookingBlockCancellation savedBlockEntity) {
        BookingBlockCancellationDetail detail = new BookingBlockCancellationDetail();
        CargoDocument cargoDocument = cargoDocumentRepository.findActiveCargoDocById(cargoDocumentId)
                .orElseThrow(() -> new BookingBlockCancellationException("Active CargoDocument not found."));
        detail.setBookingBlockCancellation(savedBlockEntity);
        detail.setCargoDocument(cargoDocument);
        detail.setReleasedBooking(false);
        detail.setActive(true);
        bookingBlockCancellationDetailRepository.save(detail);
    }

    private void updateBookingStatus(Booking booking, Integer userRegistrationId) {
        if (booking.getCatBookingStatus() != null && booking.getCatBookingStatus().getId() == 43061) {
            booking.setCatBookingStatus(new Catalog(43062));
            booking.setTraceBooking("cancelado manual");
            booking.setModificationDate(LocalDateTime.now());
            booking.setModificationUser(new User(userRegistrationId));
            bookingRepository.save(booking);
        }
    }

    private void updateCargoDocumentStatus(Integer cargoDocumentId, Integer userRegistrationId) {
        CargoDocument cargoDocument = cargoDocumentRepository.findActiveCargoDocById(cargoDocumentId)
                .orElseThrow(() -> new BookingBlockCancellationException("Active CargoDocument not found."));
        if (cargoDocument.getCatDocumentCargoStatus() != null && cargoDocument.getCatDocumentCargoStatus().getId() == 43061) {
            cargoDocument.setCatDocumentCargoStatus(new Catalog(43062));
            cargoDocument.setTraceCargoDocument("cancelado manual");
            cargoDocument.setModificationDate(LocalDateTime.now());
            cargoDocument.setModificationUser(new User(userRegistrationId));
            cargoDocumentRepository.save(cargoDocument);
        }
    }

    private void setSuccessMessage(Integer catType, Integer languageId, String bookingNumber, BookingBlockCancellationRegisterOutput output) {
        String translated;
        if (catType == 48167) {
            translated = messageLanguageRepository.fnTranslatedMessage(CNC_BLQ_BK, 3, languageId);
            if (translated == null) {
                translated = "El Booking {BK} ha sido cancelado.";
            }
            translated = translated.replace("{BK}", bookingNumber == null ? "" : bookingNumber);
        } else {
            translated = messageLanguageRepository.fnTranslatedMessage(GENERAL, 9, languageId);
            if (translated == null) {
                translated = "Registro realizado de forma satisfactoria.";
            }
        }
        output.setRespMensaje(translated);
        output.setRespEstado(1);
        output.setRespNewId(output.getRespNewId());
    }
}