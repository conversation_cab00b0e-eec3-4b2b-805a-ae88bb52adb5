package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.sde.controller.dto.BookingDetailsListOutput;
import com.maersk.sd1.sde.dto.BookingDetailsProcessDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class BookingDetailsListService {

    private static final Logger logger = LogManager.getLogger(BookingDetailsListService.class);

    private final BookingDetailRepository bookingDetailRepository;

    public BookingDetailsListService(BookingDetailRepository bookingDetailRepository) {
        this.bookingDetailRepository = bookingDetailRepository;
    }

    @Transactional(readOnly = true)
    public BookingDetailsListOutput getBookingDetailsList(Integer bookingId) {
        BookingDetailsListOutput output = new BookingDetailsListOutput();
        try {
            if (bookingId == null) {
                throw new IllegalArgumentException("booking_id cannot be null");
            }
            List<BookingDetailsProcessDTO> details = bookingDetailRepository.listBookingDetalleByBookingId(bookingId);

            if (details != null ){
                output = generateOutput(details);
            }

        } catch (Exception e) {
            logger.error("Error in getBookingDetailsList", e);
            output.setRespStatus(0);
            output.setRespMessage("Error processing request: " + e.getMessage());
        }
        return output;
    }

    private BookingDetailsListOutput generateOutput(List<BookingDetailsProcessDTO> details) {
        BookingDetailsListOutput output = new BookingDetailsListOutput();
        List<BookingDetailsListOutput.BookingDetailsListOutputDetail> outputDetails = new ArrayList<>();


        for (BookingDetailsProcessDTO detail : details) {
            BookingDetailsListOutput.BookingDetailsListOutputDetail outputDetail = new BookingDetailsListOutput.BookingDetailsListOutputDetail();
            outputDetail.setContainerNumber(detail.getDescription());
            outputDetail.setContainerTypeDescription(detail.getFlag());
            outputDetail.setRequested(detail.getResQuantity());
            outputDetail.setAssigned(detail.getAttendQuantity());
            outputDetail.setMaxLoadRequired(detail.getMaxRequired());
            outputDetail.setBookingDetailId(detail.getId());

            outputDetails.add(outputDetail);
        }
        output.setListContainers(outputDetails);
        output.setRespStatus(1);
        output.setRespMessage("Booking Details List executed successfully");
        return output;
    }
}