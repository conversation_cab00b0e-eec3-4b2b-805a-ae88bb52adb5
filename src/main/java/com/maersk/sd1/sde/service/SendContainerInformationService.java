package com.maersk.sd1.sde.service;


import com.maersk.sd1.common.repository.GateTransmissionSentRepository;
import com.maersk.sd1.sde.controller.dto.SendContainerInformationOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SendContainerInformationService {

    private static final Logger logger = LogManager.getLogger(SendContainerInformationService.class);

    private final GateTransmissionSentRepository gateTransmissionSentRepository;

    @Autowired
    public SendContainerInformationService(GateTransmissionSentRepository gateTransmissionSentRepository)
    {
        this.gateTransmissionSentRepository = gateTransmissionSentRepository;
    }

    // This constant corresponds to the "GENERADO" state in the stored procedure (cat_estado_envio_codeco_id = 48158)
    private static final Integer ESTADO_GENERADO = 48158;

    /**
     * Retrieves the pending Codeco transmissions that match the stored procedure's criteria.
     * catGateTransmissionStatus.id = 48158 (Generado) and active = 1.
     *
     * @return List of ServicioCodecoEnviarOutput DTOs
     */
    public List<SendContainerInformationOutput> getPendingToSend() {
        logger.info("Fetching pending Codeco transmissions with estado = {}.", ESTADO_GENERADO);
        List<SendContainerInformationOutput> pendingList = gateTransmissionSentRepository.findPendingToSend(ESTADO_GENERADO);
        logger.info("Found {} record(s) to send.", pendingList.size());
        return pendingList;
    }
}
