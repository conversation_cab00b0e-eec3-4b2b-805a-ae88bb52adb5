package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EirRepository;
import com.maersk.sd1.common.repository.CedexMercRepository;
import com.maersk.sd1.sde.dto.InspectionShowCleaningSectionOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class InspectionShowCleaningSectionService {

    private static final Logger logger = LogManager.getLogger(InspectionShowCleaningSectionService.class);

    private final EirRepository eirRepository;
    private final CatalogRepository catalogRepository;
    private final CedexMercRepository cedexMercRepository;

    @Autowired
    public InspectionShowCleaningSectionService(EirRepository eirRepository,
                                                CatalogRepository catalogRepository,
                                                CedexMercRepository cedexMercRepository) {
        this.eirRepository = eirRepository;
        this.catalogRepository = catalogRepository;
        this.cedexMercRepository = cedexMercRepository;
    }

    @Transactional(readOnly = true)
    public InspectionShowCleaningSectionOutput getShowCleaningSection(Integer catCleaningTypeId, Integer eirId) {
        try {
            // 1) Find Eir
            Eir eir = eirRepository.findByIdAndActiveTrue(eirId);
            if (eir == null) {
                throw new IllegalArgumentException("Eir not found or inactive for eir_id: " + eirId);
            }

            // 2) Check container is active
            if (eir.getContainer() == null || Boolean.FALSE.equals(eir.getContainer().getActive())) {
                throw new IllegalArgumentException("Container is not active or not found for eir_id: " + eirId);
            }

            // 3) is_equipment_type_container => catalog with alias 'sd1_equipment_category_container'
            Catalog equipmentTypeContainerCat = catalogRepository.findByAlias("sd1_equipment_category_container");
            if (equipmentTypeContainerCat == null) {
                throw new IllegalArgumentException("Catalog not found for alias sd1_equipment_category_container");
            }

            // 4) cat_repair_group_code => alias 'sd1_repair_code_cleaning_type'
            Catalog repairGroupCodeCat = catalogRepository.findByAlias("sd1_repair_code_cleaning_type");
            if (repairGroupCodeCat == null) {
                throw new IllegalArgumentException("Catalog not found for alias sd1_repair_code_cleaning_type");
            }

            // 5) Gather needed fields from eir
            Integer shippingLineId = eir.getShippingLine().getId();
            if (shippingLineId != null && shippingLineId.equals(4102)) {
                shippingLineId = 4104; // logic from stored procedure
            }

            // tipcon.codigo => catContainerType.code
            // if that code is '0', we do DRY => 'FPP', else => 'MCO'
            String isReeferCode = (eir.getContainer().getCatContainerType().getCode() == null)
                    ? "" : eir.getContainer().getCatContainerType().getCode().trim();

            String codigoComponente = ("0".equals(isReeferCode)) ? "FPP" : "MCO";

            // container_size => eir.getContainer().getCatSize().getDescription()
            String containerSize = eir.getContainer().getCatSize().getDescription();
            if (containerSize == null) {
                containerSize = "";
            }

            // sub_unit_bussines_id => eir.getSubBusinessUnit().getId()
            Integer subUnitBusinessId = eir.getSubBusinessUnit().getId();

            // do the query in cedex_merc
            return cedexMercRepository.findTopByFilter(
                    codigoComponente,
                    repairGroupCodeCat.getId(),
                    containerSize.trim(),
                    catCleaningTypeId,
                    subUnitBusinessId,
                    shippingLineId,
                    equipmentTypeContainerCat.getId(),
                    PageRequest.of(0, 1)
            ).stream().findFirst().map(cm -> {
                InspectionShowCleaningSectionOutput output = new InspectionShowCleaningSectionOutput();
                output.setCedexMercId(cm.getId());
                output.setFlagShowCleaningSectionInterior(cm.getFlagShowCleaningSectionInterior());
                output.setFlagShowCleaningSectionBottom(cm.getFlagShowCleaningSectionBottom());
                output.setFlagShowCleaningSectionTop(cm.getFlagShowCleaningSectionTop());
                output.setFlagShowCleaningSectionRight(cm.getFlagShowCleaningSectionRight());
                output.setFlagShowCleaningSectionLeft(cm.getFlagShowCleaningSectionLeft());
                return output;
            }).orElseGet(() -> {
                // If no record found, return empty flags or null
                InspectionShowCleaningSectionOutput output = new InspectionShowCleaningSectionOutput();
                output.setCedexMercId(null);
                output.setFlagShowCleaningSectionInterior(false);
                output.setFlagShowCleaningSectionBottom(false);
                output.setFlagShowCleaningSectionTop(false);
                output.setFlagShowCleaningSectionRight(false);
                output.setFlagShowCleaningSectionLeft(false);
                return output;
            });
        } catch (Exception e) {
            logger.error("Error in getShowCleaningSection", e);
            throw e;
        }
    }
}