package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.BookingBlockCancellationDetailRepository;
import com.maersk.sd1.sde.dto.CancellationBlockingBookingReleaseObtainOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;
// cancelacion_bloqueo_booking_liberacion_obtener
@Service
@Log4j2
@RequiredArgsConstructor
public class CancellationBlockingBookingReleaseObtainService {

    private final BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;

    public List<CancellationBlockingBookingReleaseObtainOutput> obtainCancellationBlockingBookingRelease(Integer cancelBloqueoBookingId) {
        try{
            return bookingBlockCancellationDetailRepository.obtainCancellationBlockingBookingRelease(cancelBloqueoBookingId);
        } catch (Exception e) {
            log.error("Error while obtaining cancellation blocking booking release", e);
            throw e;
        }
    }
}
