package com.maersk.sd1.sde.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * Output DTO for Gate Out Empty Export Report.
 * Holds a list of summarized rows plus the total record count.
 * Incorporates additional fields for revised logic.
 */
@Data
public class GateOutEmptyExportReportOutput {

    @JsonProperty("total_records")
    private Long totalRecords;

    @JsonProperty("rows")
    private List<GateOutEmptyExportReportRow> rows;

}
