package com.maersk.sd1.sde.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ConfigureEDISystemToGetSendingParameterOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("azure_storage_config_id")
    private Integer azureStorageConfigId;

    @JsonProperty("azure_id")
    private String azureId;

    @JsonProperty("azure_container")
    private String azureContainer;

    @JsonProperty("azure_path")
    private String azurePath;

    @JsonProperty("azure_storage_key")
    private String azureStorageKey;

    @JsonProperty("azure_storage_name")
    private String azureStorageName;

    @JsonProperty("email_plantilla")
    private String emailPlantilla;
}