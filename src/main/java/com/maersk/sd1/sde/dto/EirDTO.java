package com.maersk.sd1.sde.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EirDTO {

    private Integer businessUnitId;
    private Integer eirId;
    private Integer shippingLineId;
    private Integer containerId;
    private String container;
    private Integer containerTypeId;
    private Integer isBoxRepairApproved;
    private Integer isMachineRepairApproved;
    private Integer activityZoneId;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private Integer eirActivityZoneId;
    private LocalDateTime activityZoneRegisterDate;
    private Boolean isStructureDamaged;
    private Boolean isMachineryDamagedResult;
    private Boolean hasSensor;
    private Boolean isSensorDamaged;
    private Integer localBusinessUnitId;
    private Integer containerSizeId;
    private Integer isoCodeId;
    private Integer reeferTypeId;
    private Integer engineBrandId;
    private Boolean isMachineryDamagedFlag;
    private LocalDateTime truckEntryDate;
    private Integer maxContainerLoad;
    private Integer containerTareWeight;
    private Integer containerClassId;
    private LocalDateTime manufacturingDate;
    private Integer movementTypeId;
    private Integer originTypeId;
    private Boolean isPartialInspection;
    private Boolean withSensor;
    private Boolean isCompleted;
    private LocalDateTime finishDate;
}

