package com.maersk.sd1.sde.dto;

import java.time.LocalDateTime;

public interface EirTempProjection {
    Integer getEirId();
    String getNumeroContenedor();
    String getProcedenciaDestino();
    LocalDateTime getFechaEir();
    String getObservacion();
    String getTipo();
    String getCliente();
    String getTipoEirDescripcion();
    Long getUnidadNegocioId();
    String getTypeCnt();
    LocalDateTime getFechaSalida();
    String getEmpresaTransporte();
    Integer getLineaNavieraId();
    Integer getPersonaConductorId();
    Integer getPersonaInspectorId();
    LocalDateTime getFechaFirmaConductor();
    String getUrlFirmaConductor();
    String getUrlFirmaInspector();
    Long getEirChassisId();
    Integer getPersonChassisInspectorId();
    String getUrlSignatureInspectorChassis();
}

