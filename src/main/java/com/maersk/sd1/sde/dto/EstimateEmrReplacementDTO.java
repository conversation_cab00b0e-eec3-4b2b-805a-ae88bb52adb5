package com.maersk.sd1.sde.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EstimateEmrReplacementDTO {

    private Integer replacementId;
    private Integer estimateEmrDetailId;
    private Integer replacementPieces;
    private String replacementPartNumber;
    private String previousSerial;
    private String newSerial;
    private Integer replacementCostPerPiece;
}
