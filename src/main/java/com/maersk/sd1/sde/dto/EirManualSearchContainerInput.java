package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@Data
@UtilityClass
public class EirManualSearchContainerInput {

    @Data
    public static class Input {
        @JsonProperty("business_unit_id")
        @NotNull
        private Long businessUnitId;

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Long subBusinessUnitId;

        @JsonProperty("sub_business_unit_local_id")
        @NotNull
        private Long subBusinessUnitLocalId;

        @JsonProperty("container_number")
        @NotNull
        @Size(max = 11)
        private String containerNumber;

        @JsonProperty("eir_type_id")
        @NotNull
        private Long eirTypeId;

        @JsonProperty("move_type_id")
        private Long moveTypeId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
