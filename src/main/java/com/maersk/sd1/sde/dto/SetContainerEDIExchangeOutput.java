package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class SetContainerEDIExchangeOutput {

    // --------------------- MAIN RECORD FIELDS ---------------------

    @JsonProperty("Response")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<ResponseData> response;

    // --------------------- LINKED LISTS ---------------------
    @J<PERSON><PERSON>roperty("subUnidadesLocal")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<List<String>> subUnidadesLocal;

    @JsonProperty("procedenciaGateIn")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<List<String>> procedenciaGateIn;

    @JsonProperty("procedenciaGateOut")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<List<String>> procedenciaGateOut;

    // --------------------- RESPONSE STATUS ---------------------
//    @JsonProperty("resp_estado")
//    private Integer respEstado;
//
//    @JsonProperty("resp_mensaje")
//    private String respMensaje;

    @Data
    public static class ResponseData{
        @JsonProperty("seteo_edi_codeco_id")
        private Integer seteoEdiCodecoId;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("sistema_entrega")
        private String sistemaEntrega;

        @JsonProperty("info_sistema_entrega")
        private String infoSistemaEntrega;

        @JsonProperty("identificador_receptor")
        private String identificadorReceptor;

        @JsonProperty("enviar_gate_in_empty")
        private Boolean enviarGateInEmpty;

        @JsonProperty("enviar_gate_out_empty")
        private Boolean enviarGateOutEmpty;

        @JsonProperty("enviar_gate_in_full")
        private Boolean enviarGateInFull;

        @JsonProperty("enviar_gate_out_full")
        private Boolean enviarGateOutFull;

        @JsonProperty("enviar_status_activity")
        private Boolean enviarStatusActivity;

        @JsonProperty("cat_formato_gate_out_empty")
        private Integer catFormatoGateOutEmpty;

        @JsonProperty("cat_formato_gate_in_full")
        private Integer catFormatoGateInFull;

        @JsonProperty("cat_formato_gate_out_full")
        private Integer catFormatoGateOutFull;

        @JsonProperty("cat_formato_gate_in_empty")
        private Integer catFormatoGateInEmpty;

        @JsonProperty("cat_formato_status_activity")
        private Integer catFormatoStatusActivity;

        @JsonProperty("cat_canal_envio_id")
        private Integer catCanalEnvioId;

        @JsonProperty("cat_modo_generar_archivo_id")
        private Integer catModoGenerarArchivoId;

        @JsonProperty("correo_codeco_destino")
        private String correoCodecoDestino;

        @JsonProperty("correo_telex_destino")
        private String correoTelexDestino;

        @JsonProperty("parametro_1")
        private String parametro1;

        @JsonProperty("parametro_2")
        private String parametro2;

        @JsonProperty("parametro_3")
        private String parametro3;

        @JsonProperty("parametro_4")
        private String parametro4;

        @JsonProperty("es_historico")
        private Boolean esHistorico;

        @JsonProperty("fecha_debaja")
        private String fechaDebaja;

        @JsonProperty("motivo_debaja")
        private String motivoDebaja;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro")
        private String fechaRegistro;

        @JsonProperty("fecha_modificacion")
        private String fechaModificacion;

        @JsonProperty("parametro_5")
        private String parametro5;

        @JsonProperty("parametro_6")
        private String parametro6;

        @JsonProperty("azure_id_codeco")
        private String azureIdCodeco;

        @JsonProperty("azure_id_telex")
        private String azureIdTelex;

        @JsonProperty("sftp_id")
        private String sftpId;

        @JsonProperty("extension_archivo_enviar")
        private String extensionArchivoEnviar;

        @JsonProperty("minutos_trancurridos")
        private Integer minutosTrancurridos;
    }

    // --------------------- INNER CLASSES (DTO SHAPES) ---------------------
    @Data
    public static class SubUnidadesLocalDTO {
        @JsonProperty("seleccionado")
        private Integer seleccionado;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("seteo_edi_codeco_local_id")
        private Integer seteoEdiCodecoLocalId;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("identificador_emisor")
        private String identificadorEmisor;

        @JsonProperty("locacion_actividad")
        private String locacionActividad;

        // Additional fields if needed from the entity, but above covers main usage
    }

    @Data
    public static class ActivityEmptyDTO {
        @JsonProperty("seleccionado")
        private Integer seleccionado;

        @JsonProperty("catalogo_id")
        private Integer catalogoId;

        @JsonProperty("seteo_edi_codeco_actividad_id")
        private Integer seteoEdiCodecoActividadId;

        @JsonProperty("activo")
        private Boolean activo;

        // Additional fields if needed from the entity
    }
}
