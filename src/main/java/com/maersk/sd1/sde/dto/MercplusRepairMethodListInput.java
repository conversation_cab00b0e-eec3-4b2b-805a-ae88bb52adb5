package com.maersk.sd1.sde.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class MercplusRepairMethodListInput {

    @Data
    public static class Input {
        // Currently, no fields are required for this procedure.
        // Null field validation can be applied here if fields get added in the future.
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
