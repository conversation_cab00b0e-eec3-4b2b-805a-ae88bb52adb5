package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.util.List;

@UtilityClass
public class GateTransmissionSettingEditInput {

    @Data
    public static class SubUnit {
        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("identificador_emisor")
        @NotNull
        @Size(max = 10)
        private String identifierEmitter;

        @JsonProperty("locacion_actividad")
        @Size(max = 10)
        private String locacionActivity;
    }

    @Data
    public static class Procedencia {
        @JsonProperty("catalogo_id")
        @NotNull
        private Integer catalogId;
    }

    @Data
    public static class Input {
        @JsonProperty("seteo_edi_codeco_id")
        @NotNull
        private Integer gateTransmissionSettingId;

        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("linea_naviera_id")
        @NotNull
        private Integer shippingLineId;

        @JsonProperty("sistema_entrega")
        @NotNull
        @Size(max = 50)
        private String systemDelivery;

        @JsonProperty("info_sistema_entrega")
        @Size(max = 200)
        private String infoSystemDelivery;

        @JsonProperty("identificador_receptor")
        @NotNull
        @Size(max = 10)
        private String receptorIdentifier;

        @JsonProperty("enviar_gate_in_empty")
        @NotNull
        private Boolean sendGateInEmpty;

        @JsonProperty("enviar_gate_out_empty")
        @NotNull
        private Boolean sendGateOutEmpty;

        @JsonProperty("enviar_gate_in_full")
        @NotNull
        private Boolean sendGateInFull;

        @JsonProperty("enviar_gate_out_full")
        @NotNull
        private Boolean sendGateOutFull;

        @JsonProperty("enviar_status_activity")
        @NotNull
        private Boolean sendStatusActivity;

        @JsonProperty("cat_formato_gate_out_empty")
        private Integer catFormatoGateOutEmptyId;

        @JsonProperty("cat_formato_gate_in_full")
        private Integer catFormatoGateInFullId;

        @JsonProperty("cat_formato_gate_out_full")
        private Integer catFormatoGateOutFullId;

        @JsonProperty("cat_formato_gate_in_empty")
        private Integer catFormatoGateInEmptyId;

        @JsonProperty("cat_formato_status_activity")
        private Integer catFormatoStatusActivityId;

        @JsonProperty("cat_canal_envio_id")
        @NotNull
        private Integer catCanalEnvioId;

        @JsonProperty("cat_modo_generar_archivo_id")
        @NotNull
        private Integer catModoGenerarArchivoId;

        @JsonProperty("correo_codeco_destino")
        @Size(max = 200)
        private String gateTransmissionDestinationMail;

        @JsonProperty("correo_telex_destino")
        @Size(max = 200)
        private String telexDestinationMail;

        @JsonProperty("parametro_1")
        @Size(max = 10)
        private String parameter1;

        @JsonProperty("parametro_2")
        @Size(max = 10)
        private String parameter2;

        @JsonProperty("parametro_3")
        @Size(max = 10)
        private String parameter3;

        @JsonProperty("parametro_4")
        @Size(max = 10)
        private String parameter4;

        @JsonProperty("es_historico")
        @NotNull
        private Boolean historical;

        @JsonProperty("fecha_debaja")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
        private LocalDate deactivationDate;

        @JsonProperty("motivo_debaja")
        @Size(max = 200)
        private String deactivationReason;

        @JsonProperty("activo")
        @NotNull
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer userModificationId;

        @JsonProperty("parametro_5")
        @Size(max = 10)
        private String parameter5;

        @JsonProperty("parametro_6")
        @Size(max = 10)
        private String parameter6;

        @JsonProperty("azure_id_codeco")
        @Size(max = 100)
        private String azureIdGateTransmission;

        @JsonProperty("azure_id_telex")
        @Size(max = 100)
        private String azureIdTelex;

        @JsonProperty("sftp_id")
        @Size(max = 100)
        private String sftpId;

        @JsonProperty("extension_archivo_enviar")
        @Size(max = 100)
        private String extensionFileSend;

        @JsonProperty("minutos_trancurridos")
        private Integer elapsedMinutes;

//        @JsonProperty("sub_unidades_json")
//        private List<SubUnit> subUnits;

        @JsonProperty("sub_unidades_json")
        private String subUnits;

        @JsonProperty("gate_in_empty_movimiento_id")
        private Integer gateInEmptyMovimientoId;

//        @JsonProperty("gate_in_empty_procedencia_json")
//        private List<Procedencia> gateInEmptyProcedencias;

        @JsonProperty("gate_in_empty_procedencia_json")
        private String gateInEmptyProcedencias;

        @JsonProperty("gate_out_empty_movimiento_id")
        private Integer gateOutEmptyMovimientoId;

//        @JsonProperty("gate_out_empty_procedencia_json")
//        private List<Procedencia> gateOutEmptyProcedencias;

        @JsonProperty("gate_out_empty_procedencia_json")
        private String gateOutEmptyProcedencias;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        @NotNull
        private Prefix prefix;
    }
}
