package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import lombok.Data;

public class ComponentSearchInput {

    @Data
    public static class Input {

        @JsonProperty("component")
        @Size(max = 100, message = "The component field can have a maximum of 100 characters.")
        private String component;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
