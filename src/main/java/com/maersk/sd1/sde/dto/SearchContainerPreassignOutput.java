package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class SearchContainerPreassignOutput {

    @JsonProperty("response")
    private ResponseStatus response;

    @JsonProperty("contenedor")
    private ContainerDetails contenedor;

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class ResponseStatus {
        @JsonProperty("estado_mensaje")
        private Integer estadoMensaje;

        @JsonProperty("mensaje")
        private String mensaje;
    }

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class ContainerDetails {
        @JsonProperty("contenedor_id")
        private Integer containerId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("clase")
        private String classType;

        @JsonProperty("carga_maxima_cnt")
        private Integer maxPayloadContainer;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("local")
        private String localBusinessUnit;
    }
}
