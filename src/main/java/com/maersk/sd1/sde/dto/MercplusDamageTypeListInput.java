package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Input DTO mirroring the prefix/root structure from the sample.
 * There are no required fields for this request but validation structure is maintained.
 */
public class MercplusDamageTypeListInput {

    @Data
    public static class Input {
        // Currently no fields needed, but here for future extension
        // and to follow the structure from the reference.
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "Input object cannot be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        @NotNull(message = "Prefix object cannot be null")
        private Prefix prefix;
    }
}
