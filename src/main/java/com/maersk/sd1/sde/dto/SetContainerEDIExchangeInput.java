package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class SetContainerEDIExchangeInput {

    @Data
    public static class Input {

        @JsonProperty("seteo_edi_codeco_id")
        @NotNull
        private Integer seteoEdiCodecoId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
