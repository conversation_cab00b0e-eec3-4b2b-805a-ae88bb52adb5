package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;

/**
 * Input DTO for Gate Out Empty Export Report.
 * Mirrors the parameters of the stored procedure particularly with
 * extra parameters controlling revision, inspection, assignment,
 * plus booking references.
 */
@UtilityClass
public class GateOutEmptyExportReportInput {

    @Data
    public static class Input {

        @JsonProperty("pf_usuario_id")
        @NotNull
        private Integer userId;

        @JsonProperty("pf_sub_unidad_negocio_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("pf_idioma_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("pd_ingreso_desde")
        @NotNull
        @PastOrPresent
        private LocalDate fromDate;

        @JsonProperty("pd_ingreso_hasta")
        @NotNull
        @PastOrPresent
        private LocalDate toDate;

        @JsonProperty("pagina")
        @NotNull
        private Integer page;

        @JsonProperty("cantidad")
        @NotNull
        private Integer size;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}

