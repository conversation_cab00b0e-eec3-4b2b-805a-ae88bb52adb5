package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ComponentSearchOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("components")
    private List<ComponentData> components;

    @Data
    public static class ComponentData {
        @JsonProperty("cat_component_id")
        private Integer catComponentId;

        @JsonProperty("component")
        private String component;

        @JsonProperty("component_code")
        private String componentCode;
    }
}
