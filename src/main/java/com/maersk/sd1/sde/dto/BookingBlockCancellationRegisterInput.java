package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
@Data
public class BookingBlockCancellationRegisterInput {
    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "unidad_negocio_id cannot be null")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull(message = "sub_unidad_negocio_id cannot be null")
        private Integer subBusinessUnitId;

        @JsonProperty("cat_tipo")
        @NotNull(message = "cat_tipo cannot be null")
        private Integer catType;

        @JsonProperty("cat_motivo")
        @NotNull(message = "cat_motivo cannot be null")
        private Integer catReason;

        @JsonProperty("comentario")
        @Size(max = 200, message = "comentario cannot exceed 200 characters")
        private String comment;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "usuario_registro_id cannot be null")
        private Integer userRegistrationId;

        @JsonProperty("documento_carga_id")
        @NotNull(message = "documento_carga_id cannot be null")
        private Integer documentCargoId;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}