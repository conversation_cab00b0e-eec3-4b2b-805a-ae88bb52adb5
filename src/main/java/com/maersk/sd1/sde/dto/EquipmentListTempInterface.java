package com.maersk.sd1.sde.dto;



import java.time.LocalDateTime;


public interface EquipmentListTempInterface {
    Long getBusinessUnitId();
    Long getSubBusinessUnitId();
    Integer getProgramacionNaveDetalleId();
    Integer getContainerId();
    Integer getChassisId();
    Long getCatEmptyFullId();
    String getTipoMov();
    String getLocal();
    Integer getEirId();
    LocalDateTime getFechaIngresoCamion();
    LocalDateTime getFechaSalidaCamion();
    String getEquipmentNumber();
    String getEquipmentSizeType();
    Long getCatEquipmentCategory();
    String getIsoCode();
    String getOwnerPropietario();
    String getPlateTruckNumber();
    Long getTransportCompanyId();
    String getTransportCompanyName();
    String getDriverName();
    Long getUserModificationId();
    String getUserModificationName();
    String getUserModificationLastName();
    LocalDateTime getFechaModificacion();
    Long getUserRegistrationId();
    String getUserRegistrationName();
    String getUserRegistrationLastName();
    LocalDateTime getFechaRegistro();
    String getSeals();
    Long getCatCargoDocumentTypeId();
    String getCargoDocumentNumber();
    Long getOperationTypeId();
    String getOperationTypeName();
    Integer getIsShow();
    Integer getEirChassisId();
    Long getCatStructureConditionId();
    Long getCatMachineryConditionId();
    String getOperationGroupType();
    Long getGradeId();
    String getContainerOperationType();
    String getVesselName();
    String getVoyageName();
    String getDepotOperationType();
    Boolean getEstructuaConDano();
    Boolean getMaquinariaConDano();
    String getParaVenta();
    String getCamionMultipleCarga();
    Long getUserDepartureId();
    String getUserDepartureName();
    String getUserDepartureLastName();
    String getOrigenCreacionEir();
    Long getCatMovimientoId();
    String getCatOperacionDescripcion();
    String getTipoGate();
    String getInspectionPhotos();
    Integer getInspectionsQuantity();
    String getGatePhotos();
}
