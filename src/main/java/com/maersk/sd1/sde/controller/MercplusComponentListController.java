package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.MercplusComponentListInput;
import com.maersk.sd1.sde.dto.MercplusComponentListOutput;
import com.maersk.sd1.sde.service.MercplusComponentListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("modulesde/ModuleSDE/module/sde/SDEMercPlusService")
public class MercplusComponentListController {

    private static final Logger logger = LogManager.getLogger(MercplusComponentListController.class);
    private final MercplusComponentListService mercplusComponentListService;

    @PostMapping("/sdemercplusComponentList")
    public ResponseEntity<ResponseController<List<MercplusComponentListOutput>>> mercplusComponentList(@RequestBody @Valid MercplusComponentListInput.Root request) {
        logger.info("Request received for mercplusComponentList: {}", request);
        try {
            List<MercplusComponentListOutput> components = mercplusComponentListService.fetchMercplusComponents();
            return ResponseEntity.ok(new ResponseController<>(components));
        } catch (Exception e) {
            logger.error("An error occurred while processing mercplusComponentList request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.getMessage()));
        }
    }
}
