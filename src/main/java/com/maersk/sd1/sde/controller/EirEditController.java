package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.EirEditInput;
import com.maersk.sd1.sde.controller.dto.EirEditOutput;
import com.maersk.sd1.sde.service.EirEditService;
import com.maersk.sd1.seg.dto.EditRoleOutput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEEIRServiceImp")
public class EirEditController {

    private EirEditService eirEditService;

    @Autowired
    public EirEditController(EirEditService eirEditService) {
        this.eirEditService = eirEditService;
    }

    @PostMapping("/sdeeirEdit")
    public ResponseEntity<ResponseController<EirEditOutput>> eirEdit(@RequestBody EirEditInput.Root request) {
        EirEditInput.Input input = request.getPrefix().getInput();
        EirEditOutput output = eirEditService.eirEdit(input);
        return ResponseEntity.ok(new ResponseController<>(output));
    }
}
