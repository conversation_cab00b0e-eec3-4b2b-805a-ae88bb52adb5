package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.PreallocateContainerInput;
import com.maersk.sd1.sde.controller.dto.PreallocateContainerOutput;
import com.maersk.sd1.sde.service.PreallocateContainerService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEPreasignacionServiceImp")
public class PreallocateController {

    private static final Logger logger = LogManager.getLogger(PreallocateController.class);

    private final PreallocateContainerService preallocateContainerService;

    public PreallocateController(PreallocateContainerService preallocateContainerService) {
        this.preallocateContainerService = preallocateContainerService;
    }
    @PostMapping("/sdepreAsignarContenedor")
    public ResponseEntity<ResponseController<PreallocateContainerOutput>> search(@RequestBody @Valid PreallocateContainerInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null ) {
                PreallocateContainerOutput output = new PreallocateContainerOutput();
                output.setRespStatus(0);
                output.setRespMessage("repair_method cannot be null");
                return ResponseEntity.status(400).body(new ResponseController<>(output));
            }


            PreallocateContainerOutput output = preallocateContainerService.preallocateContainer(request.getPrefix().getInput());
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while searching repair methods.", e);
            PreallocateContainerOutput output = new PreallocateContainerOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}