package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class PreallocateContainerInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("lista_contenedores")
        private String listaContenedores;

        @JsonProperty("booking_detalle_id")
        private Integer bookingDetalleId;

        @JsonProperty("usuario_registro_id")
        private Integer usuarioRegistroId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private PreallocateContainerInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private PreallocateContainerInput.Prefix prefix;
    }
}
