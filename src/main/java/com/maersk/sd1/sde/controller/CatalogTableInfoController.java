package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.CatalogTableRequestInput;
import com.maersk.sd1.sde.dto.CatalogTableResponseOutput;
import com.maersk.sd1.sde.service.CatalogTableInfoService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEPrincipalServiceImp")
public class CatalogTableInfoController {

    private static final Logger logger = LogManager.getLogger(CatalogTableInfoController.class);

    private final CatalogTableInfoService catalogTableInfoService;

    public CatalogTableInfoController(CatalogTableInfoService catalogTableInfoService){
        this.catalogTableInfoService = catalogTableInfoService;
    }

    @PostMapping("/sdeobtenerCatalogoPorTablas")
    public ResponseEntity<ResponseController<CatalogTableResponseOutput>> getCatalogTables(@RequestBody @Valid CatalogTableRequestInput.Root request) {
        try {
            CatalogTableRequestInput.Input input = request.getPrefix().getInput();
            CatalogTableResponseOutput responseOutput = catalogTableInfoService.getCatalogTables(input);
            return ResponseEntity.ok(new ResponseController<>(responseOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CatalogTableResponseOutput errorOutput = new CatalogTableResponseOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}