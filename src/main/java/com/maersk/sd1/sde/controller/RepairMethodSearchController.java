package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.RepairMethodSearchInput;
import com.maersk.sd1.sde.controller.dto.RepairMethodSearchOutput;
import com.maersk.sd1.sde.service.RepairMethodSearchService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEMercPlusServiceImp")
public class RepairMethodSearchController {

    private static final Logger logger = LogManager.getLogger(RepairMethodSearchController.class);

    private final RepairMethodSearchService repairMethodSearchService;

    public RepairMethodSearchController(RepairMethodSearchService repairMethodSearchService) {
        this.repairMethodSearchService = repairMethodSearchService;
    }
    @PostMapping("/sdemercplusRepairMethodSearch")
    public ResponseEntity<ResponseController<RepairMethodSearchOutput>> search(@RequestBody @Valid RepairMethodSearchInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null || request.getPrefix().getInput().getRepairMethod() == null) {
                RepairMethodSearchOutput output = new RepairMethodSearchOutput();
                output.setRespEstado(0);
                output.setRespMensaje("repair_method cannot be null");
                return ResponseEntity.status(400).body(new ResponseController<>(output));
            }

            String repairMethodParam = request.getPrefix().getInput().getRepairMethod();
            RepairMethodSearchOutput output = repairMethodSearchService.searchRepairMethods(repairMethodParam);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while searching repair methods.", e);
            RepairMethodSearchOutput output = new RepairMethodSearchOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}