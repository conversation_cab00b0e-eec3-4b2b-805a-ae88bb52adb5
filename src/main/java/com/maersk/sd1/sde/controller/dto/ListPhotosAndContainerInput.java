package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ListPhotosAndContainerInput {

    @Data
    public static class Input {
        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("contenedor_eir")
        @NotNull
        private String containerId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}