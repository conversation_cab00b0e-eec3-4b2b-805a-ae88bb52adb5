package com.maersk.sd1.sde.controller.dto;

import lombok.Data;

import java.util.List;

@Data
public class InspectionBoxGetOutputDTO {

    private String successFailure;
    private String failureMessage;
    private List<InspectionBoxDTO> inspectionBoxDTO;
    private List<NextZoneDTO> nextZoneWithoutDamageDTO;
    private List<NextZoneDTO> nextZoneWithDamageDTO;
    private List<EstimatedEmrDetailDTO> estimatedEmrDetailDTO;
    private List<EstimatedEmrPhotoHeaderDTO> estimatedEmrPhotoHeaderDTO;
    private List<EstimatedEmrDetailPhotoDTO> estimatedEmrDetailPhotoDTO;
    private List<ObservationDTO> observationStructureDTOList;
    private List<ObservationDTO> observationMachineryDTOList;

}
