package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.RegisterActivityZoneCntInput;
import com.maersk.sd1.sde.dto.RegisterActivityZoneCntOutput;
import com.maersk.sd1.sde.service.RegisterActivityZoneCntService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEActividadZonaServiceImp")
public class RegisterActivityZoneCntController {

    private static final Logger logger = LogManager.getLogger(RegisterActivityZoneCntController.class);

    private final RegisterActivityZoneCntService registrarActividadZonaCntService;

    @Autowired
    public RegisterActivityZoneCntController(RegisterActivityZoneCntService registrarActividadZonaCntService) {
        this.registrarActividadZonaCntService = registrarActividadZonaCntService;
    }

    @PostMapping("/sderegistrarActividadZonaCnt")
    public ResponseEntity<ResponseController<RegisterActivityZoneCntOutput>> sdgRegistrarActividadZonaCnt(
            @RequestBody @Valid RegisterActivityZoneCntInput.Root request) {
        try {
            RegisterActivityZoneCntInput.Input input = request.getPrefix().getInput();
            RegisterActivityZoneCntOutput output = registrarActividadZonaCntService.registrarActividadZonaCnt(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing registrarActividadZonaCnt.", e);
            RegisterActivityZoneCntOutput output = new RegisterActivityZoneCntOutput();
            output.setRespMensaje(e.getMessage());
            output.setRespEstado(0);
            output.setRespNewId(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}