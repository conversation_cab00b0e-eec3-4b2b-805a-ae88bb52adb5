package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class EstimateEmrCostManHoursListOutput {

    @JsonProperty("total_registros")
    private List<List<Long>> totalRegistros;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("data")
    private List<EstimateEmrCostManHoursItem> data;

    @Data
    public static class EstimateEmrCostManHoursItem {

        @JsonProperty("estimado_emr_costo_hh_id")
        private Integer estimateEmrCostHHId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("cat_tipo_estimado_id")
        private Integer catTipoEstimadoId;

        @JsonProperty("moneda_id")
        private Integer monedaId;

        @JsonProperty("costo_hora_hombre")
        private Double costoHoraHombre;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro")
        private String fechaRegistro = null;

        @JsonProperty("fecha_modificacion")
        private String fechaModificacion = null;

        @JsonProperty("usuario_registro_id")
        private Integer userRegistrationId;

        @JsonProperty("usuario_registro_nombres")
        private String userRegistrationNames;

        @JsonProperty("usuario_registro_apellidos")
        private String usuarioRegistroApellidos;

        @JsonProperty("usuario_modificacion_id")
        private Integer userModificationId;

        @JsonProperty("usuario_modificacion_nombres")
        private String userModificationNames;

        @JsonProperty("usuario_modificacion_apellidos")
        private String usuarioModificacionApellidos;

        @JsonProperty("cat_equipment_category")
        private Integer catEquipmentCategory;

        @JsonProperty("cat_equipment_category_desc")
        private String catEquipmentCategoryDesc;

        @JsonProperty("chassis_owner")
        private String chassisOwner;

        @JsonProperty("business_unit")
        private String businessUnit;

        @JsonProperty("sub_business_unit")
        private String subBusinessUnit;

        @JsonProperty("shipping_line")
        private String shippingLine;

        @JsonProperty("estimate_type")
        private String estimateType;

        @JsonProperty("money_name")
        private String moneyName;
    }
}
