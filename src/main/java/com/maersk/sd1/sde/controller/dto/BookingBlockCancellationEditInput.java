package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class BookingBlockCancellationEditInput {

    @Data
    public static class Input {

        @JsonProperty("cancel_bloqueo_booking_id")
        @NotNull
        private Integer cancelBloqueoBookingId;

        @JsonProperty("comentario")
        @Size(max = 200)
        private String comments;

        @JsonProperty("usuario_modifiacion_id")
        @NotNull
        private Integer userModificationId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }

    private BookingBlockCancellationEditInput() {
        // Private constructor to hide the implicit public one
    }
}