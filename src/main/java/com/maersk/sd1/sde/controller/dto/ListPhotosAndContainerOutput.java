package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ListPhotosAndContainerOutput {

    @JsonProperty("resp_estado")
    private Integer responseStatus;

    @JsonProperty("resp_mensaje")
    private String responseMessage;

    @JsonProperty("data_result")
    private List<EirContainerResponse> dataResult= new ArrayList<>();

    @JsonProperty("estimate_photos")
    private List<EstimateEmrPhotoDto> estimatePhotos=new ArrayList<>();

    @JsonProperty("estimate_details")
    private List<EstimateEmrDetailDto> estimateDetails=new ArrayList<>();

    @JsonProperty("estimate_detail_photos")
    private List<EstimateEmrDetailPhotoDto> estimateDetailPhotos=new ArrayList<>();

    @JsonProperty("inspection_records")
    private List<InspectionGateInfoDto> inspectionGateRecords=new ArrayList<>();

    @JsonProperty("inspection_photos")
    private List<InspectionGatePhotoDto> inspectionGatePhotos=new ArrayList<>();

    @Data
    public static class EirContainerResponse {
        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("procedencia_destino")
        private String originDestination;

        @JsonProperty("fecha_eir")
        private String eirDate;

        @JsonProperty("observacion")
        private String observation;

        @JsonProperty("tipo")
        private String type;

        @JsonProperty("cliente")
        private String client;

        @JsonProperty("tipo_eir_descripcion")
        private String eirTypeDescription;

        @JsonProperty("type_cnt")
        private String containerType;

        @JsonProperty("empresa_transporte")
        private String transportCompany;

        @JsonProperty("documento")
        private String document;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("conductor_id")
        private Integer conductorId;

        @JsonProperty("conductor")
        private String conductor;

        @JsonProperty("inspector_id")
        private Integer inspectorId;

        @JsonProperty("inspector")
        private String inspector;

        @JsonProperty("fecha_firma_conductor")
        private String conductorSignatureDate;

        @JsonProperty("url_firma_conductor")
        private String conductorSignatureUrl;

        @JsonProperty("url_firma_inspector")
        private String inspectorSignatureUrl;

        @JsonProperty("eir_chassis_id")
        private Integer eirChassisId;

        @JsonProperty("person_chassis_inspector_id")
        private Integer personChassisInspectorId;

        @JsonProperty("inspector_chassis")
        private String inspectorChassis;

        @JsonProperty("url_signature_inspector_chassis")
        private String urlSignatureInspectorChassis;
    }

    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class EstimateEmrPhotoDto {
        @JsonProperty("estimado_emr_eir_foto_id")
        private Integer id;
        @JsonProperty("adjunto_id")
        private Integer attachmentId;
        @JsonProperty("url")
        private String url;
        @JsonProperty("attachment_code")
        private String code;
    }

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class EstimateEmrDetailDto {
        @JsonProperty("estimado_emr_detalle_id")
        private Integer id;
        @JsonProperty("ubicacion_dmg")
        private String damageLocation;
        @JsonProperty("dmg_tipo")
        private String damageType;
        @JsonProperty("componente")
        private String component;
        @JsonProperty("metodo")
        private String method;
        @JsonProperty("dimension_tipo")
        private String dimensionType;
        @JsonProperty("asume_costo")
        private String asumeCost;
        @JsonProperty("nroPiezas")
        private Integer pieces;
        @JsonProperty("hh")
        private Double hh;
        @JsonProperty("costoHH")
        private Double costHh;
        @JsonProperty("costoMaterial")
        private Double costMaterial;
        @JsonProperty("totalItem")
        private Double totalItem;
        @JsonProperty("obs_item")
        private String observation;
    }

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class EstimateEmrDetailPhotoDto {
        @JsonProperty("estimado_emr_detalle_id")
        private Integer detailId;
        @JsonProperty("estimado_emr_detalle_foto_id")
        private Integer id;
        @JsonProperty("adjunto_id")
        private Integer attachmentId;
        @JsonProperty("url")
        private String url;
        @JsonProperty("attachment_code")
        private String code;
    }

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class InspectionGateInfoDto {
        @JsonProperty("inspeccion_gate_id")
        private Integer inspectionGateId;
        @JsonProperty("eir_id")
        private Integer eirId;
        @JsonProperty("numero_contenedor_original")
        private String originalContainer;
        @JsonProperty("control_inspeccion")
        private Integer inspectionControl;
        @JsonProperty("numero_contenedor_reasig")
        private String reassignedContainer;
        @JsonProperty("observacion")
        private String observation;
        @JsonProperty("obsv_reasignacion")
        private String reassignmentObservation;
    }

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class InspectionGatePhotoDto {
        @JsonProperty("inspeccion_gate_id")
        private Integer inspectionGateId;
        @JsonProperty("adjunto_id")
        private Integer attachmentId;
        @JsonProperty("url")
        private String url;
        @JsonProperty("attachment_code")
        private String code;
    }
}