package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.SetContainerEDIExchangeInput;
import com.maersk.sd1.sde.dto.SetContainerEDIExchangeOutput;
import com.maersk.sd1.sde.service.SetContainerEDIExchangeService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
public class SetContainerEDIExchangeController {

    private static final Logger logger = LogManager.getLogger(SetContainerEDIExchangeController.class);

    private final SetContainerEDIExchangeService setContainerEDIExchangeService;

    @Autowired
    public SetContainerEDIExchangeController(SetContainerEDIExchangeService setContainerEDIExchangeService) {
        this.setContainerEDIExchangeService = setContainerEDIExchangeService;
    }

    @PostMapping("/sdeseteoEdiCodecoObtener")
    public ResponseEntity<ResponseController<SetContainerEDIExchangeOutput>> obtenerSeteoEdiCodeco(@RequestBody @Valid SetContainerEDIExchangeInput.Root request) {
        SetContainerEDIExchangeOutput output = new SetContainerEDIExchangeOutput();
        try {
            Integer seteoEdiCodecoId = request.getPrefix().getInput().getSeteoEdiCodecoId();
            output = setContainerEDIExchangeService.getSeteoEdiCodecoData(seteoEdiCodecoId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
//            output.setRespMensaje(e.toString());
//            output.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

