package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.BookingBlockCancellationEditInput;
import com.maersk.sd1.sde.controller.dto.BookingBlockCancellationEditOutput;
import com.maersk.sd1.sde.service.BookingBlockCancellationEditService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDECancelacionBloqueoBookingServiceImp")
public class BookingBlockCancellationEditController {

    private static final Logger logger = LogManager.getLogger(BookingBlockCancellationEditController.class);

    private final BookingBlockCancellationEditService bookingBlockCancellationEditService;

    public BookingBlockCancellationEditController(BookingBlockCancellationEditService bookingBlockCancellationEditService) {
        this.bookingBlockCancellationEditService = bookingBlockCancellationEditService;
    }

    @PostMapping("/sdecancelacionBloqueoBookingEditar")
    public ResponseEntity<ResponseController<BookingBlockCancellationEditOutput>> editBookingBlockCancellation(@RequestBody @Valid BookingBlockCancellationEditInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            BookingBlockCancellationEditInput.Input input = request.getPrefix().getInput();
            if(input.getCancelBloqueoBookingId() == null || input.getComments() == null ){
                return ResponseEntity.status(400).body(new ResponseController<>("CancelBloqueoBookingId and Comentario cannot be null."));
            }
            BookingBlockCancellationEditOutput output = bookingBlockCancellationEditService.editBookingBlockCancellation(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            BookingBlockCancellationEditOutput out = new BookingBlockCancellationEditOutput();
            out.setRespStatus(0);
            out.setRespMessage(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(out));
        }
    }
}