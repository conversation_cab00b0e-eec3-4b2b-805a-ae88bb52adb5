package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.ListPhotosAndContainerInput;
import com.maersk.sd1.sde.controller.dto.ListPhotosAndContainerOutput;
import com.maersk.sd1.sde.service.ListPhotosAndContainerService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEFotosServiceImp")
public class ListPhotosAndContainerController {

    private static final Logger logger = LogManager.getLogger(ListPhotosAndContainerController.class);

    private final ListPhotosAndContainerService listPhotosAndContainerService;

    public ListPhotosAndContainerController(ListPhotosAndContainerService listPhotosAndContainerService) {
        this.listPhotosAndContainerService = listPhotosAndContainerService;
    }

    @PostMapping("/sdelistarFotosEirContenedor")
    public ResponseEntity<ResponseController<ListPhotosAndContainerOutput>> listPhotosAndContainer(@RequestBody @Valid ListPhotosAndContainerInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                ListPhotosAndContainerOutput errorOutput = new ListPhotosAndContainerOutput();
                return ResponseEntity.badRequest().body(new ResponseController<>(errorOutput));
            }

            ListPhotosAndContainerInput.Input input = request.getPrefix().getInput();
            if (input.getSubBusinessUnitId() == null || input.getContainerId() == null || input.getLanguageId() == null) {
                ListPhotosAndContainerOutput errorOutput = new ListPhotosAndContainerOutput();
                return ResponseEntity.badRequest().body(new ResponseController<>(errorOutput));
            }

            ListPhotosAndContainerOutput serviceOutput = listPhotosAndContainerService.getFotosEirContenedor(
                    input.getSubBusinessUnitId(),
                    input.getContainerId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(serviceOutput));
        } catch (Exception ex) {
            logger.error("Error in listPhotosAndContainer endpoint", ex);
            ListPhotosAndContainerOutput errorOutput = new ListPhotosAndContainerOutput();
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}