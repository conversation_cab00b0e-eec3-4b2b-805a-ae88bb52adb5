package com.maersk.sd1.sde.repository;

import com.maersk.sd1.common.model.EirZone;
import com.maersk.sd1.common.repository.EirZoneRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SdeEirZoneRepository extends EirZoneRepository {

    @Query(value = "select ez from EirZone ez " +
            "where ez.eir.id in :eirIds " +
            "and ez.active = true " +
            "group by ez.eir.id")
    List<EirZone> findByEirIds(@Param("eirIds") List<Integer> eirIds);

}
