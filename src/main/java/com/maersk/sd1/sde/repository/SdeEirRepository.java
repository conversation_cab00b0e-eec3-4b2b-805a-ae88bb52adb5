package com.maersk.sd1.sde.repository;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.repository.EirRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface SdeEirRepository extends EirRepository {

    @Query(value = "select e from Eir e " +
            "where e.container.containerNumber = :containerNumber " +
            "   OR FUNCTION('RIGHT', e.container.containerNumber, LENGTH(:containerNumber)) = :containerNumber " +
            "and e.subBusinessUnit.id = :subBusinessUnitId " +
            "and e.catMovement.id = :moveTypeId " +
            "and e.active = true " +
            "order by e.truckArrivalDate desc")
    List<Eir> findByContainerNumberAndSubBusinessUnitId(@Param("containerNumber") String containerNumber, @Param("subBusinessUnitId") Integer subBusinessUnitId, @Param("moveTypeId") Integer moveTypeId);

    @Procedure(name = "Eir.getNextZone")
    List<Object[]> getNextZone(
            @Param("eir_id") Integer eirId,
            @Param("Actividad") String activity,
            @Param("ConDano") Boolean withDamage
    );

    @Procedure(name = "Eir.listActivityZoneEir")
    List<Object[]> listActivityZoneEir(
            @Param("Tipo") String type,
            @Param("numero_contenedor") String containerNumber,
            @Param("Zona") String zone,
            @Param("eir_id") Integer eirId
    );


    @Query("SELECT e "
            + "FROM Eir e "
            + "JOIN BusinessUnit b ON e.subBusinessUnit.id = b.id "
            + "JOIN Catalog catMov ON e.catMovement.id = catMov.id "
            + "JOIN Catalog catEmpFull ON e.catEmptyFull.id = catEmpFull.id "
            + "WHERE e.container.id = :containerId "
            + "AND e.businessUnit.id = :businessUnitId "
            + "AND e.active = true "
            + "ORDER BY e.truckArrivalDate DESC")
    Optional<Eir> findLastEirByContainerAndBusinessUnit(@Param("containerId") int containerId, @Param("businessUnitId") int businessUnitId);

    @Query("SELECT e "
            + "FROM Eir e "
            + "JOIN BusinessUnit b ON e.subBusinessUnit.id = b.id "
            + "JOIN BusinessUnit ub ON e.businessUnit.id = ub.id "
            + "JOIN Catalog c ON e.catMovement.id = c.id "
            + "JOIN Catalog mf ON e.catEmptyFull.id = mf.id "
            + "WHERE e.container.id = :containerId "
            + "AND e.truckArrivalDate = :truckInDate "
            + "AND e.active = true "
            + "ORDER BY e.registrationDate DESC")
    Optional<Eir> findEirByContainerAndTruckInDate(@Param("containerId") int containerId, @Param("truckInDate") LocalDateTime truckInDate);

    @Query(value = "select e from Eir e where e.container.id = :containerId " +
            "and e.vesselProgrammingDetail.id = :vesselProgrammingDetailId " +
            "and e.businessUnit.id = :businessUnitId " +
            "and e.subBusinessUnit.id = :subBusinessUnitId " +
            "and e.truck.id = :truckId " +
            "and e.driverPerson.id = :driverId " +
            "and e.catMovement.id = :catMovId " +
            "and e.catEmptyFull.id = :catEmptyFullId " +
            "and e.active = true")
    Optional<Eir> findIfExistsByParams(@Param("containerId") Integer containerId,
                                       @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                       @Param("businessUnitId") Integer businessUnitId,
                                       @Param("truckId") Integer truckId,
                                       @Param("driverId") Integer driverId,
                                       @Param("catEmptyFullId") Integer catEmptyFullId,
                                       @Param("catMovId") Integer catMovId,
                                       @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Procedure(name = "Eir.firstActivityZoneEmptyContainer")
    void firstActivityZoneEmptyContainer(
            @Param("eir_id") Integer eirId,
            @Param("cat_movimiento_id") Integer catMovimientoId,
            @Param("cat_empty_full_id") Integer catEmptyFullId,
            @Param("user_id") Integer userId,
            @Param("creation_origen_id") Integer creationOrigenId,
            @Param("type_cnt_id") Integer typeCntId
    );

    @Query("SELECT e FROM Eir e " +
            "WHERE e.container.id = :containerId " +
            "AND e.businessUnit.id = :businessUnitId " +
            "AND e.active = true " +
            "ORDER BY e.truckArrivalDate DESC")
    List<Eir> findTopEirByContainerId(@Param("containerId") Integer containerId,
                                      @Param("businessUnitId") Integer businessUnitId,
                                      Pageable pageable);
}

