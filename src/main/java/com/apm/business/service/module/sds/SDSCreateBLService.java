package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSCreateBLService {

	@RequestMapping(value = "/sdsblManualBusqueda", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsblManualBusqueda(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.bl_manual_busqueda","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("numero_bl", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsdepositoObtenerPorUn", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsdepositoObtenerPorUn(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.deposito_obtener_por_un","SDS");
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("sub_unidad_negocio_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsempresasClienteObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsempresasClienteObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.empresas_cliente_obtener","SDS");
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsblNavesListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsblNavesListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.bl_naves_listar","SDS");
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("nombre_nave", Jpo.STRING);
			pResult.input("viaje", Jpo.STRING);
			pResult.input("manifiesto_ano", Jpo.STRING);
			pResult.input("manifiesto_numero", Jpo.STRING);
			pResult.input("language_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsblManualRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsblManualRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.bl_manual_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("documento_carga_id", Jpo.INTEGER);
			pResult.input("numero_bl", Jpo.STRING);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			pResult.input("puerto_embarque_id", Jpo.INTEGER);
			pResult.input("puerto_descarga_id", Jpo.INTEGER);
			pResult.input("empresa_embarcador_id", Jpo.INTEGER);
			pResult.input("empresa_consignatario_id", Jpo.INTEGER);
			pResult.input("embarcador_detalle", Jpo.STRING);
			pResult.input("consignatario_detalle", Jpo.STRING);
			pResult.input("deposito_vacio_id", Jpo.INTEGER);
			pResult.input("contenedores_json", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsblManualSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsblManualSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.bl_manual_search","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("numero_bl", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsemptyBlContainerRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsemptyBlContainerRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.empty_bl_container_register","SDS");
			pResult.input("bl_number", Jpo.STRING);
			pResult.input("container_number", Jpo.STRING);
			pResult.input("container_type", Jpo.DECIMAL);
			pResult.input("container_size", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.DECIMAL);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.input("sub_business_unit_local_id", Jpo.INTEGER);
			pResult.output("resp_state", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsblManualRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsblManualRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.bl_manual_register","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("documento_carga_id", Jpo.INTEGER);
			pResult.input("numero_bl", Jpo.STRING);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			pResult.input("puerto_embarque_id", Jpo.INTEGER);
			pResult.input("puerto_descarga_id", Jpo.INTEGER);
			pResult.input("empresa_embarcador_id", Jpo.INTEGER);
			pResult.input("empresa_consignatario_id", Jpo.INTEGER);
			pResult.input("embarcador_detalle", Jpo.STRING);
			pResult.input("consignatario_detalle", Jpo.STRING);
			pResult.input("deposito_vacio_id", Jpo.INTEGER);
			pResult.input("contenedores_json", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("pass_restriction", Jpo.INTEGER);
			pResult.input("cat_move_type_id", Jpo.DECIMAL);
			pResult.input("maersk_depot_with_sd1", Jpo.CHARACTER);
			pResult.input("origin_destination_depot_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}