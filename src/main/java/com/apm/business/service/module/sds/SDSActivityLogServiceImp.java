package com.apm.business.service.module.sds;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import org.json.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.apm.business.externalService.bean.ServiceConnectionHeader;
import com.apm.business.externalService.integration.ServiceConnection;

import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.JpoUtil;
import ohSolutions.ohRest.util.bean.Response;

@RestController
@RequestMapping("/module/sds/SDSActivityLogServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSActivityLogServiceImp extends SDSActivityLogService {

	@RequestMapping(value = "/sdsactivityLogList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsactivityLogList(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsactivityLogList(ppo, request);
	}
	
	@RequestMapping(value = "/sdsactivityLogUpdate", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsactivityLogUpdate(Jpo ppo, HttpServletRequest request) throws Exception {
		
		Response response = new Response();
		
		try {
			Response log_status_check = (Response) super.sdsactivityLogStatusCheck(ppo, request);
			ArrayList<Object> check_result = (ArrayList<Object>) log_status_check.getResult();
			
			if (Integer.valueOf(check_result.get(0).toString()) == 1) {
				HashMap<String, Object> integration_response = new HashMap<>();
				
				switch (ppo.getData("SDS", "module_alias")) {
					case "sdy":
						integration_response = SDYServiceRetry(ppo);
					break;
				}
				
				if ((Boolean) integration_response.get("update_log")) {
					response = (Response) super.sdsactivityLogUpdate(ppo, request);
					Response sdy_response = (Response) integration_response.get("resp");
					((ArrayList<Object>) response.getResult()).set(2, sdy_response.getResult());					
				} else {
					Response int_resp = (Response) integration_response.get("resp");
					response.setCorrect(true);
					response.setMessage(int_resp.getMessage());
					response.setResult(new ArrayList<Object>() {{
						add(0);
						add(int_resp.getMessage());
					}});
				}
			} else {				
				response.setCorrect(true);
				response.setMessage(check_result.get(1).toString());
				response.setResult(log_status_check.getResult());
			}						
			
		} catch (Exception e) {
			response.setCorrect(true);
			response.setMessage("Internal Server Error");
			response.setResult(new ArrayList<Object>() {{
				add(0);
				add("Internal Server Error");
			}});
		}
		
		return response;
	}
	
	public HashMap<String, Object> SDYServiceRetry(Jpo ppo) throws Exception {
		
		//INITIALLIZE RESULT OBJECT
		HashMap<String, Object> sdy_response = new HashMap<>();
		
		try {			
			//MAKE SDY INTEGRATION CALL				
			ServiceConnection sdyConnection = new ServiceConnection(
				JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.loginUrl"),
				JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.user"),
				JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.password"),
				JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.system")
			);
			
			//IDENTIFY SDY INTEGRATION SERVICES
			Map<String, String> yard_services = new HashMap<String, String>();			
			yard_services.put("sdyGinAsg", JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.gateInUrl"));
			yard_services.put("sdyInsp", JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.sdyobtenerUbicacionNuevaZona"));
			yard_services.put("sdyGoutAsg", JpoUtil.getPropertie("SDS", "msk.api.yard.apiUserLogin.sdy.gateOutUrl"));
			
			//SELECT SPECIFIC SERVICE BASED ON LOG ACTIVITY ALIAS 
			String service_url = yard_services.get(ppo.getData("SDS", "activity_alias").toString().trim());
			String raw_data_input = ppo.getData("SDS", "data_input");
			
			//CREATE FORMATTED JSON OBJECT
			JSONObject formatted_data_input = new JSONObject();
			JSONObject f_json= new JSONObject();
			f_json.put("F", new JSONObject(raw_data_input));
			formatted_data_input.put(ppo.getData("SDS", "module_alias").toString().toUpperCase(), f_json);
			
			//ATTACH HEADERS TO HTTP REQUEST AND PERFORM REQUEST
			ServiceConnectionHeader headerItem = new ServiceConnectionHeader();			
			headerItem.setKey("SD1LogActivityTraceId");
			headerItem.setValue(ppo.getData("SDS", "activity_log_id"));
			
			//PERFORM HTTP REQUEST
			Response resp = sdyConnection.post(service_url, formatted_data_input.toString(), new ArrayList<ServiceConnectionHeader>() {{
				add(headerItem);
			}});

			//CHECK HTTP RESPONSE STATUS
			if (resp.isCorrect() && resp.getMessage().equals("Success")) {
				if (resp.getResult() instanceof JSONObject) {
					JSONObject containers = (JSONObject) resp.getResult();					
					resp.setResult(containers.toString());
					sdy_response.put("update_log", true);					
				}
			} else {
				sdy_response.put("update_log", false);
			}
			sdy_response.put("resp", resp);			
			
		} catch (Exception e) {
			e.printStackTrace();
			sdy_response.put("updateLog", false);
		}
		
		return sdy_response;
	}

}