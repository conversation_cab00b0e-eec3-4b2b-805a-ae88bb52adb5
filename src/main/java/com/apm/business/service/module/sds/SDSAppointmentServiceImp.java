package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sds/SDSAppointmentServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSAppointmentServiceImp extends SDSAppointmentService {

	@RequestMapping(value = "/sdsopacifCitasBusquedaBl", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsopacifCitasBusquedaBl(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsopacifCitasBusquedaBl(ppo, request);
	}

	@RequestMapping(value = "/sdsopacifCitasBusquedaBooking", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsopacifCitasBusquedaBooking(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsopacifCitasBusquedaBooking(ppo, request);
	}

}