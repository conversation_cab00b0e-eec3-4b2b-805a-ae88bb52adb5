package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSNaveService {

	@RequestMapping(value = "/sdsnaveListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsnaveListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.nave_listar","SDS");
			pResult.input("nave_id", Jpo.INTEGER);
			pResult.input("nave", Jpo.STRING);
			pResult.input("call_sign", Jpo.STRING);
			pResult.input("imo_number", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsnaveRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsnaveRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.nave_registrar","SDS");
			pResult.input("nave", Jpo.STRING);
			pResult.input("call_sign", Jpo.STRING);
			pResult.input("imo_number", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("nombre", Jpo.STRING);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsnaveObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsnaveObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.nave_obtener","SDS");
			pResult.input("nave_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsnaveEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsnaveEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.nave_editar","SDS");
			pResult.input("nave_id", Jpo.INTEGER);
			pResult.input("nave", Jpo.STRING);
			pResult.input("call_sign", Jpo.STRING);
			pResult.input("imo_number", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("nombre", Jpo.STRING);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsnaveEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsnaveEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.nave_eliminar","SDS");
			pResult.input("nave_id", Jpo.DECIMAL);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsnaveListar2", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsnaveListar2(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.nave_listar2","SDS");
			pResult.input("nave_id", Jpo.INTEGER);
			pResult.input("nave", Jpo.STRING);
			pResult.input("call_sign", Jpo.STRING);
			pResult.input("imo_number", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdssearchVessel", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdssearchVessel(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.search_Vessel","SDS");
			pResult.input("nombre", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}