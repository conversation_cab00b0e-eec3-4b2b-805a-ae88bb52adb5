package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSAppEIRConfigService {

	@RequestMapping(value = "/sdsappEirConfigList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsappEirConfigList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.app_eir_config_list","SDS");
			pResult.input("app_eir_config_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("shop_email", Jpo.STRING);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			pResult.input("sub_business_unit_id_user", Jpo.DECIMAL);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsappEirConfigRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsappEirConfigRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.app_eir_config_register","SDS");
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("shop_email", Jpo.STRING);
			pResult.input("client_id", Jpo.STRING);
			pResult.input("client_secret", Jpo.STRING);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_status", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsappEirConfigEdit", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsappEirConfigEdit(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.app_eir_config_edit","SDS");
			pResult.input("app_eir_config_id", Jpo.INTEGER);
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("shop_email", Jpo.STRING);
			pResult.input("client_id", Jpo.STRING);
			pResult.input("client_secret", Jpo.STRING);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_status", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsappEirConfigGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsappEirConfigGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.app_eir_config_get","SDS");
			pResult.input("app_eir_config_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsappEirConfigDelete", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsappEirConfigDelete(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.app_eir_config_delete","SDS");
			pResult.input("app_eir_config_id", Jpo.INTEGER);
			pResult.input("user_modification_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_status", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}