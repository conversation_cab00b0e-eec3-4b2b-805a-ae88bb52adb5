package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSSftpService {

	@RequestMapping(value = "/sdssftpConfigList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdssftpConfigList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.sftp_config_list","SDS");
			pResult.input("sftp_config_id", Jpo.DECIMAL);
			pResult.input("id", Jpo.STRING);
			pResult.input("es_ftp", Jpo.CHARACTER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}