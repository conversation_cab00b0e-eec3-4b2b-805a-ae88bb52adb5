package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSContenedorService {

	@RequestMapping(value = "/sdscontenedorEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscontenedorEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.contenedor_eliminar","SDS");
			pResult.input("contenedor_id", Jpo.DECIMAL);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscontenedorListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscontenedorListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.contenedor_listar","SDS");
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("contenedor_id", Jpo.INTEGER);
			pResult.input("numero_contenedor", Jpo.STRING);
			pResult.input("cat_familia_id", Jpo.DECIMAL);
			pResult.input("cat_tamano_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_contenedor_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_nombre", Jpo.STRING);
			pResult.input("tara", Jpo.DECIMAL);
			pResult.input("carga_maxima", Jpo.DECIMAL);
			pResult.input("codigo_iso_id", Jpo.INTEGER);
			pResult.input("cat_clase_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_reefer_id", Jpo.DECIMAL);
			pResult.input("cat_marca_motor_id", Jpo.DECIMAL);
			pResult.input("fecha_fabricacion_min", Jpo.DATE);
			pResult.input("fecha_fabricacion_max", Jpo.DATE);
			pResult.input("shipper_own", Jpo.CHARACTER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscontenedorRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscontenedorRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.contenedor_registrar","SDS");
			pResult.input("numero_contenedor", Jpo.STRING);
			pResult.input("cat_familia_id", Jpo.DECIMAL);
			pResult.input("cat_tamano_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_contenedor_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("tara", Jpo.DECIMAL);
			pResult.input("carga_maxima", Jpo.DECIMAL);
			pResult.input("codigo_iso_id", Jpo.INTEGER);
			pResult.input("cat_clase_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_reefer_id", Jpo.DECIMAL);
			pResult.input("cat_marca_motor_id", Jpo.DECIMAL);
			pResult.input("fecha_fabricacion", Jpo.DATE);
			pResult.input("shipper_own", Jpo.CHARACTER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("para_venta", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscontenedorObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscontenedorObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.contenedor_obtener","SDS");
			pResult.input("contenedor_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscontenedorEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscontenedorEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.contenedor_editar","SDS");
			pResult.input("contenedor_id", Jpo.INTEGER);
			pResult.input("numero_contenedor", Jpo.STRING);
			pResult.input("cat_familia_id", Jpo.DECIMAL);
			pResult.input("cat_tamano_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_contenedor_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("tara", Jpo.DECIMAL);
			pResult.input("carga_maxima", Jpo.DECIMAL);
			pResult.input("codigo_iso_id", Jpo.INTEGER);
			pResult.input("cat_clase_id", Jpo.DECIMAL);
			pResult.input("cat_tipo_reefer_id", Jpo.DECIMAL);
			pResult.input("cat_marca_motor_id", Jpo.DECIMAL);
			pResult.input("fecha_fabricacion", Jpo.DATE);
			pResult.input("shipper_own", Jpo.CHARACTER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("para_venta", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsconsultarStockContenedores", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsconsultarStockContenedores(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.consultar_stock_contenedores","SDS");
			pResult.input("tipo_contenedor", Jpo.INTEGER);
			pResult.input("tmn_contenedor", Jpo.INTEGER);
			pResult.input("tipo_reefer", Jpo.INTEGER);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("sub_unidad_negocio_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsequipmentFind", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsequipmentFind(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.equipment_find","SDS");
			pResult.input("equipment_number", Jpo.STRING);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("user_register_id", Jpo.DECIMAL);
			pResult.output("resp_state", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			pResult.output("iso_code_id", Jpo.INTEGER);
			pResult.output("iso_code", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}