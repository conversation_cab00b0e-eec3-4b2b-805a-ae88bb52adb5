package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sds/SDSAzureServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSAzureServiceImp extends SDSAzureService {

	@RequestMapping(value = "/sdsazureStorageConfigList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsazureStorageConfigList(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsazureStorageConfigList(ppo, request);
	}

}