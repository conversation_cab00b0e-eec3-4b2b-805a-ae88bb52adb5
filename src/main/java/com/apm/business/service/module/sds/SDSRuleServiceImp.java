package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sds/SDSRuleServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSRuleServiceImp extends SDSRuleService {

	@RequestMapping(value = "/sdsruleYardValidate", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsruleYardValidate(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsruleYardValidate(ppo, request);
	}

	@RequestMapping(value = "/sdsruleGeneralGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsruleGeneralGet(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsruleGeneralGet(ppo, request);
	}

}