package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSMonitoringService {

	@RequestMapping(value = "/sdscoparnMonitoreoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscoparnMonitoreoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.coparn_monitoreo_listar","SDS");
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("tipo_listado_id", Jpo.DECIMAL);
			pResult.input("seteo_edi_coparn_id", Jpo.DECIMAL);
			pResult.input("nave_nombre", Jpo.STRING);
			pResult.input("viaje", Jpo.STRING);
			pResult.input("numero_Booking", Jpo.STRING);
			pResult.input("fecha_recepcion_desde", Jpo.DATE);
			pResult.input("fecha_recepcion_hasta", Jpo.DATE);
			pResult.input("fecha_procesado_desde", Jpo.DATE);
			pResult.input("fecha_procesado_hasta", Jpo.DATE);
			pResult.input("todos_depositos", Jpo.CHARACTER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscoparnMonitoreoEliminarEdi", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscoparnMonitoreoEliminarEdi(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.coparn_monitoreo_eliminar_edi","SDS");
			pResult.input("edi_coparn_id", Jpo.INTEGER);
			pResult.input("usuario_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscoparnMonitoreoVistaEdi", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscoparnMonitoreoVistaEdi(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.coparn_monitoreo_vista_edi","SDS");
			pResult.input("edi_coparn_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}