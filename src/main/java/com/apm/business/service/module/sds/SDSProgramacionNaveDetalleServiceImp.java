package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sds/SDSProgramacionNaveDetalleServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSProgramacionNaveDetalleServiceImp extends SDSProgramacionNaveDetalleService {

	@RequestMapping(value = "/sdsprogramacionNaveDetalleListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsprogramacionNaveDetalleListar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsprogramacionNaveDetalleListar(ppo, request);
	}

	@RequestMapping(value = "/sdsprogramacionNaveDetalleExportacion", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsprogramacionNaveDetalleExportacion(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdsprogramacionNaveDetalleExportacion(ppo, request);
	}

}