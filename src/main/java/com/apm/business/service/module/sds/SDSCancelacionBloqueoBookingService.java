package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSCancelacionBloqueoBookingService {

	@RequestMapping(value = "/sdecancelacionBloqueoBookingEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_editar","SDS");
			pResult.input("cancel_bloqueo_booking_id", Jpo.INTEGER);
			pResult.input("comentario", Jpo.STRING);
			pResult.input("usuario_modifiacion_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdecancelacionBloqueoBookingLiberacionEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingLiberacionEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_liberacion_editar","SDS");
			pResult.input("cancel_bloqueo_booking_id", Jpo.INTEGER);
			pResult.input("comentario", Jpo.STRING);
			pResult.input("usuario_modifiacion_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdecancelacionBloqueoBookingLiberacionObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingLiberacionObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_liberacion_obtener","SDS");
			pResult.input("cancel_bloqueo_booking_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdecancelacionBloqueoBookingLiberacionRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingLiberacionRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_liberacion_registrar","SDS");
			pResult.input("cancel_bloqueo_booking_id", Jpo.INTEGER);
			pResult.input("comentario", Jpo.STRING);
			pResult.input("cat_motivo", Jpo.INTEGER);
			pResult.input("usuario_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdecancelacionBloqueoBookingListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_listar","SDS");
			pResult.input("sub_unidad_negocio_id", Jpo.INTEGER);
			pResult.input("booking", Jpo.STRING);
			pResult.input("tipoCancelacion", Jpo.INTEGER);
			pResult.input("nave", Jpo.STRING);
			pResult.input("viaje", Jpo.STRING);
			pResult.input("id", Jpo.STRING);
			pResult.input("fecha_cb_desde", Jpo.DATETIME);
			pResult.input("fecha_cb_hasta", Jpo.DATETIME);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdecancelacionBloqueoBookingObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_obtener","SDS");
			pResult.input("cancel_bloqueo_booking_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdecancelacionBloqueoBookingRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdecancelacionBloqueoBookingRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.cancelacion_bloqueo_booking_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("sub_unidad_negocio_id", Jpo.INTEGER);
			pResult.input("cat_tipo", Jpo.INTEGER);
			pResult.input("cat_motivo", Jpo.INTEGER);
			pResult.input("comentario", Jpo.STRING);
			pResult.input("usuario_registro_id", Jpo.INTEGER);
			pResult.input("documento_carga_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdscancelacionBloqueoBookingUbicar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdscancelacionBloqueoBookingUbicar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.cancelacion_bloqueo_booking_ubicar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("cat_tipo", Jpo.DECIMAL);
			pResult.input("numero_booking", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdebookingBlockCancelationGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdebookingBlockCancelationGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.booking_block_cancelation_get","SDS");
			pResult.input("cancel_bloqueo_booking_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}