package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSManHourCostService {

	@RequestMapping(value = "/sdeestimadoEmrCostoHhEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimadoEmrCostoHhEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimado_emr_costo_hh_editar","SDS");
			pResult.input("estimado_emr_costo_hh_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_tipo_estimado_id", Jpo.DECIMAL);
			pResult.input("moneda_id", Jpo.DECIMAL);
			pResult.input("costo_hora_hombre", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("cat_equipment_category", Jpo.DECIMAL);
			pResult.input("chassis_owner_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimadoEmrCostoHhEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimadoEmrCostoHhEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimado_emr_costo_hh_eliminar","SDS");
			pResult.input("estimado_emr_costo_hh_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimadoEmrCostoHhListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimadoEmrCostoHhListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimado_emr_costo_hh_listar","SDS");
			pResult.input("estimado_emr_costo_hh_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_tipo_estimado_id", Jpo.DECIMAL);
			pResult.input("moneda_id", Jpo.DECIMAL);
			pResult.input("costo_hora_hombre", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("cat_equipment_category_id", Jpo.INTEGER);
			pResult.input("chassis_owner_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimadoEmrCostoHhRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimadoEmrCostoHhRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimado_emr_costo_hh_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_tipo_estimado_id", Jpo.DECIMAL);
			pResult.input("moneda_id", Jpo.DECIMAL);
			pResult.input("costo_hora_hombre", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("cat_equipment_category", Jpo.DECIMAL);
			pResult.input("chassis_owner_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimadoEmrCostoHhObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimadoEmrCostoHhObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimado_emr_costo_hh_obtener","SDS");
			pResult.input("estimado_emr_costo_hh_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}