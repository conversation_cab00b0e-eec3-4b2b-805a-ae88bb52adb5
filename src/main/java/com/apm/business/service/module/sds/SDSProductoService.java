package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSProductoService {

	@RequestMapping(value = "/sdsproductoRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsproductoRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.producto_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("codigo_producto", Jpo.STRING);
			pResult.input("nombre_producto", Jpo.STRING);
			pResult.input("cat_grupo_producto_id", Jpo.DECIMAL);
			pResult.input("cat_unidad_medida_peso_id", Jpo.DECIMAL);
			pResult.input("cat_envase_id", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("coparn_commodity1", Jpo.STRING);
			pResult.input("coparn_commodity2", Jpo.STRING);
			pResult.input("cat_tipo_refrigeracion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsproductoObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsproductoObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.producto_obtener","SDS");
			pResult.input("producto_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsproductoEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsproductoEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.producto_eliminar","SDS");
			pResult.input("producto_id", Jpo.DECIMAL);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsproductoEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsproductoEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.producto_editar","SDS");
			pResult.input("producto_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("codigo_producto", Jpo.STRING);
			pResult.input("nombre_producto", Jpo.STRING);
			pResult.input("cat_grupo_producto_id", Jpo.DECIMAL);
			pResult.input("cat_unidad_medida_peso_id", Jpo.DECIMAL);
			pResult.input("cat_envase_id", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("coparn_commodity1", Jpo.STRING);
			pResult.input("coparn_commodity2", Jpo.STRING);
			pResult.input("cat_tipo_refrigeracion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsproductoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsproductoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.producto_listar","SDS");
			pResult.input("producto_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("codigo_producto", Jpo.STRING);
			pResult.input("nombre_producto", Jpo.STRING);
			pResult.input("cat_grupo_producto_id", Jpo.DECIMAL);
			pResult.input("cat_unidad_medida_peso_id", Jpo.DECIMAL);
			pResult.input("cat_envase_id", Jpo.DECIMAL);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("coparn_commodity1", Jpo.STRING);
			pResult.input("coparn_commodity2", Jpo.STRING);
			pResult.input("cat_tipo_refrigeracion_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_usuario_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsproductSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsproductSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.product_search","SDS");
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("product_id", Jpo.INTEGER);
			pResult.input("product_name", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}