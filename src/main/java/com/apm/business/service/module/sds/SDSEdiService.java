package com.apm.business.service.module.sds;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSEdiService {

	@RequestMapping(value = "/sdeseteoEstimadoEmrEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEstimadoEmrEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_estimado_emr_editar","SDS");
			pResult.input("seteo_estimado_emr_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_modo_generar_archivo_esimado_id", Jpo.DECIMAL);
			pResult.input("descripcion_servicio", Jpo.STRING);
			pResult.input("archivo_correlativo", Jpo.INTEGER);
			pResult.input("archivo_extension", Jpo.STRING);
			pResult.input("correo_envio", Jpo.STRING);
			pResult.input("minutos_transcurridos", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("shopcode_merc", Jpo.STRING);
			pResult.input("azure_id", Jpo.STRING);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEstimadoEmrEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEstimadoEmrEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_estimado_emr_eliminar","SDS");
			pResult.input("seteo_estimado_emr_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEstimadoEmrListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEstimadoEmrListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_estimado_emr_listar","SDS");
			pResult.input("seteo_estimado_emr_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_modo_generar_archivo_esimado_id", Jpo.DECIMAL);
			pResult.input("descripcion_servicio", Jpo.STRING);
			pResult.input("archivo_correlativo", Jpo.INTEGER);
			pResult.input("archivo_extension", Jpo.STRING);
			pResult.input("correo_envio", Jpo.STRING);
			pResult.input("minutos_transcurridos", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("usuario_registro", Jpo.DECIMAL);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("shopcode_merc", Jpo.STRING);
			pResult.input("azure_id", Jpo.STRING);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEstimadoEmrObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEstimadoEmrObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_estimado_emr_obtener","SDS");
			pResult.input("seteo_estimado_emr_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEstimadoEmrRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEstimadoEmrRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_estimado_emr_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_modo_generar_archivo_esimado_id", Jpo.DECIMAL);
			pResult.input("descripcion_servicio", Jpo.STRING);
			pResult.input("archivo_correlativo", Jpo.INTEGER);
			pResult.input("archivo_extension", Jpo.STRING);
			pResult.input("correo_envio", Jpo.STRING);
			pResult.input("minutos_transcurridos", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro", Jpo.DECIMAL);
			pResult.input("shopcode_merc", Jpo.STRING);
			pResult.input("azure_id", Jpo.STRING);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEdiCodecoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEdiCodecoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_edi_codeco_listar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("sistema_entrega", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEdiCodecoEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEdiCodecoEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_edi_codeco_eliminar","SDS");
			pResult.input("seteo_edi_codeco_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEdiCodecoRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEdiCodecoRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_edi_codeco_registrar","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("sistema_entrega", Jpo.STRING);
			pResult.input("info_sistema_entrega", Jpo.STRING);
			pResult.input("identificador_receptor", Jpo.STRING);
			pResult.input("enviar_gate_in_empty", Jpo.CHARACTER);
			pResult.input("enviar_gate_out_empty", Jpo.CHARACTER);
			pResult.input("enviar_gate_in_full", Jpo.CHARACTER);
			pResult.input("enviar_gate_out_full", Jpo.CHARACTER);
			pResult.input("enviar_status_activity", Jpo.CHARACTER);
			pResult.input("cat_formato_gate_out_empty", Jpo.DECIMAL);
			pResult.input("cat_formato_gate_in_full", Jpo.DECIMAL);
			pResult.input("cat_formato_gate_out_full", Jpo.DECIMAL);
			pResult.input("cat_formato_gate_in_empty", Jpo.DECIMAL);
			pResult.input("cat_formato_status_activity", Jpo.DECIMAL);
			pResult.input("cat_canal_envio_id", Jpo.DECIMAL);
			pResult.input("cat_modo_generar_archivo_id", Jpo.DECIMAL);
			pResult.input("correo_codeco_destino", Jpo.STRING);
			pResult.input("correo_telex_destino", Jpo.STRING);
			pResult.input("parametro_1", Jpo.STRING);
			pResult.input("parametro_2", Jpo.STRING);
			pResult.input("parametro_3", Jpo.STRING);
			pResult.input("parametro_4", Jpo.STRING);
			pResult.input("es_historico", Jpo.CHARACTER);
			pResult.input("fecha_debaja", Jpo.DATE);
			pResult.input("motivo_debaja", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("parametro_5", Jpo.STRING);
			pResult.input("parametro_6", Jpo.STRING);
			pResult.input("azure_id_codeco", Jpo.STRING);
			pResult.input("azure_id_telex", Jpo.STRING);
			pResult.input("sftp_id", Jpo.STRING);
			pResult.input("extension_archivo_enviar", Jpo.STRING);
			pResult.input("minutos_trancurridos", Jpo.INTEGER);
			pResult.input("sub_unidades_json", Jpo.STRING);
			pResult.input("gate_in_empty_movimiento_id", Jpo.DECIMAL);
			pResult.input("gate_in_empty_procedencia_json", Jpo.STRING);
			pResult.input("gate_out_empty_movimiento_id", Jpo.DECIMAL);
			pResult.input("gate_out_empty_procedencia_json", Jpo.STRING);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEdiCodecoObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEdiCodecoObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_edi_codeco_obtener","SDS");
			pResult.input("seteo_edi_codeco_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeseteoEdiCodecoEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeseteoEdiCodecoEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.seteo_edi_codeco_editar","SDS");
			pResult.input("seteo_edi_codeco_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("sistema_entrega", Jpo.STRING);
			pResult.input("info_sistema_entrega", Jpo.STRING);
			pResult.input("identificador_receptor", Jpo.STRING);
			pResult.input("enviar_gate_in_empty", Jpo.CHARACTER);
			pResult.input("enviar_gate_out_empty", Jpo.CHARACTER);
			pResult.input("enviar_gate_in_full", Jpo.CHARACTER);
			pResult.input("enviar_gate_out_full", Jpo.CHARACTER);
			pResult.input("enviar_status_activity", Jpo.CHARACTER);
			pResult.input("cat_formato_gate_out_empty", Jpo.DECIMAL);
			pResult.input("cat_formato_gate_in_full", Jpo.DECIMAL);
			pResult.input("cat_formato_gate_out_full", Jpo.DECIMAL);
			pResult.input("cat_formato_gate_in_empty", Jpo.DECIMAL);
			pResult.input("cat_formato_status_activity", Jpo.DECIMAL);
			pResult.input("cat_canal_envio_id", Jpo.DECIMAL);
			pResult.input("cat_modo_generar_archivo_id", Jpo.DECIMAL);
			pResult.input("correo_codeco_destino", Jpo.STRING);
			pResult.input("correo_telex_destino", Jpo.STRING);
			pResult.input("parametro_1", Jpo.STRING);
			pResult.input("parametro_2", Jpo.STRING);
			pResult.input("parametro_3", Jpo.STRING);
			pResult.input("parametro_4", Jpo.STRING);
			pResult.input("es_historico", Jpo.CHARACTER);
			pResult.input("fecha_debaja", Jpo.DATE);
			pResult.input("motivo_debaja", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("parametro_5", Jpo.STRING);
			pResult.input("parametro_6", Jpo.STRING);
			pResult.input("azure_id_codeco", Jpo.STRING);
			pResult.input("azure_id_telex", Jpo.STRING);
			pResult.input("sftp_id", Jpo.STRING);
			pResult.input("extension_archivo_enviar", Jpo.STRING);
			pResult.input("minutos_trancurridos", Jpo.INTEGER);
			pResult.input("sub_unidades_json", Jpo.STRING);
			pResult.input("gate_in_empty_movimiento_id", Jpo.DECIMAL);
			pResult.input("gate_in_empty_procedencia_json", Jpo.STRING);
			pResult.input("gate_out_empty_movimiento_id", Jpo.DECIMAL);
			pResult.input("gate_out_empty_procedencia_json", Jpo.STRING);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsseteoEdiCoparnRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsseteoEdiCoparnRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.seteo_edi_coparn_registrar","SDS");
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_canal_recepcion_coparn_id", Jpo.DECIMAL);
			pResult.input("cat_modo_procesar_coparn_id", Jpo.DECIMAL);
			pResult.input("edi_coparn_descripcion", Jpo.STRING);
			pResult.input("azure_id", Jpo.STRING);
			pResult.input("sftp_coparn_id", Jpo.STRING);
			pResult.input("ftp_coparn_id", Jpo.STRING);
			pResult.input("carpeta_coparn_ruta", Jpo.STRING);
			pResult.input("extension_archivo_descargar", Jpo.STRING);
			pResult.input("ruta_mover_edi", Jpo.STRING);
			pResult.input("permitir_crear_prog_nave_automatico", Jpo.CHARACTER);
			pResult.input("permitir_crear_cliente_automatico", Jpo.CHARACTER);
			pResult.input("es_historico", Jpo.CHARACTER);
			pResult.input("fecha_debaja", Jpo.DATE);
			pResult.input("motivo_debaja", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("detalle", Jpo.STRING);
			pResult.input("cat_bkedi_message_type_id", Jpo.DECIMAL);
			pResult.input("cat_owner_edi_booking_id", Jpo.DECIMAL);
			pResult.input("filename_mask", Jpo.STRING);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsseteoEdiCoparnEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsseteoEdiCoparnEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.seteo_edi_coparn_editar","SDS");
			pResult.input("seteo_edi_coparn_id", Jpo.INTEGER);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_canal_recepcion_coparn_id", Jpo.DECIMAL);
			pResult.input("cat_modo_procesar_coparn_id", Jpo.DECIMAL);
			pResult.input("edi_coparn_descripcion", Jpo.STRING);
			pResult.input("azure_id", Jpo.STRING);
			pResult.input("sftp_coparn_id", Jpo.STRING);
			pResult.input("ftp_coparn_id", Jpo.STRING);
			pResult.input("carpeta_coparn_ruta", Jpo.STRING);
			pResult.input("extension_archivo_descargar", Jpo.STRING);
			pResult.input("ruta_mover_edi", Jpo.STRING);
			pResult.input("permitir_crear_prog_nave_automatico", Jpo.CHARACTER);
			pResult.input("permitir_crear_cliente_automatico", Jpo.CHARACTER);
			pResult.input("es_historico", Jpo.CHARACTER);
			pResult.input("fecha_debaja", Jpo.DATE);
			pResult.input("motivo_debaja", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("detalle", Jpo.STRING);
			pResult.input("cat_bkedi_message_type_id", Jpo.DECIMAL);
			pResult.input("cat_owner_edi_booking_id", Jpo.DECIMAL);
			pResult.input("filename_mask", Jpo.STRING);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsseteoEdiCoparnEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsseteoEdiCoparnEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.seteo_edi_coparn_eliminar","SDS");
			pResult.input("seteo_edi_coparn_id", Jpo.INTEGER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsseteoEdiCoparnListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsseteoEdiCoparnListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.seteo_edi_coparn_listar","SDS");
			pResult.input("seteo_edi_coparn_id", Jpo.INTEGER);
			pResult.input("linea_naviera_id", Jpo.INTEGER);
			pResult.input("cat_canal_recepcion_coparn_id", Jpo.DECIMAL);
			pResult.input("cat_modo_procesar_coparn_id", Jpo.DECIMAL);
			pResult.input("edi_coparn_descripcion", Jpo.STRING);
			pResult.input("azure_id", Jpo.STRING);
			pResult.input("sftp_coparn_id", Jpo.STRING);
			pResult.input("ftp_coparn_id", Jpo.STRING);
			pResult.input("carpeta_coparn_ruta", Jpo.STRING);
			pResult.input("extension_archivo_descargar", Jpo.STRING);
			pResult.input("ruta_mover_edi", Jpo.STRING);
			pResult.input("permitir_crear_prog_nave_automatico", Jpo.CHARACTER);
			pResult.input("permitir_crear_cliente_automatico", Jpo.CHARACTER);
			pResult.input("es_historico", Jpo.CHARACTER);
			pResult.input("fecha_debaja_min", Jpo.DATE);
			pResult.input("fecha_debaja_max", Jpo.DATE);
			pResult.input("motivo_debaja", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsseteoEdiCoparnObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsseteoEdiCoparnObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.seteo_edi_coparn_obtener","SDS");
			pResult.input("seteo_edi_coparn_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsfleetSetEdiList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsfleetSetEdiList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.fleet_set_edi_list","SDS");
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("business_unit_id", Jpo.DECIMAL);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("status_id", Jpo.INTEGER);
			pResult.input("date_registration_min", Jpo.DATE);
			pResult.input("date_registration_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsfleetEquipmentEdiServiceSettingDelete", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsfleetEquipmentEdiServiceSettingDelete(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.fleet_equipment_edi_service_setting_delete","SDS");
			pResult.input("fleet_equipment_edi_setting_id", Jpo.INTEGER);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsfleetEquipmentEdiServiceSettingRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsfleetEquipmentEdiServiceSettingRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.fleet_equipment_edi_service_setting_register","SDS");
			pResult.input("business_unit_id", Jpo.DECIMAL);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("send_mode_id", Jpo.DECIMAL);
			pResult.input("send_file_extension", Jpo.STRING);
			pResult.input("description", Jpo.STRING);
			pResult.input("azure_storage_chassis_edi_id", Jpo.DECIMAL);
			pResult.input("sftp_chassis_edi_id", Jpo.DECIMAL);
			pResult.input("is_historical", Jpo.CHARACTER);
			pResult.input("cancellation_reason", Jpo.STRING);
			pResult.input("user_registration_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("filename_mask", Jpo.STRING);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_status", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsfleetEquipmentEdiServiceSettingEdit", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsfleetEquipmentEdiServiceSettingEdit(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.fleet_equipment_edi_service_setting_edit","SDS");
			pResult.input("fleet_equipment_edi_setting_id", Jpo.INTEGER);
			pResult.input("business_unit_id", Jpo.DECIMAL);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("send_mode_id", Jpo.DECIMAL);
			pResult.input("send_file_extension", Jpo.STRING);
			pResult.input("description", Jpo.STRING);
			pResult.input("azure_storage_chassis_edi_id", Jpo.DECIMAL);
			pResult.input("sftp_chassis_edi_id", Jpo.DECIMAL);
			pResult.input("is_historical", Jpo.CHARACTER);
			pResult.input("cancellation_reason", Jpo.STRING);
			pResult.input("user_modification_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("filename_mask", Jpo.STRING);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_status", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsfleetEquipmentEdiServiceSettingGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsfleetEquipmentEdiServiceSettingGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.fleet_equipment_edi_service_setting_get","SDS");
			pResult.input("fleet_equipment_edi_setting_id", Jpo.INTEGER);
			pResult.input("language_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}