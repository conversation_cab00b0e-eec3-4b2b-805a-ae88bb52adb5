package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ProductRegisterInput;
import com.maersk.sd1.sds.dto.ProductRegisterOutput;
import com.maersk.sd1.sds.service.ProductRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductRegisterControllerTest {

    @Mock
    private ProductRegisterService productRegisterService;

    @InjectMocks
    private ProductRegisterController productRegisterController;

    private ProductRegisterInput.Root request;

    @BeforeEach
    public void setUp() {
        request = new ProductRegisterInput.Root();
        ProductRegisterInput.Prefix prefix = new ProductRegisterInput.Prefix();
        ProductRegisterInput.Input input = new ProductRegisterInput.Input();
        input.setProductCode("P001");
        input.setProductName("Product Name");
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void testSdsProductRegister_Success() {
        ProductRegisterOutput output = new ProductRegisterOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Registration completed successfully.");
        when(productRegisterService.registerProduct(any(ProductRegisterInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<ProductRegisterOutput>> response =
                productRegisterController.sdsProductRegister(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("Registration completed successfully.", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testSdsProductRegister_BadRequest() {
        ResponseEntity<ResponseController<ProductRegisterOutput>> response =
                productRegisterController.sdsProductRegister(null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }
}
