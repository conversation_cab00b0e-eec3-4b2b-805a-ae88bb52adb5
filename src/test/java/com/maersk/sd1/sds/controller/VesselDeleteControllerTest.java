package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.VesselDeleteInput;
import com.maersk.sd1.sds.dto.VesselDeleteOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.service.VesselDeleteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class VesselDeleteControllerTest {

    @Mock
    private VesselDeleteService vesselDeleteService;

    @InjectMocks
    private VesselDeleteController vesselDeleteController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testDeleteVesselSuccess() {
        VesselDeleteInput.Root request = new VesselDeleteInput.Root();
        VesselDeleteInput.Input input = new VesselDeleteInput.Input();
        input.setVesselId(3710);
        input.setUserModificationId(1);
        input.setLanguageId(1);
        request.setPrefix(new VesselDeleteInput.Prefix());
        request.getPrefix().setInput(input);

        VesselDeleteOutput output = new VesselDeleteOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Record successfully deleted.");

        when(vesselDeleteService.deleteVessel(3710, 1, 1)).thenReturn(output);

        ResponseEntity<ResponseController<VesselDeleteOutput>> response = vesselDeleteController.deleteVessel(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Record successfully deleted.", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testDeleteVesselBadRequest() {
        VesselDeleteInput.Root request = new VesselDeleteInput.Root();

        ResponseEntity<ResponseController<VesselDeleteOutput>> response = vesselDeleteController.deleteVessel(request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(null, response.getBody().getResult().getRespEstado());
    }

    @Test
    void testDeleteVesselException() {
        VesselDeleteInput.Root request = new VesselDeleteInput.Root();
        VesselDeleteInput.Input input = new VesselDeleteInput.Input();
        input.setVesselId(3710);
        input.setUserModificationId(1);
        input.setLanguageId(1);
        request.setPrefix(new VesselDeleteInput.Prefix());
        request.getPrefix().setInput(input);

        when(vesselDeleteService.deleteVessel(3710, 1, 1)).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<VesselDeleteOutput>> response = vesselDeleteController.deleteVessel(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Database error", response.getBody().getResult().getRespMensaje());
    }
}