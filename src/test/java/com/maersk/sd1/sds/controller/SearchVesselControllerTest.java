package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.SearchVesselInput;
import com.maersk.sd1.sds.dto.SearchVesselOutputDTO;
import com.maersk.sd1.sds.service.SearchVesselService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class SearchVesselControllerTest {

    @Mock
    private SearchVesselService searchVesselService;

    @InjectMocks
    private SearchVesselController searchVesselController;


    private SearchVesselInput.Root invalidRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        invalidRequest = new SearchVesselInput.Root();
        invalidRequest.setPrefix(new SearchVesselInput.Prefix());
    }

    @Test
    void given_ValidRequest_When_SearchVesselIsCalled_Then_ReturnVesselList() {
        SearchVesselInput.Root request = new SearchVesselInput.Root();
        SearchVesselInput.Input input = new SearchVesselInput.Input();
        input.setName("Test Vessel");
        request.setPrefix(new SearchVesselInput.Prefix());
        request.getPrefix().setInput(input);
        List<SearchVesselOutputDTO> mockResponse = List.of(SearchVesselOutputDTO.builder().id(1).build());
        when(searchVesselService.searchVessel("Test Vessel")).thenReturn(mockResponse);


        ResponseEntity<ResponseController<List<SearchVesselOutputDTO>>> response = searchVesselController.searchVessel(request);

        assertEquals(200, response.getStatusCode().value(), "Expected HTTP 200 OK");
        assertNotNull(response.getBody(), "Response body should not be null");
        assertNotNull(response.getBody().getResult(), "Response data should not be null");
        assertEquals(1, response.getBody().getResult().getFirst().getId(), "Response should match");
    }

    @Test
    void given_InvalidRequest_When_SearchVesselIsCalled_Then_ReturnBadRequest() {
        ResponseEntity<ResponseController<List<SearchVesselOutputDTO>>> response = searchVesselController.searchVessel(invalidRequest);

        assertEquals(400, response.getStatusCode().value(), "Expected HTTP 400 Bad Request");
        assertNotNull(response.getBody(), "Response body should not be null");
        assertNotNull(response.getBody().getResult(), "Response data should not be null");
        assertTrue(response.getBody().getResult().isEmpty(), "Response should be empty");
    }

    @Test
    void given_ExceptionOccurs_When_SearchVesselIsCalled_Then_ReturnInternalServerError() {
        SearchVesselInput.Root request = new SearchVesselInput.Root();
        SearchVesselInput.Input input = new SearchVesselInput.Input();
        input.setName("Test Vessel");
        request.setPrefix(new SearchVesselInput.Prefix());
        request.getPrefix().setInput(input);
        when(searchVesselService.searchVessel(anyString())).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<List<SearchVesselOutputDTO>>> response = searchVesselController.searchVessel(request);

        assertEquals(500, response.getStatusCode().value(), "Expected HTTP 500 Internal Server Error");
        assertNotNull(response.getBody(), "Response body should not be null");
        assertNotNull(response.getBody().getResult(), "Response data should not be null");
        assertTrue(response.getBody().getResult().isEmpty(), "Response should be empty when exception occurs");
    }
}
