package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselObtainInput;
import com.maersk.sd1.sds.dto.VesselObtainOutput;
import com.maersk.sd1.sds.service.VesselObtainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VesselObtainControllerTest {

    @Mock
    private VesselObtainService vesselObtainService;

    @InjectMocks
    private VesselObtainController vesselObtainController;

    private VesselObtainInput.Root request;
    private VesselObtainOutput output;

    @BeforeEach
    void setUp() {
        request = new VesselObtainInput.Root();
        VesselObtainInput.Input input = new VesselObtainInput.Input();
        input.setNaveId(12345);
        VesselObtainInput.Prefix prefix = new VesselObtainInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        output = new VesselObtainOutput();
    }

    @Test
    void Given_ValidNaveId_When_ObtainVesselIsCalled_Then_VesselIsReturnedSuccessfully() {
        // Arrange
        when(vesselObtainService.getVessel(12345)).thenReturn(List.of(output));

        // Act
        ResponseEntity<ResponseController<List<VesselObtainOutput>>> response = vesselObtainController.obtainVessel(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        verify(vesselObtainService, times(1)).getVessel(12345);
    }

    @Test
    void Given_InvalidNaveId_When_ObtainVesselFails_Then_ErrorMessageIsReturned() {
        // Arrange
        when(vesselObtainService.getVessel(12345)).thenThrow(new RuntimeException("Vessel not found"));

        // Act
        ResponseEntity<ResponseController<List<VesselObtainOutput>>> response = vesselObtainController.obtainVessel(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        verify(vesselObtainService, times(1)).getVessel(12345);
    }

}
