package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursRegisterInput;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursRegisterOutput;
import com.maersk.sd1.sds.service.EstimateEmrCostManHoursRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EstimateEmrCostManHoursRegisterControllerTest {

    @Mock
    private EstimateEmrCostManHoursRegisterService service;


    @InjectMocks
    private EstimateEmrCostManHoursRegisterController controller;

    private EstimateEmrCostManHoursRegisterInput.Root validRequest;
    private EstimateEmrCostManHoursRegisterOutput validOutput;

    @BeforeEach
    void setUp() {
        EstimateEmrCostManHoursRegisterInput.Input input = new EstimateEmrCostManHoursRegisterInput.Input();
        input.setUnidadNegocioId(1);
        input.setSubUnidadNegocioId(2);
        input.setCatTipoEstimadoId(3);
        input.setCostoHoraHombre(100.0);
        input.setActivo(true);

        EstimateEmrCostManHoursRegisterInput.Prefix prefix = new EstimateEmrCostManHoursRegisterInput.Prefix();
        prefix.setInput(input);

        validRequest = new EstimateEmrCostManHoursRegisterInput.Root();
        validRequest.setPrefix(prefix);

        validOutput = new EstimateEmrCostManHoursRegisterOutput();
        validOutput.setRespEstado(1);
        validOutput.setRespMensaje("Success");
        validOutput.setRespNewId(123);
    }

    @Test
    void given_ValidRequest_When_ServiceReturnsOutput_Then_ReturnsStatus200() {
        // Arrange
        when(service.registerCostManHour(any(EstimateEmrCostManHoursRegisterInput.Input.class))).thenReturn(validOutput);

        // Act
        ResponseEntity<ResponseController<EstimateEmrCostManHoursRegisterOutput>> response = controller.register(validRequest);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
        assertEquals(123, response.getBody().getResult().getRespNewId());
        verify(service, times(1)).registerCostManHour(any(EstimateEmrCostManHoursRegisterInput.Input.class));
    }

    @Test
    void given_ValidRequest_When_ServiceThrowsException_Then_ReturnsStatus500() {
        // Arrange
        when(service.registerCostManHour(any(EstimateEmrCostManHoursRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        ResponseEntity<ResponseController<EstimateEmrCostManHoursRegisterOutput>> response = controller.register(validRequest);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("java.lang.RuntimeException: Test exception", response.getBody().getResult().getRespMensaje());
        assertEquals(0, response.getBody().getResult().getRespNewId());
        verify(service, times(1)).registerCostManHour(any(EstimateEmrCostManHoursRegisterInput.Input.class));
    }

    @Test
    void given_InvalidRequest_When_RequestBodyIsNull_Then_ReturnsStatus500() {
        // Act
        ResponseEntity<ResponseController<EstimateEmrCostManHoursRegisterOutput>> response = controller.register(null);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
    }
}