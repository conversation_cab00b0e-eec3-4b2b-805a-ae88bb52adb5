package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.TruckRegisterInput;
import com.maersk.sd1.sds.dto.TruckRegisterOutput;
import com.maersk.sd1.sds.service.TruckRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class TruckRegisterControllerTest {

    @Mock
    private TruckRegisterService truckRegisterService;

    @InjectMocks
    private TruckRegisterController truckRegisterController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testRegisterTruckSuccess() {
        TruckRegisterInput.Root request = new TruckRegisterInput.Root();
        TruckRegisterInput.Input input = new TruckRegisterInput.Input();
        input.setPlaca("ABC123");
        input.setCargaUtil(BigDecimal.valueOf(1000.0));
        input.setModelo("Model X");
        input.setPesoNeto(BigDecimal.valueOf(2000.0));
        input.setPesoBruto(BigDecimal.valueOf(3000.0));
        input.setEmpresaTransporteId(1);
        input.setActivo(true);
        input.setUsuarioRegistroId(1);
        input.setIdiomaId(1);
        request.setPrefix(new TruckRegisterInput.Prefix());
        request.getPrefix().setInput(input);

        TruckRegisterOutput output = new TruckRegisterOutput();
        output.setRespMensaje("Success");
        output.setRespEstado(1);

        when(truckRegisterService.registerTruck(any(TruckRegisterInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<TruckRegisterOutput>> response = truckRegisterController.registerTruck(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals("Success", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
        assertEquals(1, response.getBody().getResult().getRespEstado());
    }

    @Test
    void testRegisterTruckException() {
        TruckRegisterInput.Root request = new TruckRegisterInput.Root();
        TruckRegisterInput.Input input = new TruckRegisterInput.Input();
        input.setPlaca("ABC123");
        input.setCargaUtil(BigDecimal.valueOf(1000.0));
        input.setModelo("Model X");
        input.setPesoNeto(BigDecimal.valueOf(2000.0));
        input.setPesoBruto(BigDecimal.valueOf(3000.0));
        input.setEmpresaTransporteId(1);
        input.setActivo(true);
        input.setUsuarioRegistroId(1);
        input.setIdiomaId(1);
        request.setPrefix(new TruckRegisterInput.Prefix());
        request.getPrefix().setInput(input);

        when(truckRegisterService.registerTruck(any(TruckRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Test Exception"));

        ResponseEntity<ResponseController<TruckRegisterOutput>> response = truckRegisterController.registerTruck(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals("java.lang.RuntimeException: Test Exception", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
        assertEquals(0, response.getBody().getResult().getRespEstado());
    }
}