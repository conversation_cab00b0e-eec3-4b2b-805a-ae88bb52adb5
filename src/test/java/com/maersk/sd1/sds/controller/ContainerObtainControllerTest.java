package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.controller.dto.ContainerObtainInput;
import com.maersk.sd1.sds.dto.ContainerDetailsFinalDTO;
import com.maersk.sd1.sds.service.ContainerObtainService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class ContainerObtainControllerTest {

    @Mock
    private ContainerObtainService containerObtainService;

    @InjectMocks
    private ContainerObtainController containerObtainController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidRequest_When_ContainerIdIsProvided_Then_ReturnOkResponse() {
        // Arrange
        ContainerObtainInput.Root request = new ContainerObtainInput.Root();
        ContainerObtainInput.Prefix prefix = new ContainerObtainInput.Prefix();
        ContainerObtainInput.Input input = new ContainerObtainInput.Input();
        input.setContainerId(123);
        prefix.setInput(input);
        request.setPrefix(prefix);

        ContainerDetailsFinalDTO output = new ContainerDetailsFinalDTO();
        when(containerObtainService.obtainContainer(123)).thenReturn(output);

        // Act
        ResponseEntity<ResponseController<List<ContainerDetailsFinalDTO>>> response = containerObtainController.containerObtain(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().size());
        verify(containerObtainService).obtainContainer(123);
    }

    @Test
    void given_InvalidRequest_When_RequestIsNull_Then_ReturnBadRequestResponse() {
        // Act
        ResponseEntity<ResponseController<List<ContainerDetailsFinalDTO>>> response = containerObtainController.containerObtain(null);

        // Assert
        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure.", response.getBody().getMessage());
    }

    @Test
    void given_InvalidRequest_When_PrefixIsNull_Then_ReturnBadRequestResponse() {
        // Arrange
        ContainerObtainInput.Root request = new ContainerObtainInput.Root();

        // Act
        ResponseEntity<ResponseController<List<ContainerDetailsFinalDTO>>> response = containerObtainController.containerObtain(request);

        // Assert
        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure.", response.getBody().getMessage());
    }

    @Test
    void given_InvalidRequest_When_ContainerIdIsNull_Then_ReturnBadRequestResponse() {
        // Arrange
        ContainerObtainInput.Root request = new ContainerObtainInput.Root();
        ContainerObtainInput.Prefix prefix = new ContainerObtainInput.Prefix();
        ContainerObtainInput.Input input = new ContainerObtainInput.Input();
        prefix.setInput(input);
        request.setPrefix(prefix);

        // Act
        ResponseEntity<ResponseController<List<ContainerDetailsFinalDTO>>> response = containerObtainController.containerObtain(request);

        // Assert
        assertEquals(400, response.getStatusCode().value());
        assertEquals("containerId cannot be null.", response.getBody().getMessage());
    }

    @Test
    void given_ValidRequest_When_ServiceThrowsException_Then_ReturnInternalServerErrorResponse() {
        // Arrange
        ContainerObtainInput.Root request = new ContainerObtainInput.Root();
        ContainerObtainInput.Prefix prefix = new ContainerObtainInput.Prefix();
        ContainerObtainInput.Input input = new ContainerObtainInput.Input();
        input.setContainerId(123);
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(containerObtainService.obtainContainer(123)).thenThrow(new RuntimeException("Service exception"));

        // Act
        ResponseEntity<ResponseController<List<ContainerDetailsFinalDTO>>> response = containerObtainController.containerObtain(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        verify(containerObtainService).obtainContainer(123);
    }
}