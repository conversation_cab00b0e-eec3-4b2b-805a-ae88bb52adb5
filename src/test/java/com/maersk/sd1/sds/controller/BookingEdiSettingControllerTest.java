package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingEdiSettingInputDTO;
import com.maersk.sd1.sds.dto.BookingEdiSettingOutputDTO;
import com.maersk.sd1.sds.service.BookingEdiSettingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BookingEdiSettingControllerTest {

    @Mock
    private BookingEdiSettingService bookingEdiSettingService;

    @InjectMocks
    private BookingEdiSettingController bookingEdiSettingController;

    private BookingEdiSettingInputDTO.Root request;

    @BeforeEach
    void setUp() {
        request = new BookingEdiSettingInputDTO.Root();
        BookingEdiSettingInputDTO.Prefix prefix = new BookingEdiSettingInputDTO.Prefix();
        BookingEdiSettingInputDTO.Input input = new BookingEdiSettingInputDTO.Input();
        input.setBkEdiDescription("Description");
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void testCreateBookingEdiSettingRegisterSuccess() {
        BookingEdiSettingOutputDTO outputDTO = new BookingEdiSettingOutputDTO();
        outputDTO.setRespEstado(1);
        outputDTO.setRespMensaje("Registration completed successfully.");
        when(bookingEdiSettingService.createBookingEdiSetting(any(BookingEdiSettingInputDTO.Input.class)))
                .thenReturn(outputDTO);

        ResponseEntity<ResponseController<BookingEdiSettingOutputDTO>> response =
                bookingEdiSettingController.createBookingEdiSettingRegister(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("Registration completed successfully.", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testCreateBookingEdiSettingRegisterBadRequest() {
        ResponseEntity<ResponseController<BookingEdiSettingOutputDTO>> response =
                bookingEdiSettingController.createBookingEdiSettingRegister(null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }
}