package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.CargoDocumentObtainInput;
import com.maersk.sd1.sds.controller.dto.CargoDocumentObtainOutput;
import com.maersk.sd1.sds.service.CargoDocumentObtainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CargoDocumentObtainControllerTest {

    @Mock
    private CargoDocumentObtainService cargoDocumentObtainService;

    @InjectMocks
    private CargoDocumentObtainController cargoDocumentObtainController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidRequest_WhenCargoDocumentObtain_ThenReturn200() {
        CargoDocumentObtainInput.Root request = new CargoDocumentObtainInput.Root();
        CargoDocumentObtainInput.Prefix prefix = new CargoDocumentObtainInput.Prefix();
        CargoDocumentObtainInput.Input input = new CargoDocumentObtainInput.Input();
        input.setSubBusinessUnitId(1);
        input.setCargoDocumentId(1);
        input.setLanguageId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        CargoDocumentObtainOutput output = new CargoDocumentObtainOutput();
        when(cargoDocumentObtainService.cargoDocumentObtain(1, 1, 1)).thenReturn(output);

        ResponseEntity<ResponseController<CargoDocumentObtainOutput>> response = cargoDocumentObtainController.cargoDocumentObtain(request);

        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
    }

    @Test
    void givenInvalidRequest_WhenCargoDocumentObtain_ThenReturn400() {
        CargoDocumentObtainInput.Root request = new CargoDocumentObtainInput.Root();

        ResponseEntity<ResponseController<CargoDocumentObtainOutput>> response = cargoDocumentObtainController.cargoDocumentObtain(request);

        assertEquals(400, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals("Invalid input payload structure.", response.getBody().getMessage());
    }

    @Test
    void givenServiceThrowsException_WhenCargoDocumentObtain_ThenReturn500() {
        CargoDocumentObtainInput.Root request = new CargoDocumentObtainInput.Root();
        CargoDocumentObtainInput.Prefix prefix = new CargoDocumentObtainInput.Prefix();
        CargoDocumentObtainInput.Input input = new CargoDocumentObtainInput.Input();
        input.setSubBusinessUnitId(1);
        input.setCargoDocumentId(1);
        input.setLanguageId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(cargoDocumentObtainService.cargoDocumentObtain(1, 1, 1)).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<CargoDocumentObtainOutput>> response = cargoDocumentObtainController.cargoDocumentObtain(request);

        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
    }
}