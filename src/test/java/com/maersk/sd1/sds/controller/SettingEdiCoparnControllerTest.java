package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.SettingEdiCoparnObtainInputDTO;
import com.maersk.sd1.sds.controller.dto.SettingEdiCoparnObtainOutputDTO;
import com.maersk.sd1.sds.service.SettingEdiCoparnService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

class SettingEdiCoparnControllerTest {

    @Mock
    private SettingEdiCoparnService settingEdiCoparnService;

    @InjectMocks
    private SettingEdiCoparnController settingEdiCoparnController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidRequest_WhenObtenerSeteoEdiCoparn_ThenReturnSuccessResponse() {
        SettingEdiCoparnObtainInputDTO.Root request = new SettingEdiCoparnObtainInputDTO.Root();
        SettingEdiCoparnObtainInputDTO.Prefix prefix = new SettingEdiCoparnObtainInputDTO.Prefix();
        SettingEdiCoparnObtainInputDTO.Input input = new SettingEdiCoparnObtainInputDTO.Input();
        input.setSettingEdiCoparnId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        SettingEdiCoparnObtainOutputDTO serviceOutput = new SettingEdiCoparnObtainOutputDTO();
        serviceOutput.setRespStatus(1);
        serviceOutput.setRespMessage("Success");

        when(settingEdiCoparnService.obtainSettingEdiCoparn(anyInt())).thenReturn(serviceOutput);

        ResponseEntity<ResponseController<SettingEdiCoparnObtainOutputDTO>> response = settingEdiCoparnController.obtenerSeteoEdiCoparn(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespStatus());
        assertEquals("Success", response.getBody().getResult().getRespMessage());
    }

    @Test
    void givenInvalidRequest_WhenObtenerSeteoEdiCoparn_ThenReturnBadRequest() {
        SettingEdiCoparnObtainInputDTO.Root request = new SettingEdiCoparnObtainInputDTO.Root();

        ResponseEntity<ResponseController<SettingEdiCoparnObtainOutputDTO>> response = settingEdiCoparnController.obtenerSeteoEdiCoparn(request);

        assertEquals(400, response.getStatusCode().value());
    }

    @Test
    void givenException_WhenObtenerSeteoEdiCoparn_ThenReturnInternalServerError() {
        SettingEdiCoparnObtainInputDTO.Root request = new SettingEdiCoparnObtainInputDTO.Root();
        SettingEdiCoparnObtainInputDTO.Prefix prefix = new SettingEdiCoparnObtainInputDTO.Prefix();
        SettingEdiCoparnObtainInputDTO.Input input = new SettingEdiCoparnObtainInputDTO.Input();
        input.setSettingEdiCoparnId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(settingEdiCoparnService.obtainSettingEdiCoparn(anyInt())).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<SettingEdiCoparnObtainOutputDTO>> response = settingEdiCoparnController.obtenerSeteoEdiCoparn(request);

        assertEquals(500, response.getStatusCode().value());
    }
}