package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesInputDTO;
import com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesOutputDTO;
import com.maersk.sd1.sds.service.BookingEdiProcessService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class BookingEdiProcessControllerTest {

    @InjectMocks
    private BookingEdiProcessController bookingEdiProcessController;

    @Mock
    private BookingEdiProcessService bookingEdiProcessService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidRequest_whenProcessBookingEdiUpdates_thenReturnOk() {
        BookingEdiProcessUpdatesInputDTO.Root request = new BookingEdiProcessUpdatesInputDTO.Root();
        request.getPrefix().getInput().setUsuarioRegistroId(1);
        request.getPrefix().getInput().setBookingId(1);
        request.getPrefix().getInput().setDocumentoCargaId(1);

        doNothing().when(bookingEdiProcessService).processUpdatesDocumentDetail(1, 1, 1);

        ResponseEntity<BookingEdiProcessUpdatesOutputDTO.Root> response = bookingEdiProcessController.processBookingEdiUpdates(request);

        verify(bookingEdiProcessService, times(1)).processUpdatesDocumentDetail(1, 1, 1);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().getPrefix().getOutput().getDocumentoCargaId());
    }

    @Test
    void givenInvalidRequest_whenProcessBookingEdiUpdates_thenReturnBadRequest() {
        BookingEdiProcessUpdatesInputDTO.Root request = new BookingEdiProcessUpdatesInputDTO.Root();
        request.getPrefix().getInput().setUsuarioRegistroId(0);
        request.getPrefix().getInput().setBookingId(0);
        request.getPrefix().getInput().setDocumentoCargaId(0);

        doThrow(new RuntimeException("Invalid data")).when(bookingEdiProcessService).processUpdatesDocumentDetail(0, 0, 0);

        ResponseEntity<BookingEdiProcessUpdatesOutputDTO.Root> response = bookingEdiProcessController.processBookingEdiUpdates(request);

        verify(bookingEdiProcessService, times(1)).processUpdatesDocumentDetail(0, 0, 0);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void givenNullRequest_whenProcessBookingEdiUpdates_thenReturnBadRequest() {
        BookingEdiProcessUpdatesInputDTO.Root request = null;

        ResponseEntity<BookingEdiProcessUpdatesOutputDTO.Root> response = bookingEdiProcessController.processBookingEdiUpdates(request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }
}
