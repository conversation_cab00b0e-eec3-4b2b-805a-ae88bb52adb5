package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ProductUpdateInput;
import com.maersk.sd1.sds.dto.ProductUpdateOutput;
import com.maersk.sd1.sds.service.ProductUpdateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ProductUpdateControllerTest {

    @Mock
    private ProductUpdateService productUpdateService;

    @InjectMocks
    private ProductUpdateController productUpdateController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdateProductSuccess() {
        ProductUpdateInput.Root request = new ProductUpdateInput.Root();
        ProductUpdateInput.Input input = new ProductUpdateInput.Input();
        input.setProductId(1);
        request.setPrefix(new ProductUpdateInput.Prefix());
        request.getPrefix().setInput(input);

        ProductUpdateOutput output = new ProductUpdateOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Product updated successfully");

        when(productUpdateService.updateProduct(input)).thenReturn(output);

        ResponseEntity<ResponseController<ProductUpdateOutput>> response = productUpdateController.updateProduct(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Product updated successfully", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testUpdateProductBadRequest() {
        ProductUpdateInput.Root request = new ProductUpdateInput.Root();

        ResponseEntity<ResponseController<ProductUpdateOutput>> response = productUpdateController.updateProduct(request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(null, response.getBody().getResult().getRespEstado());
    }

    @Test
    void testUpdateProductException() {
        ProductUpdateInput.Root request = new ProductUpdateInput.Root();
        ProductUpdateInput.Input input = new ProductUpdateInput.Input();
        input.setProductId(1);
        request.setPrefix(new ProductUpdateInput.Prefix());
        request.getPrefix().setInput(input);

        when(productUpdateService.updateProduct(input)).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<ProductUpdateOutput>> response = productUpdateController.updateProduct(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("java.lang.RuntimeException: Database error", response.getBody().getResult().getRespMensaje());
    }
}