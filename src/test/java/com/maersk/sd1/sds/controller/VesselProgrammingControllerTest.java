package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ProgrammingVesselRegisterInput;
import com.maersk.sd1.sds.dto.ProgrammingVesselRegisterOutput;
import com.maersk.sd1.sds.service.VesselProgrammingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VesselProgrammingControllerTest {

    @Mock
    private VesselProgrammingService vesselProgrammingService;

    @InjectMocks
    private VesselProgrammingController vesselProgrammingController;

    private ProgrammingVesselRegisterInput.Root validRequest;
    private ProgrammingVesselRegisterInput.Input validInput;
    private ProgrammingVesselRegisterOutput validOutput;

    @BeforeEach
    void setUp() {
        validInput = new ProgrammingVesselRegisterInput.Input();
        validInput.setBusinessUnitId(1L);
        validInput.setSubBusinessUnitId(2L);
        validInput.setVesselId(3);
        validInput.setVoyage("VOY123");
        validInput.setEtaDate("01/02/2024");
        validInput.setEtdDate("05/02/2024");
        validInput.setShippingAgencyCompanyId(4L);
        validInput.setOpePortCompanyId(5L);
        validInput.setActive(true);
        validInput.setUserRegistrationId(6L);
        validInput.setLanguageId(1);

        validRequest = new ProgrammingVesselRegisterInput.Root();
        validRequest.setPrefix(new ProgrammingVesselRegisterInput.Prefix());
        validRequest.getPrefix().setInput(validInput);

        validOutput = new ProgrammingVesselRegisterOutput();
        validOutput.setRespEstado(1);
        validOutput.setRespMensaje("Success");
        validOutput.setRespNewId(100);

    }

    @Test
    void GivenValidRequest_WhenRegisterVesselProgramming_ThenReturnSuccessResponse() {
        when(vesselProgrammingService.registerVesselProgramming(any(ProgrammingVesselRegisterInput.Input.class)))
                .thenReturn(validOutput);

        ResponseEntity<ResponseController<ProgrammingVesselRegisterOutput>> response =
                vesselProgrammingController.register(validRequest);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void GivenServiceThrowsException_WhenRegisterVesselProgramming_ThenReturnErrorResponse() {
        when(vesselProgrammingService.registerVesselProgramming(any(ProgrammingVesselRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Service Failure"));

        ResponseEntity<ResponseController<ProgrammingVesselRegisterOutput>> response =
                vesselProgrammingController.register(validRequest);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Service Failure", response.getBody().getResult().getRespMensaje());
    }
}