package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.DepotObtainInput;
import com.maersk.sd1.sds.controller.dto.DepotObtainOutput;
import com.maersk.sd1.sds.service.DepotObtainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class DepotObtainControllerTest {

    @Mock
    private DepotObtainService depotObtainService;

    @InjectMocks
    private DepotObtainController depotObtainController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidRequest_When_ObtainDepot_Then_ReturnsDepotData() {
        DepotObtainInput.Root request = new DepotObtainInput.Root();
        DepotObtainInput.Prefix prefix = new DepotObtainInput.Prefix();
        DepotObtainInput.Input input = new DepotObtainInput.Input();
        input.setDepotId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        DepotObtainOutput output = new DepotObtainOutput(1, 1, "code", "name", "address", true, null, null, 1, true, 1, 1);
        when(depotObtainService.getDepotById(1)).thenReturn(output);

        ResponseEntity<ResponseController<DepotObtainOutput>> response = depotObtainController.obtainDepot(request);

        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void Given_NullRequest_When_ObtainDepot_Then_ReturnsBadRequest() {
        ResponseEntity<ResponseController<DepotObtainOutput>> response = depotObtainController.obtainDepot(null);

        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure.", response.getBody().getMessage());
    }

    @Test
    void Given_NullPrefix_When_ObtainDepot_Then_ReturnsBadRequest() {
        DepotObtainInput.Root request = new DepotObtainInput.Root();

        ResponseEntity<ResponseController<DepotObtainOutput>> response = depotObtainController.obtainDepot(request);

        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure.", response.getBody().getMessage());
    }

    @Test
    void Given_NullInput_When_ObtainDepot_Then_ReturnsBadRequest() {
        DepotObtainInput.Root request = new DepotObtainInput.Root();
        DepotObtainInput.Prefix prefix = new DepotObtainInput.Prefix();
        request.setPrefix(prefix);

        ResponseEntity<ResponseController<DepotObtainOutput>> response = depotObtainController.obtainDepot(request);

        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure.", response.getBody().getMessage());
    }

    @Test
    void Given_NullDepotId_When_ObtainDepot_Then_ReturnsBadRequest() {
        DepotObtainInput.Root request = new DepotObtainInput.Root();
        DepotObtainInput.Prefix prefix = new DepotObtainInput.Prefix();
        DepotObtainInput.Input input = new DepotObtainInput.Input();
        prefix.setInput(input);
        request.setPrefix(prefix);

        ResponseEntity<ResponseController<DepotObtainOutput>> response = depotObtainController.obtainDepot(request);

        assertEquals(400, response.getStatusCode().value());
        assertEquals("depotId cannot be null.", response.getBody().getMessage());
    }

    @Test
    void Given_NonExistentDepotId_When_ObtainDepot_Then_ReturnsDepotNotFound() {
        DepotObtainInput.Root request = new DepotObtainInput.Root();
        DepotObtainInput.Prefix prefix = new DepotObtainInput.Prefix();
        DepotObtainInput.Input input = new DepotObtainInput.Input();
        input.setDepotId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(depotObtainService.getDepotById(1)).thenReturn(null);

        ResponseEntity<ResponseController<DepotObtainOutput>> response = depotObtainController.obtainDepot(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals("Depot not found for ID: 1", response.getBody().getMessage());
    }
}