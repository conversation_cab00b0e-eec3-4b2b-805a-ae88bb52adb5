package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.PortRegisterInput;
import com.maersk.sd1.sds.dto.PortRegisterOutput;
import com.maersk.sd1.sds.service.PortRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PortRegisterControllerTest {

    @Mock
    private PortRegisterService portRegisterService;

    @InjectMocks
    private PortRegisterController portRegisterController;

    private PortRegisterInput.Root validRequest;
    private PortRegisterOutput expectedOutput;

    @BeforeEach
    void setUp() {
        PortRegisterInput.Input input = new PortRegisterInput.Input();
        input.setPort("DUMMY");
        input.setName("DUMMY NAME");
        input.setCountryId(1);
        input.setActive(true);
        input.setUserRegistrationId(1);
        input.setLanguageId(1);

        PortRegisterInput.Prefix prefix = new PortRegisterInput.Prefix();
        prefix.setInput(input);

        validRequest = new PortRegisterInput.Root();
        validRequest.setPrefix(prefix);

        expectedOutput = new PortRegisterOutput();
        expectedOutput.setRespEstado(1);
        expectedOutput.setRespMensaje("Success");
        expectedOutput.setRespNewId(1001);
    }

    @Test
    void registerPortSuccess() {
        when(portRegisterService.registerPort(
                anyString(), anyString(), anyInt(), anyBoolean(), anyInt(), anyInt()))
                .thenReturn(expectedOutput);

        ResponseEntity<ResponseController<PortRegisterOutput>> response = portRegisterController.registerPort(validRequest);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
        assertEquals(1001, response.getBody().getResult().getRespNewId());

        verify(portRegisterService, times(1)).registerPort(
                "DUMMY", "DUMMY NAME", 1, true, 1, 1);
    }

    @Test
    void registerPortFailureNullPrefix() {
        PortRegisterInput.Root invalidRequest = new PortRegisterInput.Root();

        ResponseEntity<ResponseController<PortRegisterOutput>> response = portRegisterController.registerPort(invalidRequest);

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertTrue(response.getBody().getResult().getRespMensaje().contains("Invalid request"));

        verifyNoInteractions(portRegisterService);
    }

    @Test
    void registerPortFailureNullInput() {
        PortRegisterInput.Root invalidRequest = new PortRegisterInput.Root();
        PortRegisterInput.Prefix prefix = new PortRegisterInput.Prefix();
        invalidRequest.setPrefix(prefix);

        ResponseEntity<ResponseController<PortRegisterOutput>> response = portRegisterController.registerPort(invalidRequest);

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertTrue(response.getBody().getResult().getRespMensaje().contains("Invalid request"));

        verifyNoInteractions(portRegisterService);
    }

    @Test
    void registerPortFailureServiceException() {
        when(portRegisterService.registerPort(
                anyString(), anyString(), anyInt(), anyBoolean(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<PortRegisterOutput>> response = portRegisterController.registerPort(validRequest);

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertTrue(response.getBody().getResult().getRespMensaje().contains("Database error"));

        verify(portRegisterService, times(1)).registerPort(
                anyString(), anyString(), anyInt(), anyBoolean(), anyInt(), anyInt());
    }
}

