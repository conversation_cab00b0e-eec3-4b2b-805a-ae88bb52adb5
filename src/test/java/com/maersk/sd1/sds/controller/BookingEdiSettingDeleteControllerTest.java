package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.BookingEdiSettingDeleteInput;
import com.maersk.sd1.sds.controller.dto.BookingEdiSettingDeleteOutput;
import com.maersk.sd1.sds.service.BookingEdiSettingDeleteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

class BookingEdiSettingDeleteControllerTest {

    @Mock
    private BookingEdiSettingDeleteService bookingEdiSettingDeleteService;

    @InjectMocks
    private BookingEdiSettingDeleteController bookingEdiSettingDeleteController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidRequest_WhenDeleteBookingEdiSetting_ThenReturnSuccessResponse() {
        BookingEdiSettingDeleteInput.Root request = new BookingEdiSettingDeleteInput.Root();
        BookingEdiSettingDeleteInput.Prefix prefix = new BookingEdiSettingDeleteInput.Prefix();
        BookingEdiSettingDeleteInput.Input input = new BookingEdiSettingDeleteInput.Input();
        input.setSettingEdiCoparnId(1);
        input.setUserModificationId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        BookingEdiSettingDeleteOutput serviceOutput = new BookingEdiSettingDeleteOutput();
        serviceOutput.setRespStatus(1);
        serviceOutput.setRespMessage("Success");

        when(bookingEdiSettingDeleteService.deleteBookingEdiSetting(anyInt(), anyInt())).thenReturn(serviceOutput);

        ResponseEntity<ResponseController<BookingEdiSettingDeleteOutput>> response = bookingEdiSettingDeleteController.deleteBookingEdiSetting(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespStatus());
        assertEquals("Success", response.getBody().getResult().getRespMessage());
    }

    @Test
    void givenInvalidRequest_WhenDeleteBookingEdiSetting_ThenReturnBadRequestResponse() {
        BookingEdiSettingDeleteInput.Root request = new BookingEdiSettingDeleteInput.Root();

        ResponseEntity<ResponseController<BookingEdiSettingDeleteOutput>> response = bookingEdiSettingDeleteController.deleteBookingEdiSetting(request);

        assertEquals(400, response.getStatusCode().value());
    }
}