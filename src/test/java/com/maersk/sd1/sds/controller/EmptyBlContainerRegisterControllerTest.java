package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.EmptyBlContainerRegisterInput;
import com.maersk.sd1.sds.dto.EmptyBlContainerRegisterOutput;
import com.maersk.sd1.sds.service.EmptyBlContainerRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmptyBlContainerRegisterControllerTest {

    @Mock
    private EmptyBlContainerRegisterService service;

    @InjectMocks
    private EmptyBlContainerRegisterController controller;

    private EmptyBlContainerRegisterInput.Root request;

    @BeforeEach
    public void setUp() {
        EmptyBlContainerRegisterInput.Input input = new EmptyBlContainerRegisterInput.Input();
        input.setBlNumber("BL123");
        input.setContainerNumber("CON123");
        input.setContainerType(1);
        input.setContainerSize(1);
        input.setLanguageId(1);
        input.setUserRegistrationId(1);
        input.setSubBusinessUnitLocalId(1);

        EmptyBlContainerRegisterInput.Prefix prefix = new EmptyBlContainerRegisterInput.Prefix();
        prefix.setInput(input);

        request = new EmptyBlContainerRegisterInput.Root();
        request.setPrefix(prefix);
    }

    @Test
    void testSdgEmptyBlContainerRegister_success() {
        EmptyBlContainerRegisterOutput output = new EmptyBlContainerRegisterOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");

        when(service.registerEmptyBlContainer(any(EmptyBlContainerRegisterInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<EmptyBlContainerRegisterOutput>> response = controller.sdgEmptyBlContainerRegister(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testSdgEmptyBlContainerRegister_failure() {
        when(service.registerEmptyBlContainer(any(EmptyBlContainerRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Error in registerEmptyBlContainer"));

        ResponseEntity<ResponseController<EmptyBlContainerRegisterOutput>> response = controller.sdgEmptyBlContainerRegister(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("java.lang.RuntimeException: Error in registerEmptyBlContainer", response.getBody().getResult().getRespMensaje());
    }
}