package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ValidateBookingExistenceInput;
import com.maersk.sd1.sds.dto.ValidateBookingExistenceOutput;
import com.maersk.sd1.sds.service.ValidateBookingExistenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ValidateBookingExistenceControllerTest {

    @InjectMocks
    private ValidateBookingExistenceController controller;

    @Mock
    private ValidateBookingExistenceService service;

    private ValidateBookingExistenceInput.Root validRequest;

    @BeforeEach
    void setUp() {
        ValidateBookingExistenceInput.Input input = new ValidateBookingExistenceInput.Input();
        input.setBookingNumber("12345");
        input.setSubBusinessUnitId(1);
        input.setLanguageId(1);

        ValidateBookingExistenceInput.Prefix prefix = new ValidateBookingExistenceInput.Prefix();
        prefix.setInput(input);

        validRequest = new ValidateBookingExistenceInput.Root();
        validRequest.setPrefix(prefix);
    }

    @Test
    void testValidateBookingExistenceServiceSuccess() {
        ValidateBookingExistenceOutput output = new ValidateBookingExistenceOutput();
        output.setBookingNumber("test");

        when(service.validateBookingExistenceService(any(ValidateBookingExistenceInput.Root.class)))
                .thenReturn(List.of(output));

        ResponseEntity<ResponseController<List<ValidateBookingExistenceOutput>>> response =
                controller.validateBookingExistenceService(validRequest);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().size());
        assertEquals(output, response.getBody().getResult().getFirst());
        verify(service, times(1)).validateBookingExistenceService(any(ValidateBookingExistenceInput.Root.class));
    }

    @Test
    void givenNullRequest_whenValidateBookingExistence_thenReturnsBadRequest() {
        ResponseEntity<ResponseController<List<ValidateBookingExistenceOutput>>> response =
                controller.validateBookingExistenceService(null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Request is null", Objects.requireNonNull(response.getBody()).getMessage());
        verifyNoInteractions(service);
    }

    @Test
    void givenServiceThrowsException_whenValidateBookingExistence_thenReturnsInternalServerError() {
        when(service.validateBookingExistenceService(any(ValidateBookingExistenceInput.Root.class)))
                .thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<List<ValidateBookingExistenceOutput>>> response =
                controller.validateBookingExistenceService(validRequest);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("An error occurred while processing the request: Service error",
                Objects.requireNonNull(response.getBody()).getMessage());
        verify(service, times(1)).validateBookingExistenceService(any(ValidateBookingExistenceInput.Root.class));
    }

    @ParameterizedTest
    @MethodSource("provideInvalidRequests")
    void givenInvalidRequest_whenValidateBookingExistence_thenReturnsBadRequest(ValidateBookingExistenceInput.Root invalidRequest) {
        // When
        ResponseEntity<ResponseController<List<ValidateBookingExistenceOutput>>> response =
                controller.validateBookingExistenceService(invalidRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Request is null", response.getBody().getMessage());
        verifyNoInteractions(service); // Ensures service method is never called
    }

    private static Stream<ValidateBookingExistenceInput.Root> provideInvalidRequests() {
        ValidateBookingExistenceInput.Root requestWithNullPrefix = new ValidateBookingExistenceInput.Root();
        requestWithNullPrefix.setPrefix(null);

        ValidateBookingExistenceInput.Root requestWithNullInput = new ValidateBookingExistenceInput.Root();
        requestWithNullInput.setPrefix(new ValidateBookingExistenceInput.Prefix());
        requestWithNullInput.getPrefix().setInput(null);

        return Stream.of(
                null,
                requestWithNullPrefix,
                requestWithNullInput
        );
    }
}
