package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.DemurrageDateRegisterInput;
import com.maersk.sd1.sds.controller.dto.DemurrageDateRegisterOutput;
import com.maersk.sd1.sds.service.DemurrageDateRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class DemurrageDateRegisterControllerTest {

    @Mock
    private DemurrageDateRegisterService demurrageDateRegisterService;

    @InjectMocks
    private DemurrageDateRegisterController demurrageDateRegisterController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidRequestWithValidPrefix_When_RegisterDemurrageDate_Then_ReturnSuccessResponse() {
        DemurrageDateRegisterInput.Root request = new DemurrageDateRegisterInput.Root();
        DemurrageDateRegisterInput.Prefix prefix = new DemurrageDateRegisterInput.Prefix();
        DemurrageDateRegisterInput.Input input = new DemurrageDateRegisterInput.Input();
        input.setSubUnidadNegocioId(1L);
        input.setDocumentoCargaId(100);
        input.setUsuarioRegistroId(999L);
        input.setMotivoId(123L);
        input.setComentarioUsuario("Test comment");
        input.setIdiomaId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        DemurrageDateRegisterOutput output = new DemurrageDateRegisterOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
        output.setRespNewId(1000);

        // Mock
        Mockito.when(demurrageDateRegisterService.registerDemurrageDate(Mockito.any(DemurrageDateRegisterInput.Input.class)))
                .thenReturn(output);

        // Act
        ResponseEntity<ResponseController<DemurrageDateRegisterOutput>> response = demurrageDateRegisterController.registerDemurrageDate(request);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void Given_NullPrefix_When_RegisterDemurrageDate_Then_ReturnErrorResponseWithMessagePrefixCannotBeNull() {
        // Arrange
        DemurrageDateRegisterInput.Root request = new DemurrageDateRegisterInput.Root();

        // Act
        ResponseEntity<ResponseController<DemurrageDateRegisterOutput>> response = demurrageDateRegisterController.registerDemurrageDate(request);

        // Assert
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals("Prefix cannot be null", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void Given_NullInput_When_RegisterDemurrageDate_Then_ReturnErrorResponseWithMessageInputCannotBeNull() {
        // Arrange
        DemurrageDateRegisterInput.Root request = new DemurrageDateRegisterInput.Root();
        DemurrageDateRegisterInput.Prefix prefix = new DemurrageDateRegisterInput.Prefix();
        request.setPrefix(prefix);

        // Act
        ResponseEntity<ResponseController<DemurrageDateRegisterOutput>> response = demurrageDateRegisterController.registerDemurrageDate(request);

        // Assert
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().getResult().getRespEstado());
        assertEquals("Input cannot be null", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void Given_ValidRequestWithServiceFailure_When_RegisterDemurrageDate_Then_ReturnErrorResponseWithExceptionMessage() {
        // Arrange
        DemurrageDateRegisterInput.Root request = new DemurrageDateRegisterInput.Root();
        DemurrageDateRegisterInput.Prefix prefix = new DemurrageDateRegisterInput.Prefix();
        DemurrageDateRegisterInput.Input input = new DemurrageDateRegisterInput.Input();
        input.setSubUnidadNegocioId(1L);
        input.setDocumentoCargaId(100);
        input.setUsuarioRegistroId(999L);
        input.setMotivoId(123L);
        input.setComentarioUsuario("Test comment");
        input.setIdiomaId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);

        // Mock a service failure
        Mockito.when(demurrageDateRegisterService.registerDemurrageDate(Mockito.any(DemurrageDateRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Service failure"));

        // Act
        ResponseEntity<ResponseController<DemurrageDateRegisterOutput>> response = demurrageDateRegisterController.registerDemurrageDate(request);

        // Assert
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().getResult().getRespEstado());
        assertEquals("Service failure", response.getBody().getResult().getRespMensaje());
    }
}
