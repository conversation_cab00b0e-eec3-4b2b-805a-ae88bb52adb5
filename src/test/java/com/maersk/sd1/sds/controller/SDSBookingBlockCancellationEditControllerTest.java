package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.SDSBookingBlockCancellationEditInput;
import com.maersk.sd1.sds.controller.dto.SDSBookingBlockCancellationEditOutput;
import com.maersk.sd1.sds.service.SDSBookingBlockCancellationEditService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SDSBookingBlockCancellationEditControllerTest {

    @InjectMocks
    private SDSBookingBlockCancellationEditController controller;

    @Mock
    private SDSBookingBlockCancellationEditService service;

    private SDSBookingBlockCancellationEditInput.Root request;

    @BeforeEach
    void setUp() {
        request = new SDSBookingBlockCancellationEditInput.Root();
        SDSBookingBlockCancellationEditInput.Prefix prefix = new SDSBookingBlockCancellationEditInput.Prefix();
        SDSBookingBlockCancellationEditInput.Input input = new SDSBookingBlockCancellationEditInput.Input();
        input.setCancelBloqueoBookingId(100);
        input.setComments("Test 123");
        input.setUserModificationId(200);
        input.setLanguageId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void givenValidRequest_WhenEditBookingBlockCancellation_ThenReturnOkResponse() {

        SDSBookingBlockCancellationEditOutput serviceOutput = new SDSBookingBlockCancellationEditOutput();
        serviceOutput.setRespStatus(1);
        serviceOutput.setRespMessage("Ok");
        when(service.editBookingBlockCancellation(any(SDSBookingBlockCancellationEditInput.Input.class)))
                .thenReturn(serviceOutput);

        ResponseEntity<ResponseController<SDSBookingBlockCancellationEditOutput>> response = controller.editBookingBlockCancellation(request);

        Assertions.assertNotNull(response);
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void givenValidRequest_WhenExceptionThrown_ThenReturnInternalServerError() {

        when(service.editBookingBlockCancellation(any(SDSBookingBlockCancellationEditInput.Input.class)))
                .thenThrow(new RuntimeException("Some error"));

        ResponseEntity<ResponseController<SDSBookingBlockCancellationEditOutput>> response = controller.editBookingBlockCancellation(request);

        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }
}