package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.PictureMarkGetInput;
import com.maersk.sd1.sds.dto.PictureMarkGetOutput;
import com.maersk.sd1.sds.service.PictureMarkGetService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PictureMarkGetControllerTest {

    @Mock
    private PictureMarkGetService pictureMarkGetService;

    @InjectMocks
    private PictureMarkGetController controller;

    @Test
    void testSdsPictureMarkGet_ValidRequest() {
        PictureMarkGetInput.Root input = new PictureMarkGetInput.Root();
        PictureMarkGetInput.Prefix prefix = new PictureMarkGetInput.Prefix();
        prefix.setInput(new PictureMarkGetInput.Input());
        input.setPrefix(prefix);

        PictureMarkGetOutput output = new PictureMarkGetOutput();
        output.setUrlPictureMark("http://example.com/pic.jpg");

        when(pictureMarkGetService.getPictureMarkUrl()).thenReturn(output);

        ResponseEntity<ResponseController<List<PictureMarkGetOutput>>> response =
                controller.sdsPictureMarkGet(input);

        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals("http://example.com/pic.jpg", response.getBody().getResult().getFirst().getUrlPictureMark());
    }

    @Test
    void testSdsPictureMarkGet_NullRequest() {
        ResponseEntity<ResponseController<List<PictureMarkGetOutput>>> response =
                controller.sdsPictureMarkGet(null);

        assertEquals(400, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNull(response.getBody().getResult());
    }

    @Test
    void testSdsPictureMarkGet_NullPrefix() {
        PictureMarkGetInput.Root input = new PictureMarkGetInput.Root();
        input.setPrefix(null);

        ResponseEntity<ResponseController<List<PictureMarkGetOutput>>> response =
                controller.sdsPictureMarkGet(input);

        assertEquals(400, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNull(response.getBody().getResult());
    }

    @Test
    void testSdsPictureMarkGet_ServiceThrowsException() {
        PictureMarkGetInput.Root input = new PictureMarkGetInput.Root();
        PictureMarkGetInput.Prefix prefix = new PictureMarkGetInput.Prefix();
        prefix.setInput(new PictureMarkGetInput.Input());
        input.setPrefix(prefix);

        when(pictureMarkGetService.getPictureMarkUrl()).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<List<PictureMarkGetOutput>>> response =
                controller.sdsPictureMarkGet(input);

        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getResult());
        assertNull(response.getBody().getResult().get(0).getUrlPictureMark());
    }
}