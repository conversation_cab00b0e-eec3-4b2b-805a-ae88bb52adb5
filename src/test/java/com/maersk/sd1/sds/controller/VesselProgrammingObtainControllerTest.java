package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingObtainInputDTO;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingObtainOutputDTO;
import com.maersk.sd1.sds.service.VesselProgrammingObtainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

class VesselProgrammingObtainControllerTest {

    @Mock
    private VesselProgrammingObtainService vesselProgrammingObtainService;

    @InjectMocks
    private VesselProgrammingObtainController vesselProgrammingObtainController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidProgrammingShipId_When_ServiceReturnsRecord_Then_ResponseIsOk() {
        List<VesselProgrammingObtainOutputDTO> outputDTO = new ArrayList<>();

        when(vesselProgrammingObtainService.obtainVesselProgramming(anyInt())).thenReturn(outputDTO);

        VesselProgrammingObtainInputDTO.Input input = new VesselProgrammingObtainInputDTO.Input();
        input.setProgrammingShipId(1);

        VesselProgrammingObtainInputDTO.Prefix prefix = new VesselProgrammingObtainInputDTO.Prefix();
        prefix.setInput(input);

        VesselProgrammingObtainInputDTO.Root request = new VesselProgrammingObtainInputDTO.Root();
        request.setPrefix(prefix);

        ResponseEntity<ResponseController<List<VesselProgrammingObtainOutputDTO>>> response = vesselProgrammingObtainController.vesselProgrammingObtain(request);

        assertEquals(200, response.getStatusCode().value());
    }

    @Test
    void given_ValidProgrammingShipId_When_ServiceThrowsException_Then_ResponseIsInternalServerError() {
        when(vesselProgrammingObtainService.obtainVesselProgramming(anyInt())).thenThrow(new RuntimeException("Error occurred"));

        VesselProgrammingObtainInputDTO.Input input = new VesselProgrammingObtainInputDTO.Input();
        input.setProgrammingShipId(1);

        VesselProgrammingObtainInputDTO.Prefix prefix = new VesselProgrammingObtainInputDTO.Prefix();
        prefix.setInput(input);

        VesselProgrammingObtainInputDTO.Root request = new VesselProgrammingObtainInputDTO.Root();
        request.setPrefix(prefix);

        ResponseEntity<ResponseController<List<VesselProgrammingObtainOutputDTO>>> response = vesselProgrammingObtainController.vesselProgrammingObtain(request);

        assertEquals(500, response.getStatusCode().value());
    }
}