package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.controller.dto.DocumentDeleteInput;
import com.maersk.sd1.sds.controller.dto.DocumentDeleteOutput;
import com.maersk.sd1.sds.service.DocumentDeleteService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class DocumentDeleteControllerTest {

    @Mock
    private DocumentDeleteService documentDeleteService;

    @InjectMocks
    private DocumentDeleteController documentDeleteController;

    @Test
    void givenValidRequest_WhenDeleteCargoDocument_ThenReturnSuccessResponse() {
        DocumentDeleteInput.Root request = new DocumentDeleteInput.Root();
        DocumentDeleteInput.Prefix prefix = new DocumentDeleteInput.Prefix();
        DocumentDeleteInput.Input input = new DocumentDeleteInput.Input();
        input.setDocumentCargoId(123);
        input.setLanguageId(1);
        input.setUserId(10);
        prefix.setInput(input);
        request.setPrefix(prefix);

        DocumentDeleteOutput serviceOutput = new DocumentDeleteOutput();
        serviceOutput.setRespStatus(1);
        serviceOutput.setRespMessage("Eliminado correctamente.");

        Mockito.when(documentDeleteService.deleteCargoDocument(123, 1, 10)).thenReturn(serviceOutput);

        ResponseEntity<com.maersk.sd1.common.controller.dto.ResponseController<DocumentDeleteOutput>> responseEntity =
                documentDeleteController.deleteCargoDocument(request);

        assertEquals(200, responseEntity.getStatusCode().value());
    }

    @Test
    void givenException_WhenDeleteCargoDocument_ThenReturnErrorResponse() {
        DocumentDeleteInput.Root request = new DocumentDeleteInput.Root();
        DocumentDeleteInput.Prefix prefix = new DocumentDeleteInput.Prefix();
        DocumentDeleteInput.Input input = new DocumentDeleteInput.Input();
        input.setDocumentCargoId(999);
        input.setLanguageId(2);
        input.setUserId(15);
        prefix.setInput(input);
        request.setPrefix(prefix);

        Mockito.when(documentDeleteService.deleteCargoDocument(999, 2, 15))
                .thenThrow(new RuntimeException("Simulated exception"));

        ResponseEntity<com.maersk.sd1.common.controller.dto.ResponseController<DocumentDeleteOutput>> responseEntity =
                documentDeleteController.deleteCargoDocument(request);

        assertEquals(500, responseEntity.getStatusCode().value());
    }
}