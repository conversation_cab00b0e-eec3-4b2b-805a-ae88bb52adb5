package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.RegisterBlLoadJsonInputDTO;
import com.maersk.sd1.sds.dto.RegisterBlLoadJsonOutputDTO;
import com.maersk.sd1.sds.service.RegisterBlLoadJsonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RegisterBlLoadJsonControllerTest {

    @Mock
    private RegisterBlLoadJsonService registerBlLoadJsonService;

    @InjectMocks
    private RegisterBlLoadJsonController registerBlLoadJsonController;

    private RegisterBlLoadJsonInputDTO.Root validRequest;
    private RegisterBlLoadJsonOutputDTO validOutput;

    @BeforeEach
    void setUp() {
        RegisterBlLoadJsonInputDTO.Input input = new RegisterBlLoadJsonInputDTO.Input();
        input.setBusinessUnitId(1L);
        input.setSubBusinessUnitId(2L);
        input.setVesselScheduleId(100);
        input.setRegisteredUserId(10L);
        input.setFileSource("XLS");
        input.setDocuments("[]");
        input.setLanguageId(1);

        RegisterBlLoadJsonInputDTO.Prefix prefix = new RegisterBlLoadJsonInputDTO.Prefix();
        prefix.setInput(input);

        validRequest = new RegisterBlLoadJsonInputDTO.Root();
        validRequest.setPrefix(prefix);

        validOutput = new RegisterBlLoadJsonOutputDTO();
        validOutput.setResponseStatus(0);
        validOutput.setResponseMessage("Success");
    }

    @Test
    void shouldReturnSuccessResponseForValidRequest() {
        when(registerBlLoadJsonService.registerBlLoadJson(validRequest.getPrefix().getInput())).thenReturn(validOutput);

        ResponseEntity<ResponseController<RegisterBlLoadJsonOutputDTO>> response = registerBlLoadJsonController.registerBlLoadJson(validRequest);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertEquals(validOutput, Objects.requireNonNull(response.getBody()).getResult());
    }

    @Test
    void shouldReturnBadRequestForNullRequest() {
        ResponseEntity<ResponseController<RegisterBlLoadJsonOutputDTO>> response = registerBlLoadJsonController.registerBlLoadJson(null);

        assertNotNull(response);
        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure", Objects.requireNonNull(response.getBody()).getMessage());
    }

    @Test
    void shouldReturnBadRequestForInvalidStructure() {
        RegisterBlLoadJsonInputDTO.Root invalidRequest = new RegisterBlLoadJsonInputDTO.Root();
        invalidRequest.setPrefix(null);

        ResponseEntity<ResponseController<RegisterBlLoadJsonOutputDTO>> response = registerBlLoadJsonController.registerBlLoadJson(invalidRequest);

        assertNotNull(response);
        assertEquals(400, response.getStatusCode().value());
        assertEquals("Invalid input payload structure", Objects.requireNonNull(response.getBody()).getMessage());
    }

    @Test
    void shouldHandleServiceExceptionGracefully() {
        when(registerBlLoadJsonService.registerBlLoadJson(validRequest.getPrefix().getInput()))
                .thenThrow(new RuntimeException("Service failure"));

        ResponseEntity<ResponseController<RegisterBlLoadJsonOutputDTO>> response = registerBlLoadJsonController.registerBlLoadJson(validRequest);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertEquals("Service failure", Objects.requireNonNull(response.getBody()).getMessage());
    }
}
