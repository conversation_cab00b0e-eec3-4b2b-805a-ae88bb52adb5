package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.VesselProgrammingDetailRepository;
import com.maersk.sd1.sds.dto.Detalle;
import com.maersk.sd1.sds.dto.VesselProgrammingDetailExportOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VesselProgrammingDetailExportServiceTest {

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @InjectMocks
    private VesselProgrammingDetailExportService vesselProgrammingDetailExportService;

    private Integer unidadNegocioId;
    private List<Object[]> resultList;
    private List<Detalle> detalles;

    @BeforeEach
    void setUp() {
        unidadNegocioId = 1;
        resultList = new ArrayList<>();
        resultList.add(new Object[]{1, "Voyage1", "Operation1"});
        detalles = new ArrayList<>();
        Detalle detalle = new Detalle();
        detalle.setProgramacionNaveDetalleId(1);
        detalle.setNaveViaje("Voyage1");
        detalle.setOperacion("Operation1");
        detalles.add(detalle);
    }

    @Test
    void Given_ValidUnidadNegocioId_When_ExportDetails_Then_ReturnExportedDetails() {
        when(vesselProgrammingDetailRepository.countExportDetailsByBusinessUnitId(unidadNegocioId)).thenReturn(1L);
        when(vesselProgrammingDetailRepository.findExportDetailsByBusinessUnitId(unidadNegocioId)).thenReturn(resultList);

        VesselProgrammingDetailExportOutput result = vesselProgrammingDetailExportService.exportDetails(unidadNegocioId);

        assertEquals(1L, result.getTotalRegistros());
        assertEquals(detalles, result.getDetalles());
        verify(vesselProgrammingDetailRepository, times(1)).countExportDetailsByBusinessUnitId(unidadNegocioId);
        verify(vesselProgrammingDetailRepository, times(1)).findExportDetailsByBusinessUnitId(unidadNegocioId);
    }

    @Test
    void Given_ValidUnidadNegocioId_When_NoDetailsFound_Then_ReturnEmptyDetails() {
        when(vesselProgrammingDetailRepository.countExportDetailsByBusinessUnitId(unidadNegocioId)).thenReturn(0L);
        when(vesselProgrammingDetailRepository.findExportDetailsByBusinessUnitId(unidadNegocioId)).thenReturn(new ArrayList<>());

        VesselProgrammingDetailExportOutput result = vesselProgrammingDetailExportService.exportDetails(unidadNegocioId);

        assertEquals(0L, result.getTotalRegistros());
        assertEquals(new ArrayList<>(), result.getDetalles());
        verify(vesselProgrammingDetailRepository, times(1)).countExportDetailsByBusinessUnitId(unidadNegocioId);
        verify(vesselProgrammingDetailRepository, times(1)).findExportDetailsByBusinessUnitId(unidadNegocioId);
    }

    @Test
    void Given_ValidUnidadNegocioId_When_ExceptionOccurs_Then_ThrowRuntimeException() {
        when(vesselProgrammingDetailRepository.countExportDetailsByBusinessUnitId(unidadNegocioId)).thenThrow(new RuntimeException("Database error"));

        RuntimeException exception = null;
        try {
            vesselProgrammingDetailExportService.exportDetails(unidadNegocioId);
        } catch (RuntimeException e) {
            exception = e;
        }

        assertEquals("Error exporting vessel programming details.", exception.getMessage());
        verify(vesselProgrammingDetailRepository, times(1)).countExportDetailsByBusinessUnitId(unidadNegocioId);
    }
}