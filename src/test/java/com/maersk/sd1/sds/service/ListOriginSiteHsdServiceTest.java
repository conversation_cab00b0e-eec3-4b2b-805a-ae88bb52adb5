package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdiSetting;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class ListOriginSiteHsdServiceTest {

    @InjectMocks
    private ListOriginSiteHsdService listOriginSiteHsdService;

    @Mock
    private BookingEdiSettingRepository bookingEdiSettingRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListOriginSitesForCoparnService() {
        BookingEdiSetting bookingEdiSetting1 = new BookingEdiSetting();
        BookingEdiSetting bookingEdiSetting2 = new BookingEdiSetting();
        List<BookingEdiSetting> expectedBookingDetails = Arrays.asList(bookingEdiSetting1, bookingEdiSetting2);

        when(bookingEdiSettingRepository.findBookingDetails(4104, false)).thenReturn(expectedBookingDetails);

        List<BookingEdiSetting> actualBookingDetails = listOriginSiteHsdService.listOriginSiteHsdService();

        assertEquals(expectedBookingDetails, actualBookingDetails);
    }
}

