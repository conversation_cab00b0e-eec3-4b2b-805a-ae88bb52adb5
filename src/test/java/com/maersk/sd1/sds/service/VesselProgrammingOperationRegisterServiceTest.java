package com.maersk.sd1.sds.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingOperationRegisterInputDTO;
import com.maersk.sd1.sds.controller.dto.VesselProgrammingOperationRegisterOutputDTO;
import com.maersk.sd1.sds.exception.CutoffRetiroVaciosParseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VesselProgrammingOperationRegisterServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private VesselProgrammingOperationRegisterService service;

    private VesselProgrammingOperationRegisterInputDTO.Input input;

    @BeforeEach
    public void setUp() {
        input = new VesselProgrammingOperationRegisterInputDTO.Input();
        input.setVesselProgrammingId(1);
        input.setCatOperationId(1);
        input.setManifestYear("2023");
        input.setManifestNumber("12345");
        input.setBeginningOperation(LocalDateTime.now());
        input.setEndingOperation(LocalDateTime.now().plusHours(1));
        input.setUserRegistrationId(1);
        input.setManifestCustomsDate(LocalDateTime.now());
        input.setBeginningReturnAppointment(LocalDateTime.now());
        input.setDryPortCutoffDate(LocalDateTime.now());
        input.setReeferPortCutoffDate(LocalDateTime.now());
        input.setDryyDepositCutoffDate(LocalDateTime.now());
        input.setReeferDepositCutoffDate(LocalDateTime.now());
        input.setExpoEcuAppointmentsBegginingDate(LocalDateTime.now());
        input.setLanguageId(1);
    }

    @Test
    void testRegisterVesselProgrammingOperationNewDetail() {
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt()))
                .thenReturn("Success");

        when(vesselProgrammingDetailRepository.save(any(VesselProgrammingDetail.class)))
                .thenAnswer(invocation -> {
                    VesselProgrammingDetail detail = invocation.getArgument(0);
                    detail.setId(1);
                    return detail;
                });

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        System.out.println("RespEstado: " + result.getRespEstado());
        System.out.println("RespMensaje: " + result.getRespMensaje());

        assertEquals(1, result.getRespEstado());
        assertEquals("Success", result.getRespMensaje());

        verify(vesselProgrammingDetailRepository, times(1)).save(any(VesselProgrammingDetail.class));
    }


    @Test
    void testRegisterVesselProgrammingOperationExistingDetail() {
        VesselProgrammingDetail detail = new VesselProgrammingDetail();
        detail.setId(1);
        detail.setActive(true);

        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenReturn(Optional.of(detail));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt()))
                .thenReturn("Detail already exists");

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        assertEquals(2, result.getRespEstado());
        assertEquals("Detail already exists", result.getRespMensaje());
        verify(vesselProgrammingDetailRepository, never()).save(any(VesselProgrammingDetail.class));
    }

    @Test
    void testRegisterVesselProgrammingOperationException() {
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Database error"));

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        assertEquals(0, result.getRespEstado());
        assertEquals("Database error", result.getRespMensaje());
        verify(vesselProgrammingDetailRepository, never()).save(any(VesselProgrammingDetail.class));
    }

    @Test
    void testRegisterVesselProgrammingOperationNullCutoffRecords() throws CutoffRetiroVaciosParseException {
        input.setCutoffRetiroVacios(null);

        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt()))
                .thenReturn("Success");

        when(vesselProgrammingDetailRepository.save(any(VesselProgrammingDetail.class)))
                .thenAnswer(invocation -> {
                    VesselProgrammingDetail detail = invocation.getArgument(0);
                    detail.setId(1);
                    return detail;
                });

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        assertEquals(1, result.getRespEstado());
        assertEquals("Success", result.getRespMensaje());
        verify(vesselProgrammingDetailRepository, times(1)).save(any(VesselProgrammingDetail.class));
    }

    @Test
    void testRegisterVesselProgrammingOperationEmptyCutoffRecords() throws CutoffRetiroVaciosParseException {
        input.setCutoffRetiroVacios(String.valueOf(new ArrayList<>()));

        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt()))
                .thenReturn("Success");

        when(vesselProgrammingDetailRepository.save(any(VesselProgrammingDetail.class)))
                .thenAnswer(invocation -> {
                    VesselProgrammingDetail detail = invocation.getArgument(0);
                    detail.setId(1);
                    return detail;
                });

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        assertEquals(1, result.getRespEstado());
        assertEquals("Success", result.getRespMensaje());
        verify(vesselProgrammingDetailRepository, times(1)).save(any(VesselProgrammingDetail.class));
    }

    @Test
    void testRegisterVesselProgrammingOperationExistingDetailInactive() {
        VesselProgrammingDetail detail = new VesselProgrammingDetail();
        detail.setId(1);
        detail.setActive(false);

        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenReturn(Optional.of(detail));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt()))
                .thenReturn("Updated successfully");

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        assertEquals(1, result.getRespEstado());
        assertEquals("Updated successfully", result.getRespMensaje());
        verify(vesselProgrammingDetailRepository, times(1)).save(any(VesselProgrammingDetail.class));
    }

    @Test
    void testInsertCutoffsValidAndInvalidShippingLineIds() throws CutoffRetiroVaciosParseException, JsonProcessingException {
        List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffs = getCutoffRetiroVacios();

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonCutoffs = objectMapper.writeValueAsString(cutoffs);

        input.setCutoffRetiroVacios(jsonCutoffs);

        when(shippingLineRepository.findByIdInAndActiveTrue(anyList())).thenReturn(List.of(new ShippingLine(1)));

        List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> result = service.cleanAndFilterCutoffRecords(input.getCutoffRetiroVacios());

        assertEquals(1, result.size()); // Should remove the invalid one
        assertEquals(1, result.getFirst().getShippingLineId());
    }

    private static List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> getCutoffRetiroVacios() {
        List<VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio> cutoffs = new ArrayList<>();

        VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio validCutoff = new VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio();
        validCutoff.setShippingLineId(1);
        validCutoff.setRetiroDry("01/01/2025 12:00:00");
        cutoffs.add(validCutoff);

        VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio invalidCutoff = new VesselProgrammingOperationRegisterInputDTO.CutoffRetiroVacio();
        invalidCutoff.setShippingLineId(9999);
        cutoffs.add(invalidCutoff);
        return cutoffs;
    }


    @Test
    void testRegisterVesselProgrammingOperationSaveFailure() {
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndCatOperationId(anyInt(), anyInt()))
                .thenReturn(Optional.empty());
        when(vesselProgrammingDetailRepository.save(any(VesselProgrammingDetail.class)))
                .thenThrow(new RuntimeException("Save failed"));

        VesselProgrammingOperationRegisterOutputDTO result = service.registerVesselProgrammingOperation(input);

        assertEquals(0, result.getRespEstado());
        assertEquals("Save failed", result.getRespMensaje());
    }

    @Test
    void testParseLocalDateTimeValidFormats() {
        String date1 = "01/01/2025 12:00:00";
        String date2 = "01-01-2025 12:00:00";
        String date3 = "2025/01/01 12:00:00";
        String date4 = "2025-01-01 12:00:00";

        assertNotNull(VesselProgrammingOperationRegisterService.parseLocalDateTime(date1));
        assertNotNull(VesselProgrammingOperationRegisterService.parseLocalDateTime(date2));
        assertNotNull(VesselProgrammingOperationRegisterService.parseLocalDateTime(date3));
        assertNotNull(VesselProgrammingOperationRegisterService.parseLocalDateTime(date4));
    }

    @Test
    void testParseLocalDateTimeInvalidFormats() {
        assertNull(VesselProgrammingOperationRegisterService.parseLocalDateTime("Invalid Date"));
        assertNull(VesselProgrammingOperationRegisterService.parseLocalDateTime(""));
        assertNull(VesselProgrammingOperationRegisterService.parseLocalDateTime(null));
    }

}