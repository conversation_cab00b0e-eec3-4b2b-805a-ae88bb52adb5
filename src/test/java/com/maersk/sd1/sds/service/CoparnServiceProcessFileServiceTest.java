package com.maersk.sd1.sds.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.BookingEdiUtils;
import com.maersk.sd1.sds.dto.CoparnServiceProcessFileOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CoparnServiceProcessFileServiceTest {
    @Mock
    CatalogRepository catalogRepository;
    @Mock
    BusinessUnitRepository businessUnitRepository;
    @Mock
    BookingEdiRepository bookingEdiRepository;
    @Mock
    BookingEdiSettingRepository bookingEdiSettingRepository;
    @Mock
    BookingEdiFileRepository ediFileRepository;
    @Mock
    SystemRuleRepository systemRuleRepository;
    @Mock
    VesselRepository vesselRepository;
    @Mock
    PortRepository portRepository;
    @Mock
    BookingEdiSettingBURepository bookingEdiSettingBURepository;
    @Mock
    PortMasterRepository portMasterRepository;
    @Mock
    IsoCodeRepository isoCodeRepository;
    @Mock
    CompanyRepository companyRepository;
    @Mock
    CompanyRoleRepository companyRoleRepository;
    @Mock
    ImoRepository imoRepository;
    @Mock
    VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    @Mock
    VesselProgrammingRepository vesselProgrammingRepository;
    @Mock
    ProductRepository productRepository;
    @Mock
    BookingEdiUtils bookingEdiUtils;
    @InjectMocks
    CoparnServiceProcessFileService coparnServiceProcessFileService;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    BookingEdi generateBookingEdi() {
        BookingEdi bookingEdi = new BookingEdi();
        BusinessUnit bu = new BusinessUnit();
        bookingEdi.setSubBusinessUnit(bu);
        Catalog catalog = new Catalog();
        catalog.setId(1);
        bookingEdi.setCatBkEdiStatus(catalog);
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(1);
        bookingEdi.setShippingLine(shippingLine);

        return bookingEdi;
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar


        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(generateBookingEdi()));

        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1001));

        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("  UNB+UNOA:1+MAEU+BOGTWAY+211228:2202+435'  UNH+43500001+COPARN:D:95B:UN:SMDG16'  BGM+12+20211228220216+9'  RFF+BN:215577583'  TDT+20+201N+1++SEA:172:20+++9437062:146:11:GSL SYROS'  LOC+9+CLARI:139:6'  LOC+88+CLARI:139:6'  DTM+133:202201082300:203'  NAD+CA+MAE:172:20'  NAD+CZ+***********+JATARIY IMPORT EXPORT S.R.L.'  GID+1'  FTX+AAA+++SEEDS, GRAINS, INDUSTRIAL PLANTS, MEDICINAL PLANTS'  EQD+CN++22G1:102:5+2+2+4'  RFF+BN:215577583'  EQN+1'  TMD+4++2'  DTM+201:202112210100:203'  LOC+98+BOCBB:139:6'  LOC+8+FRLEH:139:6'  LOC+11+PABLB:139:6'  MEA+AAE+T+KGM:2170'  MEA+AAE+G+KGM:18000'  MEA+AAE+EGW+KGM:20170'  FTX+AAI+++APTO APTO.'  TDT+1++2'  LOC+11+PAMIT:139:6'  CNT+16:1'  UNT+27+43500001'  UNZ+1+435'  ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        // Act
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        // Assert
        assertNotNull(result);

        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_invalidEdiId_shouldReturnNotNull() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 999;
        Integer seteoEdiCoparnId = 2;

        when(bookingEdiRepository.findByIdAndActiveIsTrue(ediCoparnId)).thenReturn(Optional.empty());

        // Act
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        // Assert
        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }


    @Test
    void processFile_validInput_shouldReturnExpectedOutput2() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        BookingEdi bookingEdi = generateBookingEdi();
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(4103);
        bookingEdi.setShippingLine(shippingLine);
        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));

        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("ISA*00*          *00*          *ZZ*MAEU           *ZZ*SDONAMCHI      *240729*1622*U*00200*000155364*0*P*~ GS*RO*MAEU*SDONAMCHI*20240729*1622*155364*X*004010 ST*301*553640001 B1*MAEU*240417358*20240729*U Y3*240417358*MAEU Y4*240417358****10*45G1 N9*BN*240417358 N1*SH*MAERSK, INC.*2*MAEU N3*GIRALDA FARMS, MADISON AVE*PO BOX 880 N4*MADISON*NJ R4*R*UN*USRNZ*RENSSELAER*US DTM*059*20240802 R4*I*UN*USCHI*CHICAGO*US R4*L*UN*USLAX*LOS ANGELES*US DTM*005*20240812 R4*D*UN*AUSYD*SYDNEY*AU DTM*096*20240906 LX*1 K1*Amy Schaffer +19528873730....*. L0*1***440924.524*N***0*PCS**L L5*1*FAK** V1*9484572*CAP JERVIS*SG*430S*MAEU***L V9*ZZZ*Booking Confirmation*20240729*1622 SE*22*553640001 GE*1*155364 IEA*1*000155364 ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);
        Port port = new Port();
        port.setId(1);
        when(portRepository.findByPort(anyString())).thenReturn(Optional.of(port));

        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        // Assert
        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput3() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4103);
        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);
        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();
        PortMaster portMaster1 = new PortMaster();
        portMasters1.add(portMaster1);
        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("UNB+UNOA:4+MAERSKLINE+CISCRICA+240627:1828+46334'  UNH+4633400001+COPARN:D:95B:UN:SMDG16'  BGM+12+20240627182811+5'  RFF+BN:241548565'  TDT+20+426N+1++MAE:172:20+++9786750:146:11:POLAR MEXICO'  LOC+88+CRPMN:139:6'  LOC+9+CRPMN:139:6'  DTM+133:202407030001:203'  NAD+CA+MAE:172:20'  NAD+CZ+***********+CHIQUITA BRANDS COSTA RICA SRL'  NAD+CN+***********+CHIQUITA FRESH NORTH AMERICA'  NAD+FW+***********+CHIQUITA BRANDS COSTA RICA SRL'  GID+1'  FTX+AAA+++PINEAPPLES, NON-FROZEN, FRUIT'  EQD+CN+SILU7046125+45R1:102:5+1+2+4'  RFF+BN:241548565'  RFF+SQ:5GIRN7KVP2K3B'  TMD+3++2'  DTM+201:202406240100:203'  LOC+8+USPHL:139:6'  LOC+98+CRLIO:139:6'  LOC+11+USPHL:139:6'  MEA+AAE+G+KGM:21478'  MEA+AAE+T+KGM:4600'  MEA+AAE+EGW+KGM:26078'  MEA+AAE+AAS+MTQ:30'  TMP+2+007:CEL'  FTX+HAN++CNR'  CNT+16:1'  UNT+29+4633400001'  UNZ+1+46334'  ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);

        Port port = new Port();
        port.setId(1);
        when(portRepository.findByPort(anyString())).thenReturn(Optional.of(port));
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        // Assert
        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput_SH() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4103);
        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);
        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();
        PortMaster portMaster1 = new PortMaster();
        portMasters1.add(portMaster1);
        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("UNB+UNOA:1+MAEU+COCTGAP+220509:0940+179610'  UNH+***********+COPARN:D:95B:UN'  BGM+12+20220509094057+9'  RFF+BN:218326907'  TDT+20+221S+1++OTH:172:20+++9HA4291:103::X-PRESS SHANNON'  RFF+VON:221S'  LOC+88+COCTG:139:6+COCTGPG:TER:ZZZ'  LOC+9+COCTG:139:6+COCTGPG:TER:ZZZ'  DTM+133:202205272300:203'  NAD+CZ+308S3000501+AJOVER DARNEL S.A.S'  NAD+CA+SEA:172:20'  GID+1'  FTX+AAA+++PLASTIC, PLASTIC ARTICLES, NEW'  EQD+CN++45G1:102:5+2+2+4'  RFF+BN:218326907'  RFF+ACD:APTO ALIMENTO'  EQN+2'  TMD+3++2'  DTM+201:202205180000:203'  LOC+8+AWORJ:139:6'  LOC+98+COCTG:139:6+COCTGAP:TER:ZZZ'  LOC+11+AWORJ:139:6'  MEA+AAE+G+KGM:25000'  MEA+AAE+T+KGM:4004'  MEA+AAE+EGW+KGM:29004'  FTX+AAI+++| APTO ALIMENTOAJOVER DARNEL S.A.S ALIMENTOS'  CNT+16:1'  UNT+27+***********'  UNZ+1+179610'  ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput_SH2() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4103);

        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);

        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();


        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("ISA*00*          *00*          *ZZ*MAEU           *ZZ*SDONAMCHI      *230301*1506*U*00200*000000129*0*P*~ GS*RO*MAEU*SDONAMCHI*20230301*1506*129*X*004010 ST*301*1290001 B1*MAEU*225551422*20230301*N Y3*225551422*MAEU Y4*225551422****1*45R1 N9*BN*225551422 N1*SH*MAERSK, INC.*2*MAEU N3*GIRALDA FARMS, MADISON AVE*PO BOX 880 N4*MADISON*NJ R4*R*UN*USFMS*FORT MADISON*US DTM*059*20230330 R4*I*UN*USCHI*CHICAGO*US R4*L*UN*USLGB*LONG BEACH*US DTM*005*20230413 R4*D*UN*CNNSA*NANSHA NEW PORT*CN DTM*096*20230520 LX*1 W09*CZ*25*CE*25*CE**E*0 K1*. L0*1***43878.604*N***1*PCS**L L5*1*Computers, computer parts, new*001402*A V1*9461427*MSC LIVORNO*DE*315S*MSCU***L V9*ZZZ*Booking Confirmation*20230301*1506 SE*23*1290001 GE*1*129 IEA*1*000000129 ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);
        Port port = new Port();
        port.setId(1);
        when(portRepository.findByPort(anyString())).thenReturn(Optional.of(port));
        Company company = new Company();
        company.setId(1);
        company.setLegalName("MAERSK");
        when(companyRepository.save(any())).thenReturn(company);
        when(companyRepository.findByLegalNameAndActiveTrue(anyString())).thenReturn(Optional.of(company));
        when(vesselRepository.save(any())).thenReturn(new Vessel());
        when(bookingEdiRepository.findByIdAndStatus(any(), any())).thenReturn(Optional.of(new BookingEdi()));

        when(bookingEdiRepository.findById(any())).thenReturn(Optional.of(new BookingEdi()));
        when(companyRepository.countByLegalNameAndActiveTrue(anyString())).thenReturn(2);

        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput_SH2_no_company() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4103);

        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);

        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();


        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("ISA*00*          *00*          *ZZ*MAEU           *ZZ*SDONAMCHI      *230301*1506*U*00200*000000129*0*P*~ GS*RO*MAEU*SDONAMCHI*20230301*1506*129*X*004010 ST*301*1290001 B1*MAEU*225551422*20230301*N Y3*225551422*MAEU Y4*225551422****1*45R1 N9*BN*225551422 N1*SH*MAERSK, INC.*2*MAEU N3*GIRALDA FARMS, MADISON AVE*PO BOX 880 N4*MADISON*NJ R4*R*UN*USFMS*FORT MADISON*US DTM*059*20230330 R4*I*UN*USCHI*CHICAGO*US R4*L*UN*USLGB*LONG BEACH*US DTM*005*20230413 R4*D*UN*CNNSA*NANSHA NEW PORT*CN DTM*096*20230520 LX*1 W09*CZ*25*CE*25*CE**E*0 K1*. L0*1***43878.604*N***1*PCS**L L5*1*Computers, computer parts, new*001402*A V1*9461427*MSC LIVORNO*DE*315S*MSCU***L V9*ZZZ*Booking Confirmation*20230301*1506 SE*23*1290001 GE*1*129 IEA*1*000000129 ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);
        Port port = new Port();
        port.setId(1);
        when(portRepository.findByPort(anyString())).thenReturn(Optional.of(port));
        Company company = new Company();
        company.setId(null);
        company.setLegalName("MAERSK");
        when(companyRepository.save(any())).thenReturn(company);
        when(companyRepository.findByLegalNameAndActiveTrue(anyString())).thenReturn(Optional.of(company));
        when(vesselRepository.save(any())).thenReturn(new Vessel());
        when(bookingEdiRepository.findByIdAndStatus(any(), any())).thenReturn(Optional.of(new BookingEdi()));

        when(bookingEdiRepository.findById(any())).thenReturn(Optional.of(new BookingEdi()));
        when(companyRepository.countByLegalNameAndActiveTrue(anyString())).thenReturn(2);

        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput_SH2_SHIPPING_LINE_MAERSK() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        when(portRepository.findByPort(anyString())).thenReturn(Optional.empty());
        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(generateBookingEdi()));

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4105);

        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);

        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();
        PortMaster portMaster1 = new PortMaster();
        portMasters1.add(portMaster1);
        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("ISA*00*          *00*          *ZZ*MAEU           *ZZ*SDONAMCHI      *230301*1506*U*00200*000000129*0*P*~ GS*RO*MAEU*SDONAMCHI*20230301*1506*129*X*004010 ST*301*1290001 B1*MAEU*225551422*20230301*N Y3*225551422*MAEU Y4*225551422****1*45R1 N9*BN*225551422 N1*SH*MAERSK, INC.*2*MAEU N3*GIRALDA FARMS, MADISON AVE*PO BOX 880 N4*MADISON*NJ R4*R*UN*USFMS*FORT MADISON*US DTM*059*20230330 R4*I*UN*USCHI*CHICAGO*US R4*L*UN*USLGB*LONG BEACH*US DTM*005*20230413 R4*D*UN*CNNSA*NANSHA NEW PORT*CN DTM*096*20230520 LX*1 W09*CZ*25*CE*25*CE**E*0 K1*. L0*1***43878.604*N***1*PCS**L L5*1*Computers, computer parts, new*001402*A V1*9461427*MSC LIVORNO*DE*315S*MSCU***L V9*ZZZ*Booking Confirmation*20230301*1506 SE*23*1290001 GE*1*129 IEA*1*000000129 ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);
        Port port = new Port();
        port.setId(1);
        when(portRepository.findByPort(anyString())).thenReturn(Optional.of(port));
        Company company = new Company();
        company.setId(1);
        company.setLegalName("MAERSK");
        when(companyRepository.save(any())).thenReturn(company);
        when(companyRepository.findByLegalNameAndActiveTrue(anyString())).thenReturn(Optional.of(company));
        when(vesselRepository.save(any())).thenReturn(new Vessel());
        when(bookingEdiRepository.findByIdAndStatus(any(), any())).thenReturn(Optional.of(new BookingEdi()));

        when(bookingEdiRepository.findById(any())).thenReturn(Optional.of(new BookingEdi()));
        when(companyRepository.countByLegalNameAndActiveTrue(anyString())).thenReturn(2);
        when(ediFileRepository.findById(anyInt())).thenReturn(Optional.of(new BookingEdiFile()));
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput_SH2_SHIPPING_LINE_MAERSK_no_port_master() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        when(portRepository.findByPort(anyString())).thenReturn(Optional.empty());
        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(generateBookingEdi()));

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4103);

        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);

        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();

        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("ISA*00*          *00*          *ZZ*MAEU           *ZZ*SDONAMCHI      *230301*1506*U*00200*000000129*0*P*~ GS*RO*MAEU*SDONAMCHI*20230301*1506*129*X*004010 ST*301*1290001 B1*MAEU*225551422*20230301*N Y3*225551422*MAEU Y4*225551422****1*45R1 N9*BN*225551422 N1*SH*MAERSK, INC.*2*MAEU N3*GIRALDA FARMS, MADISON AVE*PO BOX 880 N4*MADISON*NJ R4*R*UN*USFMS*FORT MADISON*US DTM*059*20230330 R4*I*UN*USCHI*CHICAGO*US R4*L*UN*USLGB*LONG BEACH*US DTM*005*20230413 R4*D*UN*CNNSA*NANSHA NEW PORT*CN DTM*096*20230520 LX*1 W09*CZ*25*CE*25*CE**E*0 K1*. L0*1***43878.604*N***1*PCS**L L5*1*Computers, computer parts, new*001402*A V1*9461427*MSC LIVORNO*DE*315S*MSCU***L V9*ZZZ*Booking Confirmation*20230301*1506 SE*23*1290001 GE*1*129 IEA*1*000000129 ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);

        when(portRepository.findByPort(anyString())).thenReturn(Optional.empty());


        Company company = new Company();
        company.setId(1);
        company.setLegalName("MAERSK");
        when(companyRepository.save(any())).thenReturn(company);
        when(companyRepository.findByLegalNameAndActiveTrue(anyString())).thenReturn(Optional.of(company));
        when(vesselRepository.save(any())).thenReturn(new Vessel());
        when(bookingEdiRepository.findByIdAndStatus(any(), any())).thenReturn(Optional.of(new BookingEdi()));
      
        when(bookingEdiRepository.findById(any())).thenReturn(Optional.of(new BookingEdi()));
        when(companyRepository.countByLegalNameAndActiveTrue(anyString())).thenReturn(2);
        when(ediFileRepository.findById(anyInt())).thenReturn(Optional.of(new BookingEdiFile()));
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }

    @Test
    void processFile_validInput_shouldReturnExpectedOutput_SH2_SHIPPING_LINE_MAERSK_no_port_master_no_vessel() throws JsonProcessingException {
        // Arrange
        Integer ediCoparnId = 1;
        Integer seteoEdiCoparnId = 2;

        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_coparn")).thenReturn(1001);
        when(catalogRepository.findIdByAlias("sd1_bkedi_message_type_301")).thenReturn(1002);
        when(catalogRepository.findIdByAlias("48271")).thenReturn(1); // Por Leer
        when(catalogRepository.findIdByAlias("48272")).thenReturn(2); // Por Procesar

        when(portRepository.findByPort(anyString())).thenReturn(Optional.empty());
        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(generateBookingEdi()));

        BookingEdi bookingEdi = generateBookingEdi();
        bookingEdi.getShippingLine().setId(4103);

        when(bookingEdiRepository.findByIdAndActiveIsTrue(anyInt())).thenReturn(Optional.of(bookingEdi));
        when(bookingEdiRepository.findSubBusinessUNitIdByFileName(anyInt())).thenReturn(1);
        List<BookingEdiSettingBU> bookingEdiSettingBUS = new ArrayList<>();
        BookingEdiSettingBU bookingEdiSettingBU = new BookingEdiSettingBU();
        bookingEdiSettingBU.setId(1);
        bookingEdiSettingBUS.add(bookingEdiSettingBU);

        when(bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(anyInt(), anyInt())).thenReturn(bookingEdiSettingBUS);
        Optional<BookingEdi> bookingEdiOptional = Optional.of(new BookingEdi());
        bookingEdiOptional.get().setCatBkEdiStatus(new Catalog(1));
        List<PortMaster> portMasters1 = new ArrayList<>();

        when(portMasterRepository.findByPortCode(anyString())).thenReturn(portMasters1);

        Optional<BookingEdiSetting> bookingEdiSetting = Optional.of(new BookingEdiSetting());
        bookingEdiSetting.get().setBusinessUnit(new BusinessUnit());
        bookingEdiSetting.get().setAllowCreateAutomaticVesselProgramming(true);
        bookingEdiSetting.get().setAllowCreateAutomaticCustomer(true);
        bookingEdiSetting.get().setCatBkEdiMessageType(new Catalog(1002));
        when(bookingEdiSettingRepository.findById(seteoEdiCoparnId)).thenReturn(bookingEdiSetting);

        Optional<BookingEdiFile> bookingEdiFile = Optional.of(new BookingEdiFile());
        bookingEdiFile.get().setBkEdiContent("ISA*00*          *00*          *ZZ*MAEU           *ZZ*SDONAMCHI      *230301*1506*U*00200*000000129*0*P*~ GS*RO*MAEU*SDONAMCHI*20230301*1506*129*X*004010 ST*301*1290001 B1*MAEU*225551422*20230301*N Y3*225551422*MAEU Y4*225551422****1*45R1 N9*BN*225551422 N1*SH*MAERSK, INC.*2*MAEU N3*GIRALDA FARMS, MADISON AVE*PO BOX 880 N4*MADISON*NJ R4*R*UN*USFMS*FORT MADISON*US DTM*059*20230330 R4*I*UN*USCHI*CHICAGO*US R4*L*UN*USLGB*LONG BEACH*US DTM*005*20230413 R4*D*UN*CNNSA*NANSHA NEW PORT*CN DTM*096*20230520 LX*1 W09*CZ*25*CE*25*CE**E*0 K1*. L0*1***43878.604*N***1*PCS**L L5*1*Computers, computer parts, new*001402*A DE*315S*MSCU***L V9*ZZZ*Booking Confirmation*20230301*1506 SE*23*1290001 GE*1*129 IEA*1*000000129 ");
        when(ediFileRepository.findByBookingEdiId(ediCoparnId)).thenReturn(bookingEdiFile);
        List<SystemRule> systemRules = new ArrayList<>();
        SystemRule systemRule = new SystemRule();
        systemRule.setId(1);
        systemRule.setRule("[{\"remark_rules_id\":1, \"remark_rules_name\":\"FLAG_TO_FLEX\", \"type_edi_booking\":\"301\", \"list_of_keyword\":\"OK TO FLEX,OK TO SUB,OKAY TO FLEX,OKTO FLEX,OKAYTO FLEX\", \"remark_rules_definition\":\"This rule means that the booking dispatch can be a type HC or DC container\"} ]");
        systemRules.add(systemRule);
        when(systemRuleRepository.findSystemRuleByAliasAndActive(anyString(), anyBoolean())).thenReturn(systemRules);

        when(portRepository.findByPort(anyString())).thenReturn(Optional.empty());


        Company company = new Company();
        company.setId(1);
        company.setLegalName("MAERSK");
        when(companyRepository.save(any())).thenReturn(company);
        when(companyRepository.findByLegalNameAndActiveTrue(anyString())).thenReturn(Optional.of(company));
        when(vesselRepository.save(any())).thenReturn(new Vessel());
        when(bookingEdiRepository.findByIdAndStatus(any(), any())).thenReturn(Optional.of(new BookingEdi()));

        when(bookingEdiRepository.findById(any())).thenReturn(Optional.of(new BookingEdi()));
        when(companyRepository.countByLegalNameAndActiveTrue(anyString())).thenReturn(2);
        when(ediFileRepository.findById(anyInt())).thenReturn(Optional.of(new BookingEdiFile()));
        CoparnServiceProcessFileOutput result = coparnServiceProcessFileService.processFile(ediCoparnId, seteoEdiCoparnId);

        assertNotNull(result);
        verify(bookingEdiRepository, atLeast(1)).findByIdAndActiveIsTrue(ediCoparnId);
    }


    @Test
     void test_vessel_exists_but_no_id_or_list() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "TEST_VESSEL", null, null,
                1, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Nave TEST_VESSEL not exist in SD1.", result);
    }

    @Test
     void test_blank_vessel_name() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "", 1, List.of(1),
                1, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Vessel EDI in blank.", result);
    }

    @Test
     void test_missing_port_of_loading_id() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                null, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Port of Loading PORT1 not exist in SD1.", result);
    }

    @Test
     void test_blank_voyage() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                1, "PORT1", "", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Voyage EDI in blank.", result);
    }

    @Test
     void test_missing_vessel_schedule_id() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                1, "PORT1", "VOY1", null, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Vessel Schedule VESSEL1/VOY1 not generated.", result);
    }

    @Test
     void test_blank_booking() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                1, "PORT1", "VOY1", 1, "",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Booking EDI in blank.", result);
    }

    @Test
     void test_null_vessel_name() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                null, 1, List.of(1),
                1, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Vessel EDI in blank.", result);
    }

    @Test
     void test_null_port_ids() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                null, "PORT1", "VOY1", 1, "BOOK1",
                null, "DEST1", null, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, 1);

        assertEquals(" *Port of Loading PORT1 not exist in SD1. *Port of Destination DEST1 not exist in SD1. *Port of Discharge DISCH1 not exist in SD1.", result);
    }


    @Test
     void test_missing_customer_id_with_code() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                1, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", null, "CUST1",
                1, true, 1);

        assertEquals(" *Customer SD1 in blank.", result);
    }

    @Test
     void test_missing_customer_code_specific_line() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                1, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "",
                4104, false, 1);

        assertEquals(" *Customer SCV EDI in blank.", result);
    }

    @Test
     void test_null_container_quantity() {


        String result = coparnServiceProcessFileService.buildObservationMessage(
                "VESSEL1", 1, List.of(1),
                1, "PORT1", "VOY1", 1, "BOOK1",
                1, "DEST1", 1, "DISCH1",
                1, "ISO1", 1, "CUST1",
                1, true, null);

        assertEquals(" *Quantity Containers EDI in blank.", result);
    }

    @Test
    void clearSuffixes_shouldRemoveSuffix_whenNameContainsSAC() {
        assertEquals("EMPRESA", coparnServiceProcessFileService.clearSuffixes("Empresa S.A.C."));
    }

    @Test
    void clearSuffixes_shouldRemoveSuffix_whenNameContainsSA() {
        assertEquals("EMPRESA", coparnServiceProcessFileService.clearSuffixes("Empresa S.A."));
    }

    @Test
    void clearSuffixes_shouldRemoveSuffix_whenNameContainsSRL() {
        assertEquals("EMPRESA", coparnServiceProcessFileService.clearSuffixes("Empresa SRL"));
    }

    @Test
    void clearSuffixes_shouldRemoveSuffix_whenNameContainsEIRL() {
        assertEquals("EMPRESA", coparnServiceProcessFileService.clearSuffixes("Empresa EIRL"));
    }

    @Test
    void clearSuffixes_shouldReturnNull_whenNameDoesNotContainSuffix() {
        String name = "Empresa";
        String result = coparnServiceProcessFileService.clearSuffixes(name);
        assertNull(result);
    }


}

