package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.BusinessUnitConfigRepository;
import com.maersk.sd1.common.repository.BusinessUnitCurrencyRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.sds.dto.GeneralDataInput;
import com.maersk.sd1.sds.dto.GeneralDataOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

class GeneralDataServiceTest {

    @InjectMocks
    private GeneralDataService generalDataService;

    @Mock
    private BusinessUnitConfigRepository businessUnitConfigRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private BusinessUnitCurrencyRepository businessUnitCurrencyRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGeneralDataService() {
        GeneralDataInput.Root request = new GeneralDataInput.Root();
        GeneralDataInput.Input input = new GeneralDataInput.Input();
        input.setBusinessUnitId(1);
        input.setUserId(1);
        GeneralDataInput.Prefix prefix = new GeneralDataInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(businessUnitConfigRepository.findValueByBusinessUnitIdAndCatConfigurationTypeId(1, 20251)).thenReturn("2");
        when(businessUnitConfigRepository.findValueByBusinessUnitIdAndCatConfigurationTypeId(1, 183)).thenReturn("5.0");
        when(userRepository.findFullNameByUserId(1)).thenReturn("John Doe");

        List<Object[]> currencyDetails = Collections.singletonList(new Object[]{
                BigDecimal.valueOf(1), "USD", "Dollar", "$", ",", ".", "2", "USD"
        });
        when(businessUnitCurrencyRepository.getCurrencyDetailByBusinessUnitId(1)).thenReturn(currencyDetails);

        GeneralDataOutput response = generalDataService.generalDataService(input);

        assertNotNull(response);
        assertEquals(2, response.getGeneralDetails().getFirst().getTimeZoneGmt());
        assertEquals(5.0, response.getGeneralDetails().getFirst().getSalesTax());
        assertEquals("John Doe", response.getGeneralDetails().getFirst().getUserFullName());
        assertNotNull(response.getCurrencies());
        assertEquals(1, response.getCurrencies().size());
        GeneralDataOutput.CurrencyDetailDTO currency = response.getCurrencies().getFirst();
        assertEquals(1, currency.getCurrencyId());
        assertEquals("USD", currency.getName());
        assertEquals("Dollar", currency.getAbbreviation());
        assertEquals("$", currency.getSymbol());
        assertEquals(",", currency.getThousandSeparator());
        assertEquals(".", currency.getDecimalSeparator());
        assertEquals("2", currency.getPrecision());
        assertEquals("USD", currency.getIcu());
    }
}
