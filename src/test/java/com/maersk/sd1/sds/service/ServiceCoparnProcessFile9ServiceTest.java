package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.ServiceCoparnProcessFile9Input;
import com.maersk.sd1.sds.controller.dto.ServiceCoparnProcessFile9Output;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ServiceCoparnProcessFile9ServiceTest {

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private BookingBlockCancellationRepository bookingBlockCancellationRepository;

    @Mock
    private BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;

    @Mock
    private BookingDetailRepository bookingDetailRepository;

    @Mock
    private BookingEdiRepository bookingEdiRepository;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private DepotRepository depotRepository;

    @Mock
    private EirRepository eirRepository;

    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @Mock
    private ImoRepository imoRepository;

    @Mock
    private PortRepository portRepository;

    @Mock
    private VesselRepository vesselRepository;

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private VesselProgrammingPortRepository vesselProgrammingPortRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @Mock
    private ProductRepository productRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private BookingEdiProcessService bookingEdiProcessService;

    @InjectMocks
    private ServiceCoparnProcessFile9Service serviceCoparnProcessFile9Service;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(catalogRepository.getReferenceById(anyInt())).thenReturn(new Catalog());
    }

    @Test
    void given_ValidInput_When_NotExists_ServiceCoparnProcessFile9Service_Then_Success() {
        ServiceCoparnProcessFile9Input input = new ServiceCoparnProcessFile9Input();
        input.setEdiCoparnId(1);
        input.setProgrammingNaveDetailId(1);
        input.setUnitBusinessId(1);
        input.setSubUnitBusinessId(1);
        input.setBooking("BK123");
        input.setPortOfEmbarqueId(1);
        input.setPortOfDescargaId(1);
        input.setPortOfDestinoId(1);
        input.setCustomerId(1);
        input.setProductId(1);
        input.setQuantityReserve(1);
        input.setQuantityReserve2(1);
        input.setUserRecordId(1);
        input.setTemperature("10");
        input.setColdTreatment(true);
        input.setControlledAtmosphere(true);
        input.setCntDimenId(1);
        input.setCntDimen2Id(1);
        input.setCntTypeId(1);
        input.setCustomerId(1);
        input.setCntType2Id(1);
        input.setPassCoparn5x9(true);
        input.setParamSequenceDetails("[{\"sequence_alias\":\"EY7U2ZTO41\"}]");

        BookingEdi bookingEdi = new BookingEdi();
        bookingEdi.setCatBkEdiStatus(new Catalog());
        bookingEdi.getCatBkEdiStatus().setId(1);
        bookingEdi.setRemarkRulesName("FLAG_TO_FLEX");
        bookingEdi.setShippingLine(new ShippingLine());
        bookingEdi.getShippingLine().setId(1);
        bookingEdi.setBkEdiCreationDate("2023-01-01");

        VesselProgramming vesselProgramming = new VesselProgramming();
        vesselProgramming.setId(1);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        Vessel vessel = new Vessel();
        vessel.setName("VesselName");
        vesselProgramming.setVessel(vessel);
        vesselProgrammingDetail.setVesselProgramming(vesselProgramming);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(bookingEdiRepository.findByIdAndActive(anyInt(), eq(true))).thenReturn(bookingEdi);
        Catalog catOperation = new Catalog();
        catOperation.setId(1);
        vesselProgrammingDetail.setCatOperation(catOperation);
        when(vesselProgrammingDetailRepository.getReferenceById(anyInt())).thenReturn(vesselProgrammingDetail);
        Depot depot = new Depot();
        depot.setId(1);
        when(depotRepository.findTopByBusinessUnitIdAndSubBusinessUnitIdAndDepositDefaultTrue(anyInt(), anyInt())).thenReturn(depot);
        when(portRepository.getReferenceById(anyInt())).thenReturn(new Port());
        when(companyRepository.getReferenceById(anyInt())).thenReturn(new Company());
        when(productRepository.getReferenceById(anyInt())).thenReturn(new Product());
        when(userRepository.getReferenceById(anyInt())).thenReturn(new User());
        when(imoRepository.getReferenceById(anyInt())).thenReturn(new Imo());
        when(cargoDocumentRepository.existsByVesselProgrammingDetailIdAndCatCargoOriginIdAndCargoDocumentAndActive(anyInt(), anyInt(), anyString(), eq(true))).thenReturn(false);
        when(cargoDocumentRepository.save(any())).thenReturn(new CargoDocument());
        when(cargoDocumentDetailRepository.save(any())).thenReturn(new CargoDocumentDetail());
        when(eirRepository.save(any())).thenReturn(new Eir());
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(new EirDocumentCargoDetail());
        when(bookingRepository.existsByVesselProgrammingDetailIdAndBookingNumberAndActive(anyInt(), anyString(), eq(true))).thenReturn(false);
        when(bookingRepository.save(any())).thenReturn(new Booking());
        when(bookingDetailRepository.save(any())).thenReturn(new BookingDetail());
        when(!vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(false);
        doAnswer(invocation -> {
            VesselProgrammingPort vesselProgrammingPort = invocation.getArgument(0);
            vesselProgrammingPort.setId(1); // Mock ID
            return vesselProgrammingPort;
        }).when(vesselProgrammingPortRepository).save(any(VesselProgrammingPort.class));
        ServiceCoparnProcessFile9Output result = serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(input);

        assertEquals(200, result.getResponseStatus());
        assertEquals("The EDI has been processed.", result.getResponseMessage());
    }

    @Test
    void given_ValidInput_When_Exists_ServiceCoparnProcessFile9Service_Then_Success() {
        ServiceCoparnProcessFile9Input input = new ServiceCoparnProcessFile9Input();
        input.setEdiCoparnId(1);
        input.setProgrammingNaveDetailId(1);
        input.setUnitBusinessId(1);
        input.setSubUnitBusinessId(1);
        input.setBooking("BK123");
        input.setPortOfEmbarqueId(1);
        input.setPortOfDescargaId(1);
        input.setPortOfDestinoId(1);
        input.setCustomerId(1);
        input.setProductId(1);
        input.setQuantityReserve(1);
        input.setUserRecordId(1);
        input.setTemperature("10");
        input.setColdTreatment(true);
        input.setControlledAtmosphere(true);
        input.setCntDimenId(1);
        input.setCntDimen2Id(1);
        input.setQuantityReserve2(1);
        input.setCntTypeId(2);
        input.setCustomerId(1);
        input.setCntType2Id(1);
        input.setPassCoparn5x9(true);
        input.setParamSequenceDetails("[{\"sequence_alias\":\"EY7U2ZTO41\"}]");

        BookingEdi bookingEdi = new BookingEdi();
        bookingEdi.setCatBkEdiStatus(new Catalog());
        bookingEdi.getCatBkEdiStatus().setId(1);
        bookingEdi.setRemarkRulesName("FLAG_TO_FLEX");
        bookingEdi.setShippingLine(new ShippingLine());
        bookingEdi.getShippingLine().setId(1);
        bookingEdi.setBkEdiCreationDate("2023-01-01");

        VesselProgramming vesselProgramming = new VesselProgramming();
        vesselProgramming.setId(1);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        Vessel vessel = new Vessel();
        vessel.setName("VesselName");
        vesselProgramming.setVessel(vessel);
        vesselProgrammingDetail.setVesselProgramming(vesselProgramming);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(bookingEdiRepository.findByIdAndActive(anyInt(), eq(true))).thenReturn(bookingEdi);
        Catalog catOperation = new Catalog();
        catOperation.setId(1);
        vesselProgrammingDetail.setCatOperation(catOperation);
        when(vesselProgrammingDetailRepository.getReferenceById(anyInt())).thenReturn(vesselProgrammingDetail);
        Depot depot = new Depot();
        depot.setId(1);
        when(depotRepository.findTopByBusinessUnitIdAndSubBusinessUnitIdAndDepositDefaultTrue(anyInt(), anyInt())).thenReturn(depot);
        when(portRepository.getReferenceById(anyInt())).thenReturn(new Port());
        when(companyRepository.getReferenceById(anyInt())).thenReturn(new Company());
        when(productRepository.getReferenceById(anyInt())).thenReturn(new Product());
        when(userRepository.getReferenceById(anyInt())).thenReturn(new User());
        when(imoRepository.getReferenceById(anyInt())).thenReturn(new Imo());
        when(cargoDocumentRepository.existsByVesselProgrammingDetailIdAndCatCargoOriginIdAndCargoDocumentAndActive(anyInt(), anyInt(), anyString(), eq(true))).thenReturn(true);
        when(cargoDocumentRepository.save(any())).thenReturn(new CargoDocument());
        when(cargoDocumentDetailRepository.save(any())).thenReturn(new CargoDocumentDetail());
        when(eirRepository.save(any())).thenReturn(new Eir());
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(new EirDocumentCargoDetail());
        when(bookingRepository.existsByVesselProgrammingDetailIdAndBookingNumberAndActive(anyInt(), anyString(), eq(true))).thenReturn(true);
        when(bookingRepository.save(any())).thenReturn(new Booking());
        when(bookingDetailRepository.save(any())).thenReturn(new BookingDetail());
        Booking booking = new Booking();
        booking.setId(1);
        booking.setApprovedBooking(true);
        when(bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdAndCatBookingStatusIdAndActiveOrderByBookingIssueDateDesc(anyString(), anyInt(), anyInt(), eq(true))).thenReturn(booking);
        when(!vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(true);

        BookingBlockCancellationDetail bookingBlockCancellationDetail = new BookingBlockCancellationDetail();
        BookingBlockCancellation bookingBlockCancellation = new BookingBlockCancellation();
        bookingBlockCancellation.setId(1);
        bookingBlockCancellationDetail.setBookingBlockCancellation(bookingBlockCancellation);
        CargoDocument cargoDocument = new CargoDocument();
        cargoDocument.setId(1);
        bookingBlockCancellationDetail.setCargoDocument(cargoDocument);
        when(bookingBlockCancellationDetailRepository.findCancelBlockBookingIdAndCargoDocumentId(anyString(), anyInt(), anyInt())).thenReturn(bookingBlockCancellationDetail);
        when(bookingRepository.existsByBookingNumberAndVesselProgrammingDetailIdAndCatBookingStatusIdAndActive(anyString(), anyInt(), anyInt(), eq(true))).thenReturn(true);
        when(bookingBlockCancellationRepository.save(any())).thenReturn(new BookingBlockCancellation());
        when(bookingBlockCancellationDetailRepository.save(any())).thenReturn(new BookingBlockCancellationDetail());
        when(bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdAndCatBookingStatusIdAndActiveOrderByBookingIssueDateDesc(anyString(), anyInt(), anyInt(), eq(true))).thenReturn(new Booking());
        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        when(cargoDocumentDetailRepository.findAllByBookingIdAndActive(anyInt())).thenReturn(List.of(cargoDocumentDetail));
        when(bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdAndCatBookingStatusIdAndActiveOrderByBookingIssueDateDesc(anyString(), anyInt(), anyInt(), eq(true))).thenReturn(new Booking());
        ServiceCoparnProcessFile9Output result = serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(input);

        assertEquals(200, result.getResponseStatus());
        assertEquals("The EDI has been processed.", result.getResponseMessage());
    }

    @Test
    void given_InvalidEdiCoparnId_When_ServiceCoparnProcessFile9Service_Then_NotFound() {
        ServiceCoparnProcessFile9Input input = new ServiceCoparnProcessFile9Input();
        input.setEdiCoparnId(1);
        input.setCntTypeId(1);

        when(bookingEdiRepository.findByIdAndActive(anyInt(), eq(true))).thenReturn(null);

        ServiceCoparnProcessFile9Output result = serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(input);

        assertEquals(404, result.getResponseStatus());
        assertEquals("The EDI does not exist.", result.getResponseMessage());
    }

    @Test
    void given_ValidInput_When_ServiceCoparnProcessFile9Service_Then_RejectEdi() {
        ServiceCoparnProcessFile9Input input = new ServiceCoparnProcessFile9Input();
        input.setEdiCoparnId(1);
        input.setProgrammingNaveDetailId(1);
        input.setUnitBusinessId(1);
        input.setSubUnitBusinessId(1);
        input.setBooking("BK123");
        input.setPortOfEmbarqueId(1);
        input.setPortOfDescargaId(1);
        input.setPortOfDestinoId(1);
        input.setCustomerId(1);
        input.setProductId(1);
        input.setQuantityReserve(1);
        input.setUserRecordId(1);
        input.setTemperature("10");
        input.setColdTreatment(true);
        input.setControlledAtmosphere(true);
        input.setCntTypeId(1);

        BookingEdi bookingEdi = new BookingEdi();
        bookingEdi.setCatBkEdiStatus(new Catalog());
        bookingEdi.getCatBkEdiStatus().setId(1);
        bookingEdi.setShippingLine(new ShippingLine());
        bookingEdi.getShippingLine().setId(1);
        bookingEdi.setBkEdiCreationDate("2023-01-01");

        VesselProgramming vesselProgramming = new VesselProgramming();
        vesselProgramming.setId(1);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        Vessel vessel = new Vessel();
        vessel.setName("VesselName");
        vesselProgramming.setVessel(vessel);
        vesselProgrammingDetail.setVesselProgramming(vesselProgramming);

        when(bookingEdiRepository.findByIdAndActive(anyInt(), eq(true))).thenReturn(bookingEdi);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Catalog catOperation = new Catalog();
        catOperation.setId(1);
        vesselProgrammingDetail.setCatOperation(catOperation);
        when(vesselProgrammingDetailRepository.getReferenceById(anyInt())).thenReturn(vesselProgrammingDetail);
        Depot depot = new Depot();
        depot.setId(1);
        when(depotRepository.findTopByBusinessUnitIdAndSubBusinessUnitIdAndDepositDefaultTrue(anyInt(), anyInt())).thenReturn(depot);
        when(portRepository.getReferenceById(anyInt())).thenReturn(new Port());
        when(companyRepository.getReferenceById(anyInt())).thenReturn(new Company());
        when(productRepository.getReferenceById(anyInt())).thenReturn(new Product());
        when(userRepository.getReferenceById(anyInt())).thenReturn(new User());
        when(imoRepository.getReferenceById(anyInt())).thenReturn(new Imo());

        doNothing().when(bookingEdiRepository).rejectEdiCoparn(anyString(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyString());

        ServiceCoparnProcessFile9Output result = serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(input);

        assertEquals(200, result.getResponseStatus());
        assertEquals("The EDI has been processed.", result.getResponseMessage());
    }

    @Test
    void given_ValidInput_When_TableMissing_ServiceCoparnProcessFile9Service_Then_Success() {
        ServiceCoparnProcessFile9Input input = new ServiceCoparnProcessFile9Input();
        input.setEdiCoparnId(1);
        input.setProgrammingNaveDetailId(1);
        input.setUnitBusinessId(1);
        input.setSubUnitBusinessId(1);
        input.setBooking("BK123");
        input.setPortOfEmbarqueId(1);
        input.setPortOfDescargaId(1);
        input.setPortOfDestinoId(1);
        input.setCustomerId(1);
        input.setProductId(1);
        input.setQuantityReserve(1);
        input.setUserRecordId(1);
        input.setTemperature("10");
        input.setColdTreatment(true);
        input.setControlledAtmosphere(true);
        input.setCntDimenId(1);
        input.setCntTypeId(1);
        input.setCustomerId(1);
        input.setCntType2Id(1);
        input.setPassCoparn5x9(true);
        input.setParamSequenceDetails("");

        BookingEdi bookingEdi = new BookingEdi();
        bookingEdi.setCatBkEdiStatus(new Catalog());
        bookingEdi.getCatBkEdiStatus().setId(1);
        bookingEdi.setRemarkRulesName("FLAG_TO_FLEX");
        bookingEdi.setShippingLine(new ShippingLine());
        bookingEdi.getShippingLine().setId(1);
        bookingEdi.setBkEdiCreationDate("2023-01-01");

        VesselProgramming vesselProgramming = new VesselProgramming();
        vesselProgramming.setId(1);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        Vessel vessel = new Vessel();
        vessel.setName("VesselName");
        vesselProgramming.setVessel(vessel);
        vesselProgrammingDetail.setVesselProgramming(vesselProgramming);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(catalogRepository.findIdByAlias("31049")).thenReturn(1);
        when(bookingEdiRepository.findByIdAndActive(anyInt(), eq(true))).thenReturn(bookingEdi);
        Catalog catOperation = new Catalog();
        catOperation.setId(1);
        vesselProgrammingDetail.setCatOperation(catOperation);
        when(vesselProgrammingDetailRepository.getReferenceById(anyInt())).thenReturn(vesselProgrammingDetail);
        Depot depot = new Depot();
        depot.setId(1);
        when(depotRepository.findTopByBusinessUnitIdAndSubBusinessUnitIdAndDepositDefaultTrue(anyInt(), anyInt())).thenReturn(depot);
        when(portRepository.getReferenceById(anyInt())).thenReturn(new Port());
        when(companyRepository.getReferenceById(anyInt())).thenReturn(new Company());
        when(productRepository.getReferenceById(anyInt())).thenReturn(new Product());
        when(userRepository.getReferenceById(anyInt())).thenReturn(new User());
        when(imoRepository.getReferenceById(anyInt())).thenReturn(new Imo());
        when(cargoDocumentRepository.existsByVesselProgrammingDetailIdAndCatCargoOriginIdAndCargoDocumentAndActive(anyInt(), anyInt(), anyString(), eq(true))).thenReturn(false);
        when(cargoDocumentRepository.save(any())).thenReturn(new CargoDocument());
        when(cargoDocumentDetailRepository.save(any())).thenReturn(new CargoDocumentDetail());
        when(eirRepository.save(any())).thenReturn(new Eir());
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(new EirDocumentCargoDetail());
        when(bookingRepository.existsByVesselProgrammingDetailIdAndBookingNumberAndActive(anyInt(), anyString(), eq(true))).thenReturn(false);
        when(bookingRepository.save(any())).thenReturn(new Booking());
        when(bookingDetailRepository.save(any())).thenReturn(new BookingDetail());
        when(!vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(false);
        doAnswer(invocation -> {
            VesselProgrammingPort vesselProgrammingPort = invocation.getArgument(0);
            vesselProgrammingPort.setId(1); // Mock ID
            return vesselProgrammingPort;
        }).when(vesselProgrammingPortRepository).save(any(VesselProgrammingPort.class));
        ServiceCoparnProcessFile9Output result = serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(input);

        assertEquals(200, result.getResponseStatus());
        assertEquals("The EDI has been processed.", result.getResponseMessage());
    }

    @Test
    void given_ValidInput_When_RemarkEmpty_ServiceCoparnProcessFile9Service_Then_Success() {
        ServiceCoparnProcessFile9Input input = new ServiceCoparnProcessFile9Input();
        input.setEdiCoparnId(1);
        input.setProgrammingNaveDetailId(1);
        input.setUnitBusinessId(1);
        input.setSubUnitBusinessId(1);
        input.setBooking("BK123");
        input.setPortOfEmbarqueId(1);
        input.setPortOfDescargaId(1);
        input.setPortOfDestinoId(1);
        input.setCustomerId(1);
        input.setProductId(1);
        input.setQuantityReserve(1);
        input.setUserRecordId(1);
        input.setTemperature("10");
        input.setColdTreatment(true);
        input.setControlledAtmosphere(true);
        input.setCntDimenId(1);
        input.setCntDimen2Id(1);
        input.setCntTypeId(1);
        input.setCustomerId(1);
        input.setCntType2Id(1);
        input.setQuantityReserve2(1);
        input.setPassCoparn5x9(true);
        input.setParamSequenceDetails("");

        BookingEdi bookingEdi = new BookingEdi();
        bookingEdi.setCatBkEdiStatus(new Catalog());
        bookingEdi.getCatBkEdiStatus().setId(1);
        bookingEdi.setRemarkRulesName("");
        bookingEdi.setShippingLine(new ShippingLine());
        bookingEdi.getShippingLine().setId(1);
        bookingEdi.setBkEdiCreationDate("2023-01-01");

        VesselProgramming vesselProgramming = new VesselProgramming();
        vesselProgramming.setId(1);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        Vessel vessel = new Vessel();
        vessel.setName("VesselName");
        vesselProgramming.setVessel(vessel);
        vesselProgrammingDetail.setVesselProgramming(vesselProgramming);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        when(bookingEdiRepository.findByIdAndActive(anyInt(), eq(true))).thenReturn(bookingEdi);
        Catalog catOperation = new Catalog();
        catOperation.setId(1);
        vesselProgrammingDetail.setCatOperation(catOperation);
        when(vesselProgrammingDetailRepository.getReferenceById(anyInt())).thenReturn(vesselProgrammingDetail);
        Depot depot = new Depot();
        depot.setId(1);
        when(depotRepository.findTopByBusinessUnitIdAndSubBusinessUnitIdAndDepositDefaultTrue(anyInt(), anyInt())).thenReturn(depot);
        when(portRepository.getReferenceById(anyInt())).thenReturn(new Port());
        when(companyRepository.getReferenceById(anyInt())).thenReturn(new Company());
        when(productRepository.getReferenceById(anyInt())).thenReturn(new Product());
        when(userRepository.getReferenceById(anyInt())).thenReturn(new User());
        when(imoRepository.getReferenceById(anyInt())).thenReturn(new Imo());
        when(cargoDocumentRepository.existsByVesselProgrammingDetailIdAndCatCargoOriginIdAndCargoDocumentAndActive(anyInt(), anyInt(), anyString(), eq(true))).thenReturn(false);
        when(cargoDocumentRepository.save(any())).thenReturn(new CargoDocument());
        when(cargoDocumentDetailRepository.save(any())).thenReturn(new CargoDocumentDetail());
        when(eirRepository.save(any())).thenReturn(new Eir());
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(new EirDocumentCargoDetail());
        when(bookingRepository.existsByVesselProgrammingDetailIdAndBookingNumberAndActive(anyInt(), anyString(), eq(true))).thenReturn(false);
        when(bookingRepository.save(any())).thenReturn(new Booking());
        when(bookingDetailRepository.save(any())).thenReturn(new BookingDetail());
        when(!vesselProgrammingPortRepository.existsByVesselProgrammingIdAndPortId(anyInt(), anyInt())).thenReturn(false);
        doAnswer(invocation -> {
            VesselProgrammingPort vesselProgrammingPort = invocation.getArgument(0);
            vesselProgrammingPort.setId(1); // Mock ID
            return vesselProgrammingPort;
        }).when(vesselProgrammingPortRepository).save(any(VesselProgrammingPort.class));
        ServiceCoparnProcessFile9Output result = serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(input);

        assertEquals(200, result.getResponseStatus());
        assertEquals("The EDI has been processed.", result.getResponseMessage());
    }
}