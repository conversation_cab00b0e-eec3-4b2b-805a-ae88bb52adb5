package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.sds.controller.dto.ContainerSearchInput;
import com.maersk.sd1.sds.controller.dto.ContainerSearchOutput;
import com.maersk.sd1.common.repository.ContainerRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ContainerSearchServiceTest {

    @InjectMocks
    private ContainerSearchService containerSearchService;

    @Mock
    private ContainerRepository containerRepository;

    private ContainerSearchInput.Input input;

    @BeforeEach
    void setUp() {
        input = new ContainerSearchInput.Input();
        input.setContainerId(null);
        input.setContainerNumber(null);
        input.setCatSizeId(null);
        input.setCatContainerTypeId(null);
        input.setShippingLineName(null);
        input.setActive(null);
        input.setPage(1);
        input.setSize(10);
    }

    @Test
    void givenValidSearchCriteria_WhenSearchingContainers_ThenReturnMatchingContainers() {
        Container container = new Container();
        container.setId(123);
        Page<Container> page = new PageImpl<>(List.of(container), PageRequest.of(0, 10), 1);
        when(containerRepository.searchContainers(any(), any(), any(), any(), any(), any(), any(Pageable.class)))
                .thenReturn(page);

        ContainerSearchOutput result = containerSearchService.searchContainers(input);

        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords().get(0).get(0));
        assertFalse(result.getContainerList().isEmpty());
        assertEquals(123, result.getContainerList().get(0).getContainerId());
    }

    @Test
    void givenNoMatchingCriteria_WhenSearchingContainers_ThenReturnEmptyResult() {
        Page<Container> emptyPage = new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0);
        when(containerRepository.searchContainers(any(), any(), any(), any(), any(), any(), any(Pageable.class)))
                .thenReturn(emptyPage);

        ContainerSearchOutput result = containerSearchService.searchContainers(input);

        assertNotNull(result);
        assertEquals(0L, result.getTotalRecords().get(0).get(0));
        assertTrue(result.getContainerList().isEmpty());
    }
}