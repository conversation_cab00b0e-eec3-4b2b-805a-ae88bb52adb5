package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.PrinterFormat;
import com.maersk.sd1.common.repository.PrinterFormatRepository;
import com.maersk.sd1.sds.dto.PrinterFormatSearchOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PrinterFormatSearchServiceTest {

    @Mock
    private PrinterFormatRepository printerFormatRepository;

    @InjectMocks
    private PrinterFormatSearchService printerFormatSearchService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidPrinterNameAndSubBusinessUnitId_WhenPrinterFormatsExist_ThenReturnListOfPrinterFormats() {
        String printerName = "Printer A";
        int subBusinessUnitId = 101;

        PrinterFormat printerFormat1 = PrinterFormat.builder()
                .id(1)
                .name("Format A")
                .alias("Alias A")
                .configuration("Config A")
                .build();

        PrinterFormat printerFormat2 = PrinterFormat.builder()
                .id(2)
                .name("Format B")
                .alias("Alias B")
                .configuration("Config B")
                .build();

        when(printerFormatRepository.findPrinterFormatById(printerName, subBusinessUnitId))
                .thenReturn(Arrays.asList(printerFormat1, printerFormat2));

        List<PrinterFormatSearchOutputDTO> result =
                printerFormatSearchService.searchPrinterFormat(printerName, subBusinessUnitId);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Format A", result.get(0).getName());
        assertEquals("Alias B", result.get(1).getAlias());

        verify(printerFormatRepository, times(1)).findPrinterFormatById(printerName, subBusinessUnitId);
    }

    @Test
    void givenValidPrinterNameAndSubBusinessUnitId_WhenNoPrinterFormatsExist_ThenThrowIllegalArgumentException() {
        String printerName = "Printer A";
        int subBusinessUnitId = 101;

        when(printerFormatRepository.findPrinterFormatById(printerName, subBusinessUnitId))
                .thenReturn(Collections.emptyList());

        List<PrinterFormatSearchOutputDTO> result =
                printerFormatSearchService.searchPrinterFormat(printerName, subBusinessUnitId);

        assertNull(result);
        verify(printerFormatRepository, times(1)).findPrinterFormatById(printerName, subBusinessUnitId);
    }

    @Test
    void givenValidPrinterNameAndSubBusinessUnitId_WhenMoreThan10PrinterFormatsExist_ThenReturnFirst10PrinterFormats() {
        String printerName = "Printer A";
        int subBusinessUnitId = 101;

        List<PrinterFormat> printerFormats = Arrays.asList(
                PrinterFormat.builder().id(1).name("Format 1").build(),
                PrinterFormat.builder().id(2).name("Format 2").build(),
                PrinterFormat.builder().id(3).name("Format 3").build(),
                PrinterFormat.builder().id(4).name("Format 4").build(),
                PrinterFormat.builder().id(5).name("Format 5").build(),
                PrinterFormat.builder().id(6).name("Format 6").build(),
                PrinterFormat.builder().id(7).name("Format 7").build(),
                PrinterFormat.builder().id(8).name("Format 8").build(),
                PrinterFormat.builder().id(9).name("Format 9").build(),
                PrinterFormat.builder().id(10).name("Format 10").build(),
                PrinterFormat.builder().id(11).name("Format 11").build()
        );

        when(printerFormatRepository.findPrinterFormatById(printerName, subBusinessUnitId))
                .thenReturn(printerFormats);

        List<PrinterFormatSearchOutputDTO> result =
                printerFormatSearchService.searchPrinterFormat(printerName, subBusinessUnitId);

        assertNotNull(result);
        assertEquals(10, result.size());
        assertEquals("Format 1", result.get(0).getName());
        assertEquals("Format 10", result.get(9).getName());

        verify(printerFormatRepository, times(1)).findPrinterFormatById(printerName, subBusinessUnitId);
    }
}
