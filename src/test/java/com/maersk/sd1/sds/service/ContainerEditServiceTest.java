package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.ContainerEditInput;
import com.maersk.sd1.sds.controller.dto.ContainerEditOutput;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.ContainerRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ContainerEditServiceTest {

    @Mock
    private ContainerRepository containerRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private ContainerEditService containerEditService;

    private Container existingContainer;

    @BeforeEach
    void setUp() {
        existingContainer = new Container();
        existingContainer.setId(100);
        existingContainer.setContainerNumber("OLD123");
    }

    @Test
    void GivenValidContainerDetails_WhenContainerIsUpdated_ThenDetailsAreUpdatedSuccessfully() {

        ContainerEditInput.Input input = new ContainerEditInput.Input();
        input.setContainerId(100);
        input.setContainerNumber("NEW123");
        input.setFamilyId(1);
        input.setSizeId(2);
        input.setContainerTypeId(3);
        input.setShippingLineId(4);
        input.setTare(500);
        input.setMaxPayload(2000);
        input.setIsoCodeId(10);
        input.setCatClassId(null);
        input.setReeferTypeId(null);
        input.setEngineBrandId(null);
        input.setShipperOwn(true);
        input.setActive(true);

        Catalog familyCatalog = new Catalog();
        familyCatalog.setId(1);
        when(catalogRepository.findById(1)).thenReturn(Optional.of(familyCatalog));

        Catalog sizeCatalog = new Catalog();
        sizeCatalog.setId(2);
        when(catalogRepository.findById(2)).thenReturn(Optional.of(sizeCatalog));

        Catalog containerTypeCatalog = new Catalog();
        containerTypeCatalog.setId(3);
        when(catalogRepository.findById(3)).thenReturn(Optional.of(containerTypeCatalog));

        when(containerRepository.findById(100)).thenReturn(Optional.of(existingContainer));
        when(containerRepository.findByContainerNumberIgnoreCaseAndIdNot("NEW123", 100)).thenReturn(null);
        when(containerRepository.save(any(Container.class))).thenAnswer(i -> i.getArguments()[0]);

        ContainerEditOutput output = containerEditService.updateContainer(input);

        assertNotNull(output);
        assertEquals(1, output.getRespStatus());
        assertTrue(output.getRespMessage().contains("Updated the details successfully"));
    }

    @Test
    void GivenContainerNumberConflict_WhenUpdateContainerIsAttempted_ThenConflictMessageIsReturned() {
        Container conflictingContainer = new Container();
        conflictingContainer.setId(999);
        conflictingContainer.setContainerNumber("NEW123");

        ContainerEditInput.Input input = new ContainerEditInput.Input();
        input.setContainerId(100);
        input.setContainerNumber("NEW123");
        input.setFamilyId(1);
        input.setSizeId(2);
        input.setContainerTypeId(3);
        input.setShippingLineId(4);
        input.setTare(500);
        input.setMaxPayload(2000);
        input.setShipperOwn(true);
        input.setActive(true);

        when(containerRepository.findById(100)).thenReturn(Optional.of(existingContainer));
        when(containerRepository.findByContainerNumberIgnoreCaseAndIdNot("NEW123", 100)).thenReturn(conflictingContainer);

        ContainerEditOutput output = containerEditService.updateContainer(input);

        assertNotNull(output);
        assertEquals(2, output.getRespStatus());
        assertTrue(output.getRespMessage().contains("Ya existe un contenedor con el mismo número"));
    }

    @Test
    void GivenNonExistingContainer_WhenUpdateIsAttempted_ThenContainerNotFoundMessageIsReturned() {
        ContainerEditInput.Input input = new ContainerEditInput.Input();
        input.setContainerId(200);
        input.setContainerNumber("SOMECONTAINER");
        input.setFamilyId(1);
        input.setSizeId(2);
        input.setContainerTypeId(3);
        input.setShippingLineId(4);
        input.setTare(500);
        input.setMaxPayload(2000);
        input.setShipperOwn(true);
        input.setActive(true);

        when(containerRepository.findById(200)).thenReturn(Optional.empty());

        ContainerEditOutput output = containerEditService.updateContainer(input);

        assertNotNull(output);
        assertEquals(0, output.getRespStatus());
        assertTrue(output.getRespMessage().contains("Container not found"));
    }

    @Test
    void GivenDatabaseError_WhenUpdateContainerIsAttempted_ThenErrorMessageIsReturned() {
        ContainerEditInput.Input input = new ContainerEditInput.Input();
        input.setContainerId(100);
        input.setContainerNumber("THROWS");

        when(containerRepository.findById(100)).thenThrow(new RuntimeException("Database error"));

        ContainerEditOutput output = containerEditService.updateContainer(input);
        assertNotNull(output);
        assertEquals(0, output.getRespStatus());
        assertTrue(output.getRespMessage().contains("Database error"));
    }
}