package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.controller.dto.SdeseteoEstimadoEmrRegistrarInput;
import com.maersk.sd1.sds.controller.dto.SdeseteoEstimadoEmrRegistrarOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SdeseteoEstimadoEmrRegistrarServiceTest {

    @Mock
    private EstimateEmrSettingRepository estimateEmrSettingRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private SdeseteoEstimadoEmrRegistrarService sdeseteoEstimadoEmrRegistrarService;

    private SdeseteoEstimadoEmrRegistrarInput.Input input;

    @BeforeEach
    public void setUp() {
        input = new SdeseteoEstimadoEmrRegistrarInput.Input();
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(2);
        input.setShippingLineId(3);
        input.setCatModeGenerateFileEstimatedId(4);
        input.setServiceDescription("Test Service");
        input.setFileCorrelative(1);
        input.setFileExtension("txt");
        input.setSendEmail("<EMAIL>");
        input.setMinutesElapsed(30);
        input.setActive(true);
        input.setUserRegistration(1);
        input.setShopcodeMerc("SHOP123");
        input.setAzureId("AZURE123");
    }

    @Test
    public void testRegistrarEstimadoEmr_Success() {
        when(estimateEmrSettingRepository.findExistingEstimateEmrSetting(any(), any(), any())).thenReturn(null);
        when(businessUnitRepository.getReferenceById(any())).thenReturn(new BusinessUnit());
        when(shippingLineRepository.getReferenceById(any())).thenReturn(new ShippingLine());
        when(catalogRepository.getReferenceById(any())).thenReturn(new Catalog());
        when(userRepository.getReferenceById(any())).thenReturn(new User());
        when(estimateEmrSettingRepository.save(any(EstimateEmrSetting.class))).thenReturn(new EstimateEmrSetting());

        SdeseteoEstimadoEmrRegistrarOutput output = sdeseteoEstimadoEmrRegistrarService.registrarEstimadoEmr(input);

        assertEquals(1, output.getStatus());
        assertEquals("Successfully register", output.getMessage());
    }

    @Test
    public void testRegistrarEstimadoEmr_DuplicateEntry() {
        when(estimateEmrSettingRepository.findExistingEstimateEmrSetting(any(), any(), any())).thenReturn(1);

        SdeseteoEstimadoEmrRegistrarOutput output = sdeseteoEstimadoEmrRegistrarService.registrarEstimadoEmr(input);

        assertEquals(2, output.getStatus());
        assertEquals("There is another mercplus with the same key (Business Unit, Sub Business Unit and Shipping Line)", output.getMessage());
    }

    @Test
    public void testRegistrarEstimadoEmr_Exception() {
        when(estimateEmrSettingRepository.findExistingEstimateEmrSetting(any(), any(), any())).thenThrow(new RuntimeException("Test Exception"));

        SdeseteoEstimadoEmrRegistrarOutput output = sdeseteoEstimadoEmrRegistrarService.registrarEstimadoEmr(input);

        assertEquals(0, output.getStatus());
        assertEquals("An error occurred while processing the request", output.getMessage());
    }
}