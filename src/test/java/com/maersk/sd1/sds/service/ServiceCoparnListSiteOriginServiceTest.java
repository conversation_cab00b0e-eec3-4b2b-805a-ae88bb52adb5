package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ServiceCoparnListOriginSiteInput;
import com.maersk.sd1.sds.dto.ServiceCoparnListOriginSiteOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

class ServiceCoparnListSiteOriginServiceTest {

    @InjectMocks
    private ServiceCoparnListSiteOriginService serviceCoparnListSiteOriginService;

    @Mock
    private ListOriginSitesForCoparnService listOriginSitesForCoparnService;

    @Mock
    private ListOriginSiteHsdService listOriginSiteHsdService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testServiceCoparnListSiteOriginServiceEsMslIs1() {
        ServiceCoparnListOriginSiteInput.Root request = new ServiceCoparnListOriginSiteInput.Root();
        ServiceCoparnListOriginSiteInput.Input input = new ServiceCoparnListOriginSiteInput.Input();
        input.setEsMsl('1');
        request.setPrefix(new ServiceCoparnListOriginSiteInput.Prefix());
        request.getPrefix().setInput(input);

        ServiceCoparnListOriginSiteOutput expectedOutput = new ServiceCoparnListOriginSiteOutput();
        expectedOutput.setBookingDetails(Collections.emptyList());

        when(listOriginSitesForCoparnService.listOriginSitesForCoparnService()).thenReturn(Collections.emptyList());

        ResponseEntity<ResponseController<ServiceCoparnListOriginSiteOutput>> response = serviceCoparnListSiteOriginService.serviceCoparnListSiteOriginService(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(expectedOutput.getBookingDetails(), response.getBody().getResult().getBookingDetails());
    }

    @Test
    void testServiceCoparnListSiteOriginServiceEsMslIs0() {
        ServiceCoparnListOriginSiteInput.Root request = new ServiceCoparnListOriginSiteInput.Root();
        ServiceCoparnListOriginSiteInput.Input input = new ServiceCoparnListOriginSiteInput.Input();
        input.setEsMsl('0');
        request.setPrefix(new ServiceCoparnListOriginSiteInput.Prefix());
        request.getPrefix().setInput(input);

        ServiceCoparnListOriginSiteOutput expectedOutput = new ServiceCoparnListOriginSiteOutput();
        expectedOutput.setBookingDetails(Collections.emptyList());

        when(listOriginSiteHsdService.listOriginSiteHsdService()).thenReturn(Collections.emptyList());

        ResponseEntity<ResponseController<ServiceCoparnListOriginSiteOutput>> response = serviceCoparnListSiteOriginService.serviceCoparnListSiteOriginService(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(expectedOutput.getBookingDetails(), response.getBody().getResult().getBookingDetails());
    }
}