package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdi;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.BookingEdiRepository;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.dto.BookingEdiDeleteOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class BookingEdiDeleteServiceTest {

    @Mock
    private BookingEdiRepository bookingEdiRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    @InjectMocks
    private BookingEdiDeleteService bookingEdiDeleteService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testDeleteEdiCoparnSuccess() {
        Integer ediCoparnId = 1;
        Integer usuarioId = 1;
        Integer idiomaId = 1;

        BookingEdi bookingEdi = new BookingEdi();
        Catalog status = new Catalog();
        status.setId(48271);
        bookingEdi.setCatBkEdiStatus(status);
        bookingEdi.setBkEdiProcessedComment("Old comment");

        when(bookingEdiRepository.findById(ediCoparnId)).thenReturn(Optional.of(bookingEdi));
        when(messageLanguageRepository.fnTranslatedMessage("INTERFAZ_EDI", 2, idiomaId)).thenReturn("Rejected by user");
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, idiomaId)).thenReturn("Deleted successfully");

        BookingEdiDeleteOutput output = bookingEdiDeleteService.deleteEdiCoparn(ediCoparnId, usuarioId, idiomaId);

        assertEquals(1, output.getRespEstado());
        assertEquals("Deleted successfully", output.getRespMensaje());
        verify(bookingEdiRepository).updateCoparnToRejected(eq(ediCoparnId), eq(usuarioId), any(LocalDateTime.class), eq("Rejected by user Old comment"));
    }

    @Test
    void testDeleteEdiCoparnNotFound() {
        Integer ediCoparnId = 1;
        Integer idiomaId = 1;

        when(bookingEdiRepository.findById(ediCoparnId)).thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage("INTERFAZ_EDI", 5, idiomaId)).thenReturn("EDI Coparn record not found.");

        BookingEdiDeleteOutput output = bookingEdiDeleteService.deleteEdiCoparn(ediCoparnId, null, idiomaId);

        assertEquals(2, output.getRespEstado());
        assertEquals("EDI Coparn record not found.", output.getRespMensaje());
    }

    @Test
    void testDeleteEdiCoparnInvalidState() {
        Integer ediCoparnId = 1;
        Integer idiomaId = 1;

        BookingEdi bookingEdi = new BookingEdi();
        Catalog status = new Catalog();
        status.setId(48273);
        bookingEdi.setCatBkEdiStatus(status);

        when(bookingEdiRepository.findById(ediCoparnId)).thenReturn(Optional.of(bookingEdi));
        when(messageLanguageRepository.fnTranslatedMessage("INTERFAZ_EDI", 4, idiomaId)).thenReturn("Current state {ESX} does not allow deletion.");
        when(catalogLanguageRepository.fnCatalogoTraducidoDes(48273, idiomaId)).thenReturn("In Progress");

        BookingEdiDeleteOutput output = bookingEdiDeleteService.deleteEdiCoparn(ediCoparnId, null, idiomaId);

        assertEquals(2, output.getRespEstado());
        assertEquals("Current state In Progress does not allow deletion.", output.getRespMensaje());
    }

    @Test
    void testDeleteEdiCoparnException() {
        Integer ediCoparnId = 1;
        Integer usuarioId = 1;
        Integer idiomaId = 1;

        when(bookingEdiRepository.findById(ediCoparnId)).thenThrow(new RuntimeException("Database error"));

        BookingEdiDeleteOutput output = bookingEdiDeleteService.deleteEdiCoparn(ediCoparnId, usuarioId, idiomaId);

        assertEquals(2, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
    }
}