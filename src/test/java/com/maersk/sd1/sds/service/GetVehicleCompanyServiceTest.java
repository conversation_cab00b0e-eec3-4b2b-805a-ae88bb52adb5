package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.sds.dto.GetVehicleCompanyInput;
import com.maersk.sd1.sds.dto.GetVehicleCompanyOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class GetVehicleCompanyServiceTest {

    @InjectMocks
    private GetVehicleCompanyService getVehicleCompanyService;

    @Mock
    private TruckRepository truckRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetVehicleCompanyService() {
        GetVehicleCompanyInput.Root request = new GetVehicleCompanyInput.Root();
        GetVehicleCompanyInput.Input input = new GetVehicleCompanyInput.Input();
        input.setVehicleId(1);
        input.setVehiclePlate("ABC123");
        GetVehicleCompanyInput.Prefix prefix = new GetVehicleCompanyInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        GetVehicleCompanyOutput output = new GetVehicleCompanyOutput();
        output.setVehicleId(3);
        output.setPlate("ABC123");
        output.setCompanyName("Test Company");

        Pageable pageable = PageRequest.of(0, 1);
        when(truckRepository.findVehicleCompanyByCriteria(1, "ABC123", pageable)).thenReturn(Collections.singletonList(output));

        List<GetVehicleCompanyOutput> result = getVehicleCompanyService.getVehicleCompanyService(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(3, result.get(0).getVehicleId());
        assertEquals("ABC123", result.get(0).getPlate());
        assertEquals("Test Company", result.get(0).getCompanyName());
    }
}