package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.sds.dto.BookingCancellationBlockingOutput;
import com.maersk.sd1.sds.repository.CancelBlockBookingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CancelBlockBookingServiceTest {

    @Mock
    private CancelBlockBookingRepository cancelBlockBookingRepository;

    @InjectMocks
    private CancelBlockBookingService cancelBlockBookingService;

    private static final Integer BUSINESS_UNIT_ID = 1;
    private static final Integer SUB_BUSINESS_UNIT_ID = 100;
    private static final Integer CAT_TYPE = 1;
    private static final Integer LANGUAGE_ID = 1;
    private static final String VALID_BOOKING_NUMBER = "BK123";
    private static final String INVALID_BOOKING_NUMBER = "INVALID";
    private static final Integer CAT_BOOKING_VIGENTE = 43061;

    @BeforeEach
    void setUp() {
        reset(cancelBlockBookingRepository);
    }

    @Test
    void given_ValidBooking_When_CancellationAttempted_Then_BookingIsApproved() {
        Booking booking = new Booking();
        booking.setApprovedBooking(false);

        Catalog mockCatalog = new Catalog();
        when(cancelBlockBookingRepository.findCatalogByAlias("sd1_creationsource_cancellation_bk"))
                .thenReturn(Optional.of(mockCatalog));

        when(cancelBlockBookingRepository.findBookingToApprove(SUB_BUSINESS_UNIT_ID, VALID_BOOKING_NUMBER, CAT_BOOKING_VIGENTE))
                .thenReturn(Optional.of(booking));

        when(cancelBlockBookingRepository.findBookingCancellationBlockingResults(VALID_BOOKING_NUMBER, SUB_BUSINESS_UNIT_ID, CAT_BOOKING_VIGENTE))
                .thenReturn(List.of(new BookingCancellationBlockingOutput()));

        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, VALID_BOOKING_NUMBER, LANGUAGE_ID
        );

        verify(cancelBlockBookingRepository, times(1)).save(booking);
        assertNotNull(result);
    }

    @Test
    void given_InvalidBooking_When_CancellationAttempted_Then_NoChangesAreMade() {
        when(cancelBlockBookingRepository.findBookingToApprove(SUB_BUSINESS_UNIT_ID, INVALID_BOOKING_NUMBER, CAT_BOOKING_VIGENTE))
                .thenReturn(Optional.empty());

        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, INVALID_BOOKING_NUMBER, LANGUAGE_ID
        );


        assertTrue(result.isEmpty());

        verify(cancelBlockBookingRepository, never()).save(any());
    }

    @Test
    void given_NullBookingNumber_When_CancellationAttempted_Then_NoChangesAreMade() {
        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, null, LANGUAGE_ID
        );


        assertTrue(result.isEmpty());

        verify(cancelBlockBookingRepository, never()).save(any());
    }

    @Test
    void given_EmptyBookingNumber_When_CancellationAttempted_Then_NoChangesAreMade() {
        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, "   ", LANGUAGE_ID
        );
        assertTrue(result.isEmpty());

        verify(cancelBlockBookingRepository, never()).save(any());
    }

    @Test
    void given_ValidBooking_When_NoCatalogFound_Then_BookingIsStillApproved() {
        Booking booking = new Booking();
        booking.setApprovedBooking(false);

        when(cancelBlockBookingRepository.findCatalogByAlias("sd1_creationsource_cancellation_bk"))
                .thenReturn(Optional.empty());

        when(cancelBlockBookingRepository.findBookingToApprove(SUB_BUSINESS_UNIT_ID, VALID_BOOKING_NUMBER, CAT_BOOKING_VIGENTE))
                .thenReturn(Optional.of(booking));

        when(cancelBlockBookingRepository.findBookingCancellationBlockingResults(VALID_BOOKING_NUMBER, SUB_BUSINESS_UNIT_ID, CAT_BOOKING_VIGENTE))
                .thenReturn(new ArrayList<>());

        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, VALID_BOOKING_NUMBER, LANGUAGE_ID
        );

        assertNotNull(result);

        verify(cancelBlockBookingRepository, times(1)).save(booking);
    }

    @Test
    void given_ValidBooking_When_CatalogFound_Then_BookingIsApprovedWithCatalog() {
        Booking booking = new Booking();
        booking.setApprovedBooking(false);

        Catalog mockCatalog = new Catalog();
        when(cancelBlockBookingRepository.findCatalogByAlias("sd1_creationsource_cancellation_bk"))
                .thenReturn(Optional.of(mockCatalog));

        when(cancelBlockBookingRepository.findBookingToApprove(SUB_BUSINESS_UNIT_ID, VALID_BOOKING_NUMBER, CAT_BOOKING_VIGENTE))
                .thenReturn(Optional.of(booking));

        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, VALID_BOOKING_NUMBER, LANGUAGE_ID
        );

        assertEquals(mockCatalog, booking.getCatOriginBookingCreation());
        assertNotNull(result);
        verify(cancelBlockBookingRepository, times(1)).save(booking);
    }

    @Test
    void given_ValidBooking_When_ExceptionOccurs_Then_ErrorResponseReturned() {
        when(cancelBlockBookingRepository.findBookingToApprove(SUB_BUSINESS_UNIT_ID, VALID_BOOKING_NUMBER, CAT_BOOKING_VIGENTE))
                .thenThrow(new RuntimeException("Database error"));

        List<BookingCancellationBlockingOutput> result = cancelBlockBookingService.cancelOrBlockBooking(
                BUSINESS_UNIT_ID, SUB_BUSINESS_UNIT_ID, CAT_TYPE, VALID_BOOKING_NUMBER, LANGUAGE_ID
        );

        assertTrue(result.isEmpty());

        verify(cancelBlockBookingRepository, never()).save(any());
    }
}
