package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Imo;
import com.maersk.sd1.common.repository.ImoRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.controller.dto.ImoSearchOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class ImoSearchServiceTest {

    @Mock
    ImoRepository imoRepository;

    @Mock
    MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    ImoSearchService imoSearchService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ImoIdNotNull_When_SearchImo_Then_ReturnAll() {
        // given
        Integer languageId = 1;
        Integer imoId = 10;
        String imoName = null;

        Imo imo = new Imo();
        imo.setId(10);
        imo.setImoCode("IMO10");
        imo.setDescription("Desc10");
        imo.setLanguageMessageAlias("Alias10");
        imo.setActive(true);

        List<Imo> imoList = new ArrayList<>();
        imoList.add(imo);

        Page<Imo> pageResult = new PageImpl<>(imoList);
        when(imoRepository.searchImo(imoId, imoName, PageRequest.of(0, 10))).thenReturn(pageResult);
        when(messageLanguageRepository.fnTranslatedMessage("Alias10", 1, languageId)).thenReturn("Translated10");

        // when
        List<ImoSearchOutput> result = imoSearchService.searchImo(languageId, imoId, imoName);

        // then
        assertEquals(1, result.size());
        assertEquals(10, result.getFirst().getImoId());
        assertEquals("IMO10 Translated10", result.getFirst().getImoName());
    }

    @Test
    void given_ImoIdIsNullAndName_When_SearchImo_Then_ReturnOnlyActiveMatches() {
        // given
        Integer languageId = 1;
        Integer imoId = null;
        String imoName = "buscar";

        Page<Imo> pageResult = getImos();
        when(imoRepository.searchImo(imoId, imoName, PageRequest.of(0, 10))).thenReturn(pageResult);
        when(messageLanguageRepository.fnTranslatedMessage("alias11", 1, languageId)).thenReturn("trans11");
        when(messageLanguageRepository.fnTranslatedMessage("alias12", 1, languageId)).thenReturn("trans12");

        // when
        List<ImoSearchOutput> result = imoSearchService.searchImo(languageId, imoId, imoName);

        // then
        
        assertEquals(2, result.size());
        assertEquals(11, result.getFirst().getImoId());
        assertEquals("ABC trans11", result.getFirst().getImoName());
        assertEquals(12, result.get(1).getImoId());
        assertEquals("XYZ trans12", result.get(1).getImoName());
    }

    private Page<Imo> getImos() {
        Imo imo1 = new Imo();
        imo1.setId(11);
        imo1.setImoCode("ABC");
        imo1.setDescription("desc");
        imo1.setLanguageMessageAlias("alias11");
        imo1.setActive(true);

        Imo imo2 = new Imo();
        imo2.setId(12);
        imo2.setImoCode("XYZ");
        imo2.setDescription("desc xyz");
        imo2.setLanguageMessageAlias("alias12");
        imo2.setActive(true);

        List<Imo> imoList = new ArrayList<>();
        imoList.add(imo1);
        imoList.add(imo2);

        return new PageImpl<>(imoList);
    }
}
