package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Vessel;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.repository.VesselRepository;
import com.maersk.sd1.sds.dto.VesselRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VesselRegisterServiceTest {

    @Mock
    private VesselRepository vesselRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private VesselRegisterService vesselRegisterService;

    private User mockUser;
    private Vessel mockVessel;

    @BeforeEach
    void setUp() {
        mockUser = new User();
        mockUser.setId(1);

        mockVessel = new Vessel();
        mockVessel.setId(100);
        mockVessel.setShip("TEST VESSEL");
        mockVessel.setName("Test Vessel");
        mockVessel.setCallSign("CALL123");
        mockVessel.setImoNumber("IMO12345");
        mockVessel.setActive(true);
        mockVessel.setRegistrationUser(mockUser);
        mockVessel.setRegistrationDate(LocalDateTime.now());
    }

    @Test
    void registerVesselSuccess() {
        when(vesselRepository.findFirstByShipIgnoreCase("TEST VESSEL")).thenReturn(Optional.empty());
        when(vesselRepository.findFirstByNameIgnoreCase("Test Vessel")).thenReturn(Optional.empty());
        when(userRepository.findById(1)).thenReturn(Optional.of(mockUser));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, 1)).thenReturn("Vessel registered successfully.");
        when(vesselRepository.save(any(Vessel.class))).thenAnswer(invocation -> {
            Vessel savedVessel = invocation.getArgument(0);
            savedVessel.setId(101);
            return savedVessel;
        });

        VesselRegisterOutput output = vesselRegisterService.registerVessel(
                "TEST VESSEL", "CALL123", "IMO12345", true, 1, "Test Vessel", 1
        );

        assertEquals(1, output.getRespEstado());
        assertEquals(101, output.getRespNewId());
        assertEquals("Vessel registered successfully.", output.getRespMensaje());

        verify(vesselRepository, times(1)).save(any(Vessel.class));
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage("GENERAL", 9, 1);
    }

    @Test
    void registerVesselDuplicateShip() {
        when(vesselRepository.findFirstByShipIgnoreCase("TEST VESSEL")).thenReturn(Optional.of(mockVessel));
        when(messageLanguageRepository.fnTranslatedMessage("INS_NAVE", 1, 1)).thenReturn("Ship already exists with ID {IDX}");

        VesselRegisterOutput output = vesselRegisterService.registerVessel(
                "TEST VESSEL", "CALL123", "IMO12345", true, 1, "Test Vessel", 1
        );

        assertEquals(2, output.getRespEstado());
        assertEquals(0, output.getRespNewId());
        assertTrue(output.getRespMensaje().contains("Ship already exists with ID 100"));

        verify(vesselRepository, never()).save(any(Vessel.class));
    }

    @Test
    void registerVesselDuplicateName() {
        when(vesselRepository.findFirstByNameIgnoreCase("Test Vessel")).thenReturn(Optional.of(mockVessel));
        when(messageLanguageRepository.fnTranslatedMessage("INS_NAVE", 2, 1)).thenReturn("Name already exists with ID {IDX}");

        VesselRegisterOutput output = vesselRegisterService.registerVessel(
                "NEW VESSEL", "CALL123", "IMO12345", true, 1, "Test Vessel", 1
        );

        assertEquals(2, output.getRespEstado());
        assertEquals(0, output.getRespNewId());
        assertTrue(output.getRespMensaje().contains("Name already exists with ID 100"));

        verify(vesselRepository, never()).save(any(Vessel.class));
    }

    @Test
    void registerVessel_ExceptionHandling() {
        when(vesselRepository.findFirstByShipIgnoreCase("TEST VESSEL")).thenThrow(new RuntimeException("Database error"));

        VesselRegisterOutput output = vesselRegisterService.registerVessel(
                "TEST VESSEL", "CALL123", "IMO12345", true, 1, "Test Vessel", 1
        );

        assertEquals(0, output.getRespEstado());
        assertEquals(0, output.getRespNewId());
        assertEquals("Database error", output.getRespMensaje());

        verify(vesselRepository, never()).save(any(Vessel.class));
    }
}
