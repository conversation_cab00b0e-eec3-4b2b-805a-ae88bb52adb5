package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Country;
import com.maersk.sd1.common.model.Port;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.PortRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.dto.PortRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
class PortRegisterServiceTest {

    @InjectMocks
    private PortRegisterService portRegisterService;

    @Mock
    private PortRepository portRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    private Port port;
    private Country country;
    private User user;

    @BeforeEach
    void setUp() {
        port = new Port();
        port.setId(1);
        port.setPort("OldPort");
        port.setName("OldName");
        port.setActive(true);
        port.setRegistrationDate(LocalDateTime.now());

        country = new Country();
        country.setId(1);

        user = new User();
        user.setId(1);
        user.setAlias("alias");
        user.setNames("name");
        user.setFirstLastName("lastName");
        user.setKey("key");
        user.setStatus('1');
        user.setRegistrationUser(user);
        user.setRegistrationDate(LocalDateTime.now());
    }

    @Test
    void testRegisterPortSuccess() {
        when(portRepository.findByPortIgnoreCase(anyString())).thenReturn(Optional.empty());
        when(portRepository.save(any(Port.class))).thenReturn(port);
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");

        PortRegisterOutput result = portRegisterService.registerPort("NewPort", "NewName", 1, true, 1, 1);

        assertEquals(0, result.getRespEstado());
    }

    @Test
    void testRegisterPortPortExists() {
        when(portRepository.findByPortIgnoreCase(anyString())).thenReturn(Optional.of(port));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Port already exists");

        PortRegisterOutput result = portRegisterService.registerPort("OldPort", "OldName", 1, true, 1, 1);

        assertEquals(2, result.getRespEstado());
        assertEquals("Port already exists", result.getRespMensaje());
        assertEquals(0, result.getRespNewId());
    }

    @Test
    void testRegisterPortException() {
        when(portRepository.findByPortIgnoreCase(anyString())).thenThrow(new RuntimeException("Exception"));

        PortRegisterOutput result = portRegisterService.registerPort("NewPort", "NewName", 1, true, 1, 1);

        assertEquals(0, result.getRespEstado());
        assertEquals("Exception", result.getRespMensaje());
        assertEquals(0, result.getRespNewId());
    }
}
