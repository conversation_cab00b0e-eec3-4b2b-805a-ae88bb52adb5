package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.BookingEdiSettingBURepository;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import com.maersk.sd1.sds.dto.SetEdiCopyUpdateInput;
import com.maersk.sd1.sds.dto.SetEdiCopyUpdateOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
class SetEdiCopyUpdateServiceTest {

    @Mock
    private BookingEdiSettingRepository bookingEdiSettingRepository;

    @Mock
    private BookingEdiSettingBURepository bookingEdiSettingBURepository;

    @InjectMocks
    private SetEdiCopyUpdateService setEdiCopyUpdateService;

    private SetEdiCopyUpdateInput.Input inputData;
    private BookingEdiSetting existingSetting;

    @BeforeEach
    public void setUp() {
        // Initialize the input data for testing
        inputData = new SetEdiCopyUpdateInput.Input();
        inputData.setSeteoEdiCoparnId(1);
        inputData.setLineaNavieraId(100);
        inputData.setCatCanalRecepcionCoparnId(2L);
        inputData.setCatModoProcesarCoparnId(3L);
        inputData.setEdiCoparnDescripcion("EDI description for COPARN");
        inputData.setAzureId("AzureID_12345");
        inputData.setSftpCoparnId("SFTP123");
        inputData.setFtpCoparnId("FTP456");
        inputData.setCarpetaCoparnRuta("/path/to/coparn/folder");
        inputData.setExtensionArchivoDescargar(".xml");
        inputData.setRutaMoverEdi("/path/to/move/edi");
        inputData.setPermitirCrearProgNaveAutomatico(true);
        inputData.setPermitirCrearClienteAutomatico(false);
        inputData.setEsHistorico(false);
        inputData.setFechaDebaja(LocalDateTime.now());
        inputData.setMotivoDebaja("Data outdated");
        inputData.setActivo(true);
        inputData.setUsuarioModificacionId(9999L);
        inputData.setUnidadNegocioId(10L);

        // Existing Setting for testing update
        existingSetting = new BookingEdiSetting();
        existingSetting.setId(1);
    }

    @Test
    void testUpdateSeteoEdiCoparn_Success() {
        // Mock existing data in repository
        when(bookingEdiSettingRepository.findById(inputData.getSeteoEdiCoparnId())).thenReturn(Optional.of(existingSetting));

        // Mock the save operation for the repository
        when(bookingEdiSettingRepository.save(any(BookingEdiSetting.class))).thenReturn(existingSetting);

        // Perform the update operation
        SetEdiCopyUpdateOutput result = setEdiCopyUpdateService.updateSeteoEdiCoparn(inputData);

        // Verify the save operation and assert the output
        verify(bookingEdiSettingRepository, times(1)).save(any(BookingEdiSetting.class));
        assertEquals(1, result.getRespEstado());
        assertEquals("Registro actualizado correctamente", result.getRespMensaje());
    }

    @Test
    void testUpdateSeteoEdiCoparn_NoExistingSetting() {
        // Mock no existing data in repository
        when(bookingEdiSettingRepository.findById(inputData.getSeteoEdiCoparnId())).thenReturn(Optional.empty());

        // Perform the update operation
        SetEdiCopyUpdateOutput result = setEdiCopyUpdateService.updateSeteoEdiCoparn(inputData);

        // Verify the output when there is no existing setting
        assertEquals(0, result.getRespEstado());
        assertEquals("No BookingEdiSetting found for ID: 1", result.getRespMensaje());
    }

    @Test
    void testUpdateSeteoEdiCoparn_Exception() {
        // Mock the repository to throw an exception
        when(bookingEdiSettingRepository.findById(inputData.getSeteoEdiCoparnId())).thenThrow(new RuntimeException("Database error"));

        // Perform the update operation
        SetEdiCopyUpdateOutput result = setEdiCopyUpdateService.updateSeteoEdiCoparn(inputData);

        // Verify the output when there is an exception
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Database error"));
    }

    @Test
    void testUpdateSeteoEdiCoparn_UpdateChildDetails() {
        // Mock existing data in repository
        when(bookingEdiSettingRepository.findById(inputData.getSeteoEdiCoparnId())).thenReturn(Optional.of(existingSetting));

        // Prepare detail data
        SetEdiCopyUpdateInput.Detail detail = new SetEdiCopyUpdateInput.Detail();
        detail.setSubBusinessUnitId(1L);
        detail.setBkEdiApplyCopySend(true);
        detail.setCatBkEdiForwardChannelId(2L);
        detail.setBkEdiForwardFftpId("FTP456");
        detail.setBkEdiForwardSftpId("SFTP123");
        detail.setBkEdiForwardFolderRoute("/path/to/forward/coparn");

        inputData.setDetalle(Arrays.asList(detail));

        // Mock the delete and save operations for child entities
        doNothing().when(bookingEdiSettingBURepository).deleteByBookingEdiSetting(any(BookingEdiSetting.class));
        when(bookingEdiSettingBURepository.save(any(BookingEdiSettingBU.class))).thenReturn(new BookingEdiSettingBU());

        // Perform the update operation
        SetEdiCopyUpdateOutput result = setEdiCopyUpdateService.updateSeteoEdiCoparn(inputData);

        // Verify that the child entities were deleted and saved
        verify(bookingEdiSettingBURepository, times(1)).deleteByBookingEdiSetting(any(BookingEdiSetting.class));
        verify(bookingEdiSettingBURepository, times(1)).save(any(BookingEdiSettingBU.class));
        assertEquals(1, result.getRespEstado());
        assertEquals("Registro actualizado correctamente", result.getRespMensaje());
    }
}
