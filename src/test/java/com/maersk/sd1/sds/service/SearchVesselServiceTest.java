package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.VesselRepository;
import com.maersk.sd1.sds.dto.SearchVesselOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class SearchVesselServiceTest {

    @Mock
    private VesselRepository vesselRepository;

    @InjectMocks
    private SearchVesselService searchVesselService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_VesselNameIsNull_When_SearchVesselIsCalled_Then_ReturnTop10Vessels() {
        when(vesselRepository.searchVessel(null, PageRequest.of(0, 10)))
                .thenReturn(Collections.emptyList());

        List<SearchVesselOutputDTO> result = searchVesselService.searchVessel(null);

        assertTrue(result.isEmpty(), "Result should be empty when vessel name is null.");
    }

    @Test
    void given_ValidVesselName_When_SearchVesselIsCalled_Then_ReturnMatchingVessels() {
        String vesselName = "Test Vessel";
        List<SearchVesselOutputDTO> mockResponse = List.of(new SearchVesselOutputDTO());

        when(vesselRepository.searchVessel(vesselName, PageRequest.of(0, 10)))
                .thenReturn(mockResponse);

        List<SearchVesselOutputDTO> result = searchVesselService.searchVessel(vesselName);

        assertFalse(result.isEmpty(), "Result should contain matching vessels.");
        assertEquals(1, result.size(), "Expected one vessel in the result.");
    }

    @Test
    void given_RepositoryThrowsException_When_SearchVesselIsCalled_Then_ReturnEmptyList() {
        when(vesselRepository.searchVessel(anyString(), any(PageRequest.class)))
                .thenThrow(new RuntimeException("Database error"));

        List<SearchVesselOutputDTO> result = searchVesselService.searchVessel("Test");

        assertEquals(Collections.emptyList(), result, "Result should be empty when exception occurs.");
    }
}
