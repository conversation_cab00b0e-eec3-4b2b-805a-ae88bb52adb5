package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursRegisterInput;
import com.maersk.sd1.sds.dto.EstimateEmrCostManHoursRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class EstimateEmrCostManHoursRegisterServiceTest {

    @InjectMocks
    private EstimateEmrCostManHoursRegisterService service;

    @Mock
    private EstimateEmrCostManHoursRepository estimateEmrCostManHoursRepository;

    @Mock
    private MessageLanguageRepository messageLanguageService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private CurrencyRepository currencyRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @Mock
    private CompanyRepository companyRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }



    @Test
    void Given_ExistingRecord_When_RegisterCostManHour_Then_ReturnExistingId() {
        // Arrange
        EstimateEmrCostManHoursRegisterInput.Input input = new EstimateEmrCostManHoursRegisterInput.Input();
        input.setUnidadNegocioId(1);
        input.setSubUnidadNegocioId(2);
        input.setCatTipoEstimadoId(3);
        input.setCatEquipmentCategory(1);
        input.setLineaNavieraId(4);
        input.setLanguageId(1);

        when(estimateEmrCostManHoursRepository.findExistingId(anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), any())).thenReturn(100);
        when(messageLanguageService.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Record exists");

        // Act
        EstimateEmrCostManHoursRegisterOutput output = service.registerCostManHour(input);

        // Assert
        assertEquals(2, output.getRespEstado());
        assertEquals("Record exists ID: 100", output.getRespMensaje());
        assertEquals(100, output.getRespNewId());
    }

    @Test
    void Given_NoExistingRecord_When_RegisterCostManHour_Then_InsertNewRecord() {
        // Arrange
        EstimateEmrCostManHoursRegisterInput.Input input = new EstimateEmrCostManHoursRegisterInput.Input();
        input.setUnidadNegocioId(1);
        input.setSubUnidadNegocioId(2);
        input.setCatTipoEstimadoId(3);
        input.setCatEquipmentCategory(1);
        input.setMonedaId(5);
        input.setCostoHoraHombre(100.0);
        input.setActivo(true);
        input.setUsuarioRegistroId(6);
        input.setLanguageId(1);

        BusinessUnit businessUnit = new BusinessUnit();
        Catalog catalog = new Catalog();
        Currency currency = new Currency();
        User user = new User();
        EstimateEmrCostManHours savedEntity = new EstimateEmrCostManHours();
        savedEntity.setId(200);

        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(businessUnit));
        when(businessUnitRepository.findById(2)).thenReturn(Optional.of(businessUnit));
        when(catalogRepository.findById(3)).thenReturn(Optional.of(catalog));
        when(currencyRepository.findById(5)).thenReturn(Optional.of(currency));
        when(userRepository.findById(6)).thenReturn(Optional.of(user));
        when(estimateEmrCostManHoursRepository.save(any(EstimateEmrCostManHours.class))).thenReturn(savedEntity);
        when(messageLanguageService.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Record created");

        // Act
        EstimateEmrCostManHoursRegisterOutput output = service.registerCostManHour(input);

        // Assert
        assertEquals(1, output.getRespEstado());
        assertEquals("Record created", output.getRespMensaje());
        assertEquals(200, output.getRespNewId());
    }


    @Test
    void Given_ExceptionInRepository_When_RegisterCostManHour_Then_ReturnErrorResponse() {
        // Arrange
        EstimateEmrCostManHoursRegisterInput.Input input = new EstimateEmrCostManHoursRegisterInput.Input();
        input.setUnidadNegocioId(1);

        when(businessUnitRepository.findById(1)).thenThrow(new RuntimeException("Database error"));

        // Act
        EstimateEmrCostManHoursRegisterOutput output = service.registerCostManHour(input);

        // Assert
        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
        assertEquals(0, output.getRespNewId());
    }
}