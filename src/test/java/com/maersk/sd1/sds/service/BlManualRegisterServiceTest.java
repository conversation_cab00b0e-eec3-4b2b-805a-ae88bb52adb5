package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sds.controller.dto.BlManualRegisterOutput;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

import static com.maersk.sd1.common.Parameter.*;
import static com.maersk.sd1.sds.controller.dto.BlManualRegisterInput.Input;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BlManualRegisterServiceTest {

    @Mock
    private ParameterSpLogRepository parameterSpLogRepository;

    @Mock
    private MessageLanguageService messageLanguageService;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;

    @Mock
    private GESCatalogService catalogService;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private ContainerRepository containerRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private DepotRepository depotRepository;

    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Mock
    private IsoCodeRepository isoCodeRepository;

    @Mock
    private DemurrageReceptionSettingBURepository demurrageReceptionSettingBURepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private DemurrageReceptionSettingRepository demurrageReceptionSettingRepository;

    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    @Mock
    private VesselProgrammingContainerImoRepository vesselProgrammingContainerImoRepository;

    @Mock
    private DemurrageReceptionRepository demurrageReceptionRepository;

    @Mock
    private ImoRepository imoRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private BlManualRegisterService blManualRegisterService;

    private static List<String> catalogAliases = List.of(
            CATALOG_DEPOT_OPER_MTY_DISCHARGE,
            DISCHARGE_FULL,
            CATALOG_CONTAINER_PACKAGING,
            CATALOG_MEASURE_WEIGHT_KG_ALIAS,
            CATALOG_FCL_EMPTY_MEASUREMENT_UNIT,
            CATALOG_LCL_BB_MEASUREMENT_UNIT,
            CATALOG_PRIMARY_CARGO_ORIGIN,
            IS_DOCUMENT_ACTIVE,
            CATALOG_SEAWAY_TRANSPORT,
            CATALOG_BL_MANUAL_CREATION_ORIGIN,
            CATALOG_BL_AUTOMATIC_CREATION_ORIGIN,
            CONDITION_FCL,
            CONDITION_MTY,
            CATALOG_RECEPTION_ORIGIN_VARIOUS,
            CATALOG_CREATION_FROM_REGISTER_BL
    );

    private List<String> isoCodesList = List.of("22R1", "22G1", "2263", "2260", "22U1", "22T1", "45G1", "42G1", "42R1", "45R1", "4534", "4536", "45P3", "4060", "42U1", "40TG", "45U1", "2000", "4000");

    HashMap<String, Integer> mockCatalogs() {
        HashMap<String, Integer> catalogIds = new HashMap<>();
        IntStream.range(0, catalogAliases.size()).forEach(index -> {
            catalogIds.put(catalogAliases.get(index), index);
        });
        return catalogIds;
    }

    private List<IsoCode> mockIsoCodes() {
        List<IsoCode> isoCodes = new ArrayList<>();
        IntStream.range(0, isoCodesList.size()).forEach(index -> {
            isoCodes.add(IsoCode.builder()
                    .id(index + 1)
                    .isoCode(isoCodesList.get(index))
                    .build());
        });
        return isoCodes;
    }

    @BeforeEach
    void setUp() {
        // Initialize mocks if needed
        when(businessUnitRepository.findById(anyInt())).thenAnswer(invocation -> {
            Integer id = invocation.getArgument(0);
            return Optional.of(BusinessUnit.builder().id(id).build());
        });

        // Mock ImoRepository to return a valid Imo object
        when(imoRepository.findById(anyInt())).thenAnswer(invocation -> {
            Integer id = invocation.getArgument(0);
            return Optional.of(Imo.builder().id(id).imoCode("IMO" + id).build());
        });

        // Mock UserRepository to return a valid User object
        when(userRepository.findById(anyInt())).thenAnswer(invocation -> {
            Integer id = invocation.getArgument(0);
            return Optional.of(User.builder().id(id).build());
        });

        // Mock BusinessUnit and subBusinessUnit
        BusinessUnit businessUnit = BusinessUnit.builder().id(86).build();
        BusinessUnit subBusinessUnit = BusinessUnit.builder().id(87).build();

        when(businessUnitRepository.findById(86)).thenReturn(Optional.of(businessUnit));
        when(businessUnitRepository.findById(87)).thenReturn(Optional.of(subBusinessUnit));
    }

    @Test
    void givenValidInputWithExistingData_whenRegisterBL_thenReturnSuccessfullOutput() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();

        Integer unidadNegocioId = 86;
        Integer subUnidadNegocioId = 87;
        Integer documentoCargaId = null;
        String blNumber = "blNumber";
        Integer shippingLineId = 1;
        Integer vesselProgrammingDetailId = 1;
        Integer shipperPortId = 1;
        Integer dischargePortId = 2;
        Integer shipperCompanyId = 1;
        Integer consigneeCompanyId = 2;
        String shipperDetail = "";
        String consigneeDetail = "";
        Integer depositoVacioId = null;

        String containerNumber = "TERF4000100";
        Integer catSizeId = 31071;
        Integer catTypeId = 31064;
        Integer tara = 1;
        Integer catConditionId = catalogIds.get(CONDITION_MTY);
        Integer catOperationId = catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE);
        String fechaDevolucionVacio = null;
        String imoChecklist = "[]";
        String contenedoresJson = String.format(
                "[{\"contenedor\":\"%s\",\"catalogo_tamano_id\":%d,\"catalogo_tipo_contenedor_id\":%d,\"tara\":%d,\"catalogo_condicion_id\":%d,\"fecha_devolucion_vacio\":%s,\"imo_checklist\":%s, \"cat_operacion_id\":%s}]",
                containerNumber, catSizeId, catTypeId, tara, catConditionId, fechaDevolucionVacio, imoChecklist, catOperationId
        );

        Integer idiomaId = 2;
        Integer usuarioRegistroId = 46588;
        Integer passRestriction = 0;
        Integer catMoveTypeId = 43087;
        String maerskDepotWithSd1 = null;
        Integer originDestinationDepotId = null;
        Boolean containerActive = true;

        Input input = Input.builder()
                .unidadNegocioId(unidadNegocioId)
                .subUnidadNegocioId(subUnidadNegocioId)
                .documentoCargaId(documentoCargaId)
                .numeroBl(blNumber)
                .lineaNavieraId(shippingLineId)
                .programacionNaveDetalleId(vesselProgrammingDetailId)
                .puertoEmbarqueId(shipperPortId)
                .puertoDescargaId(dischargePortId)
                .empresaEmbarcadorId(shipperCompanyId)
                .empresaConsignatarioId(consigneeCompanyId)
                .embarcadorDetalle(shipperDetail)
                .consignatarioDetalle(consigneeDetail)
                .depositoVacioId(depositoVacioId)
                .contenedoresJson(contenedoresJson)
                .idiomaId(idiomaId)
                .usuarioRegistroId(usuarioRegistroId)
                .passRestriction(passRestriction)
                .catMoveTypeId(catMoveTypeId)
                .maerskDepotWithSd1(maerskDepotWithSd1)
                .originDestinationDepotId(originDestinationDepotId)
                .build();

        Pageable pageable = PageRequest.of(0, 1);
        Integer vesselProgrammingId = 1;
        Integer containerId = 1;
        Integer cargoDocumentId = 1;
        Integer cargoDocumentVesselProgrammingDetailId = 2;
        Integer emptyDepotId = 1;
        Integer cargoDocumentDetailId = 1;
        Integer demurrageReceptionSettingBuId = 1;
        Integer demurrageReceptionSettingId = 1;
        Integer catOriginReceptionId = catalogIds.get(CATALOG_RECEPTION_ORIGIN_VARIOUS);
        Integer vesselProgrammingContainerId = 1;
        String shippingLineName = "Sl";
        Boolean demurrageReceptionSettingBuActive = true;
        Integer newVesselProgrammingContainerId = 1;

        VesselProgramming vesselProgramming = VesselProgramming.builder()
                .id(vesselProgrammingId)
                .build();
        VesselProgrammingDetail vesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(vesselProgrammingDetailId)
                .vesselProgramming(vesselProgramming)
                .build();
        Catalog sizeCatalog = Catalog.builder()
                .id(catSizeId)
                .description("40")
                .build();
        Catalog typeCatalog = Catalog.builder()
                .id(catTypeId)
                .description("RF")
                .build();
        Container container = Container.builder()
                .id(containerId)
                .containerNumber(containerNumber)
                .active(containerActive)
                .build();
        Catalog catOperation = Catalog.builder()
                .id(catOperationId)
                .build();
        VesselProgrammingDetail cargoDocumentVesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(cargoDocumentVesselProgrammingDetailId)
                .catOperation(catOperation)
                .build();
        ShippingLine shippingLine = ShippingLine.builder()
                .id(shippingLineId)
                .name(shippingLineName)
                .build();
        CargoDocument cargoDocument = CargoDocument.builder()
                .id(cargoDocumentId)
                .cargoDocument(blNumber)
                .vesselProgrammingDetail(cargoDocumentVesselProgrammingDetail)
                .shippingLine(shippingLine)
                .build();
        Depot emptyDepot = Depot.builder()
                .id(emptyDepotId)
                .build();
        CargoDocumentDetail cargoDocumentDetail = CargoDocumentDetail.builder()
                .id(cargoDocumentDetailId)
                .cargoDocument(cargoDocument)
                .build();
        DemurrageReceptionSettingBU demurrageReceptionSettingBu = DemurrageReceptionSettingBU.builder()
                .id(demurrageReceptionSettingBuId)
                .active(demurrageReceptionSettingBuActive)
                .build();

        List<Catalog> catalogList = List.of(sizeCatalog, typeCatalog);
        List<Container> containers = List.of(container);
        List<CargoDocument> cargoDocuments = List.of(cargoDocument);
        List<CargoDocumentDetail> cargoDocumentDetails = List.of(cargoDocumentDetail);
        List<DemurrageReceptionSettingBU> demurrageReceptionSettingBUS = List.of(demurrageReceptionSettingBu);

        Catalog catOriginReception = Catalog.builder()
                .id(catOriginReceptionId)
                .build();
        DemurrageReceptionSetting demurrageReceptionSetting = DemurrageReceptionSetting.builder()
                .id(demurrageReceptionSettingId)
                .catOrigenRecepcion(catOriginReception)
                .build();
        VesselProgrammingContainer vesselProgrammingContainer = VesselProgrammingContainer.builder()
                .id(vesselProgrammingContainerId)
                .build();
        List<VesselProgrammingContainer> vesselProgrammingContainers = List.of(
                vesselProgrammingContainer
        );

        BusinessUnit businessUnit = BusinessUnit.builder().id(unidadNegocioId).build();
        BusinessUnit subBusinessUnit = BusinessUnit.builder().id(subUnidadNegocioId).build();

        List<VesselProgrammingDetail> vesselProgrammingDetails = List.of(
                vesselProgrammingDetail
        );
        List<Catalog> operationCatalogs = List.of(
                catOperation
        );
        VesselProgrammingContainer newVesselProgrammingContainer = VesselProgrammingContainer.builder()
                .id(newVesselProgrammingContainerId)
                .build();

        //WHEN
        when(businessUnitRepository.findById(unidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(businessUnitRepository.findById(subUnidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(catalogService.findIdsByAliases(any())).thenReturn(mockCatalogs());
        when(vesselProgrammingDetailRepository.findById(input.getProgramacionNaveDetalleId())).thenReturn(Optional.of(vesselProgrammingDetail));
        when(catalogRepository.findByIdList(any())).thenReturn(catalogList);
        when(containerRepository.findByContainerNumbers(any())).thenReturn(containers);
        when(vesselProgrammingDetailRepository.existsActiveByVesselProgrammingAndOperationCatalog(vesselProgrammingId, catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE))).thenReturn(false);
        when(cargoDocumentRepository.findByCargoDocumentsList(
                List.of(blNumber.toUpperCase()), vesselProgrammingId, businessUnit, subBusinessUnit))
                .thenReturn(cargoDocuments);
        when(vesselProgrammingDetailRepository.findAllById(any())).thenReturn(vesselProgrammingDetails);
        when(catalogRepository.findAllById(any())).thenReturn(operationCatalogs);

        when(vesselProgrammingContainerRepository.save(any())).thenReturn(newVesselProgrammingContainer);

        when(depotRepository.findDefaultActiveBySubBusinessUnit(pageable, input.getSubUnidadNegocioId())).thenReturn(List.of(emptyDepot));
        when(cargoDocumentDetailRepository.findByDocumentoCargaIdIn(List.of(cargoDocumentId))).thenReturn(cargoDocumentDetails);
        when(isoCodeRepository.findAllByIsoCode(isoCodesList)).thenReturn(mockIsoCodes());
        when(demurrageReceptionSettingBURepository.findAllByShippingLine(List.of(shippingLineId), input.getSubUnidadNegocioId())).thenReturn(demurrageReceptionSettingBUS);

        when(demurrageReceptionSettingRepository.findFirstByBusinessUnitIdAndActiveTrue(input.getUnidadNegocioId())).thenReturn(Optional.of(demurrageReceptionSetting));

        when(vesselProgrammingContainerRepository.findAllActiveByVesselProgrammingDetail(List.of(cargoDocumentVesselProgrammingDetailId))).thenReturn(vesselProgrammingContainers);

        BlManualRegisterOutput output = blManualRegisterService.blManualRegister(input);

        //THEN
        Assertions.assertEquals(1, output.getRespEstado());
    }

    @Test
    void givenValidInputWithNonExistingData_whenRegisterBL_thenReturnSuccessfullOutput() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();

        Integer unidadNegocioId = 86;
        Integer subUnidadNegocioId = 87;
        Integer documentoCargaId = null;
        String blNumber = "blNumber";
        Integer shippingLineId = 1;
        Integer vesselProgrammingDetailId = 1;
        Integer shipperPortId = 1;
        Integer dischargePortId = 2;
        Integer shipperCompanyId = 1;
        Integer consigneeCompanyId = 2;
        String shipperDetail = "";
        String consigneeDetail = "";
        Integer depositoVacioId = null;

        String containerNumber = "TERF4000100";
        Integer catSizeId = 31071;
        Integer catTypeId = 31064;
        Integer tara = 1;
        Integer catConditionId = catalogIds.get(CONDITION_MTY);
        Integer catOperationId = catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE);
        String fechaDevolucionVacio = null;
        String imoChecklist = "[{\"imo_id\":1, \"imo_code\":\"code\"}]";
        String contenedoresJson = String.format(
                "[{\"contenedor\":\"%s\",\"catalogo_tamano_id\":%d,\"catalogo_tipo_contenedor_id\":%d,\"tara\":%d,\"catalogo_condicion_id\":%d,\"fecha_devolucion_vacio\":%s,\"imo_checklist\":%s, \"cat_operacion_id\":%s}]",
                containerNumber, catSizeId, catTypeId, tara, catConditionId, fechaDevolucionVacio, imoChecklist, catOperationId
        );

        Integer idiomaId = 2;
        Integer usuarioRegistroId = 46588;
        Integer passRestriction = 0;
        Integer catMoveTypeId = 43087;
        String maerskDepotWithSd1 = null;
        Integer originDestinationDepotId = null;
        Boolean containerActive = true;

        Input input = Input.builder()
                .unidadNegocioId(unidadNegocioId)
                .subUnidadNegocioId(subUnidadNegocioId)
                .documentoCargaId(documentoCargaId)
                .numeroBl(blNumber)
                .lineaNavieraId(shippingLineId)
                .programacionNaveDetalleId(vesselProgrammingDetailId)
                .puertoEmbarqueId(shipperPortId)
                .puertoDescargaId(dischargePortId)
                .empresaEmbarcadorId(shipperCompanyId)
                .empresaConsignatarioId(consigneeCompanyId)
                .embarcadorDetalle(shipperDetail)
                .consignatarioDetalle(consigneeDetail)
                .depositoVacioId(depositoVacioId)
                .contenedoresJson(contenedoresJson)
                .idiomaId(idiomaId)
                .usuarioRegistroId(usuarioRegistroId)
                .passRestriction(passRestriction)
                .catMoveTypeId(catMoveTypeId)
                .maerskDepotWithSd1(maerskDepotWithSd1)
                .originDestinationDepotId(originDestinationDepotId)
                .build();

        Pageable pageable = PageRequest.of(0, 1);
        Integer vesselProgrammingId = 1;
        Integer containerId = 1;
        Integer cargoDocumentVesselProgrammingDetailId = 2;
        Integer emptyDepotId = 1;
        Integer demurrageReceptionSettingId = 1;
        Integer catOriginReceptionId = catalogIds.get(CATALOG_RECEPTION_ORIGIN_VARIOUS);
        Integer newCargoDocumentId = 2;
        Integer vesselProgrammingContainerId = 1;
        Integer newVesselProgrammingContainerId = 1;

        VesselProgramming vesselProgramming = VesselProgramming.builder()
                .id(vesselProgrammingId)
                .build();
        VesselProgrammingDetail vesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(vesselProgrammingDetailId)
                .vesselProgramming(vesselProgramming)
                .build();
        Catalog sizeCatalog = Catalog.builder()
                .id(catSizeId)
                .description("40")
                .build();
        Catalog typeCatalog = Catalog.builder()
                .id(catTypeId)
                .description("RF")
                .build();
        Container newContainer = Container.builder()
                .id(containerId)
                .containerNumber(containerNumber)
                .active(containerActive)
                .build();
        Catalog catOperation = Catalog.builder()
                .id(catOperationId)
                .build();
        VesselProgrammingDetail cargoDocumentVesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(cargoDocumentVesselProgrammingDetailId)
                .catOperation(catOperation)
                .build();
        Depot emptyDepot = Depot.builder()
                .id(emptyDepotId)
                .build();

        List<Catalog> catalogList = List.of(sizeCatalog, typeCatalog);
        List<Container> containers = List.of();
        List<CargoDocument> cargoDocuments = List.of();
        List<CargoDocumentDetail> cargoDocumentDetails = List.of();
        List<DemurrageReceptionSettingBU> demurrageReceptionSettingBUS = List.of();

        Catalog catOriginReception = Catalog.builder()
                .id(catOriginReceptionId)
                .build();
        DemurrageReceptionSetting demurrageReceptionSetting = DemurrageReceptionSetting.builder()
                .id(demurrageReceptionSettingId)
                .catOrigenRecepcion(catOriginReception)
                .build();
        CargoDocument newCargoDocument = CargoDocument.builder()
                .id(newCargoDocumentId)
                .cargoDocument(blNumber)
                .vesselProgrammingDetail(cargoDocumentVesselProgrammingDetail)
                .build();
        List<CargoDocument> newCargoDocuments = List.of(
                newCargoDocument
        );
        VesselProgrammingContainer vesselProgrammingContainer = VesselProgrammingContainer.builder()
                .id(vesselProgrammingContainerId)
                .build();
        List<VesselProgrammingContainer> vesselProgrammingContainers = List.of(
                vesselProgrammingContainer
        );

        BusinessUnit businessUnit = BusinessUnit.builder().id(unidadNegocioId).build();
        BusinessUnit subBusinessUnit = BusinessUnit.builder().id(subUnidadNegocioId).build();
        List<VesselProgrammingDetail> vesselProgrammingDetails = List.of(
                vesselProgrammingDetail
        );
        List<Catalog> operationCatalogs = List.of(
                catOperation
        );
        VesselProgrammingContainer newVesselProgrammingContainer = VesselProgrammingContainer.builder()
                .id(newVesselProgrammingContainerId)
                .build();

        //WHEN
        when(businessUnitRepository.findById(unidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(businessUnitRepository.findById(subUnidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(catalogService.findIdsByAliases(any())).thenReturn(mockCatalogs());
        when(vesselProgrammingDetailRepository.findById(input.getProgramacionNaveDetalleId())).thenReturn(Optional.of(vesselProgrammingDetail));
        when(catalogRepository.findByIdList(any())).thenReturn(catalogList);
        when(containerRepository.findByContainerNumbers(any())).thenReturn(containers);
        when(vesselProgrammingDetailRepository.existsActiveByVesselProgrammingAndOperationCatalog(vesselProgrammingId, catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE))).thenReturn(false);
        when(cargoDocumentRepository.findByCargoDocumentsList(
                List.of(blNumber.toUpperCase()), vesselProgrammingId, businessUnit, subBusinessUnit))
                .thenReturn(cargoDocuments).thenReturn(newCargoDocuments);
        when(vesselProgrammingDetailRepository.findAllById(any())).thenReturn(vesselProgrammingDetails);
        when(catalogRepository.findAllById(any())).thenReturn(operationCatalogs);

        when(vesselProgrammingContainerRepository.save(any())).thenReturn(newVesselProgrammingContainer);

        when(depotRepository.findDefaultActiveBySubBusinessUnit(pageable, input.getSubUnidadNegocioId())).thenReturn(List.of(emptyDepot));
        when(cargoDocumentDetailRepository.findByDocumentoCargaIdIn(List.of())).thenReturn(cargoDocumentDetails);
        when(isoCodeRepository.findAllByIsoCode(isoCodesList)).thenReturn(mockIsoCodes());
        when(demurrageReceptionSettingBURepository.findAllByShippingLine(List.of(shippingLineId), input.getSubUnidadNegocioId())).thenReturn(demurrageReceptionSettingBUS);

        when(demurrageReceptionSettingRepository.findFirstByBusinessUnitIdAndActiveTrue(input.getUnidadNegocioId())).thenReturn(Optional.empty());
        when(demurrageReceptionSettingRepository.save(any())).thenReturn(demurrageReceptionSetting);
        when(containerRepository.save(any())).thenReturn(newContainer);

        when(vesselProgrammingContainerRepository.findAllActiveByVesselProgrammingDetail(List.of(cargoDocumentVesselProgrammingDetailId))).thenReturn(vesselProgrammingContainers);

        BlManualRegisterOutput output = blManualRegisterService.blManualRegister(input);

        //THEN
        Assertions.assertEquals(1, output.getRespEstado());
    }

    @Test
    void givenValidInputWithEmptyContainerArrayAndExistingCargoDocument_whenRegisterBL_thenReturnSuccessfullOutput() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();

        Integer unidadNegocioId = 86;
        Integer subUnidadNegocioId = 87;
        Integer documentoCargaId = null;
        String blNumber = "blNumber";
        Integer shippingLineId = 1;
        Integer vesselProgrammingDetailId = 1;
        Integer shipperPortId = 1;
        Integer dischargePortId = 2;
        Integer shipperCompanyId = 1;
        Integer consigneeCompanyId = 2;
        String shipperDetail = "";
        String consigneeDetail = "";
        Integer depositoVacioId = null;

        Integer catOperationId = catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE);

        String contenedoresJson = null;

        Integer idiomaId = 2;
        Integer usuarioRegistroId = 46588;
        Integer passRestriction = 0;
        Integer catMoveTypeId = 43087;
        String maerskDepotWithSd1 = null;
        Integer originDestinationDepotId = null;

        Input input = Input.builder()
                .unidadNegocioId(unidadNegocioId)
                .subUnidadNegocioId(subUnidadNegocioId)
                .documentoCargaId(documentoCargaId)
                .numeroBl(blNumber)
                .lineaNavieraId(shippingLineId)
                .programacionNaveDetalleId(vesselProgrammingDetailId)
                .puertoEmbarqueId(shipperPortId)
                .puertoDescargaId(dischargePortId)
                .empresaEmbarcadorId(shipperCompanyId)
                .empresaConsignatarioId(consigneeCompanyId)
                .embarcadorDetalle(shipperDetail)
                .consignatarioDetalle(consigneeDetail)
                .depositoVacioId(depositoVacioId)
                .contenedoresJson(contenedoresJson)
                .idiomaId(idiomaId)
                .usuarioRegistroId(usuarioRegistroId)
                .passRestriction(passRestriction)
                .catMoveTypeId(catMoveTypeId)
                .maerskDepotWithSd1(maerskDepotWithSd1)
                .originDestinationDepotId(originDestinationDepotId)
                .build();

        Pageable pageable = PageRequest.of(0, 1);
        Integer vesselProgrammingId = 1;
        Integer cargoDocumentId = 1;
        Integer cargoDocumentVesselProgrammingDetailId = 2;
        Integer emptyDepotId = 1;
        String shippingLineName = "Sl";

        VesselProgramming vesselProgramming = VesselProgramming.builder()
                .id(vesselProgrammingId)
                .build();
        VesselProgrammingDetail vesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(vesselProgrammingDetailId)
                .vesselProgramming(vesselProgramming)
                .build();
        Catalog catOperation = Catalog.builder()
                .id(catOperationId)
                .build();
        VesselProgrammingDetail cargoDocumentVesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(cargoDocumentVesselProgrammingDetailId)
                .catOperation(catOperation)
                .build();
        ShippingLine shippingLine = ShippingLine.builder()
                .id(shippingLineId)
                .name(shippingLineName)
                .build();
        CargoDocument cargoDocument = CargoDocument.builder()
                .id(cargoDocumentId)
                .cargoDocument(blNumber)
                .vesselProgrammingDetail(cargoDocumentVesselProgrammingDetail)
                .shippingLine(shippingLine)
                .build();
        Depot emptyDepot = Depot.builder()
                .id(emptyDepotId)
                .build();

        List<CargoDocument> pDocumentLoadIds = List.of(cargoDocument);

        BusinessUnit businessUnit = BusinessUnit.builder().id(unidadNegocioId).build();
        BusinessUnit subBusinessUnit = BusinessUnit.builder().id(subUnidadNegocioId).build();

        //WHEN
        when(businessUnitRepository.findById(unidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(businessUnitRepository.findById(subUnidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(catalogService.findIdsByAliases(any())).thenReturn(mockCatalogs());
        when(vesselProgrammingDetailRepository.findById(input.getProgramacionNaveDetalleId())).thenReturn(Optional.of(vesselProgrammingDetail));

        when(depotRepository.findDefaultActiveBySubBusinessUnit(pageable, input.getSubUnidadNegocioId())).thenReturn(List.of(emptyDepot));

        when(cargoDocumentRepository.findByCargoDocumentsList(
                List.of(input.getNumeroBl().toUpperCase()), vesselProgrammingId, businessUnit, subBusinessUnit))
                .thenReturn(pDocumentLoadIds);

        BlManualRegisterOutput output = blManualRegisterService.blManualRegister(input);

        //THEN
        Assertions.assertEquals(1, output.getRespEstado());
    }

    @Test
    void givenValidInputWithEmptyContainerArrayAndNonExistingCargoDocument_whenRegisterBL_thenReturnSuccessfullOutput() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();

        Integer unidadNegocioId = 86;
        Integer subUnidadNegocioId = 87;
        Integer documentoCargaId = null;
        String blNumber = "blNumber";
        Integer shippingLineId = 1;
        Integer vesselProgrammingDetailId = 1;
        Integer shipperPortId = 1;
        Integer dischargePortId = 2;
        Integer shipperCompanyId = 1;
        Integer consigneeCompanyId = 2;
        String shipperDetail = "";
        String consigneeDetail = "";
        Integer depositoVacioId = null;

        String contenedoresJson = null;

        Integer idiomaId = 2;
        Integer usuarioRegistroId = 46588;
        Integer passRestriction = 0;
        Integer catMoveTypeId = 43087;
        String maerskDepotWithSd1 = null;
        Integer originDestinationDepotId = null;

        Input input = Input.builder()
                .unidadNegocioId(unidadNegocioId)
                .subUnidadNegocioId(subUnidadNegocioId)
                .documentoCargaId(documentoCargaId)
                .numeroBl(blNumber)
                .lineaNavieraId(shippingLineId)
                .programacionNaveDetalleId(vesselProgrammingDetailId)
                .puertoEmbarqueId(shipperPortId)
                .puertoDescargaId(dischargePortId)
                .empresaEmbarcadorId(shipperCompanyId)
                .empresaConsignatarioId(consigneeCompanyId)
                .embarcadorDetalle(shipperDetail)
                .consignatarioDetalle(consigneeDetail)
                .depositoVacioId(depositoVacioId)
                .contenedoresJson(contenedoresJson)
                .idiomaId(idiomaId)
                .usuarioRegistroId(usuarioRegistroId)
                .passRestriction(passRestriction)
                .catMoveTypeId(catMoveTypeId)
                .maerskDepotWithSd1(maerskDepotWithSd1)
                .originDestinationDepotId(originDestinationDepotId)
                .build();

        Pageable pageable = PageRequest.of(0, 1);
        Integer vesselProgrammingId = 1;
        Integer emptyDepotId = 1;

        VesselProgramming vesselProgramming = VesselProgramming.builder()
                .id(vesselProgrammingId)
                .build();
        VesselProgrammingDetail vesselProgrammingDetail = VesselProgrammingDetail.builder()
                .id(vesselProgrammingDetailId)
                .vesselProgramming(vesselProgramming)
                .build();
        Depot emptyDepot = Depot.builder()
                .id(emptyDepotId)
                .build();

        List<CargoDocument> pDocumentLoadIds = List.of();

        BusinessUnit businessUnit = BusinessUnit.builder().id(unidadNegocioId).build();
        BusinessUnit subBusinessUnit = BusinessUnit.builder().id(subUnidadNegocioId).build();

        //WHEN
        when(businessUnitRepository.findById(unidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(businessUnitRepository.findById(subUnidadNegocioId)).thenReturn(Optional.of(businessUnit));
        when(catalogService.findIdsByAliases(any())).thenReturn(catalogIds);
        when(vesselProgrammingDetailRepository.findById(input.getProgramacionNaveDetalleId())).thenReturn(Optional.of(vesselProgrammingDetail));

        when(depotRepository.findDefaultActiveBySubBusinessUnit(pageable, input.getSubUnidadNegocioId())).thenReturn(List.of(emptyDepot));

        when(cargoDocumentRepository.findByCargoDocumentsList(
                List.of(input.getNumeroBl().toUpperCase()), vesselProgrammingId, businessUnit, subBusinessUnit))
                .thenReturn(pDocumentLoadIds);

        BlManualRegisterOutput output = blManualRegisterService.blManualRegister(input);

        //THEN
        Assertions.assertEquals(1, output.getRespEstado());
    }

}