package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.sds.controller.dto.TransporterListOutput;
import com.maersk.sd1.common.repository.CompanyRoleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class TransporterListServiceTest {

    @Mock
    private CompanyRoleRepository companyRoleRepository;

    @InjectMocks
    private TransporterListService transporterListService;

    private Company company1;
    private Company company2;

    @BeforeEach
    void setUp() {
        company1 = new Company();
        company1.setId(1001);
        company1.setDocument("DOC1001");
        company1.setLegalName("Transporter A");
        company1.setStatus(true);

        company2 = new Company();
        company2.setId(1002);
        company2.setDocument("DOC1002");
        company2.setLegalName("Transporter B");
        company2.setStatus(true);
    }

    @Test
    void givenBusinessUnitIdAndViewType1_WhenGetTransporters_ThenReturnTransporterDetailsWithDocumentAndLegalName() {
        Mockito.when(companyRoleRepository.findTransportersByBusinessUnit(any())).thenReturn(Arrays.asList(company1, company2));

        TransporterListOutput detailList = transporterListService.getTransporters(1, 1);

        assertEquals(company1.getId(), detailList.getTransporterList().getFirst().getCompanyId());
        assertEquals(company1.getDocument(), detailList.getTransporterList().getFirst().getDocument());
        assertEquals(company1.getLegalName(), detailList.getTransporterList().getFirst().getLegalName());
        assertNull(detailList.getTransporterList().getFirst().getCompany());
    }

    @Test
    void givenBusinessUnitIdAndViewType2_WhenGetTransporters_ThenReturnTransporterDetailsWithCombinedDocumentAndLegalName() {
        Mockito.when(companyRoleRepository.findTransportersByBusinessUnit(any())).thenReturn(Arrays.asList(company1, company2));

        TransporterListOutput detailList = transporterListService.getTransporters(1, 2);

        assertEquals(2, detailList.getTransporterList().size());
        assertEquals(company1.getId(), detailList.getTransporterList().get(0).getCompanyId());
        assertEquals(null, detailList.getTransporterList().get(0).getDocument());
        assertEquals(null, detailList.getTransporterList().get(0).getLegalName());
        assertEquals("DOC1001 - Transporter A", detailList.getTransporterList().get(0).getCompany());
    }
}