package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.dto.DeleteVesselProgrammingDetailOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeleteVesselProgrammingDetailServiceTest {

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    @Mock
    private EirRepository eirRepository;

    @Mock
    private VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private DeleteVesselProgrammingDetailService deleteService;

    @BeforeEach
    void setUp() {
        VesselProgrammingDetail detailRef = new VesselProgrammingDetail();
        detailRef.setId(1);
    }

    @Test
    void testDeleteVesselProgrammingDetailSuccess() {
        when(bookingRepository.countByVesselProgrammingDetailAndActiveTrue(any(VesselProgrammingDetail.class))).thenReturn(0);
        when(cargoDocumentRepository.countByVesselProgrammingDetailAndActiveTrue(any(VesselProgrammingDetail.class))).thenReturn(0);
        when(vesselProgrammingContainerRepository.countByVesselProgrammingDetailAndActiveTrue(any(VesselProgrammingDetail.class))).thenReturn(0);
        when(eirRepository.countByVesselProgrammingDetailAndActiveTrue(any(VesselProgrammingDetail.class))).thenReturn(0);
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success message");

        DeleteVesselProgrammingDetailOutput output = deleteService.deleteVesselProgrammingDetail(1, BigDecimal.ONE, 1);

        assertEquals(1, output.getRespEstado());
        assertEquals("Success message", output.getRespMensaje());
        verify(vesselProgrammingCutoffRepository, times(1)).deactivateCutoff(anyInt(), anyInt(), any(LocalDateTime.class));
        verify(vesselProgrammingDetailRepository, times(1)).deactivateDetail(anyInt(), anyInt(), any(LocalDateTime.class));
    }

    @Test
    void testDeleteVesselProgrammingDetailDetailInUse() {
        when(bookingRepository.countByVesselProgrammingDetailAndActiveTrue(any(VesselProgrammingDetail.class))).thenReturn(1);
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Detail in use");

        DeleteVesselProgrammingDetailOutput output = deleteService.deleteVesselProgrammingDetail(1, BigDecimal.ONE, 1);

        assertEquals(2, output.getRespEstado());
        assertEquals("Detail in use", output.getRespMensaje());
        verify(vesselProgrammingCutoffRepository, never()).deactivateCutoff(anyInt(), anyInt(), any(LocalDateTime.class));
        verify(vesselProgrammingDetailRepository, never()).deactivateDetail(anyInt(), anyInt(), any(LocalDateTime.class));
    }

    @Test
    void testDeleteVesselProgrammingDetailException() {
        when(bookingRepository.countByVesselProgrammingDetailAndActiveTrue(any(VesselProgrammingDetail.class))).thenThrow(new RuntimeException("Database error"));

        DeleteVesselProgrammingDetailOutput output = deleteService.deleteVesselProgrammingDetail(1, BigDecimal.ONE, 1);

        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
    }
}