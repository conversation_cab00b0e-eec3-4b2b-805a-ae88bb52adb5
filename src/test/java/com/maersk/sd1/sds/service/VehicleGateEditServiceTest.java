package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.Truck;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.CompanyRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.sds.dto.VehicleGateEditOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class VehicleGateEditServiceTest {

    @Mock
    private TruckRepository truckRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private VehicleGateEditService vehicleGateEditService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidInputs_When_CompanyNotSuspendedAndTruckExists_Then_UpdateTruckAndReturnSuccess() {
        // Given
        Integer vehicleId = 1;
        String model = "ModelX";
        Integer truckCompanyId = 100;
        Integer userModificationId = 200;
        Integer languageId = 1;

        Company company = new Company();
        company.setSuspended(false);

        Truck truck = new Truck();
        truck.setId(vehicleId);

        when(companyRepository.findById(truckCompanyId)).thenReturn(Optional.of(company));
        when(truckRepository.findById(vehicleId)).thenReturn(Optional.of(truck));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, languageId)).thenReturn("Vehicle updated successfully");

        // When
        VehicleGateEditOutput output = vehicleGateEditService.editVehicleGate(vehicleId, model, truckCompanyId, userModificationId, languageId);

        // Then
        assertEquals(1, output.getRespEstado());
        assertEquals("Vehicle updated successfully", output.getRespMensaje());
        verify(truckRepository, times(1)).save(truck);
    }

    @Test
    void Given_ValidInputs_When_CompanyIsSuspended_Then_ReturnSuspendedMessage() {
        // Given
        Integer vehicleId = 1;
        String model = "ModelX";
        Integer truckCompanyId = 100;
        Integer userModificationId = 200;
        Integer languageId = 1;

        Company company = new Company();
        company.setSuspended(true);

        when(companyRepository.findById(truckCompanyId)).thenReturn(Optional.of(company));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 49, languageId)).thenReturn("Company is suspended");

        // When
        VehicleGateEditOutput output = vehicleGateEditService.editVehicleGate(vehicleId, model, truckCompanyId, userModificationId, languageId);

        // Then
        assertEquals(2, output.getRespEstado());
        assertEquals("Company is suspended", output.getRespMensaje());
        verify(truckRepository, never()).save(any());
    }

    @Test
    void Given_ValidInputs_When_TruckDoesNotExist_Then_ReturnTruckNotFoundMessage() {
        // Given
        Integer vehicleId = 1;
        String model = "ModelX";
        Integer truckCompanyId = 100;
        Integer userModificationId = 200;
        Integer languageId = 1;

        Company company = new Company();
        company.setSuspended(false);

        when(companyRepository.findById(truckCompanyId)).thenReturn(Optional.of(company));
        when(truckRepository.findById(vehicleId)).thenReturn(Optional.empty());

        // When
        VehicleGateEditOutput output = vehicleGateEditService.editVehicleGate(vehicleId, model, truckCompanyId, userModificationId, languageId);

        // Then
        assertEquals(0, output.getRespEstado());
        assertEquals("Truck not found with vehicleId=" + vehicleId, output.getRespMensaje());
        verify(truckRepository, never()).save(any());
    }

    @Test
    void Given_InvalidInputs_When_CompanyDoesNotExist_Then_ReturnCompanyNotFoundMessage() {
        // Given
        Integer vehicleId = 1;
        String model = "ModelX";
        Integer truckCompanyId = 100;
        Integer userModificationId = 200;
        Integer languageId = 1;

        when(companyRepository.findById(truckCompanyId)).thenReturn(Optional.empty());

        // When
        VehicleGateEditOutput output = vehicleGateEditService.editVehicleGate(vehicleId, model, truckCompanyId, userModificationId, languageId);

        // Then
        assertEquals(0, output.getRespEstado());
        assertEquals("Company not found. truckCompanyId=" + truckCompanyId, output.getRespMensaje());
        verify(truckRepository, never()).save(any());
    }

    @Test
    void Given_ValidInputs_When_ExceptionOccurs_Then_ReturnErrorMessage() {
        // Given
        Integer vehicleId = 1;
        String model = "ModelX";
        Integer truckCompanyId = 100;
        Integer userModificationId = 200;
        Integer languageId = 1;

        when(companyRepository.findById(truckCompanyId)).thenThrow(new RuntimeException("Database error"));

        // When
        VehicleGateEditOutput output = vehicleGateEditService.editVehicleGate(vehicleId, model, truckCompanyId, userModificationId, languageId);

        // Then
        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
        verify(truckRepository, never()).save(any());
    }
}