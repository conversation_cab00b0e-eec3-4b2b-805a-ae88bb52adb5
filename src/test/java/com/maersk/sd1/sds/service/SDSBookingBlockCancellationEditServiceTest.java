package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.BookingBlockCancellationRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.controller.dto.SDSBookingBlockCancellationEditInput;
import com.maersk.sd1.sds.controller.dto.SDSBookingBlockCancellationEditOutput;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class SDSBookingBlockCancellationEditServiceTest {

    @InjectMocks
    private SDSBookingBlockCancellationEditService service;

    @Mock
    private BookingBlockCancellationRepository repository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    private SDSBookingBlockCancellationEditInput.Input input;

    @BeforeEach
    void setUp() {
        input = new SDSBookingBlockCancellationEditInput.Input();
        input.setCancelBloqueoBookingId(123);
        input.setComments("Test Comment");
        input.setUserModificationId(456);
        input.setLanguageId(1);
    }

    @Test
    void givenValidInput_WhenEditBookingBlockCancellation_ThenSuccess() {

        given(repository.updateBookBlockCommentAndUser(eq(123), eq("Test Comment"), any(LocalDateTime.class), eq(456)))
                .willReturn(1);
        given(messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, 1)).willReturn("Success Message");

        SDSBookingBlockCancellationEditOutput output = service.editBookingBlockCancellation(input);

        Assertions.assertEquals(1, output.getRespStatus());
        Assertions.assertEquals("Success Message", output.getRespMessage());
        verify(repository, times(1)).updateBookBlockCommentAndUser(eq(123), eq("Test Comment"), any(LocalDateTime.class), eq(456));
    }

    @Test
    void givenValidInput_WhenNoRowUpdated_ThenNoRecordUpdatedMessage() {

        given(repository.updateBookBlockCommentAndUser(eq(123), eq("Test Comment"), any(LocalDateTime.class), eq(456)))
                .willReturn(0);

        SDSBookingBlockCancellationEditOutput output = service.editBookingBlockCancellation(input);

        Assertions.assertEquals(0, output.getRespStatus());
        Assertions.assertTrue(output.getRespMessage().contains("No record updated"));
    }

    @Test
    void givenValidInput_WhenExceptionThrown_ThenDBErrorMessage() {

        given(repository.updateBookBlockCommentAndUser(eq(123), eq("Test Comment"), any(LocalDateTime.class), eq(456)))
                .willThrow(new DataAccessException("DB Error") {});

        SDSBookingBlockCancellationEditOutput output = service.editBookingBlockCancellation(input);

        Assertions.assertEquals(0, output.getRespStatus());
        Assertions.assertTrue(output.getRespMessage().contains("DB Error"));
    }

    @Test
    void givenValidInput_WhenEmptyResultDataAccess_ThenNoRecordUpdatedMessage() {

        given(repository.updateBookBlockCommentAndUser(eq(123), eq("Test Comment"), any(LocalDateTime.class), eq(456)))
                .willThrow(new EmptyResultDataAccessException(1));

        SDSBookingBlockCancellationEditOutput output = service.editBookingBlockCancellation(input);

        Assertions.assertEquals(0, output.getRespStatus());
    }
}