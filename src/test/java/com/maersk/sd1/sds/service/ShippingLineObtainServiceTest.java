package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.sds.controller.dto.ShippingLineObtainOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShippingLineObtainServiceTest {

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @InjectMocks
    private ShippingLineObtainService shippingLineObtainService;

    private ShippingLine shippingLine;

    @BeforeEach
    void setUp() {
        shippingLine = new ShippingLine();
        shippingLine.setId(123);
        shippingLine.setName("Test Line");
        shippingLine.setActive(true);
        shippingLine.setColor("Blue");
        shippingLine.setRegistrationDate(LocalDateTime.now().minusDays(1));
        shippingLine.setModificationDate(LocalDateTime.now());
        shippingLine.setShippingLineCompany("LineCo");
    }

    @Test
    void givenValidRequest_WhenRemoveShippingLine_ThenReturnSuccessResponse() {
        when(shippingLineRepository.findShippingLineById(anyInt())).thenReturn(Optional.of(shippingLine));
        ShippingLineObtainOutput result = shippingLineObtainService.obtainShippingLine(123);
        assertNotNull(result);
//        assertEquals(1, result.getRespStatus());
//        assertEquals("Success", result.getRespMessage());
        assertEquals(123, result.getShippingLineId());
        assertEquals("Test Line", result.getName());
        assertEquals(true, result.getActive());
        assertEquals("Blue", result.getColor());
        assertEquals("LineCo", result.getShippingLineCompany());
    }

    @Test
    void givenNullRequest_WhenRemoveShippingLine_ThenReturnBadRequestResponse() {
        when(shippingLineRepository.findShippingLineById(anyInt())).thenReturn(Optional.empty());
        ShippingLineObtainOutput result = shippingLineObtainService.obtainShippingLine(999);
        assertNotNull(result);
//        assertEquals(0, result.getRespStatus());
//        assertTrue(result.getRespMessage().contains("No record found"));
    }

    @Test
    void givenServiceThrowsException_WhenRemoveShippingLine_ThenReturnInternalServerErrorResponse() {
        when(shippingLineRepository.findShippingLineById(anyInt())).thenThrow(new RuntimeException("DB error"));
        ShippingLineObtainOutput result = shippingLineObtainService.obtainShippingLine(123);
        assertNotNull(result);
//        assertEquals(0, result.getRespStatus());
//        assertTrue(result.getRespMessage().contains("DB error"));
    }
}