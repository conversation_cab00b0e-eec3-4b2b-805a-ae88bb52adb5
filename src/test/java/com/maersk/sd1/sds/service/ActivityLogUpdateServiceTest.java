package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.ActivityLog;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.ActivityLogRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sds.controller.dto.ActivityLogUpdateInput;
import com.maersk.sd1.sds.controller.dto.ActivityLogUpdateOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ActivityLogUpdateServiceTest {

    @Mock
    private ActivityLogRepository activityLogRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private ActivityLogUpdateService activityLogUpdateService;

    private ActivityLogUpdateInput.Input input;
    private ActivityLog mainActivityLog;
    private Catalog newCatalogStatus;

    @BeforeEach
    void setUp() {
        input = new ActivityLogUpdateInput.Input();
        input.setActivityLogId(1);
        input.setStatusAlias("completed");
        input.setContainerNumber("CNT12345678");
        input.setActivityAlias("load");
        input.setSubBusinessUnitId(789L);
        input.setModuleAlias("mod1");
        input.setEirNumber(1011L);
        input.setUserModificationId(123);
        input.setDataInput("{\"field1\":\"value1\", \"field2\":\"value2\"}");

        mainActivityLog = new ActivityLog();
        mainActivityLog.setId(1);
        mainActivityLog.setRetryable(true);

        newCatalogStatus = new Catalog();
        newCatalogStatus.setAlias("completed");
    }

    @Test
    void Given_RetryableIsTrue_When_UpdateActivityLog_Then_UpdatedCorrectly() {
        when(activityLogRepository.findById(input.getActivityLogId())).thenReturn(Optional.of(mainActivityLog));
        when(catalogRepository.findByAlias(input.getStatusAlias())).thenReturn(newCatalogStatus);
        when(activityLogRepository.findActivityLogsForUpdate(anyInt(), anyString(), anyInt(), anyString(), anyLong(), anyString()))
                .thenReturn(List.of(mainActivityLog));

        ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);

        assertEquals(1, output.getRespEstado());
        assertEquals("Updated correctly", output.getRespMensaje());
        verify(activityLogRepository, times(1)).saveAll(anyList());
    }

    @Test
    void Given_NoLogFound_When_UpdateActivityLog_Then_ErrorReturned() {
        when(activityLogRepository.findById(input.getActivityLogId())).thenReturn(Optional.empty());

        ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Activity log not found", output.getRespMensaje());
        verify(activityLogRepository, times(0)).saveAll(anyList());
    }

    @Test
    void Given_RetryableIsFalse_When_UpdateActivityLog_Then_ReturnError() {
        mainActivityLog.setRetryable(false);
        when(activityLogRepository.findById(input.getActivityLogId())).thenReturn(Optional.of(mainActivityLog));

        ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Selected log is not retryable", output.getRespMensaje());
        verify(activityLogRepository, times(0)).saveAll(anyList());
    }

    @Test
    void Given_RetryableIsTrueButNoLogsToUpdate_When_UpdateActivityLog_Then_ReturnNoActivityLogsFoundMessage() {
        when(activityLogRepository.findById(input.getActivityLogId())).thenReturn(Optional.of(mainActivityLog));

        when(activityLogRepository.findActivityLogsForUpdate(anyInt(), anyString(), anyInt(), anyString(), anyLong(), anyString()))
                .thenReturn(List.of());

        ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("No activity logs found to update.", output.getRespMensaje());
        verify(activityLogRepository, times(0)).saveAll(anyList());
    }

    @Test
    void Given_CatalogNotFound_When_UpdateActivityLog_Then_ReturnError() {
        when(activityLogRepository.findById(input.getActivityLogId())).thenReturn(Optional.of(mainActivityLog));
        when(catalogRepository.findByAlias(input.getStatusAlias())).thenReturn(null);

        when(activityLogRepository.findById(input.getActivityLogId())).thenReturn(Optional.of(mainActivityLog));
        when(catalogRepository.findByAlias(input.getStatusAlias())).thenReturn(null);
        when(activityLogRepository.findActivityLogsForUpdate(anyInt(), anyString(), anyInt(), anyString(), anyLong(), anyString()))
                .thenReturn(List.of(mainActivityLog));

        ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Status catalog not found", output.getRespMensaje());
        verify(activityLogRepository, times(0)).saveAll(anyList());
    }

    @Test
    void Given_RepositoryThrowsException_When_UpdateActivityLog_Then_CatchAndReturnError() {
        when(activityLogRepository.findById(input.getActivityLogId())).thenThrow(new RuntimeException("Database error"));

        ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
        verify(activityLogRepository, times(0)).saveAll(anyList());
    }
}


