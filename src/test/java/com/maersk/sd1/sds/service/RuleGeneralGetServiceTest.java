package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.sds.dto.RuleGeneralGetInputDTO;
import com.maersk.sd1.sds.dto.RuleGeneralGetOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RuleGeneralGetServiceTest {

    @Mock
    private SystemRuleRepository systemRuleRepository;

    @InjectMocks
    private RuleGeneralGetService ruleGeneralGetService;

    private RuleGeneralGetInputDTO.Root request;

    @BeforeEach
    public void setUp() {
        RuleGeneralGetInputDTO.Input input = new RuleGeneralGetInputDTO.Input();
        input.setSubBusinessUnitLocalAlias("alias1");
        input.setSystemRuleId("rule1");
        input.setTypeRule("[\"type1\", \"type2\"]");

        RuleGeneralGetInputDTO.Prefix prefix = new RuleGeneralGetInputDTO.Prefix();
        prefix.setInput(input);

        request = new RuleGeneralGetInputDTO.Root();
        request.setPrefix(prefix);
    }

    @Test
    void testRuleGeneralGetService() {
        String jsonResult = "[{\"business_unit_alias\": \"alias1\", \"type_rule\": \"type1\", \"action\": true}," +
                "{\"business_unit_alias\": \"alias1\", \"type_rule\": \"type2\", \"action\": false}]";
        when(systemRuleRepository.findRuleByIdAndActiveTrue(anyString())).thenReturn(jsonResult);

        ResponseEntity<ResponseController<List<RuleGeneralGetOutputDTO>>> response = ruleGeneralGetService.ruleGeneralGetService(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        List<RuleGeneralGetOutputDTO> output = response.getBody().getResult();
        assertEquals(2, output.size());

        RuleGeneralGetOutputDTO firstOutput = output.get(0);
        assertEquals("type1", firstOutput.getTypeRule());
        assertTrue(Boolean.parseBoolean(firstOutput.getAction()));

        RuleGeneralGetOutputDTO secondOutput = output.get(1);
        assertEquals("type2", secondOutput.getTypeRule());
        assertFalse(Boolean.parseBoolean(secondOutput.getAction()));
    }

    @Test
    void testRuleGeneralGetServiceWithEmptyResult() {
        when(systemRuleRepository.findRuleByIdAndActiveTrue(anyString())).thenReturn("[]");

        ResponseEntity<ResponseController<List<RuleGeneralGetOutputDTO>>> response = ruleGeneralGetService.ruleGeneralGetService(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        List<RuleGeneralGetOutputDTO> output = response.getBody().getResult();
        assertTrue(output.isEmpty());
    }

    @Test
    void testRuleGeneralGetServiceWithException() {
        when(systemRuleRepository.findRuleByIdAndActiveTrue(anyString())).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<List<RuleGeneralGetOutputDTO>>> response = ruleGeneralGetService.ruleGeneralGetService(request);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        List<RuleGeneralGetOutputDTO> output = response.getBody().getResult();
        assertTrue(output.isEmpty());
    }
}
