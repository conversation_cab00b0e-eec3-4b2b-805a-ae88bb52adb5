package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.DepotRepository;
import com.maersk.sd1.sds.dto.DepositSearchInput;
import com.maersk.sd1.sds.dto.DepositSearchOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DepositSearchServiceTest {

    @Mock
    private DepotRepository depotRepository;

    @InjectMocks
    private DepositSearchService depositSearchService;

    private DepositSearchInput.Input input;
    private DepositSearchOutput item;

    @BeforeEach
    public void setUp() {
        input = new DepositSearchInput.Input();
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(1);
        input.setDepositId(1);
        input.setDepositName("Test Deposit");

        item = new DepositSearchOutput();
        item.setDepositId(1);
        item.setDepositName("Test Deposit");
    }

    @Test
    void testSearchDeposits_Success() {
        PageRequest topTen = PageRequest.of(0, 10);
        Page<DepositSearchOutput> pageResult = new PageImpl<>(Arrays.asList(item), topTen, 1);

        when(depotRepository.findDeposits(1, 1, 1, "Test Deposit", topTen)).thenReturn(pageResult);

        List<DepositSearchOutput> output = depositSearchService.searchDeposits(input);
        
        assertEquals(1, output.size());
        assertEquals("Test Deposit", output.getFirst().getDepositName());
    }

    @Test
    void testSearchDeposits_NoResults() {
        PageRequest topTen = PageRequest.of(0, 10);
        Page<DepositSearchOutput> pageResult = new PageImpl<>(Collections.emptyList(), topTen, 0);

        when(depotRepository.findDeposits(1, 1, 1, "Test Deposit", topTen)).thenReturn(pageResult);

        List<DepositSearchOutput> output = depositSearchService.searchDeposits(input);

        assertEquals(0, output.size());
    }

    @Test
    void testSearchDeposits_Exception() {
        PageRequest topTen = PageRequest.of(0, 10);

        when(depotRepository.findDeposits(1, 1, 1, "Test Deposit", topTen)).thenThrow(new RuntimeException("Database error"));

        List<DepositSearchOutput> output = depositSearchService.searchDeposits(input);

        assertEquals(0, output.size());
    }
}
