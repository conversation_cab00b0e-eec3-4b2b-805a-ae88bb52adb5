package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.VesselProgrammingRepository;
import com.maersk.sd1.sds.dto.VesselProgrammingListInput;
import com.maersk.sd1.sds.dto.VesselProgrammingListOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class VesselProgrammingListServiceTest {

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;

    @InjectMocks
    private VesselProgrammingListService vesselProgrammingListService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListVesselProgramming() {
        VesselProgrammingListInput.Input input = new VesselProgrammingListInput.Input();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        input.setSubBusinessUnitId(1L);
        input.setVesselName("Vessel");
        input.setVoyage("Voyage");
        input.setState(true);
        input.setFromEta(LocalDate.now().minusDays(1).format(formatter));
        input.setToEta(LocalDate.now().plusDays(1).format(formatter));
        input.setPage(1);
        input.setSize(10);

        List<VesselProgrammingListOutput.DataItems> dataItemsList = new ArrayList<>();
        dataItemsList.add(new VesselProgrammingListOutput.DataItems(1, "Vessel", "Voyage", LocalDateTime.now(), LocalDateTime.now(), "Agency", "Port", true, LocalDateTime.now(), 1, "User", "User Full Name", LocalDateTime.now(), 2, "Mod User", "Mod User Full Name", "Origin", LocalDateTime.now()));

        when(vesselProgrammingRepository.findVesselProgrammingList(anyLong(), anyString(), anyString(), anyBoolean(), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(dataItemsList);

        VesselProgrammingListOutput result = vesselProgrammingListService.listVesselProgramming(input);

        assertEquals(1, result.getTotalCount().getFirst().getFirst());
        assertEquals(1, result.getData().size());
        assertEquals("Vessel", result.getData().getFirst().getVesselName());
    }

    @Test
    void testListVesselProgramming_EmptyResult() {
        VesselProgrammingListInput.Input input = new VesselProgrammingListInput.Input();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        input.setSubBusinessUnitId(1L);
        input.setVesselName("Vessel");
        input.setVoyage("Voyage");
        input.setState(true);
        input.setFromEta(LocalDate.now().minusDays(1).format(formatter));
        input.setToEta(LocalDate.now().plusDays(1).format(formatter));
        input.setPage(1);
        input.setSize(10);

        when(vesselProgrammingRepository.findVesselProgrammingList(anyLong(), anyString(), anyString(), anyBoolean(), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new ArrayList<>());

        VesselProgrammingListOutput result = vesselProgrammingListService.listVesselProgramming(input);

        assertEquals(0, result.getTotalCount().getFirst().getFirst());
        assertEquals(0, result.getData().size());
    }
}