package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.sds.dto.TemplateGetInputDTO;
import com.maersk.sd1.sds.dto.TemplateGetOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class TemplateGetServiceTest {

    @Mock
    private SystemRuleRepository systemRuleRepository;

    @InjectMocks
    private TemplateGetService templateGetService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testTemplateGetService() {
        // Arrange
        TemplateGetInputDTO.Root input = new TemplateGetInputDTO.Root();
        TemplateGetInputDTO.Prefix prefix = new TemplateGetInputDTO.Prefix();
        TemplateGetInputDTO.Input inputData = new TemplateGetInputDTO.Input();
        inputData.setTemplateName("testTemplate");
        inputData.setLanguageId(1);
        prefix.setInput(inputData);
        input.setPrefix(prefix);

        TemplateGetOutputDTO outputDTO = new TemplateGetOutputDTO();
        outputDTO.setUrl(List.of("http://example.com/template"));


        when(systemRuleRepository.findTemplateUrl(any(String.class), any(Integer.class)))
                .thenReturn("http://example.com/template");

        // Act
        ResponseEntity<ResponseController<TemplateGetOutputDTO>> response = templateGetService.templateGetService(input);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(List.of("http://example.com/template"), response.getBody().getResult().getUrl());
    }

    @Test
    void testTemplateGetService_NullResult() {
        // Prepare test data
        TemplateGetInputDTO.Root input = new TemplateGetInputDTO.Root();
        TemplateGetInputDTO.Prefix prefix = new TemplateGetInputDTO.Prefix();
        TemplateGetInputDTO.Input inputData = new TemplateGetInputDTO.Input();
        inputData.setTemplateName("templateName");
        inputData.setLanguageId(1);
        prefix.setInput(inputData);
        input.setPrefix(prefix);

        when(systemRuleRepository.findTemplateUrl(anyString(), anyInt())).thenReturn(null);

        // Call the service method
        ResponseEntity<ResponseController<TemplateGetOutputDTO>> response = templateGetService.templateGetService(input);

        // Verify the results
        TemplateGetOutputDTO outputDTO = response.getBody().getResult();
        assertEquals(null, outputDTO.getUrl());
    }

    @Test
    void testTemplateGetService_Exception() {
        // Prepare test data
        TemplateGetInputDTO.Root input = new TemplateGetInputDTO.Root();
        TemplateGetInputDTO.Prefix prefix = new TemplateGetInputDTO.Prefix();
        TemplateGetInputDTO.Input inputData = new TemplateGetInputDTO.Input();
        inputData.setTemplateName("templateName");
        inputData.setLanguageId(1);
        prefix.setInput(inputData);
        input.setPrefix(prefix);

        when(systemRuleRepository.findTemplateUrl(anyString(), anyInt())).thenThrow(new RuntimeException("Database error"));

        // Call the service method and verify exception
        assertThrows(RuntimeException.class, () -> {
            templateGetService.templateGetService(input);
        });
    }

    @Test
    void testTemplateGetService_InvalidInput() {
        // Prepare test data with invalid input
        TemplateGetInputDTO.Root input = new TemplateGetInputDTO.Root();
        TemplateGetInputDTO.Prefix prefix = new TemplateGetInputDTO.Prefix();
        TemplateGetInputDTO.Input inputData = new TemplateGetInputDTO.Input();
        inputData.setTemplateName(null); // Invalid input
        inputData.setLanguageId(1);
        prefix.setInput(inputData);
        input.setPrefix(prefix);

        // Call the service method and verify exception
        assertThrows(IllegalArgumentException.class, () -> {
            templateGetService.templateGetService(input);
        });
    }
}