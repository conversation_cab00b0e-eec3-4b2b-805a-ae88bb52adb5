package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.service.BookingService;
import com.maersk.sd1.sds.dto.ContainerDetailProcessFileInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ContainerDetailProcessFileTest {

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private BookingBlockCancellationRepository bookingBlockCancellationRepository;

    @Mock
    private BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;

    @Mock
    private BookingEdiRepository bookingEdiRepository;

    @Mock
    private BookingService bookingService;

    @Mock
    private BookingDetailRepository bookingDetailRepository;

    @InjectMocks
    private ContainerDetailProcessFileService containerDetailProcessFileService;

    private ContainerDetailProcessFileInput.Root input, input2;
    private ContainerDetailProcessFileInput.Input f, inputValues2;

    private Integer bookingId = 1;
    private Integer userRegistrationId = 46433;
    private Integer isCreationSourceForEdiCancel = 1;
    private Integer isBkStatusActive = 1;
    private Integer isBkediRejected = 2;
    private Integer isBkStatusCancelled = 3;
    private Integer isBkediDone = 4;
    private Integer isBkCancelAutoBkedi = 5;
    private Integer loadDocumentId = 1;
    private Integer isReferenceReplace = 0;
    private Integer isCreationSourceForEdi = 0;
    private Integer isCreationSourceForEdiCancelAuto = 0;
    private Integer isContainerTypeReeferCa = 0;

    @BeforeEach
    public void setUp() {
        input = new ContainerDetailProcessFileInput.Root();
        f = new ContainerDetailProcessFileInput.Input();

        f.setBookingEdi(3);
        f.setReservationStatus("1");
        f.setBusinessUnitId(2);
        f.setSubBusinessUnitId(5);
        f.setVesselProgrammingDetailId(11);
        f.setBooking("1BUE033068");
        f.setContainerDimensionId(41384);
        f.setContainerTypeId(31064);
        f.setReservedQuantity(10);
        f.setContainerDimension2id(6006);
        f.setContainerType2id(456);
        f.setReservedQuantity2(20);
        f.setClientId(8008);
        f.setProductGroupDescription("Product Group Description");
        f.setProductId(9009);
        f.setTemperature("12.5");
        f.setImoId(13013);
        f.setBookingLineId(14014);
        f.setGrossWeightEDI(100);
        f.setGrossWeightEDI2(200);
        f.setColdTreatment(true);
        f.setControlledAtmosphere(true);
        f.setUserRegistrationId(46433);
        f.setParameterSequenceDetails("");
        ContainerDetailProcessFileInput.Prefix prefix = new ContainerDetailProcessFileInput.Prefix();
        prefix.setInput(f);
        input = new ContainerDetailProcessFileInput.Root();
        input.setSds(prefix);
        isBkStatusActive = 1;


        input2 = new ContainerDetailProcessFileInput.Root();
        inputValues2 = new ContainerDetailProcessFileInput.Input();

        inputValues2 = new ContainerDetailProcessFileInput.Input();
        inputValues2.setBookingEdi(3);
        inputValues2.setReservationStatus("2");
        inputValues2.setBusinessUnitId(2);
        inputValues2.setSubBusinessUnitId(5);
        inputValues2.setVesselProgrammingDetailId(11);
        inputValues2.setBooking("1BUE033068");
        inputValues2.setContainerDimensionId(41384);
        inputValues2.setContainerTypeId(31064);
        inputValues2.setReservedQuantity(10);
        inputValues2.setContainerDimension2id(6006);
        inputValues2.setContainerType2id(456);
        inputValues2.setReservedQuantity2(20);
        inputValues2.setClientId(8008);
        inputValues2.setProductGroupDescription("Product Group Description");
        inputValues2.setProductId(9009);
        inputValues2.setTemperature("12.5");
        inputValues2.setImoId(13013);
        inputValues2.setBookingLineId(14014);
        inputValues2.setGrossWeightEDI(100);
        inputValues2.setGrossWeightEDI2(200);
        inputValues2.setColdTreatment(true);
        inputValues2.setControlledAtmosphere(true);
        inputValues2.setUserRegistrationId(46433);
        inputValues2.setParameterSequenceDetails("[{\"sequence_alias\":\"SEQ001\"}]");
        ContainerDetailProcessFileInput.Prefix prefix2 = new ContainerDetailProcessFileInput.Prefix();
        prefix2.setInput(inputValues2);
        input2 = new ContainerDetailProcessFileInput.Root();
        input2.setSds(prefix2);

    }

    @Test
    void Given_AllInputsForBookingCancel_When_ProcessContainerDetails_Then_BookingCancelWillSuccess()
    {
        Booking bookingEntity = new Booking();
        bookingEntity.setId(bookingId);
        bookingEntity.setApprovedBooking(false);
        bookingEntity.setCatBookingStatus(new Catalog(isBkStatusActive));
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(10);
        bookingEntity.setVesselProgrammingDetail(vesselProgrammingDetail);

        when(catalogRepository.findIdByAlias("48274")).thenReturn(isBkediRejected);
        when(catalogRepository.findIdByAlias("47771")).thenReturn(isCreationSourceForEdiCancel);
        when(catalogRepository.findIdByAlias("sd1_booking_block_cancel_auto_bkedi")).thenReturn(isBkCancelAutoBkedi);
        when(catalogRepository.findIdByAlias("48273")).thenReturn(isBkediDone);
        when(catalogRepository.findIdByAlias("43062")).thenReturn(isBkStatusCancelled);
        when(catalogRepository.findIdByAlias("43061")).thenReturn(isBkStatusActive);
        when(catalogRepository.findIdByAlias("sd1_bkedi_reference_replace")).thenReturn(isReferenceReplace);
        when(catalogRepository.findIdByAlias("47739")).thenReturn(isCreationSourceForEdi);
        when(catalogRepository.findIdByAlias("47740")).thenReturn(isCreationSourceForEdiCancelAuto);
        when(catalogRepository.findIdByAlias("31048")).thenReturn(isContainerTypeReeferCa);

        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setId(1);
        bookingDetail.setBooking(bookingEntity);
        bookingDetail.setActive(true);

        CargoDocument cargoDocument = new CargoDocument();
        cargoDocument.setId(456);
        cargoDocument.setActive(true);

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(1);
        cargoDocumentDetail.setBookingDetail(bookingDetail);
        cargoDocumentDetail.setCargoDocument(cargoDocument);
        cargoDocumentDetail.setActive(true);

        User registrationUser = new User();
        registrationUser.setId(1); // Assuming User class has a setId method

        User modificationUser = new User();
        modificationUser.setId(2); // Assuming User class has a setId method

        BusinessUnit parentBusinessUnit = new BusinessUnit();
        parentBusinessUnit.setId(100); // Assuming a parent business unit

        DaylightSaving daylightSaving = new DaylightSaving();
        daylightSaving.setId(1); // Assuming DaylightSaving class has a setId method

        BusinessUnit businessUnit = BusinessUnit.builder()
                .id(123)
                .name("Finance Department")
                .status(true)
                .registrationUser(registrationUser)
                .registrationDate(LocalDateTime.now())
                .modificationUser(modificationUser)
                .modificationDate(LocalDateTime.now())
                .parentBusinessUnit(parentBusinessUnit)
                .businesUnitAlias("FIN_DEPT")
                .daylightSaving(daylightSaving)
                .build();


        BookingEdiSetting bookingEdiSetting = new BookingEdiSetting();
        bookingEdiSetting.setId(1); // Assuming BookingEdiSetting class has a setId method

        BookingEdi bookingEdi = BookingEdi.builder()
                .id(1)
                .bookingEdiSetting(bookingEdiSetting)
                .build();


        when(bookingEdiRepository.findById(any(Integer.class))).thenReturn(Optional.ofNullable(bookingEdi));

        when(bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdOrderByBookingIssueDateDesc(any(String.class), any(Integer.class)))
                .thenReturn(Optional.of(bookingEntity));

        when(cargoDocumentRepository.findActiveCargoDocumentByBookingId(any(Integer.class)))
                .thenReturn(loadDocumentId);

        when(businessUnitRepository.getReferenceById(any(Integer.class)))
                .thenReturn(businessUnit);

        when(cargoDocumentRepository.findById(any(Integer.class)))
                .thenReturn(Optional.of(cargoDocument));

        when(catalogRepository.findById(48167)).thenReturn(Optional.of(new Catalog(48167)));
        when(catalogRepository.findById(48170)).thenReturn(Optional.of(new Catalog(48170)));

        when(userRepository.findById(any(Integer.class))).thenReturn(Optional.of(new User(userRegistrationId)));

        when(bookingBlockCancellationRepository.save(any(BookingBlockCancellation.class)))
                .thenReturn(new BookingBlockCancellation());

        when(bookingEdiRepository.findById(any(Integer.class))).thenReturn(Optional.of(new BookingEdi()));

        // Mock the approveBooking method
        doNothing().when(bookingRepository).approveBooking(any(Integer.class), any(Integer.class), any(Integer.class));

        containerDetailProcessFileService.processContainerDetails(input);


        verify(bookingBlockCancellationRepository).save(any(BookingBlockCancellation.class));
        verify(bookingBlockCancellationDetailRepository).save(any(BookingBlockCancellationDetail.class));
        verify(bookingRepository).save(any(Booking.class));
        verify(cargoDocumentRepository).save(any(CargoDocument.class));
    }


    @Test
    void Given_AllInputsForNewBookingWithSequenceDetails_When_ProcessContainerDetails_Then_BookingSuccess() {

        Booking bookingEntity = new Booking();
        bookingEntity.setId(bookingId);
        bookingEntity.setApprovedBooking(true);
        bookingEntity.setCatBookingStatus(new Catalog(isBkStatusActive));
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(10);
        bookingEntity.setVesselProgrammingDetail(vesselProgrammingDetail);
        Catalog catBookingStatus = new Catalog();
        catBookingStatus.setId(1);
        bookingEntity.setCatBookingStatus(catBookingStatus);

        BookingEdiSetting bookingEdiSetting = new BookingEdiSetting();
        bookingEdiSetting.setId(1); // Assuming BookingEdiSetting class has a setId method

        BookingEdi bookingEdi = BookingEdi.builder()
                .id(1)
                .bookingEdiSetting(bookingEdiSetting)
                .build();

        when(bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdOrderByBookingIssueDateDesc(anyString(), any(Integer.class)))
                .thenReturn(Optional.of(bookingEntity));
        when(bookingDetailRepository.existsByBookingIdAndAttendedQuantityGreaterThanAndActive(
                bookingId, 0, true)).thenReturn(false);


        when(bookingRepository.findById(any(Integer.class))).thenReturn(Optional.of(bookingEntity));

        when(catalogRepository.findById(any(Integer.class))).thenReturn(Optional.of(catBookingStatus));
        when(bookingEdiRepository.findById(any(Integer.class))).thenReturn(Optional.ofNullable(bookingEdi));

        containerDetailProcessFileService.processContainerDetails(input2);

        verify(bookingDetailRepository).save(any(BookingDetail.class));
        verify(bookingRepository).save(any(Booking.class));
        verify(bookingEdiRepository).save(any(BookingEdi.class));


    }

    @Test
    void Given_AllInputsForNewBookingWithoutSequenceDetails_When_ProcessContainerDetails_Then_BookingSuccess() {

        inputValues2.setParameterSequenceDetails("");
        Booking bookingEntity = new Booking();
        bookingEntity.setId(bookingId);
        bookingEntity.setApprovedBooking(true);
        bookingEntity.setCatBookingStatus(new Catalog(isBkStatusActive));
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(10);
        bookingEntity.setVesselProgrammingDetail(vesselProgrammingDetail);
        Catalog catBookingStatus = new Catalog();
        catBookingStatus.setId(1);
        bookingEntity.setCatBookingStatus(catBookingStatus);

        BookingEdiSetting bookingEdiSetting = new BookingEdiSetting();
        bookingEdiSetting.setId(1); // Assuming BookingEdiSetting class has a setId method

        BookingEdi bookingEdi = BookingEdi.builder()
                .id(1)
                .bookingEdiSetting(bookingEdiSetting)
                .build();

        when(bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdOrderByBookingIssueDateDesc(anyString(), any(Integer.class)))
                .thenReturn(Optional.of(bookingEntity));
        when(bookingDetailRepository.existsByBookingIdAndAttendedQuantityGreaterThanAndActive(
                bookingId, 0, true)).thenReturn(false);

        when(bookingRepository.findById(any(Integer.class))).thenReturn(Optional.of(bookingEntity));

        when(catalogRepository.findById(any(Integer.class))).thenReturn(Optional.of(catBookingStatus));
        when(bookingEdiRepository.findById(any(Integer.class))).thenReturn(Optional.ofNullable(bookingEdi));

        containerDetailProcessFileService.processContainerDetails(input2);

        verify(bookingDetailRepository).save(any(BookingDetail.class));
        verify(bookingRepository).save(any(Booking.class));
        verify(bookingEdiRepository).save(any(BookingEdi.class));


    }


}




