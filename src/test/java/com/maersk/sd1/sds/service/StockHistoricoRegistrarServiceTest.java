package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.HistoricalInfo;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.HistoricalInfoRepository;
import com.maersk.sd1.sds.dto.StockHistoricoRegistrarOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StockHistoricoRegistrarServiceTest {

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private HistoricalInfoRepository historicalInfoRepository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private StockHistoricoRegistrarService stockHistoricoRegistrarService;

    @BeforeEach
    void setUp() {
        // Setup mock data
        BusinessUnit colombiaSubUnit = new BusinessUnit();
        colombiaSubUnit.setId(1);
        BusinessUnit ecuadorSubUnit = new BusinessUnit();
        ecuadorSubUnit.setId(2);

        lenient().when(businessUnitRepository.findActiveSubUnitsByParent(31)).thenReturn(List.of(colombiaSubUnit));
        lenient().when(businessUnitRepository.findActiveSubUnitsByParent(2)).thenReturn(List.of(ecuadorSubUnit));
        lenient().when(jdbcTemplate.queryForList(anyString())).thenReturn(List.of(Map.of("detalleJSON", "{\"key\":\"value\"}")));
    }

    @Test
    void testRegisterStockHistorico() {
        StockHistoricoRegistrarOutput output = stockHistoricoRegistrarService.registerStockHistorico(1);

        assertEquals(1, output.getRespEstado());
        assertEquals("Procesado Correctamente", output.getRespMensaje());

        verify(historicalInfoRepository, times(2)).save(any(HistoricalInfo.class));
    }

    @Test
    void testRegisterStockHistoricoWithException() {
        when(businessUnitRepository.findActiveSubUnitsByParent(31)).thenThrow(new RuntimeException("Database error"));

        StockHistoricoRegistrarOutput output = stockHistoricoRegistrarService.registerStockHistorico(1);

        assertEquals(2, output.getRespEstado());
        assertEquals("Ocurrió un error: Database error", output.getRespMensaje());
    }
}