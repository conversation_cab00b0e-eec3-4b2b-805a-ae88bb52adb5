package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.ListShipScheduleOperationInput;
import com.maersk.sd1.sds.controller.dto.ListShipScheduleOperationOutput;
import com.maersk.sd1.common.repository.VesselProgrammingCutoffRepository;
import com.maersk.sd1.common.repository.VesselProgrammingDetailRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.model.VesselProgrammingCutoff;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ShippingLine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ListShipScheduleOperationServiceTest {

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private ListShipScheduleOperationService listShipScheduleOperationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void GivenValidVesselProgrammingIdAndLanguageId_WhenListShipScheduleOperation_ThenSuccess() {
        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(new VesselProgrammingDetail());
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        List<VesselProgrammingCutoff> mockCutoffs = Collections.singletonList(new VesselProgrammingCutoff());
        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(mockCutoffs);

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn("Operation");
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn("Creation Origin");

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void GivenDatabaseError_WhenListShipScheduleOperation_ThenFailure() {
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenThrow(new RuntimeException("Database error"));

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void GivenValidInput_WhenListShipScheduleOperation_ThenInvokeSelfMethod() {
        ListShipScheduleOperationInput.Input input = new ListShipScheduleOperationInput.Input();
        input.setVesselProgrammingId(1);
        input.setLanguageId(1);

        ListShipScheduleOperationService self = mock(ListShipScheduleOperationService.class);
        listShipScheduleOperationService.setSelf(self);

        when(self.listShipScheduleOperation(anyInt(), anyInt())).thenReturn(Collections.singletonList(new ListShipScheduleOperationOutput()));

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(input);

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void givenEmptyDetails_WhenListShipScheduleOperation_ThenReturnEmptyResponse() {
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(Collections.emptyList());

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenNullDetails_WhenListShipScheduleOperation_ThenReturnEmptyResponse() {
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(null);

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidDetailsWithNullCutoffs_WhenListShipScheduleOperation_ThenReturnDetailsWithoutCutoffs() {
        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(new VesselProgrammingDetail());
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(null);

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn("Operation");
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn("Creation Origin");

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidDetailsWithEmptyCutoffs_WhenListShipScheduleOperation_ThenReturnDetailsWithoutCutoffs() {
        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(new VesselProgrammingDetail());
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(Collections.emptyList());

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn("Operation");
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn("Creation Origin");

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidDetailsWithMultipleCutoffs_WhenListShipScheduleOperation_ThenReturnDetailsWithCutoffs() throws Exception {
        VesselProgrammingDetail detail = new VesselProgrammingDetail();
        detail.setId(1);
        detail.setCatOperation(new Catalog());
        detail.getCatOperation().setId(1);

        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(detail);
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        VesselProgrammingCutoff cutoff1 = new VesselProgrammingCutoff();
        cutoff1.setShippingLine(new ShippingLine());
        cutoff1.getShippingLine().setShippingLineCompany("Company1");
        cutoff1.setDateCutoffRetreatEmptyDry(LocalDateTime.parse("2023-01-01T00:00:00"));
        cutoff1.setDateCutoffRetreatEmptyReefer(LocalDateTime.parse("2023-01-02T00:00:00"));

        VesselProgrammingCutoff cutoff2 = new VesselProgrammingCutoff();
        cutoff2.setShippingLine(new ShippingLine());
        cutoff2.getShippingLine().setShippingLineCompany("Company2");
        cutoff2.setDateCutoffRetreatEmptyDry(LocalDateTime.parse("2023-02-01T00:00:00"));
        cutoff2.setDateCutoffRetreatEmptyReefer(LocalDateTime.parse("2023-02-02T00:00:00"));

        List<VesselProgrammingCutoff> mockCutoffs = List.of(cutoff1, cutoff2);
        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(mockCutoffs);

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn("Operation");
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn("Creation Origin");

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidDetailsWithNullUser_WhenListShipScheduleOperation_ThenReturnDetailsWithoutUserInfo() {
        VesselProgrammingDetail detail = new VesselProgrammingDetail();
        detail.setId(1);
        detail.setCatOperation(new Catalog());
        detail.getCatOperation().setId(1);

        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(detail);
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(Collections.emptyList());

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn("Operation");
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn("Creation Origin");

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidDetailsWithNullOperationCategory_WhenListShipScheduleOperation_ThenReturnDetailsWithoutOperationCategory() {
        VesselProgrammingDetail detail = new VesselProgrammingDetail();
        detail.setId(1);

        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(detail);
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(Collections.emptyList());

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn("Operation");
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn("Creation Origin");

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidDetailsWithNullCatalogDescriptions_WhenListShipScheduleOperation_ThenReturnDetailsWithNullDescriptions() {
        VesselProgrammingDetail detail = new VesselProgrammingDetail();
        detail.setId(1);
        detail.setCatOperation(new Catalog());
        detail.getCatOperation().setId(1);

        List<VesselProgrammingDetail> mockDetails = Collections.singletonList(detail);
        when(vesselProgrammingDetailRepository.findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(anyInt()))
                .thenReturn(mockDetails);

        when(vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActiveIsTrue(anyInt()))
                .thenReturn(Collections.emptyList());

        when(catalogRepository.findTranslatedLongDesc(anyInt(), anyInt())).thenReturn(null);
        when(catalogRepository.findTranslatedShortDesc(anyInt(), anyInt())).thenReturn(null);

        List<ListShipScheduleOperationOutput> result = listShipScheduleOperationService.listShipScheduleOperation(1, 1);

        assertNotNull(result);
    }
}
