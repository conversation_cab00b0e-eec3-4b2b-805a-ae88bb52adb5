package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.DepotObtainOutput;
import com.maersk.sd1.common.repository.DepotRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class DepotObtainServiceTest {

    @Mock
    private DepotRepository depotRepository;

    @InjectMocks
    private DepotObtainService depotObtainService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("Given_ValidDepositId_When_GetDepotById_Then_ReturnsDepotObtainOutput")
    void Given_ValidDepositId_When_GetDepotById_Then_ReturnsDepotObtainOutput() {

        Integer depositId = 123;
        DepotObtainOutput mockOutput = new DepotObtainOutput(
                123,
                10,
                "CODE",
                "NAME",
                "ADDRESS",
                true,
                null,
                null,
                null,
                false,
                null,
                null
        );
        when(depotRepository.findDepotObtainDataById(depositId)).thenReturn(mockOutput);

        DepotObtainOutput result = depotObtainService.getDepotById(depositId);

        assertNotNull(result);
    }

    @Test
    @DisplayName("Given_InvalidDepositId_When_GetDepotById_Then_ReturnsNull")
    void Given_InvalidDepositId_When_GetDepotById_Then_ReturnsNull() {

        Integer depositId = 999;
        when(depotRepository.findDepotObtainDataById(any())).thenReturn(null);

        DepotObtainOutput result = depotObtainService.getDepotById(depositId);

        assertNull(result);
    }
}